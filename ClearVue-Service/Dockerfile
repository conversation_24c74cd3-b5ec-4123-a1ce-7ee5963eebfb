# Use Node.js 16.13-slim as the base image
FROM node:16.13-slim

# Set the working directory inside the container
WORKDIR /src

# Install the specified version of npm globally
RUN npm install -g npm@9.6.6

# Copy the entire application code to the container
COPY . .

# Install dependencies
RUN npm install

# Build the application
RUN npm run build

EXPOSE 5000

# Specify the command to run the built application
CMD ["node", "dist/app.js"]
