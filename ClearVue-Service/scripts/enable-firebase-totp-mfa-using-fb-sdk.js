/*

#1 Visit: https://console.cloud.google.com/iam-admin/iam?inv=1&invt=Ab2vAg&project=<project-id>
Select Project from either [ClearVue, ClearVue-Staging, ClearVue-Local]

For user "firebase-adminsdk" add "Service Usage Consumer" & "Firebase Authentication Admin" role

#2 Visit: https://console.firebase.google.com/project/<project-id>/authentication/providers
Enable Authentication: [Email/Password]

#3 Visit: Project settings -> Add Web App -> Name: ClearVueWeb -> Copy "Web API Key"
FIREBASE_WEB_API_KEY=<Web API Key>

#4 Visit: Project settings -> Service account -> Generate new private key -> Copy .json file content and paste in below env variable
FIREBASE_AUTH_KEY_JSON='{"type": "service_account",  ...}'

#5 Add total following .env variables 
FIREBASE_WEB_API_KEY=<Web API Key from above step>
FIREBASE_AUTH_KEY_JSON=<Service Account JSON from above step>
FIREBASE_AUTH_ENABLED=1
FIREBASE_TOTP_MFA_ENABLED=1

#6 Now Run below script to get access token for curl command
Before Run: 
-> Update "keyFile" path in below script (Private key file download from above step #4)

Run:
-> $node scripts/enable-firebase-totp-mfa-using-fb-sdk.js
-> Copy generated token from console.

#7 Now Open Postman and run below curl command
Paste access token in "Authorization" header
Update Project ID in URL
Update "YOUR_PROJECT_ID" in URL

curl --location --request PATCH 'https://identitytoolkit.googleapis.com/admin/v2/projects/<YOUR_PROJECT_ID>/config?updateMask=mfa' \
--header 'Authorization: Bearer <access-token>' \
--header 'Content-Type: application/json' \
--header 'X-Goog-User-Project: <YOUR_PROJECT_ID>' \
--data '{
        "mfa": {
          "providerConfigs": [{
            "state": "ENABLED",
            "totpProviderConfig": {
              "adjacentIntervals": 5
            }
          }]
       }
    }'


#8 Check mfa.providerConfigs."state" => "ENABLED" in response
Then TOTP MFA is enabled for your Firebase project.
Ready to deploy your code.

*/

async function generateAccessTokenForCurl() {
  try {
    const { GoogleAuth } = require("google-auth-library");

    const auth = new GoogleAuth({
      keyFile: "/home/<USER>/Yash/clearvue/cv-local-firebase-adminsdk.json",
      scopes: ["https://www.googleapis.com/auth/cloud-platform"],
    });

    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    console.log("Access Token for curl:", accessToken.token);

    return accessToken.token;
  } catch (error) {
    console.error("Error generating access token:", error);
    throw error;
  }
}

const tok = generateAccessTokenForCurl();
console.log("Token:", tok);
