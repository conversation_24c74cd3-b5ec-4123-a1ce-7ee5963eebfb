{"name": "clearvue-service", "version": "1.0.0", "description": "Backend repo for the ClearVue", "main": "dist/app.js", "scripts": {"dev": "node dist/app.js", "build": "tsc -p src/", "clean": "rm -rf ./dist", "start": "npm run build && npm run dev"}, "repository": {"type": "git", "url": ""}, "keywords": ["nodejs, typescript, express"], "license": "MIT", "homepage": "", "dependencies": {"@bugsnag/js": "^7.14.1", "@bugsnag/plugin-express": "^7.14.0", "@google-cloud/translate": "^7.2.1", "@joi/date": "^2.1.0", "@sendgrid/mail": "^7.6.0", "aws-sdk": "^2.1053.0", "axios": "^1.7.8", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "dotenv": "^10.0.0", "download": "^8.0.0", "easy-promise-all": "^1.0.4", "express": "^4.17.1", "express-fileupload": "^1.2.1", "express-winston": "^4.1.0", "firebase-admin": "^11.11.1", "ioredis": "^4.27.6", "joi": "^17.4.0", "jsonwebtoken": "^8.5.1", "lodash.clonedeep": "^4.5.0", "moment": "^2.29.1", "multer": "^1.4.4", "mysql": "^2.18.1", "reflect-metadata": "^0.1.13", "sqlstring": "^2.3.3", "swagger-ui-express": "^4.3.0", "typeorm": "^0.2.34", "uuid": "^8.3.2", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.5", "winston-graylog2": "^2.1.2", "winston-slack-webhook-transport": "^2.0.1"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.4", "@babel/preset-typescript": "^7.16.0", "@types/express": "^4.17.12", "@types/node": "^15.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.5", "typescript": "^4.3.5"}}