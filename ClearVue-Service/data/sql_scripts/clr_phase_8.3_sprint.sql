-- Check count
SELECT count(*) FROM los_rule WHERE pay_type = 'Training';

-- Repeate untill all updated
UPDATE los_rule 
SET pay_type = 'Induction training' 
WHERE pay_type = 'Training' 
LIMIT 10000;

---------------------------------

-- Check count
SELECT count(*) FROM rate_card_data WHERE pay_type = 'Training';

-- <PERSON>eate untill all updated
UPDATE rate_card_data 
SET pay_type = 'Induction training' 
WHERE pay_type = 'Training' 
LIMIT 10000;

---------------------------------

-- Check count
SELECT count(*) FROM time_and_attendance_data WHERE pay_type = 'Training';

-- Repeate untill all updated
UPDATE time_and_attendance_data 
SET pay_type = 'Induction training' 
WHERE pay_type = 'Training' 
LIMIT 10000;

---------------------------------

-- Rename existing column
ALTER TABLE margins 
RENAME COLUMN training_margin TO induction_training_margin;

-- Create new column with specified constraints
ALTER TABLE margins 
ADD COLUMN training_margin DECIMAL(3,2) DEFAULT '0.00' After ssp;

---------------------------------

-- AWS S3--- Upload to Bucket
-- timeAndAttendance_sample.csv 
-- demo_rate_card_to_upload.csv

---------------------------------
----------------------------------

INSERT INTO permissions (access_type, user_type_id, feature_id, created_by, updated_by) VALUES
 (1, 4, 29, 1, 1),
 (1, 5, 29, 1, 1),
 (1, 7, 29, 1, 1),
 (1, 8, 29, 1, 1);


----------------------------------
----------------------------------

CREATE TABLE `client_yearly_rules` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `client_id` bigint unsigned NOT NULL,
    `financial_year_start` VARCHAR(4) NOT NULL,
    `financial_year_end` VARCHAR(4) NOT NULL,
    `start_date` date NOT NULL,
    `end_date` date NOT NULL,
    `total_weeks` int NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` BIGINT UNSIGNED DEFAULT NULL,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `updated_by` BIGINT UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_client_yearly_rules_financial_year` (`client_id`, `financial_year_start`),
    KEY `fk_client_yearly_rules_client_id` (`client_id`),
    KEY `fk_client_yearly_rules_created_by` (`created_by`),
    KEY `fk_client_yearly_rules_updated_by` (`updated_by`),
    CONSTRAINT `fk_client_yearly_rules_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
    CONSTRAINT `fk_client_yearly_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
    CONSTRAINT `fk_client_yearly_rules_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE client_yearly_rules
ADD COLUMN ni_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after total_weeks;

ALTER TABLE client_yearly_rules
ADD CONSTRAINT client_yearly_rules_chk_ni_percent CHECK (ni_percent >= 0 AND ni_percent <= 100);

ALTER TABLE client_yearly_rules
ADD COLUMN ni_threshold DECIMAL(6, 2) NOT NULL COMMENT 'Threshold value between 0 and 999' after ni_percent;

ALTER TABLE client_yearly_rules
ADD CONSTRAINT client_yearly_rules_chk_ni_threshold CHECK (ni_threshold >= 0 AND ni_threshold <= 999);

ALTER TABLE client_yearly_rules
ADD COLUMN pension_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after ni_threshold,
ADD CONSTRAINT client_yearly_rules_chk_pension_percent CHECK (pension_percent >= 0 AND pension_percent <= 100),
ADD COLUMN pension_threshold DECIMAL(6, 2) NOT NULL COMMENT 'Threshold value between 0 and 999' after pension_percent,
ADD CONSTRAINT client_yearly_rules_chk_pension_threshold CHECK (pension_threshold >= 0 AND pension_threshold <= 999),
ADD COLUMN app_levy_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after pension_threshold,
ADD CONSTRAINT client_yearly_rules_chk_app_levy_percent CHECK (app_levy_percent >= 0 AND app_levy_percent <= 100);

