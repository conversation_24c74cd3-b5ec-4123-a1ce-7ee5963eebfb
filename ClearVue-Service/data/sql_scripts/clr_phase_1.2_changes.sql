/* script to add pension opt out field in worker table*/

ALTER TABLE `workers` 
ADD COLUMN `pension_opt_out` TINYINT(1) NOT NULL DEFAULT '0' AFTER `appreciation`;

/* script to Standard Margin, Overtime Margin, Transport Fee, SSP, Training Margin and Training Hours fields in agency_client_association table*/

ALTER TABLE `agency_client_association` 
ADD COLUMN `overtime_margin` DECIMAL(3,2) NULL DEFAULT 0 AFTER `currency`,
ADD COLUMN `transport_fee` DECIMAL(3,2) NULL DEFAULT 0 AFTER `overtime_margin`,
ADD COLUMN `ssp` DECIMAL(3,2) NULL DEFAULT 0 AFTER `transport_fee`,
ADD COLUMN `training_margin` DECIMAL(3,2) NULL DEFAULT 0 AFTER `ssp`,
ADD COLUMN `training_hours` DECIMAL(3,2) NULL DEFAULT 0 AFTER `training_margin`;

/* script to add transport and internal chargeback field in worker table*/

ALTER TABLE `workers` 
ADD COLUMN `transport` TINYINT(1) NOT NULL DEFAULT 0 AFTER `pension_opt_out`,
ADD COLUMN `internal_chargeback` TINYINT(1) NOT NULL DEFAULT 0 AFTER `transport`;

/* script to add default pension opt out field to yes in worker table*/

ALTER TABLE `workers` 
CHANGE COLUMN `pension_opt_out` `pension_opt_out` TINYINT(1) NOT NULL DEFAULT 1 ;

/* script to update default pension opt out field to yes in worker table*/

UPDATE workers SET pension_opt_out=1;


/* script to add total margin field in payroll table*/

ALTER TABLE `payroll` 
ADD COLUMN `total_margin` FLOAT NULL DEFAULT NULL AFTER `actual_margin`;

/* script to add total margin field in payroll sammary table*/

ALTER TABLE `payroll_summary` 
ADD COLUMN `total_margin` FLOAT NULL AFTER `actual_margin`;

/* script to add selected tax year field in payroll sammary table*/

ALTER TABLE `site` 
ADD COLUMN `wtr` JSON NULL AFTER `country`;

UPDATE `site`
SET wtr = '{
  "2022-2023": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2023-2024": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2024-2025": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2025-2026": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2026-2027": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2027-2028": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2028-2029": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2029-2030": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2030-2031": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  },
  "2031-2032": {
    "years": {
      "1": {
        "value": "",
        "activate": "0"
      },
      "2": {
        "value": "",
        "activate": "0"
      },
      "3": {
        "value": "",
        "activate": "0"
      },
      "4": {
        "value": "",
        "activate": "0"
      },
      "5": {
        "value": "",
        "activate": "0"
      },
      "6": {
        "value": "",
        "activate": "0"
      },
      "7": {
        "value": "",
        "activate": "0"
      },
      "8": {
        "value": "",
        "activate": "0"
      },
      "9": {
        "value": "",
        "activate": "0"
      },
      "10": {
        "value": "",
        "activate": "0"
      }
    },
    "post_twelve_week": 12.55,
    "under_twelve_week": 12.55
  }
}'
WHERE wtr is null;

/* script to add Is Active field in user table*/
ALTER TABLE `user` 
ADD COLUMN `is_active` TINYINT(1) NOT NULL DEFAULT 1 AFTER `resource`;

/* script to add client persmission to access message component in permissions table*/
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('3', '2', '21', '1', '1');

/* script to add default templates for old clients in template table*/
INSERT INTO template (name, title, `from`, `type`, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Happy Birthday", "Happy Birthday!", "Site Manager", "GENERAL", '[[{"data": "Hey, we just wanted to wish you a happy birthday and to let you know we really appreciate everything you do! Big thanks", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/9eef2340-c7d0-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Reward I", "Special shout out!", "Site Manager", "REWARD", '[[{"data": "Hey, we just wanted to say we think you’re great and would like to show our appreciation by giving you a little reward. Follow the link below - big thanks! ", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/d44f8930-c7d0-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Health & Safety Skills Training ", "High Achiever!", "Site Manager", "BADGE", '[[{"data": "Hey, we just wanted to celebrate with you and your colleagues in your successful achievement of your new skill badge  - thank you for being a great member of our team! ", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/1bc76f30-c7d1-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Annual Service 1 Year", "Congratulations on your 1 Year Service", "Site Manager", "AWARD", '[[{"data": "Thank you for your loyalty! We are so pleased to have you as a member of our team and thank you for your loyalty.", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/cc2d35d0-c7d1-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Annual Service 5 Years", "Congratulations on your 1 Year Service", "Site Manager", "AWARD", '[[{"data": "Thank you for your loyalty! We are so pleased to have you as a member of our team and thank you for your loyalty.", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/cc2d35d0-c7d1-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Recognition I", "Well Done - One Recognition point earned!", "Site Manager", "RECOGNITION", '[[{"data": "Hey, just to say we really appreciate everything you do and wanted you and your colleagues to know what a great member of the team you are - big thanks!", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/ef2c0540-c7cf-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Recognition II", "Well Done - One Recognition point earned!", "Site Manager", "RECOGNITION", '[[{"data": "Hey, just to say we really appreciate everything you do and wanted you and your colleagues to know what a great member of the team you are - big thanks!", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/ef2c0540-c7cf-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Reward II", "Special shout out!", "Site Manager", "REWARD", '[[{"data": "Hey, we just wanted to say we think you’re great and would like to show our appreciation by giving you a little reward. Follow the link below - big thanks! ", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/122c2580-c7d4-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Welcome I", "Good Luck in your new role", "Site Manager", "GENERAL", '[[{"data": "We are so pleased you have chosen to work with us and hope you have a great first day in your new role.", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/4bba56a0-c7d4-11ec-b334-1f4cfe777648", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

INSERT INTO template (name, title, `from`, type, body, is_default, created_by, created_at, modified_by, modified_at) 
SELECT "Health & Safety - Packing Trolley 1", "Packing Trollies 1", "Health & Safety Team", "TRAINING", '[[{"data": "\\nSite safety is or paramount importance\\n\\nSee Image below of how NOT to stack in the warehouse.\\n\\nPacking a cage in this way is a safety hazard and could cause injury to someone. \\n\\nPlease do not pack in this way\\n", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/fac9cd60-d5bc-11ec-92b5-f1f8e821f85f.png", "type": "media"}], [{"data": "\\nSite safety is or paramount importance\\n\\nSee Image below of how to stack in the warehouse correctly\\n\\nPacking a cage in this way ensures the safety of your colleagues\\n\\nPlease pack in this way", "type": "text"}, {"data": "https://clearvue-static.s3.eu-west-2.amazonaws.com/worker-documents/162261d0-d5bd-11ec-92b5-f1f8e821f85f.png", "type": "media"}]]', 0, user.id, NOW(), user.id, NOW() FROM user WHERE user_type_id = 2;

/* script to add availability, hours fields in workers table*/
ALTER TABLE `workers` 
ADD COLUMN `availability` ENUM('FULL TIME', 'PART TIME') NULL DEFAULT NULL AFTER `internal_chargeback`,
ADD COLUMN `hours` BIGINT NULL DEFAULT NULL AFTER `availability`;

/* script to insert data in user_type table*/

INSERT INTO `user_type` (`type`, `name`) VALUES ('message_admin', 'Message Admin');

/* script to insert data for message admin in permissions table*/

INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '9', '20', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('3', '9', '11', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '9', '22', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '9', '23', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('3', '9', '21', '1', '1');

/* script to alter data in features table*/
INSERT INTO `features` (`name`, `code`, `created_by`, `updated_by`) VALUES ('Dashboard', 'dashboard', '1', '1');

INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '2', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '3', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '4', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '5', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '1', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '8', '27', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '7', '27', '1', '1');

/* script to create message admin table*/

CREATE TABLE `message_admin` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `client_id` BIGINT UNSIGNED NOT NULL,
    `created_by` BIGINT UNSIGNED DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_by` BIGINT UNSIGNED DEFAULT NULL,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `message_admin_client_id_user_id_unique_index` (`client_id` , `user_id`),
    KEY `message_admin_user_id_fk` (`user_id`),
    KEY `message_admin_user_id_fk_created_by` (`created_by`),
    KEY `message_admin_user_id_fk_updated_by` (`updated_by`),
    CONSTRAINT `message_admin_client_id_fk` FOREIGN KEY (`client_id`)
        REFERENCES `client_details` (`id`),
    CONSTRAINT `message_admin_user_id_fk` FOREIGN KEY (`user_id`)
        REFERENCES `user` (`id`),
    CONSTRAINT `message_admin_user_id_fk_created_by` FOREIGN KEY (`created_by`)
        REFERENCES `user` (`id`),
    CONSTRAINT `message_admin_user_id_fk_updated_by` FOREIGN KEY (`updated_by`)
        REFERENCES `user` (`id`)
)  ENGINE=INNODB AUTO_INCREMENT=103 DEFAULT CHARSET=UTF8MB4 COLLATE = UTF8MB4_0900_AI_CI;

/* script to add is restricted field in agency_client_association table*/

ALTER TABLE `agency_client_association` 
ADD COLUMN `is_restricted` TINYINT(1) NOT NULL DEFAULT 0 AFTER `training_hours`;

/* script to alter hours in workers table*/

ALTER TABLE `workers` 
CHANGE COLUMN `hours` `hours` FLOAT NULL DEFAULT NULL ;
