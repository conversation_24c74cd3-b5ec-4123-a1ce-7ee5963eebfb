/* script to alter data in template table*/

ALTER TABLE `workers` 
ADD COLUMN `house_number` VARCHAR(250) NULL DEFAULT NULL AFTER `hours`,
ADD COLUMN `student_visa` TINYINT(1) NOT NULL DEFAULT '0' AFTER `house_number`;

/* change temp and permanent bulk worker upload CSV*/

/* Add PERMANENT_WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY variable in env. */

/* script to alter data type in payroll table*/

ALTER TABLE `payroll` 
CHANGE COLUMN `total_hours` `total_hours` DECIMAL(10,2) NULL DEFAULT NULL ,
<PERSON><PERSON><PERSON> COLUMN `total_charge` `total_charge` DECIMAL(10,2) NOT NULL ,
CHANGE COLUMN `total_pay` `total_pay` DECIMAL(10,2) NOT NULL ,
<PERSON>ANGE COLUMN `holiday` `holiday` DECIMAL(10,2) NOT NULL ,
<PERSON><PERSON><PERSON> COLUMN `apprenticeship_levy` `apprenticeship_levy` DECIMAL(10,2) NOT NULL ,
CHANGE COLUMN `pension` `pension` DECIMAL(10,2) NOT NULL ,
CHANGE COLUMN `actual_cost_to_employ` `actual_cost_to_employ` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `total_agency_margin` `total_agency_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `actual_margin` `actual_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `total_margin` `total_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `rate_card_margin` `rate_card_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `credit_per_hour` `credit_per_hour` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `clearvue_savings` `clearvue_savings` DECIMAL(10,2) NOT NULL ;

/* script to alter data type in payroll_summary table*/

ALTER TABLE `payroll_summary` 
CHANGE COLUMN `total_hours` `total_hours` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `total_charge` `total_charge` DECIMAL(10,2) NOT NULL ,
CHANGE COLUMN `total_pay` `total_pay` DECIMAL(10,2) NOT NULL ,
CHANGE COLUMN `total_agency_margin` `total_agency_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `actual_margin` `actual_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `total_margin` `total_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `rate_card_margin` `rate_card_margin` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `credit_per_hour` `credit_per_hour` DECIMAL(10,2) NULL DEFAULT NULL ,
CHANGE COLUMN `clearvue_savings` `clearvue_savings` DECIMAL(10,2) NOT NULL ;

/* script to alter data type in time_and_attendance_data table*/

ALTER TABLE `time_and_attendance_data` 
CHANGE COLUMN `weekly_hours` `weekly_hours` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `pay_rate` `pay_rate` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `charge_rate` `charge_rate` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `standard_pay` `standard_pay` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `overtime_pay` `overtime_pay` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `total_charge` `total_charge` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `standard_charge` `standard_charge` DECIMAL(10,2) NULL DEFAULT '0' ,
CHANGE COLUMN `overtime_charge` `overtime_charge` DECIMAL(10,2) NULL DEFAULT '0' ;


/* Add new column to client_details for starting day of week selection */
ALTER TABLE `client_details` 
ADD COLUMN `weekday_start` ENUM('SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT') NOT NULL AFTER `resource`;

/* script to alter type in job table*/
ALTER TABLE `job` 
CHANGE COLUMN `type` `type` ENUM('1', '2', '3', '4', '5', '6') NULL DEFAULT NULL ;

/* Create new schema los_rule*/
CREATE TABLE `los_rule` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned DEFAULT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `name` varchar(250) NOT NULL,
  `role_type` enum('1','2','3','4','5','6') DEFAULT NULL,
  `start_tax_year` date DEFAULT NULL,
  `pay_type` varchar(45) DEFAULT NULL,
  `pre_twelve_week` float NOT NULL,
  `post_twelve_week` float NOT NULL,
  `los` json DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `FK_los_rule_client_details` (`client_id`),
  KEY `fk_site_id_idx` (`site_id`),
  KEY `fk_los_rule_created_by_idx` (`created_by`),
  KEY `fk_los_rule_updated_by_idx` (`updated_by`),
  UNIQUE KEY `unique_site_role` (`site_id`, `role_type`),
  CONSTRAINT `FK_los_rule_client_details` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_los_rule_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_los_rule_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_los_rule_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


-- Need to run python script to translate translate messages
   clearvue-service/data/translation_script_python/automated_message_translation.py


-- // Compliance Permissions
INSERT INTO `features` (`id`, `name`, `code`, `created_by`, `updated_by`) VALUES ('28', 'Compliance', 'compliance', '1', '1');

INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '2', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '3', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '4', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '5', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '6', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '7', '28', '1', '1');
INSERT INTO `permissions` (`access_type`, `user_type_id`, `feature_id`, `created_by`, `updated_by`) VALUES ('1', '8', '28', '1', '1');

-- Add new column `language` to workers table
ALTER TABLE workers ADD COLUMN language ENUM('sq', 'bg', 'cs', 'en', 'et', 'fr', 'hi', 'hu', 'it', 'lv', 'lt', 'ne', 'ur', 'pl', 'pt', 'ro', 'sk', 'sl', 'es', 'uk', 'ti') AFTER student_visa;

-- Upload Client and Agency specific Worker Update CSVs to Productions
-- And update the .env file
WORKER_UPDATE_CLIENT_SAMPLE_FILE_NAME=demo_client_worker_data_to_update.csv
WORKER_UPDATE_AGENCY_SAMPLE_FILE_NAME=demo_agency_worker_data_to_update.csv

-- TODO: Check, We did last time production hotfix realted to float or double data type, verify that it not creates the conflict in prod merge.

-- TODO: Updaye Nationality "nationalitiesPossibleValues" list in Node.js Code based on Client comment
-- Run Below Python Scripts For nationality updates(Update the nationality "MAIN" & "PATCH" list based on Client comment)
   clearvue-service/data/PythonScripts/nationality_updation_workers_table.py


-- Step 1: Remove the old unique key
ALTER TABLE `los_rule`
DROP INDEX `unique_site_role`;

-- Step 2: Add the new unique constraint for site_id, role_type, and pay_type
ALTER TABLE `los_rule`
ADD CONSTRAINT `unique_site_role_pay` UNIQUE (`site_id`, `role_type`, `pay_type`);

---------

ALTER TABLE `message` 
ADD COLUMN `title_translations` JSON NULL AFTER `title`;

ALTER TABLE `message_system_default` 
ADD COLUMN `title_translations` JSON NULL AFTER `title`;

-- Run below Python script 2 times (for "message" & "message_system_default" table)
clearvue-service/data/translation_script_python/automated_message_title_translate.py


ALTER TABLE `template` 
ADD COLUMN `title_translations` JSON NULL AFTER `title`;

UPDATE `survey` SET `name` = '{\n    \"bg\": \"Ново проучване за начинаещи - Седмица 1\",\n    \"cs\": \"Nový začátečnický průzkum – 1. týden\",\n    \"en\": \"New Starter Survey - Week 1\",\n    \"es\": \"Encuesta para nuevos principiantes - Semana 1\",\n    \"et\": \"Uus alustajate uuring – 1. nädal\",\n    \"fr\": \"Enquête Nouveau Starter - Semaine 1\",\n    \"hi\": \"नया आरंभिक सर्वेक्षण - सप्ताह 1\",\n    \"hu\": \"Új kezdő felmérés – 1. hét\",\n    \"it\": \"Sondaggio nuovi principianti - Settimana 1\",\n    \"lt\": \"Nauja pradedančiųjų apklausa – 1 savaitė\",\n    \"lv\": \"Jauna iesācēju aptauja — 1. nedēļa\",\n    \"ne\": \"नयाँ स्टार्टर सर्वेक्षण - हप्ता 1\",\n    \"pl\": \"Nowa ankieta dla początkujących — tydzień 1\",\n    \"pt\": \"Pesquisa para novos iniciantes - Semana 1\",\n    \"ro\": \"Sondaj nou pentru începători - Săptămâna 1\",\n    \"sk\": \"Nový prieskum pre začiatočníkov – 1. týždeň\",\n    \"sl\": \"Nova začetniška anketa – 1. teden\",\n    \"sq\": \"Sondazhi i ri fillestar - Javë 1\",\n    \"uk\": \"Нове опитування для початківців – тиждень 1\",\n    \"ur\": \"نیا اسٹارٹر سروے - ہفتہ 1\"\n}' WHERE (`id` = '1');
UPDATE `survey` SET `name` = '{\n    \"bg\": \"Ново проучване за начинаещи - Седмица 2\",\n    \"cs\": \"Nový začátečnický průzkum – 2. týden\",\n    \"en\": \"New Starter Survey - Week 2\",\n    \"es\": \"Encuesta para nuevos principiantes - Semana 2\",\n    \"et\": \"Uus alustajate uuring – 2. nädal\",\n    \"fr\": \"Enquête Nouveau Starter - Semaine 2\",\n    \"hi\": \"नया शुरुआती सर्वेक्षण - दूसरा सप्ताह\",\n    \"hu\": \"Új kezdő felmérés – 2. hét\",\n    \"it\": \"Sondaggio nuovi principianti - Settimana 2\",\n    \"lt\": \"Nauja pradedančiųjų apklausa – 2 savaitė\",\n    \"lv\": \"Jauna iesācēju aptauja — 2. nedēļa\",\n    \"ne\": \"नयाँ स्टार्टर सर्वेक्षण - हप्ता 2\",\n    \"pl\": \"Nowa ankieta dla początkujących — tydzień 2\",\n    \"pt\": \"Pesquisa para novos iniciantes - Semana 2\",\n    \"ro\": \"Sondaj nou pentru începători - Săptămâna 2\",\n    \"sk\": \"Nový prieskum pre začiatočníkov – 2. týždeň\",\n    \"sl\": \"Nova začetniška anketa – 2. teden\",\n    \"sq\": \"Sondazhi i ri fillestar - Javë 2\",\n    \"uk\": \"Нове опитування для початківців – тиждень 2\",\n    \"ur\": \"نیا اسٹارٹر سروے - ہفتہ 2\"\n}' WHERE (`id` = '2');
UPDATE `survey` SET `name` = '{\n    \"bg\": \"Анкета за обратна връзка - Седмица 4\",\n    \"cs\": \"Průzkum zpětné vazby – týden 4\",\n    \"en\": \"Feedback Survey - Week 4\",\n    \"es\": \"Encuesta de retroalimentación - Semana 4\",\n    \"et\": \"Tagasiside uuring – 4. nädal\",\n    \"fr\": \"Enquête de satisfaction - Semaine 4\",\n    \"hi\": \"प्रतिक्रिया सर्वेक्षण - सप्ताह 4\",\n    \"hu\": \"Visszajelzési felmérés – 4. hét\",\n    \"it\": \"Sondaggio di feedback - Settimana 4\",\n    \"lt\": \"Atsiliepimų apklausa – 4 savaitė\",\n    \"lv\": \"Atsauksmju aptauja — 4. nedēļa\",\n    \"ne\": \"प्रतिक्रिया सर्वेक्षण - हप्ता 4\",\n    \"pl\": \"Ankieta opinii — tydzień 4\",\n    \"pt\": \"Pesquisa de feedback - Semana 4\",\n    \"ro\": \"Sondaj de feedback - Săptămâna 4\",\n    \"sk\": \"Prieskum spätnej väzby – 4. týždeň\",\n    \"sl\": \"Anketa o povratnih informacijah - 4. teden\",\n    \"sq\": \"Anketa e komenteve - Javë 4\",\n    \"uk\": \"Опитування зворотного зв\'язку - тиждень 4\",\n    \"ur\": \"فیڈ بیک سروے - ہفتہ 4\"\n}' WHERE (`id` = '3');
UPDATE `survey` SET `name` = '{\n    \"bg\": \"Анкета за обратна връзка - Седмица 8\",\n    \"cs\": \"Průzkum zpětné vazby – 8. týden\",\n    \"en\": \"Feedback Survey - Week 8\",\n    \"es\": \"Encuesta de retroalimentación - Semana 8\",\n    \"et\": \"Tagasiside uuring – 8. nädal\",\n    \"fr\": \"Enquête de satisfaction - Semaine 8\",\n    \"hi\": \"प्रतिक्रिया सर्वेक्षण - सप्ताह 8\",\n    \"hu\": \"Visszajelzési felmérés – 8. hét\",\n    \"it\": \"Sondaggio di feedback - Settimana 8\",\n    \"lt\": \"Atsiliepimų apklausa – 8 savaitė\",\n    \"lv\": \"Atsauksmju aptauja — 8. nedēļa\",\n    \"ne\": \"प्रतिक्रिया सर्वेक्षण - हप्ता 8\",\n    \"pl\": \"Ankieta opinii — tydzień 8\",\n    \"pt\": \"Pesquisa de feedback - Semana 8\",\n    \"ro\": \"Sondaj de feedback - Săptămâna 8\",\n    \"sk\": \"Prieskum spätnej väzby – 8. týždeň\",\n    \"sl\": \"Anketa o povratnih informacijah - 8. teden\",\n    \"sq\": \"Anketa e komenteve - Javë 8\",\n    \"uk\": \"Опитування зворотного зв\'язку - тиждень 8\",\n    \"ur\": \"فیڈ بیک سروے - ہفتہ 8\"\n}' WHERE (`id` = '4');
UPDATE `survey` SET `name` = '{\n    \"bg\": \"Анкета за обратна връзка - Седмица 12\",\n    \"cs\": \"Průzkum zpětné vazby – 12. týden\",\n    \"en\": \"Feedback Survey - Week 12\",\n    \"es\": \"Encuesta de retroalimentación - Semana 12\",\n    \"et\": \"Tagasiside uuring – 12. nädal\",\n    \"fr\": \"Enquête de satisfaction - Semaine 12\",\n    \"hi\": \"प्रतिक्रिया सर्वेक्षण - सप्ताह 12\",\n    \"hu\": \"Visszajelzési felmérés – 12. hét\",\n    \"it\": \"Sondaggio di feedback - Settimana 12\",\n    \"lt\": \"Atsiliepimų apklausa – 12 savaitė\",\n    \"lv\": \"Atsauksmju aptauja — 12. nedēļa\",\n    \"ne\": \"प्रतिक्रिया सर्वेक्षण - हप्ता 12\",\n    \"pl\": \"Ankieta opinii — tydzień 12\",\n    \"pt\": \"Pesquisa de feedback - Semana 12\",\n    \"ro\": \"Sondaj de feedback - Săptămâna 12\",\n    \"sk\": \"Prieskum spätnej väzby – 12. týždeň\",\n    \"sl\": \"Anketa o povratnih informacijah - 12. teden\",\n    \"sq\": \"Anketa e komenteve - Javë 12\",\n    \"uk\": \"Опитування зворотного зв\'язку - тиждень 12\",\n    \"ur\": \"فیڈ بیک سروے - ہفتہ 12\"\n}' WHERE (`id` = '5');
