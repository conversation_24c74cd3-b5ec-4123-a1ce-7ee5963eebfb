/* script to create `login_attempt` table*/

CREATE TABLE `login_attempt` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `attempt_at` datetime DEFAULT NULL,
  `blocked_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `fk_user_user_id` (`user_id`),
  CONSTRAINT `fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/* Add MAX_WRONG_ATTEMPTS, LAST_ATTEMPTS, BLOCKED_TIME environment variable in env. file*/

/* script to alter message table*/
ALTER TABLE message ADD COLUMN new_type ENUM('GENERAL', 'RECOGNITION', 'AWARD', 'REWARD', 'TRAINING', '<PERSON>D<PERSON>', 'SYSTEM', 'SYSTEM_DEFAULT') NOT NULL AFTER type;
UPDATE message SET new_type = CASE type WHEN 'KUDOS' THEN 'RECOGNITION' ELSE type END;
ALTER TABLE message DROP COLUMN type;
ALTER TABLE message RENAME COLUMN new_type TO type;

/* script to alter template table*/
ALTER TABLE template ADD COLUMN new_type ENUM('RECOGNITION', 'AWARD', 'BADGE', 'GENERAL', 'REWARD', 'TRAINING') NOT NULL AFTER type;
UPDATE template SET new_type = CASE type WHEN 'KUDOS' THEN 'RECOGNITION' ELSE type END;
ALTER TABLE template DROP COLUMN type;
ALTER TABLE template RENAME COLUMN new_type TO type;

/* script to alter data in template table*/
UPDATE `template` SET `name` = 'Recognition I', `title` = 'Well Done - One Recognition point earned!' WHERE (`id` = '58');
UPDATE `template` SET `name` = 'Recognition II', `title` = 'Well Done - One Recognition point earned!' WHERE (`id` = '59');

/* script to alter data in faq table*/
UPDATE `faq` SET `answer` = '[{\"type\": \"text\", \"value\": \"Yes, all of your achievements such as Recognition, Awards, Skills and Training are recorded in your worker profile and will be available for you to provide to future employers\"}]' WHERE (`id` = '4');

/* script to alter data in workers table*/
UPDATE workers SET appreciation = REPLACE(appreciation, '"kudos":', '"recognition":');

/* script to alter the encoding of the name column of departments table to UTF-8. */
ALTER TABLE departments MODIFY COLUMN name VARCHAR(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

/*Add TRANSLATE_PROJECT_ID, TRANSLATE_KEY_DATA environment variables into staging env. file*/
