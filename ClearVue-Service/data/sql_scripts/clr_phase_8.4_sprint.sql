ALTER TABLE site 
ADD COLUMN cost_centre varchar(100) DEFAULT NULL After post_code;

ALTER TABLE departments 
ADD COLUMN cost_centre varchar(100) DEFAULT NULL After client_id;

-- Add .env variables
VAT_RATE=20
VAT_CODE=S


CREATE TABLE `payroll_detailed_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `payroll_meta_id` bigint unsigned NOT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned NOT NULL,
  `shift_id` bigint unsigned DEFAULT NULL,
  `department_id` bigint unsigned DEFAULT NULL,
  `site_id` bigint unsigned NOT NULL,
  `week` int NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `pay_type` varchar(45) NOT NULL,
  `adjustment` decimal(10,2) DEFAULT NULL,
  `total_hours` decimal(10,2) DEFAULT NULL,
  `total_charge` decimal(10,2) NOT NULL,
  `actual_cost_to_employ` decimal(10,2) NOT NULL,
  `total_margin` decimal(10,2) NOT NULL,
  `actual_margin` decimal(10,2) DEFAULT NULL,
  `rate_card_margin` decimal(10,2) DEFAULT NULL,
  `credit_per_hour` decimal(10,2) DEFAULT NULL,
  `clearvue_savings` decimal(10,2) DEFAULT NULL,
  `total_pay` decimal(10,2) NOT NULL,
  `national_insurance` decimal(10,2) DEFAULT NULL,
  `pension` decimal(10,2) DEFAULT NULL,
  `apprenticeship_levy` decimal(10,2) DEFAULT NULL,
  `holiday` decimal(10,2) DEFAULT NULL,
  `holiday_pay_type_value` decimal(10,2) DEFAULT NULL,
  `holiday_employment_cost` decimal(10,2) DEFAULT NULL,
  `under_twentyone` tinyint(1) DEFAULT NULL,
  `under_twentytwo` tinyint(1) DEFAULT NULL,
  `within_twelveweeks` tinyint(1) DEFAULT NULL,
  `other_assignment_pay_value` decimal(10,2) DEFAULT NULL,
  `credit_value` decimal(10,2) DEFAULT NULL,
  `accrual_value` decimal(10,2) DEFAULT NULL,
  `wtr_cost` decimal(10,2) DEFAULT NULL,
  `total_cost` decimal(10,2) DEFAULT NULL,
  `app_levy_difference` decimal(10,2) DEFAULT NULL,
  `app_levy_with_wtr` decimal(10,2) DEFAULT NULL,
  `total_worked_charge` decimal(10,2) DEFAULT NULL,
  `worked_hours_charge` decimal(10,2) DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_payroll_detailed_summary_payroll_meta_id` (`payroll_meta_id`),
  KEY `fk_payroll_detailed_summary_worker_id` (`worker_id`),
  KEY `fk_payroll_detailed_summary_client_id` (`client_id`),
  KEY `fk_payroll_detailed_summary_agency_id` (`agency_id`),
  KEY `fk_payroll_detailed_summary_shift_id` (`shift_id`),
  KEY `fk_payroll_detailed_summary_department_id` (`department_id`),
  KEY `fk_payroll_detailed_summary_site_id` (`site_id`),
  KEY `fk_payroll_detailed_summary_created_by` (`created_by`),
  KEY `fk_payroll_detailed_summary_updated_by` (`updated_by`),
  CONSTRAINT `fk_payroll_detailed_summary_payroll_meta_id` FOREIGN KEY (`payroll_meta_id`) REFERENCES `payroll_meta` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_shift_id` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_payroll_detailed_summary_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- ====================

ALTER TABLE payroll_meta 
ADD COLUMN site_cost_centre varchar(100) DEFAULT NULL After site_id,
ADD COLUMN vat_code VARCHAR(10) DEFAULT NULL AFTER end_date,
ADD COLUMN vat_rate DECIMAL(5,2) DEFAULT NULL AFTER vat_code;


ALTER TABLE payroll_detailed_summary 
ADD COLUMN department_cost_centre varchar(100) DEFAULT NULL After department_id;


ALTER TABLE payroll
drop column department_id, 
drop column shift_id;