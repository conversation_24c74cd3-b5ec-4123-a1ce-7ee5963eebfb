SET SQL_SAFE_UPDATES = 0;
-- 

ALTER TABLE payroll
ADD COLUMN supervisor_hours DECIMAL(10,2) DEFAULT 0.00 after total_hours,
ADD COLUMN supervisor_charges DECIMAL(10,2) DEFAULT 0.00 after total_charge,
ADD COLUMN non_supervisor_hours DECIMAL(10,2) DEFAULT 0.00 after supervisor_hours,
ADD COLUMN non_supervisor_charges DECIMAL(10,2) DEFAULT 0.00 after supervisor_charges;


UPDATE payroll
SET supervisor_hours = null,
    supervisor_charges = null,
    non_supervisor_hours = null,
    non_supervisor_charges = null;

-- 
ALTER TABLE payroll_summary
ADD COLUMN supervisor_hours DECIMAL(10,2) DEFAULT 0.00 after total_hours,
ADD COLUMN supervisor_charges DECIMAL(10,2) DEFAULT 0.00 after total_charge,
ADD COLUMN non_supervisor_hours DECIMAL(10,2) DEFAULT 0.00 after supervisor_hours,
ADD COLUMN non_supervisor_charges DECIMAL(10,2) DEFAULT 0.00 after supervisor_charges,
ADD COLUMN identified_supervisors INT unsigned DEFAULT 0 after non_supervisor_charges;


UPDATE payroll_summary
SET supervisor_hours = null,
    supervisor_charges = null,
    non_supervisor_hours = null,
    non_supervisor_charges = null,
    identified_supervisors = null;

-- 
SET SQL_SAFE_UPDATES = 1;


ALTER TABLE client_details
ADD COLUMN `worker_performance` tinyint(1) NOT NULL DEFAULT '0' after worker_invite_email;

ALTER TABLE time_and_attendance
ADD COLUMN `worker_performance_flag` tinyint(1) NOT NULL DEFAULT '0' after status;

-- ==================

CREATE TABLE `worker_performance` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `filename` varchar(45) NOT NULL,
  `aws_path` varchar(250) NOT NULL,
  `status` ENUM('Processed') DEFAULT NULL,
  `client_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `agency_id`  bigint unsigned DEFAULT NULL,
  `week` int DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `uploaded_by`  ENUM('CLIENT', 'AGENCY') NOT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_worker_performance_client_id` (`client_id`),
  KEY `fk_worker_performance_site_id` (`site_id`),
  KEY `fk_worker_performance_created_by_idx` (`created_by`),
  KEY `fk_worker_performance_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_worker_performance_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_worker_performance_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_worker_performance_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_worker_performance_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=latin1;


CREATE TABLE `worker_performance_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `worker_performance_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned DEFAULT NULL,
  `client_id` bigint unsigned DEFAULT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `performance_number` decimal(10,2) NOT NULL,
  `start_date` date DEFAULT NULL,
  `week` int DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_worker_performance_data_site_id` (`site_id`),
  KEY `fk_worker_performance_data_agency_id_idx` (`agency_id`),
  KEY `fk_worker_performance_data_client_id_idx` (`client_id`),
  KEY `fk_worker_performance_data_created_by_idx` (`created_by`),
  KEY `fk_worker_performance_data_worker_performance_id_idx` (`worker_performance_id`),
  KEY `fk_worker_performance_data_updated_by_idx` (`updated_by`),
  KEY `fk_worker_performance_data_worker_id` (`worker_id`),
  CONSTRAINT `fk_worker_performance_data_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_worker_performance_data_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_worker_performance_data_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_worker_performance_data_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_worker_performance_data_worker_performance_id` FOREIGN KEY (`worker_performance_id`) REFERENCES `worker_performance` (`id`),
  CONSTRAINT `fk_worker_performance_data_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_worker_performance_data_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=latin1;


-- Create AWS S3 Folders, "workers-performance-sheets" & "workers-performance-example-sheets" under "clearvue-assets" Bucket
-- Upload "demo_client_worker_performance_to_upload.csv" & "demo_agency_worker_performance_to_upload.csv" to `workers-performance-example-sheets` folder.

-- Add Following .env variables
-- WORKER_PERFORMANCE_FOLDER=workers-performance-sheets
-- WORKER_PERFORMANCE_SAMPLE_SHEET_FOLDER=workers-performance-example-sheets
-- WORKER_PERFORMANCE_CLIENT_SAMPLE_SHEET=demo_client_worker_performance_to_upload.csv
-- WORKER_PERFORMANCE_AGENCY_SAMPLE_SHEET=demo_agency_worker_performance_to_upload.csv


-- =================================
ALTER TABLE client_details
ADD COLUMN `worker_training` tinyint(1) NOT NULL DEFAULT '0' after worker_performance;


CREATE TABLE `training_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `region_id` bigint unsigned DEFAULT NULL,
  `site_id` bigint unsigned NOT NULL,
  `shift_id` bigint unsigned NOT NULL,
  `department_id` bigint unsigned NOT NULL,
  `threshold_week` int NOT NULL,
  `evaluation_week` int NOT NULL,
  `max_training_hours` decimal(10,2) NOT NULL,
  `performance_threshold` decimal(10,2) NOT NULL,
  `credit_rate` decimal(5,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,  
  PRIMARY KEY (`id`),
  KEY `fk_training_rules_client_id` (`client_id`),
  KEY `fk_training_rules_region_id` (`region_id`),
  KEY `fk_training_rules_site_id` (`site_id`),
  KEY `fk_training_rules_shift_id` (`shift_id`),
  KEY `fk_training_rules_department_id` (`department_id`),
  KEY `fk_training_rules_created_by_idx` (`created_by`),
  KEY `fk_training_rules_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_training_rules_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_training_rules_region_id` FOREIGN KEY (`region_id`) REFERENCES `region` (`id`),
  CONSTRAINT `fk_training_rules_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_training_rules_shift_id` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  CONSTRAINT `fk_training_rules_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),  
  CONSTRAINT `fk_training_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),  
  CONSTRAINT `fk_training_rules_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=latin1;


-- =================

ALTER TABLE client_details
ADD COLUMN `rate_card_lookup` tinyint(1) NOT NULL DEFAULT '0' after worker_training;


-- ----------------------------------------------------------------------------------
-- // Create S3 bucket folder -> "rate-card-sheets" & "rate-card-example-sheet"
-- // Upload exmplae sheet -> "demo_rate_card_to_upload.csv"


-- // Add this to .env files
RATE_CARD_FOLDER = rate-card-sheets
RATE_CARD_SAMPLE_SHEET_FOLDER =rate-card-example-sheet
RATE_CARD_SAMPLE_SHEET_NAME = demo_rate_card_to_upload.csv

-- ----------------------------------------------------------------------------------

DROP table rate_card;

CREATE TABLE `rate_card` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `card_name` varchar(45) NOT NULL,
  `filename` varchar(45) NOT NULL,
  `aws_path` varchar(250) NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned NOT NULL,
  `agency_id`  bigint unsigned NOT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_rate_card_client_id` (`client_id`),
  KEY `fk_rate_card_agency_id` (`agency_id`),
  KEY `fk_rate_card_site_id` (`site_id`),
  KEY `fk_rate_card_created_by_idx` (`created_by`),
  KEY `fk_rate_card_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_rate_card_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_rate_card_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_rate_card_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_rate_card_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_rate_card_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=latin1;


CREATE TABLE `rate_card_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `rate_card_id` bigint unsigned NOT NULL,
  `pay_rate` decimal(10,2) NOT NULL,
  `charge_rate` decimal(10,2) NOT NULL,
  `performance_low` decimal(10,2) DEFAULT NULL,
  `performance_high` decimal(10,2) DEFAULT NULL,
  `supervisor_rate` tinyint(1) NOT NULL DEFAULT '0',
  `pay_type` varchar(45) DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_rate_card_data_created_by_idx` (`created_by`),
  KEY `fk_rate_card_data_rate_card_id_idx` (`rate_card_id`),
  KEY `fk_rate_card_data_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_rate_card_data_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_rate_card_data_rate_card_id` FOREIGN KEY (`rate_card_id`) REFERENCES `rate_card` (`id`),
  CONSTRAINT `fk_rate_card_data_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=latin1;

------------------------------

ALTER TABLE payroll
ADD COLUMN `shift_id` bigint unsigned DEFAULT NULL after site_id,
ADD COLUMN `department_id` bigint unsigned DEFAULT NULL after shift_id,
ADD COLUMN `training_employment_cost` decimal(10,2) DEFAULT NULL after actual_cost_to_employ,
ADD COLUMN `training_hours` Decimal(10,2) DEFAULT NULL after actual_cost_to_employ,
ADD COLUMN `flagged_supervisor` tinyint(1) NOT NULL DEFAULT '0' after total_pay,
ADD KEY `fk_payroll_shift_id` (`shift_id`),
ADD KEY `fk_payroll_department_id` (`department_id`),
ADD CONSTRAINT `fk_payroll_shift_id` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
ADD CONSTRAINT `fk_payroll_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`);


CREATE TABLE `credit_dues` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned NOT NULL,
  `shift_id` bigint unsigned NOT NULL,
  `department_id` bigint unsigned NOT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL, 
  `time_and_attendance_id` bigint unsigned NOT NULL,
  `performance_number` decimal(10,2) NOT NULL,
  `performance_threshold` decimal(10,2) NOT NULL,
  `achievement_week` int DEFAULT NULL,
  `threshold_week` int NOT NULL,
  `evaluation_week` int NOT NULL,
  `completed_training_hours` decimal(10,2) NOT NULL,
  `max_training_hours` decimal(10,2) NOT NULL,
  `training_employment_cost` decimal(10,2) NOT NULL,
  `credit_rate` decimal(5,2) NOT NULL,
  `applied_credit_rate` decimal(5,2) NOT NULL,
  `total_credit_due` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,  
  PRIMARY KEY (`id`),
  KEY `fk_credit_dues_client_id` (`client_id`),
  KEY `fk_credit_dues_agency_id` (`agency_id`),
  KEY `fk_credit_dues_site_id` (`site_id`),
  KEY `fk_credit_dues_shift_id` (`shift_id`),
  KEY `fk_credit_dues_department_id` (`department_id`),
  KEY `fk_credit_dues_worker_id` (`worker_id`),
  KEY `fk_credit_dues_time_and_attendance_id` (`time_and_attendance_id`),
  KEY `fk_credit_dues_created_by_idx` (`created_by`),
  KEY `fk_credit_dues_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_credit_dues_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_credit_dues_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_credit_dues_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_credit_dues_shift_id` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  CONSTRAINT `fk_credit_dues_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),  
  CONSTRAINT `fk_credit_dues_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`),
  CONSTRAINT `fk_credit_dues_time_and_attendance_id` FOREIGN KEY (`time_and_attendance_id`) REFERENCES `time_and_attendance` (`id`),
  CONSTRAINT `fk_credit_dues_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),  
  CONSTRAINT `fk_credit_dues_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=latin1;


ALTER TABLE workers
ADD COLUMN `training_qualification_status` ENUM('QUALIFIED', 'NOT_QUALIFIED') DEFAULT NULL after orientation;


INSERT INTO `features` (`name`, `code`, `created_by`, `updated_by`) VALUES ('Training', 'training', '1', '1');

INSERT INTO permissions (access_type, user_type_id,feature_id, created_by, updated_by) VALUES
 (1, 2, 30, 1, 1),
 (1, 3, 30, 1, 1),
 (1, 4, 30, 1, 1),
 (1, 5, 30, 1, 1),
 (1, 7, 30, 1, 1),
 (1, 8, 30, 1, 1);

 ============

 ALTER TABLE training_rules
    DROP FOREIGN KEY fk_training_rules_shift_id,
    DROP FOREIGN KEY fk_training_rules_department_id,
    DROP COLUMN shift_id,
    DROP COLUMN department_id;

 ALTER TABLE credit_dues
    DROP FOREIGN KEY fk_credit_dues_shift_id,
    DROP FOREIGN KEY fk_credit_dues_department_id,
    DROP COLUMN shift_id,
    DROP COLUMN department_id;

==========

ALTER TABLE payroll
ADD COLUMN other_assignment_pay DECIMAL(10,2) DEFAULT 0.00 NOT NULL after total_pay;


ALTER TABLE payroll_summary
ADD COLUMN other_assignment_pay DECIMAL(10,2) DEFAULT 0.00 NOT NULL after total_pay;

## -- Run Python script -> "update_payroll_with_tap_table_for_other_assignment_pay_value.py"

UPDATE payroll_summary ps
JOIN (
    SELECT payroll_meta_id, SUM(other_assignment_pay) AS total_other_assignment_pay
    FROM payroll
    GROUP BY payroll_meta_id
) p ON ps.payroll_meta_id = p.payroll_meta_id
SET ps.other_assignment_pay = p.total_other_assignment_pay;
