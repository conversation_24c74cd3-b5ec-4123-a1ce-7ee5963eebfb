/* script to alter data in booking, booking_association table*/

ALTER TABLE `booking` 
CH<PERSON><PERSON> COLUMN `total` `total` FLOAT UNSIGNED NOT NULL ;

ALTER TABLE `booking_association` 
CHANGE COLUMN `requested_total` `requested_total` FLOAT NOT NULL ,
CH<PERSON><PERSON> COLUMN `fulfilled_total` `fulfilled_total` FLOAT NULL DEFAULT NULL ;


## -- CLR-1349 Update Surveys and Dashboard Labels

#1
ALTER TABLE survey_questions
ADD COLUMN new_question_json json DEFAULT NULL AFTER question_json,
ADD COLUMN new_label varchar(250) DEFAULT NULL AFTER label;


#2
-- TODO: RUN Below Python Script to to add new questions and lables to survey_questions table
PythonScripts/update_surveys_lables_and_questions_with_translations.py


#3
UPDATE survey_questions
SET
    question_json = COALESCE(new_question_json, question_json),
    label = COALESCE(new_label, label)
WHERE
    new_question_json IS NOT NULL
    AND new_label IS NOT NULL
    AND created_by = 1
    AND survey_id IN (1, 2, 3, 4, 5, 7, 9);


#4
ALTER TABLE survey_questions
DROP COLUMN new_question_json,
DROP COLUMN new_label;


-- Changing labels only for the "GENERAL FEEDBACK SURVEYS" for the all clients and agencies by keeping the old questions as it is.
-- 1
UPDATE survey_questions 
SET 
    label = 'Health & Safety'
WHERE
    label = 'Identification'
        AND belongs_to = 'SITE'
        AND survey_id IN (7)
        AND created_by != 1;

-- 2
UPDATE survey_questions 
SET 
    label = 'Recommendation'
WHERE
    label = 'Identification'
        AND belongs_to = 'AGENCY'
        AND survey_id IN (7)
        AND created_by != 1;

-- 3
UPDATE survey_questions 
SET 
    label = 'Recognition'
WHERE
    label = 'Role Expectations'
        AND belongs_to = 'AGENCY'
        AND survey_id IN (7)
        AND created_by != 1;




----------------------
-- For Payquery option  update

SELECT * FROM survey_mcq_options;

-- Add below new options in survey_mcq_options table with id 19,20,21 
Unauthorised wage deduction (Work Finders Fee)
Unauthorised wage deduction (Paid a deposit)
Unauthorised wage deduction (Other)

-- replace text 8: "Received incorrect pay rate" --TO--> "Incorrect pay rate"
-- replace text 6: "Missing standard hours" --TO--> "Missing hours pay"

SELECT * FROM survey_answer where answer_id in (6, 7);

UPDATE survey_answer
SET answer_id = 6
WHERE answer_id = 7;

-- Delete 7: "Missing overtime hours" option from survey_mcq_options

-- Run "data/PythonScripts/inactivated_date_fetch_for_workers.py" script and copy the "lang_result" value, and manually update the options for survey_id = 10

