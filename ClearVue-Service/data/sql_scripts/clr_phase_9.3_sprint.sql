-- ----------------------------------
-- compliance_approval table
-- ----------------------------------
CREATE TABLE IF NOT EXISTS compliance_approval (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    compliance_card_id INT NOT NULL,
    worker_id BIGINT UNSIGNED NOT NULL,
    house_number VARCHAR(250) DEFAULT NULL,
    post_code VARCHAR(45) DEFAULT NULL,
    account_number VARCHAR(15) DEFAULT NULL,
    sort_code VARCHAR(6) DEFAULT NULL,
    client_approval_status TINYINT DEFAULT NULL,
    client_approval_by BIGINT UNSIGNED DEFAULT NULL,
    client_approval_at DATETIME DEFAULT NULL,
    agency_approval_status TINYINT DEFAULT NULL,
    agency_approval_by BIGINT UNSIGNED DEFAULT NULL,
    agency_approval_at DATETIME DEFAULT NULL,
    is_reset TINYINT(1) NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_worker_card (worker_id, compliance_card_id),
    FOREIGN KEY (worker_id) REFERENCES workers(id),
    FOREIGN KEY (client_approval_by) REFERENCES user(id),
    FOREIGN KEY (agency_approval_by) REFERENCES user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;