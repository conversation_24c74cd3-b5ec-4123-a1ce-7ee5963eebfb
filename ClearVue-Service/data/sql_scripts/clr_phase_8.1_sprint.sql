-- Step 1: Create the new table
CREATE TABLE `margins` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `agency_client_association_id` BIGINT UNSIGNED NOT NULL,
  `site_id` BIGINT UNSIGNED DEFAULT NULL,
  `los` DECIMAL(4,2) DEFAULT NULL COMMENT 'In Terms Of Years',
  `margin` DECIMAL(3,2) NOT NULL,
  `overtime_margin` DECIMAL(3,2) DEFAULT '0.00',
  `transport_fee` DECIMAL(3,2) DEFAULT '0.00',
  `ssp` DECIMAL(3,2) DEFAULT '0.00',
  `training_margin` DECIMAL(3,2) DEFAULT '0.00',
  `bh_margin` DECIMAL(3,2) DEFAULT '0.00',
  `nsp_margin` DECIMAL(3,2) DEFAULT '0.00',
  `internal_standard_margin` DECIMAL(3,2) DEFAULT '0.00',
  `internal_overtime_margin` DECIMAL(3,2) DEFAULT '0.00',
  `internal_permanent_margin` DECIMAL(3,2) DEFAULT '0.00',
  `suspension_margin` DECIMAL(3,2) DEFAULT '0.00',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` BIGINT UNSIGNED DEFAULT NULL,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` BIGINT UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_margin` (`agency_client_association_id`, `site_id`, `los`),
  KEY `fk_margins_created_by` (`created_by`),
  KEY `fk_margins_updated_by` (`updated_by`),
  KEY `fk_margins_site_id` (`site_id`),
  CONSTRAINT `fk_margins_agency_client_association_id` FOREIGN KEY (`agency_client_association_id`) REFERENCES `agency_client_association` (`id`)
  CONSTRAINT `fk_margins_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_margins_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
  CONSTRAINT `fk_margins_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Step 2: Migrate data from the old table to the new table
INSERT INTO `margins` (
    `agency_client_association_id`,
    `margin`,
    `overtime_margin`,
    `transport_fee`,
    `ssp`, 
    `training_margin`,
    `bh_margin`,
    `nsp_margin`,
    `internal_standard_margin`,
    `internal_overtime_margin`,
    `internal_permanent_margin`,
    `suspension_margin`,
    `created_at`,
    `created_by`,
    `updated_at`,
    `updated_by`
)
SELECT 
    `id`,
    `margin`,
    `overtime_margin`,
    `transport_fee`,
    `ssp`,
    `training_margin`,
    `bh_margin`,
    `nsp_margin`,
    `internal_standard_margin`,
    `internal_overtime_margin`,
    `internal_permanent_margin`,
    `suspension_margin`,
    `created_at`,
    `created_by`,
    `updated_at`,
    `updated_by`
FROM `agency_client_association`;

-- Step 3: Drop the margin columns from the old table
ALTER TABLE `agency_client_association`
DROP COLUMN `margin`,
DROP COLUMN `overtime_margin`,
DROP COLUMN `transport_fee`,
DROP COLUMN `ssp`,
DROP COLUMN `training_margin`,
DROP COLUMN `bh_margin`,
DROP COLUMN `nsp_margin`,
DROP COLUMN `internal_standard_margin`,
DROP COLUMN `internal_overtime_margin`,
DROP COLUMN `internal_permanent_margin`,
DROP COLUMN `suspension_margin`;

-- Optional: Add an index on the foreign key for better performance
ALTER TABLE `margins`
ADD INDEX `idx_agency_client_association_id` (`agency_client_association_id`);

-- Create table for restricted sites per agency-client association
CREATE TABLE `agency_site_restrictions` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `agency_client_association_id` BIGINT UNSIGNED NOT NULL,
    `site_id` BIGINT UNSIGNED NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` BIGINT UNSIGNED NOT NULL,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_by` BIGINT UNSIGNED NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_association_site` (`agency_client_association_id`, `site_id`),
    KEY `fk_agency_site_restrictions_created_by` (`created_by`),
    KEY `fk_agency_site_restrictions_updated_by` (`updated_by`),
    KEY `fk_agency_site_restrictions_site_id` (`site_id`),
    CONSTRAINT `fk_agency_site_restrictions_agency_client_association_id` FOREIGN KEY (`agency_client_association_id`) REFERENCES `agency_client_association` (`id`),
    CONSTRAINT `fk_agency_site_restrictions_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
    CONSTRAINT `fk_agency_site_restrictions_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
    CONSTRAINT `fk_agency_site_restrictions_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

