 /* script to alter data in template table*/

 ALTER TABLE `template` 
ADD COLUMN `language` VARCHAR(750) NOT NULL DEFAULT 'English' AFTER `is_default`,
ADD COLUMN `code` VARCHAR(750) NOT NULL DEFAULT 'en' AFTER `language`;


 /* script to create `message_reaction` table*/

 CREATE TABLE `message_reaction` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `message_id` bigint unsigned NOT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `reaction` enum('LIKE', 'DISLIKE') NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
UNIQUE KEY `uk_message_likes_worker_id_message_id` (`worker_id`,`message_id`),
  KEY `idx_message_likes_message_id` (`message_id`),
  KEY `idx_message_likes_worker_id` (`worker_id`),
  CONSTRAINT `fk_message_likes_message_id` FOREIGN KEY (`message_id`) REFERENCES `message` (`id`),
  CONSTRAINT `fk_message_likes_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


 /* script to create `message_comment` table*/

CREATE TABLE `message_comment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `message_id` bigint unsigned NOT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_comments_message_id` (`message_id`),
  KEY `idx_message_comments_worker_id` (`worker_id`),
  CONSTRAINT `fk_message_comments_message_id` FOREIGN KEY (`message_id`) REFERENCES `message` (`id`),
  CONSTRAINT `fk_message_comments_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)


/* script to add `is_comment_allowed` column in `message` table*/

ALTER TABLE `message` ADD is_comment_allowed BOOLEAN DEFAULT 1;

/* script to add covid_margin, nsp_margin, internal_margin, suspension_margin column in `agency_client_association` table*/
ALTER TABLE `agency_client_association` 
ADD COLUMN `covid_margin` DECIMAL(3,2) NULL DEFAULT 0.00 AFTER `training_margin`,
ADD COLUMN `nsp_margin` DECIMAL(3,2) NULL DEFAULT 0.00 AFTER `covid_margin`,
ADD COLUMN `internal_margin` DECIMAL(3,2) NULL DEFAULT 0.00 AFTER `nsp_margin`,
ADD COLUMN `suspension_margin` DECIMAL(3,2) NULL DEFAULT 0.00 AFTER `internal_margin`;

/* script to drop training_hours column in `agency_client_association` table*/
ALTER TABLE`agency_client_association` 
DROP COLUMN `training_hours`;

/* script to create `message_system_default` table*/
CREATE TABLE `message_system_default` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(750) NOT NULL,
  `title` varchar(750) NOT NULL,
  `from` varchar(250) NOT NULL,
  `label` varchar(250) DEFAULT NULL,
  `body` json NOT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `assigned_to_client` BOOLEAN DEFAULT 0,
  `assigned_to_agency` BOOLEAN DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `fk_message_default_created_by_idx` (`created_by`),
  KEY `fk_message_default_modified_by_idx` (`updated_by`),
  KEY `fk_message_default_name_idx` (`name`),
  CONSTRAINT `fk_message_default_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_message_default_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


/* script to add SYSTEM_DEFAULT messages in `message_system_default` table*/
INSERT INTO message_system_default (`name`, `title`, `from`, `label`, `body`, created_by, created_at, updated_by, updated_at, assigned_to_client, assigned_to_agency) VALUES 
('New Starter Week 1', 'Well done on completing your first week!', 'Site Manager', 'new_starter_week_1', '[[{\"data\": \"We are so pleased to have you onboard and hope you enjoyed your first week. We would love to hear your feedback so please complete the new starter survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 1, \"name\": \"New Starter Week 1 Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0),
('New Starter Week 2', 'Well done on completing your first 2 weeks!', 'Site Manager', 'new_starter_week_2', '[[{\"data\": \"We are so pleased to have you onboard and hope your first 2 weeks have been enjoyable. We would love to hear your feedback so please complete the new starter survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 2, \"name\": \"New Starter Week 2 Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0),
('New Starter Week 4', 'You have successfully completed 4 weeks', 'Site Manager', 'new_starter_week_4', '[[{\"data\": \"Well done on your first 4 weeks with you and we would like to thank you for your loyalty.  We would love to hear your feedback so please complete the 4 week survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 3, \"name\": \"Feedback Survey Week 4\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0),
('New Starter Week 8', 'You have successfully completed 8 weeks', 'Site Manager', 'new_starter_week_8', '[[{\"data\": \"Well done on your first 8 weeks with you and we would like to thank you for your loyalty.  We would love to hear your feedback so please complete the 8 week survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 4, \"name\": \"New Starter Week 8 Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0),
('New Starter Week 12', 'You have successfully completed 12 weeks', 'Site Manager', 'new_starter_week_12', '[[{\"data\": \"Well done on your first 12 weeks with you and we would like to thank you for your loyalty.  We would love to hear your feedback so please complete the 12 week survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 5, \"name\": \"New Starter Week 12 Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0),
('Annual work anniversary', 'Thank you for your loyalty!', 'Site Manager', 'annual_work_anniversary', '[[{\"data\": \"We are so pleased to have you as a member of our team and thank you for your loyalty. We would love to hear your feedback so please complete the new starter survey as your opinion matters to us!\", \"type\": \"text\"}, {\"data\": {\"id\": 7, \"name\": \"General Survey\"}, \"type\": \"survey\"}, {\"data\": \"https://clearvue-static.s3.eu-west-2.amazonaws.com/messages/annual_service_message.jpg\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 1),
('Birthday wishes', 'Happy Birthday!', 'Site Manager', 'birthday_wishes', '[[{\"data\": \"Hey, we just wanted to wish you a happy birthday and to let you know we really appreciate everything you do! Big thanks.\", \"type\": \"text\"}, {\"data\": \"https://clearvue-static.s3.eu-west-2.amazonaws.com/messages/birthday_message.jpg\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 1),
('First day Welcome Message', 'Good luck in your new role', 'Site Manager', 'first_day_welcome', '[[{\"data\": \"We are so pleased you have chosen to work with us and hope you have a great first day in your new role.\", \"type\": \"text\"}, {\"data\": \"https://clearvue-static.s3.eu-west-2.amazonaws.com/messages/welcome_message.jpg\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 1),
('Unassigned Worker', 'We hope you’re OK!', 'Site Manager', 'unassign_worker', '[[{\"data\": \"We see that your status within your profile has changed to ‘Unassigned’. We are sorry to hear you are leaving us and appreciate everything you did, so please complete the survey. We hope to see you again in the future. DON’T FORGET TO CLAIM ANY OUTSTANDING HOLIDAY PAY.\", \"type\": \"text\"}, {\"data\": {\"id\": 9, \"name\": \"Exit Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 1),
('Zero Hours', 'We hope you’re OK!', 'Site Manager', 'zero_hours', '[[{\"data\": \"We have no hours recorded for you this week. Is that right? If not, please complete the survey in the link below so we can help resolve this.\", \"type\": \"text\"}, {\"data\": {\"id\": 8, \"name\": \"Worker Survey\"}, \"type\": \"survey\"}, {\"data\": \"http://app.test.theclearvue.co.uk/static/media/Logo_Colour.28e900b58bc6ce25e316.png\", \"type\": \"media\"}]]', '1', '2022-04-27 10:04:25', '1', '2022-04-27 10:04:25',1 , 0);


/* Script to assign agency-specific SYSTEM_DEFAULT messages to Existing AGENCIES */
INSERT INTO message (name, title, type, `from`, client_id, site_id, agency_id, label, body, receiver, send_by, created_by, created_at, updated_by, updated_at, is_comment_allowed)
SELECT md.name, md.title, 'SYSTEM_DEFAULT', md.from, NULL, NULL, ad.id, md.label, md.body, NULL, 1, md.created_by, NOW(), md.updated_by, NOW(), 0 FROM message_system_default as md CROSS JOIN agency_details as ad where md.assigned_to_agency = 1;

/* Script to assign client-specific SYSTEM_DEFAULT messages to Existing CLIENTS */
INSERT INTO message (name, title, type, `from`, client_id, site_id, agency_id, label, body, receiver, send_by, created_by, created_at, updated_by, updated_at, is_comment_allowed)
SELECT md.name, md.title, 'SYSTEM_DEFAULT', md.from, cd.id, NULL, NULL, md.label, md.body, NULL, 1, md.created_by, NOW(), md.updated_by, NOW(), 0 FROM message_system_default as md CROSS JOIN client_details as cd where md.assigned_to_client = 1;

Note: Upload demo worker CSV in S3 and add WORKER_UPDATE_SAMPLE_FILE_NAME variable in ENV.

 /* script to create `user_region_association` table*/

CREATE TABLE `user_region_association` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `region_id` bigint unsigned NOT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_region_association_region_id_user_id_unique_index` (`region_id`,`user_id`),
  KEY `user_region_association_user_id_fk` (`user_id`),
  KEY `user_region_association_user_id_fk_2` (`created_by`),
  CONSTRAINT `user_region_association_region_id_fk` FOREIGN KEY (`region_id`) REFERENCES `region` (`id`),
  CONSTRAINT `user_region_association_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `user_region_association_user_id_fk_2` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

/* script to add admin_ids in user_region_association table*/

INSERT INTO user_region_association (user_id, region_id, created_by, created_at, updated_by, updated_at) 
SELECT region.admin_id, region.id, region.created_by, region.created_at, region.updated_by, region.updated_at FROM region where admin_id is not null;

INSERT INTO user_region_association (user_id, region_id, created_by, created_at, updated_by, updated_at) 
SELECT region.agency_region_admin_id, region.id, region.created_by, region.created_at, region.updated_by, region.updated_at FROM region where agency_region_admin_id is not null;

/* script to alter region table*/

ALTER TABLE `region` 
DROP FOREIGN KEY `fk_region_admin_id`,
DROP FOREIGN KEY `fk_agency_region_admin_id`;

ALTER TABLE `region` 
DROP COLUMN `agency_region_admin_id`,
DROP COLUMN `admin_id`,
DROP INDEX `fk_region_admin_id_idx` ,
DROP INDEX `agency_region_admin_id` ,
DROP INDEX `admin_id` ;


/** Run below Python scripts in sequence(one by one) to apply survey database changes.

1. /data/translation_script_python/cron_jobs/1_survey_table_refactor.py
2. /data/translation_script_python/cron_jobs/2_survey_question_table_refactor_questions.py
3. /data/translation_script_python/cron_jobs/3_survey_mcq_answer_table_creation_and_insertion.py
4. /data/translation_script_python/cron_jobs/4_survey_answer_add_answer_id_clm.py
*/

INSERT INTO `clear_vue_prod`.`survey_mcq_options` (`id`, `options`) VALUES ('16', 'Tax Issue');
INSERT INTO `clear_vue_prod`.`survey_mcq_options` (`id`, `options`) VALUES ('17', 'Pension Issue');
INSERT INTO `clear_vue_prod`.`survey_mcq_options` (`id`, `options`) VALUES ('18', 'Other');

/** Continue run python script
5. /data/translation_script_python/cron_jobs/5_survey_questions_options_mapping.py
6. /data/translation_script_python/cron_jobs/6_drop_columns_after_merging.py

*/


ALTER TABLE `agency_client_association` 
ADD COLUMN `comment_restricted` BOOLEAN NOT NULL DEFAULT 0 AFTER `is_restricted`;

ALTER TABLE `message`
MODIFY COLUMN is_comment_allowed BOOLEAN DEFAULT 0;



ALTER TABLE workers ADD assignment_date DATE DEFAULT NULL AFTER start_date;

-- Please, toggle the safe mode to 'off'. workbentch -> edit -> preferences -> sqlEditor -> safemode -> reconnect. 
UPDATE workers SET assignment_date = start_date;
-- please toggle it 'on' again after query.


/*
  Ask devops to replace "demo_worker_data.csv" file to S3 bucket (for assignment_date changes.)
  BUCKET_NAME=<>
  WORKER_BUCKET_FOLDER=<>
  WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY=<>
*/

/* script to alter payroll table*/
ALTER TABLE `payroll` 
ADD COLUMN `pension_opt_out_prev` TINYINT(1) NULL AFTER `week`;
