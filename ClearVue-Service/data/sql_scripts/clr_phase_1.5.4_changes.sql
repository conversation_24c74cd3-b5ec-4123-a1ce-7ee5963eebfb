/* script to alter data in workers table*/

ALTER TABLE `workers` 
ADD COLUMN `inactivated_by` BIGINT UNSIGNED NULL DEFAULT NULL AFTER `language`;

ALTER TABLE `client_details` 
ADD COLUMN `worker_invite_email` TINYINT(1) NOT NULL DEFAULT 0 AFTER `weekday_start`;

UPDATE client_details SET worker_Invite_email = 1;

ALTER TABLE `client_details` 
ADD COLUMN `active_holiday` TINYINT(1) NOT NULL DEFAULT '0' AFTER `worker_invite_email`;


/* PUT .env vatiable */
SEND_WORKERS_MOBILE_INACTIVITY_NOTIFICATION=0


ALTER TABLE client_details
ADD COLUMN booking_format ENUM('HOURS', 'HEADS') AFTER weekday_start;

-- // Set 'HOURS' OR 'HEADS' for all existing client in PROD.

CREATE TABLE `pension_status_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `worker_id` bigint unsigned NOT NULL,
  `from` enum('OPTED_OUT','OPTED_IN') DEFAULT NULL,
  `to` enum('OPTED_OUT','OPTED_IN') DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_pension_status_log_created_by` (`created_by`),
  KEY `fk_pension_status_log_worker_id` (`worker_id`),
  CONSTRAINT `fk_pension_status_log_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_pension_status_log_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;


CREATE TABLE `department_site_association` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `department_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned NOT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_department_site` (`department_id`, `site_id`),
  KEY `fk_department_site_association_created_by_id_idx` (`created_by`),
  KEY `fk_department_site_association_department_id_idx` (`department_id`),
  KEY `fk_department_site_association_site_id_idx` (`site_id`),
  KEY `fk_department_site_association_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_department_site_association_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_department_site_association_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `fk_department_site_association_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_department_site_association_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `shift_site_association` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `shift_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint unsigned NOT NULL,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_shift_site` (`shift_id`, `site_id`),
  KEY `fk_shift_site_association_created_by_id_idx` (`created_by`),
  KEY `fk_shift_site_association_shift_id_idx` (`shift_id`),
  KEY `fk_shift_site_association_site_id_idx` (`site_id`),
  KEY `fk_shift_site_association_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_shift_site_association_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_shift_site_association_shift_id` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  CONSTRAINT `fk_shift_site_association_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_shift_site_association_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

INSERT IGNORE INTO department_site_association (department_id, site_id, created_by, updated_by, created_at, updated_at)
SELECT ja.department_id, ja.site_id, ja.created_by, ja.updated_by, ja.created_at, ja.updated_at
FROM job_association ja
WHERE NOT EXISTS (
    SELECT 1
    FROM department_site_association dsa
    WHERE dsa.department_id = ja.department_id
    AND dsa.site_id = ja.site_id
);


INSERT IGNORE INTO shift_site_association (shift_id, site_id, created_by, updated_by, created_at, updated_at)
SELECT j.shift_id, ja.site_id, ja.created_by, ja.updated_by, ja.created_at, ja.updated_at
FROM job_association ja
JOIN job j ON ja.job_id = j.id
WHERE NOT EXISTS (
    SELECT 1
    FROM shift_site_association ssa
    WHERE ssa.shift_id = j.shift_id
    AND ssa.site_id = ja.site_id
);

ALTER TABLE `client_details` DROP COLUMN `active_holiday`;

ALTER TABLE `agency_client_association` 
ADD COLUMN `holiday_activation` TINYINT(1) NOT NULL DEFAULT '0' AFTER `total_assignment_pay`,
ADD COLUMN `holiday_cost_removed` TINYINT(1) NOT NULL DEFAULT '0' AFTER `holiday_activation`;

ALTER TABLE `payroll_summary` 
ADD COLUMN `total_worked_charge` DECIMAL(10,2) NULL DEFAULT NULL AFTER `week`,
ADD COLUMN `holiday_employment_cost` DECIMAL(10,2) NULL DEFAULT NULL AFTER `total_worked_charge`,
ADD COLUMN `self_bill_value` DECIMAL(10,2) NULL DEFAULT NULL AFTER `holiday_employment_cost`,
ADD COLUMN `holiday_charge` DECIMAL(10,2) NULL DEFAULT NULL AFTER `self_bill_value`,
ADD COLUMN `accrual_value` DECIMAL(10,2) NULL DEFAULT NULL AFTER `holiday_charge`,
ADD COLUMN `calculated_pathway` ENUM('0|0', '0|1', '1|0', '1|1') DEFAULT '0|0';

CREATE TABLE `holiday_payroll_summary` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `payroll_meta_id` bigint unsigned NOT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `agency_id` bigint unsigned NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `week` int DEFAULT NULL,
  `total_app_levy_with_wtr` decimal(10,2) DEFAULT NULL,
  `app_levy_difference` decimal(10,2) DEFAULT NULL,
  `balance` decimal(10,2) DEFAULT NULL,
  `accrual_value` decimal(10,2) DEFAULT NULL,
  `worker_holiday_accrual_value` decimal(10,2) DEFAULT NULL,
  `total_worked_charge` decimal(10,2) DEFAULT NULL,
  `holiday_employment_cost` decimal(10,2) DEFAULT NULL,
  `worked_hours_saving` decimal(10,2) DEFAULT NULL,
  `calculated_pathway` ENUM('0|0', '0|1', '1|0', '1|1') DEFAULT '0|0',
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_holiday_payroll_summary_agency_id` (`agency_id`),
  KEY `fk_holiday_payroll_summary_client_id` (`client_id`),
  KEY `fk_holiday_payroll_summary_created_by` (`created_by`),
  KEY `fk_holiday_payroll_summary_site_id` (`site_id`),
  KEY `fk_holiday_payroll_summary_updated_by` (`updated_by`),
  KEY `fk_holiday_payroll_summary_worker_id` (`worker_id`),
  KEY `fk_holiday_payroll_summary_payroll_meta_id` (`payroll_meta_id`),
  CONSTRAINT `fk_holiday_payroll_summary_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_payroll_meta_id` FOREIGN KEY (`payroll_meta_id`) REFERENCES `payroll_meta` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_holiday_payroll_summary_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `agency_client_association` 
CHANGE COLUMN `covid_margin` `bh_margin` DECIMAL(3,2) NULL DEFAULT '0.00' ;

UPDATE los_rule
SET pay_type = 'Bh'
WHERE pay_type = 'Covid';

ALTER TABLE payroll
ADD COLUMN holiday_pay_type_value DECIMAL(10, 2) AFTER agency_id;

ALTER TABLE payroll_summary
ADD COLUMN total_holiday_pay DECIMAL(10, 2) AFTER agency_id;

ALTER TABLE payroll_summary
ADD COLUMN total_wtr_costs DECIMAL(10, 2) AFTER total_holiday_pay;

ALTER TABLE holiday_payroll_summary
ADD COLUMN wtr_cost DECIMAL(10, 2) AFTER holiday_employment_cost;
