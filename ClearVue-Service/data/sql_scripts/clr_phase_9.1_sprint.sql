ALTER TABLE workers
ADD COLUMN limited_hours tinyint(1) NOT NULL DEFAULT '0' after house_number;


SET SQL_SAFE_UPDATES = 0;

UPDATE workers
SET limited_hours = 1
WHERE student_visa = 1;

SET SQL_SAFE_UPDATES = 1;

-- S3 AWS 2 Worker Sample Sheet Update
-- demo_agency_worker_data_to_upload.csv
-- demo_client_worker_data_to_upload.csv

ALTER TABLE training_rules
ADD COLUMN limited_hours_threshold_week int NULL DEFAULT NULL after evaluation_week,
ADD COLUMN limited_hours_evaluation_week int NULL DEFAULT NULL after limited_hours_threshold_week;

ALTER TABLE credit_dues
ADD COLUMN limited_hours tinyint(1) NOT NULL DEFAULT '0' after worker_id;

CREATE TABLE `client_financial_rules` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `client_id` bigint unsigned NOT NULL,
    `financial_year_start` VARCHAR(4) NOT NULL,
    `financial_year_end` VARCHAR(4) NOT NULL,
    `start_date` date NOT NULL,
    `end_date` date NOT NULL,
    `total_weeks` int NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` BIGINT UNSIGNED DEFAULT NULL,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `updated_by` BIGINT UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `fk_client_financial_rules_client_id` (`client_id`),
    KEY `fk_client_financial_rules_created_by` (`created_by`),
    KEY `fk_client_financial_rules_updated_by` (`updated_by`),
    CONSTRAINT `fk_client_financial_rules_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
    CONSTRAINT `fk_client_financial_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
    CONSTRAINT `fk_client_financial_rules_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE client_financial_rules
ADD COLUMN ni_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after total_weeks,
ADD CONSTRAINT client_financial_rules_chk_ni_percent CHECK (ni_percent >= 0 AND ni_percent <= 100),
ADD COLUMN ni_threshold DECIMAL(6, 2) NOT NULL COMMENT 'Threshold value between 0 and 999' after ni_percent,
ADD CONSTRAINT client_financial_rules_chk_ni_threshold CHECK (ni_threshold >= 0 AND ni_threshold <= 999),
ADD COLUMN pension_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after ni_threshold,
ADD CONSTRAINT client_financial_rules_chk_pension_percent CHECK (pension_percent >= 0 AND pension_percent <= 100),
ADD COLUMN pension_threshold DECIMAL(6, 2) NOT NULL COMMENT 'Threshold value between 0 and 999' after pension_percent,
ADD CONSTRAINT client_financial_rules_chk_pension_threshold CHECK (pension_threshold >= 0 AND pension_threshold <= 999),
ADD COLUMN app_levy_percent DECIMAL(5, 2) NOT NULL COMMENT 'Percentage value between 0 and 100' after pension_threshold,
ADD CONSTRAINT client_financial_rules_chk_app_levy_percent CHECK (app_levy_percent >= 0 AND app_levy_percent <= 100);

-- Copy data from client_yearly_rules to client_financial_rules
INSERT INTO client_financial_rules (
  client_id,
  financial_year_start,
  financial_year_end,
  start_date,
  end_date,
  total_weeks,
  ni_percent,
  ni_threshold,
  pension_percent,
  pension_threshold,
  app_levy_percent,
  created_at,
  created_by,
  updated_at,
  updated_by
)
SELECT
  client_id,
  financial_year_start,
  financial_year_end,
  start_date,
  end_date,
  total_weeks,
  ni_percent,
  ni_threshold,
  pension_percent,
  pension_threshold,
  app_levy_percent,
  created_at,
  created_by,
  updated_at,
  updated_by
FROM
 client_yearly_rules;


-- SQL query to drop specified columns from client_yearly_rules table
ALTER TABLE client_yearly_rules
  DROP COLUMN ni_percent,
  DROP COLUMN ni_threshold,
  DROP COLUMN pension_percent,
  DROP COLUMN pension_threshold,
  DROP COLUMN app_levy_percent;

-- 

ALTER TABLE payroll
ADD COLUMN limited_hours tinyint(1) NOT NULL DEFAULT '0' after within_twelveweeks;

