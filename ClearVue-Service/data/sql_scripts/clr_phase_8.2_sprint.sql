ALTER TABLE workers
ADD COLUMN sort_code VARCHAR(6) DEFAULT NULL AFTER payroll_ref ,
ADD COLUMN account_number VARCHAR(15) DEFAULT NULL AFTER sort_code;

-- Update S3 bucket for "demo_agency_worker_data_to_upload.csv"


CREATE TABLE `booking_association_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `booking_association_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned NOT NULL,
  `requested_workers_heads` json NOT NULL,
  `requested_workers_total` float NOT NULL,
  `requested_supervisors_heads` json NOT NULL,
  `requested_supervisors_total` float NOT NULL,
  `requested_total` float NOT NULL,
  `fulfilled_workers_heads` json DEFAULT NULL,
  `fulfilled_workers_total` float DEFAULT NULL,
  `fulfilled_supervisors_heads` json DEFAULT NULL,
  `fulfilled_supervisors_total` float DEFAULT NULL,
  `fulfilled_total` float DEFAULT NULL,
  `booking_id` bigint unsigned NOT NULL,
  `status` enum('0','1','2') NOT NULL,
  `original_created_by` bigint unsigned NOT NULL,
  `original_created_at` datetime NOT NULL,
  `original_updated_by` bigint unsigned NOT NULL,
  `original_updated_at` datetime NOT NULL,
  `archived_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `archived_by` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_booking_association_history_booking_association` (`booking_association_id`),
  KEY `FK_booking_association_history_agency_details` (`agency_id`),
  KEY `FK_booking_association_history_booking` (`booking_id`),
  KEY `FK_booking_association_history_original_created_user` (`original_created_by`),
  KEY `FK_booking_association_history_original_updated_user` (`original_updated_by`),
  KEY `FK_booking_association_history_archived_user` (`archived_by`),
  CONSTRAINT `FK_booking_association_history_agency_details` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `FK_booking_association_history_archived_user` FOREIGN KEY (`archived_by`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_booking_association_history_booking` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`id`),
  CONSTRAINT `FK_booking_association_history_booking_association` FOREIGN KEY (`booking_association_id`) REFERENCES `booking_association` (`id`),
  CONSTRAINT `FK_booking_association_history_original_created_user` FOREIGN KEY (`original_created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_booking_association_history_original_updated_user` FOREIGN KEY (`original_updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


INSERT INTO `features` (`name`, `code`, `created_by`, `updated_by`) VALUES ('Reporting', 'reporting', '1', '1');

INSERT INTO permissions (access_type, user_type_id, feature_id, created_by, updated_by) VALUES
 (1, 2, 31, 1, 1),
 (1, 3, 31, 1, 1),
 (1, 4, 31, 1, 1),
 (1, 5, 31, 1, 1),
 (1, 7, 31, 1, 1),
 (1, 8, 31, 1, 1);