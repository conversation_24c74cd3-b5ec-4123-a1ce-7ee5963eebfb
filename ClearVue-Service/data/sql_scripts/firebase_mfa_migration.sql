-- Firebase MFA Migration Script
-- Adds necessary columns to support Firebase TOTP MFA functionality

-- Add MFA-related columns to the user table
ALTER TABLE `user` 
ADD COLUMN `firebase_uid` VARCHAR(255) NULL AFTER `password`,
ADD COLUMN `mfa_enabled` BOOLEAN DEFAULT FALSE AFTER `firebase_uid`,
ADD COLUMN `mfa_enrolled_at` TIMESTAMP NULL AFTER `mfa_enabled`,
ADD COLUMN `force_mfa_setup` BOOLEAN DEFAULT FALSE AFTER `mfa_enrolled_at`;

-- Add indexes for better performance
CREATE INDEX `idx_user_firebase_uid` ON `user` (`firebase_uid`);
CREATE INDEX `idx_user_mfa_enabled` ON `user` (`mfa_enabled`);

-- Update existing users to force MFA setup when M<PERSON> is enabled
-- This will be handled programmatically in the application logic
-- UPDATE `user` SET `force_mfa_setup` = TRUE WHERE `mfa_enabled` = FALSE;


/*
#########################################################

Now Open below file and follow instructions to enable TOTP MFA for Firebase project
scripts/enable-firebase-totp-mfa-using-fb-sdk.js

# Make sure you have added following .env variables as shown in above file
FIREBASE_WEB_API_KEY=<Web API Key from above step>
FIREBASE_AUTH_KEY_JSON=<Service Account JSON from above step>
FIREBASE_AUTH_ENABLED=1
FIREBASE_TOTP_MFA_ENABLED=1

#########################################################
*/



/* Add Following .env variables

USER_INACTIVITY_TIMEOUT_FIREBASE=600
USER_MAX_SESSION_DURATION_FIREBASE=28800

*/

-- Migration: Create user_activity table for tracking user session activity
-- This table helps resolve the issue where users inactive for more than 1 hour
-- could bypass inactivity timeout checks due to Firebase token expiration

CREATE TABLE IF NOT EXISTS `user_activity` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `last_activity_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `auth_time` datetime NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_user_activity_user_id` (`user_id`),
  KEY `idx_user_session` (`user_id`, `session_id`),
  KEY `idx_last_activity` (`last_activity_at`),
  KEY `idx_auth_time` (`auth_time`),
  CONSTRAINT `fk_user_activity_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for performance
CREATE INDEX `idx_user_activity_cleanup` ON `user_activity` (`created_at`);
CREATE INDEX `idx_user_activity_session_lookup` ON `user_activity` (`user_id`, `session_id`, `last_activity_at`);

-- Comments for documentation
ALTER TABLE `user_activity` 
  COMMENT = 'Tracks user session activity to handle inactivity timeouts beyond Firebase token expiration';

ALTER TABLE `user_activity` 
  MODIFY COLUMN `user_id` bigint(20) unsigned NOT NULL COMMENT 'Reference to user.id',
  MODIFY COLUMN `last_activity_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of last user activity/request',
  MODIFY COLUMN `auth_time` datetime NOT NULL COMMENT 'Timestamp when user authenticated (from Firebase auth_time)',
  MODIFY COLUMN `session_id` varchar(255) NOT NULL COMMENT 'Session identifier (Firebase UID or JWT token hash)',
  MODIFY COLUMN `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  MODIFY COLUMN `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update timestamp';
