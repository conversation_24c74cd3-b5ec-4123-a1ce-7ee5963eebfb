
----------------------------------
------------ los_rule ------------
----------------------------------

-- Check count
SELECT count(*) FROM los_rule WHERE pay_type = 'Internal standard';

-- <PERSON>eat<PERSON> untill all updated
UPDATE los_rule 
SET pay_type = 'Supervisor standard' WHERE pay_type = 'Internal standard' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM los_rule WHERE pay_type = 'Internal overtime';

-- <PERSON>eat<PERSON> untill all updated
UPDATE los_rule 
SET pay_type = 'Supervisor overtime' WHERE pay_type = 'Internal overtime' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM los_rule WHERE pay_type = 'Internal permanent';

-- <PERSON>eat<PERSON> untill all updated
UPDATE los_rule 
SET pay_type = 'Supervisor permanent' WHERE pay_type = 'Internal permanent' 
LIMIT 10000;


----------------------------------
-------- rate_card_data ----------
----------------------------------
-- Check count
SELECT count(*) FROM rate_card_data WHERE pay_type = 'Internal standard';

-- Repeate untill all updated
UPDATE rate_card_data 
SET pay_type = 'Supervisor standard' WHERE pay_type = 'Internal standard' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM rate_card_data WHERE pay_type = 'Internal overtime';

-- Repeate untill all updated
UPDATE rate_card_data 
SET pay_type = 'Supervisor overtime' WHERE pay_type = 'Internal overtime' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM rate_card_data WHERE pay_type = 'Internal permanent';

-- Repeate untill all updated
UPDATE rate_card_data 
SET pay_type = 'Supervisor permanent' WHERE pay_type = 'Internal permanent' 
LIMIT 10000;


----------------------------------
----- time_and_attendance_data ---
----------------------------------

-- Check count
SELECT count(*) FROM time_and_attendance_data WHERE pay_type = 'Internal standard';

-- Repeate untill all updated
UPDATE time_and_attendance_data 
SET pay_type = 'Supervisor standard' WHERE pay_type = 'Internal standard' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM time_and_attendance_data WHERE pay_type = 'Internal overtime';

-- Repeate untill all updated
UPDATE time_and_attendance_data 
SET pay_type = 'Supervisor overtime' WHERE pay_type = 'Internal overtime' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM time_and_attendance_data WHERE pay_type = 'Internal permanent';

-- Repeate untill all updated
UPDATE time_and_attendance_data 
SET pay_type = 'Supervisor permanent' WHERE pay_type = 'Internal permanent' 
LIMIT 10000;

----------------------------------
----- payroll_detailed_summary ---
----------------------------------

-- Check count
SELECT count(*) FROM payroll_detailed_summary WHERE pay_type = 'Internal standard';

-- Repeate untill all updated
UPDATE payroll_detailed_summary 
SET pay_type = 'Supervisor standard' WHERE pay_type = 'Internal standard' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM payroll_detailed_summary WHERE pay_type = 'Internal overtime';

-- Repeate untill all updated
UPDATE payroll_detailed_summary 
SET pay_type = 'Supervisor overtime' WHERE pay_type = 'Internal overtime' 
LIMIT 10000;

----------------------------------

-- Check count
SELECT count(*) FROM payroll_detailed_summary WHERE pay_type = 'Internal permanent';

-- Repeate untill all updated
UPDATE payroll_detailed_summary 
SET pay_type = 'Supervisor permanent' WHERE pay_type = 'Internal permanent' 
LIMIT 10000;

----------------------------------
----------------------------------

-- Rename existing column
ALTER TABLE margins 
RENAME COLUMN internal_standard_margin TO supervisor_standard_margin,
RENAME COLUMN internal_overtime_margin TO supervisor_overtime_margin,
RENAME COLUMN internal_permanent_margin TO supervisor_permanent_margin;

-----------------------------------

-- AWS Update sheets
-- timeAndAttendance_sample.csv
-- demo_rate_card_to_upload.csv

-----------------------------------

Alter table client_details
ADD column hide_ratings tinyint(1) NOT NULL DEFAULT '0' after rate_card_lookup;


------------------------------------- Add adjustment column to relevant tables
ALTER TABLE payroll_detailed_summary 
drop column adjustment;

ALTER TABLE payroll_detailed_summary 
ADD COLUMN adjustment tinyint(1) DEFAULT 0 After pay_type;

ALTER TABLE time_and_attendance_data 
ADD COLUMN adjustment tinyint(1) DEFAULT 0 After pay_type;

ALTER TABLE payroll_summary
ADD COLUMN adjustment_count INT DEFAULT 0 After calculated_pathway;

-------------------------------------- Add pay_correction column to relevant tables
ALTER TABLE payroll_detailed_summary 
ADD COLUMN pay_correction tinyint(1) DEFAULT 0 After adjustment;

ALTER TABLE time_and_attendance_data 
ADD COLUMN pay_correction tinyint(1) DEFAULT 0 After adjustment;

ALTER TABLE payroll_summary
ADD COLUMN pay_correction_count INT DEFAULT 0 After adjustment_count;

-- Deploy Python repo as well

-- env changes
MAX_WRONG_ATTEMPTS=5
LAST_ATTEMPTS=5
BLOCKED_TIME=30