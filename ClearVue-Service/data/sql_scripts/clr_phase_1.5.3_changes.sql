/* script to alter data in workers table*/

ALTER TABLE `workers` 
ADD COLUMN `other_assignment` TINYINT(1) NOT NULL DEFAULT '0' AFTER `transport`;

/* script to alter data in agency_client_association table*/

ALTER TABLE `agency_client_association` 
ADD COLUMN `total_assignment_pay` TINYINT(1) NOT NULL DEFAULT '0' AFTER `comment_restricted`;

/* CLR-1387 | Update SEX & NAtionality Validations */
SELECT count(*), orientation FROM clear_vue_prod.workers group by orientation;

UPDATE workers
SET orientation = NULL
WHERE orientation IN ('N', 'T', "");

UPDATE workers
SET orientation = "Male"
WHERE orientation IN ('male', 'm', 'M');

UPDATE workers
SET orientation = "Female"
WHERE orientation IN ('female', 'f', 'F', 'Femlae', 'Femae');

UPDATE workers
SET orientation = "Other"
WHERE orientation IN ('other', 'o', 'O');

/* Add "sample_total_agency_pay_file.csv" file to S3 "total-agency-pay-sheets" folder */

/*  Add below .env variables to environment  */
TAP_SHEETS_BUCKET_FOLDER=total-agency-pay-sheets
TAP_SAMPLE_FILE_BUCKET_FOLDER=total-agency-pay-sample
TAP_UPLOAD_SAMPLE_FILE_BUCKET_KEY=sample_total_agency_pay_file.csv

--
-- Table structure for table `total_agency_pay`
--

CREATE TABLE `total_agency_pay` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `path` varchar(250) NOT NULL,
  `status` varchar(45) NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned DEFAULT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `week` int DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_total_agency_pay_agency_id` (`agency_id`),
  KEY `fk_total_agency_pay_client_id` (`client_id`),
  KEY `fk_total_agency_pay_site_id` (`site_id`),
  KEY `fk_total_agency_pay_created_by_idx` (`created_by`),
  KEY `fk_total_agency_pay_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_total_agency_pay_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_total_agency_pay_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_total_agency_pay_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_total_agency_pay_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_total_agency_pay_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;

--
-- Table structure for table `total_agency_pay_data`
--

CREATE TABLE `total_agency_pay_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `total_agency_pay_id` bigint unsigned NOT NULL,
  `agency_id` bigint unsigned DEFAULT NULL,
  `client_id` bigint unsigned DEFAULT NULL,
  `worker_id` bigint unsigned NOT NULL,
  `employee_id` varchar(250) DEFAULT NULL,
  `site_id` bigint unsigned DEFAULT NULL,
  `tap_value` decimal(10,2) NOT NULL,
  `start_date` date DEFAULT NULL,
  `week` int DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_total_agency_pay_data_site_id` (`site_id`),
  KEY `fk_total_agency_pay_data_agency_id_idx` (`agency_id`),
  KEY `fk_total_agency_pay_data_client_id_idx` (`client_id`),
  KEY `fk_total_agency_pay_data_created_by_idx` (`created_by`),
  KEY `fk_total_agency_pay_data_total_agency_pay_id_idx` (`total_agency_pay_id`),
  KEY `fk_total_agency_pay_data_updated_by_idx` (`updated_by`),
  KEY `fk_total_agency_pay_data_worker_id` (`worker_id`),
  CONSTRAINT `fk_total_agency_pay_data_agency_id` FOREIGN KEY (`agency_id`) REFERENCES `agency_details` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_site_id` FOREIGN KEY (`site_id`) REFERENCES `site` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_total_agency_pay_id` FOREIGN KEY (`total_agency_pay_id`) REFERENCES `total_agency_pay` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_total_agency_pay_data_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=latin1;

/* Create total-agency-pay-sheets folder in S3 bucket. Add TOTAL_AGENCY_PAY_FOLDER=total-agency-pay-sheets in env. file */


-- Add this .env variable
-- TNA_SHEETS_BUCKET_FOLDER=time-and-attendance-sheets

ALTER TABLE `agency_client_association` 
ADD COLUMN `internal_overtime_margin` DECIMAL(3,2) NULL DEFAULT '0.00' AFTER `internal_margin`;


ALTER TABLE `agency_client_association` 
CHANGE COLUMN `internal_margin` `internal_standard_margin` DECIMAL(3,2) NULL DEFAULT '0.00' ;

SET SQL_SAFE_UPDATES = 0; 

UPDATE los_rule
SET pay_type = 'Internal standard'
WHERE pay_type = 'Internal';

UPDATE time_and_attendance_data
SET pay_type = 'Internal standard'
WHERE pay_type = 'Internal';

SET SQL_SAFE_UPDATES = 1;

--  Ask devops to replace "timeAndAttendance_sample.csv" file to S3 bucket (For new pay type - internal.)

ALTER TABLE `agency_client_association` 
ADD COLUMN `internal_permanent_margin` DECIMAL(3,2) NULL DEFAULT '0.00' AFTER `internal_overtime_margin`;
