ALTER TABLE `payroll`
ADD COLUMN `under_twentyone` BOOLEAN DEFAULT NULL AFTER `agency_id`,
ADD COLUMN `under_twentytwo` BOOLEAN DEFAULT NULL AFTER `under_twentyone`,
ADD COLUMN `within_twelveweeks` BOOLEAN DEFAULT NULL AFTER `under_twentytwo`;

-- *** Check count for below PROCEDURE Execution *** ---
SELECT count(*) FROM payroll order by id desc;
-----

-- Batched Update Procedure
DELIMITER //
CREATE PROCEDURE BatchUpdatePayrollFlagsWithTemp()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE batch_size INT DEFAULT 1000;
    DECLARE offset_value INT DEFAULT 0;
    
    -- Create temporary table for batch processing
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_batch (
        id BIGINT PRIMARY KEY
    );
    
    REPEAT
        -- Clear temporary table
        TRUNCATE TABLE temp_batch;
        
        -- Insert batch of IDs
        INSERT INTO temp_batch (id)
        SELECT id FROM payroll
        WHERE id >= offset_value
        ORDER BY id
        LIMIT batch_size;
        
        -- Process batch
        UPDATE payroll p
        JOIN workers w ON p.worker_id = w.id
        JOIN temp_batch t ON p.id = t.id
        SET 
            p.under_twentyone = CASE 
                WHEN TIMESTAMPDIFF(YEAR, w.date_of_birth, p.end_date) < 21 THEN 1 
                ELSE 0 
            END,
            p.under_twentytwo = CASE 
                WHEN TIMESTAMPDIFF(YEAR, w.date_of_birth, p.end_date) < 22 THEN 1 
                ELSE 0 
            END,
            p.within_twelveweeks = CASE 
                WHEN TIMESTAMPDIFF(WEEK, w.assignment_date, p.end_date) < 12 THEN 1 
                ELSE 0 
            END
        WHERE 
            p.end_date IS NOT NULL 
            AND w.date_of_birth IS NOT NULL 
            AND w.assignment_date IS NOT NULL;
            
        SET offset_value = offset_value + batch_size;
        SET done = NOT EXISTS (SELECT 1 FROM payroll WHERE id >= offset_value LIMIT 1);
        
        DO SLEEP(0.005);
        
    UNTIL done END REPEAT;
    
    -- Clean up
    DROP TEMPORARY TABLE IF EXISTS temp_batch;
END //
DELIMITER ;


-- Call the procedure
CALL BatchUpdatePayrollFlagsWithTemp();

-- Optional: Drop procedure after use
DROP PROCEDURE IF EXISTS BatchUpdatePayrollFlagsWithTemp;

==================
-- # AWS Sheet Updates
-- Update "demo_agency_worker_data_to_upload.csv" and "demo_client_worker_data_to_upload.csv" to S3 Bucket