

Alter table workers
ADD COLUMN workers_supervisor_status ENUM('FRONT_OFFICE', 'BACK_OFFICE') DEFAULT NULL AFTER `type`;

-- Update "demo_agency_worker_data_to_upload.csv"  and "demo_agency_worker_data_to_update.csv"

INSERT INTO `features` (`name`, `code`, `created_by`, `updated_by`) VALUES ('Agency Management', 'agency_management', '1', '1');

INSERT INTO permissions (access_type, user_type_id,feature_id, created_by, updated_by) VALUES
 (1, 2, 29, 1, 1),
 (1, 3, 29, 1, 1)

-------------------------------------

 SET SQL_SAFE_UPDATES = 0;

-- Rename column 'required_heads' to 'required_workers_heads' with JSON datatype
ALTER TABLE booking
CHANGE COLUMN required_heads required_workers_heads JSON NOT NULL;

-- Add new column 'required_workers_total' with FLOAT datatype and copy values from 'total' column
ALTER TABLE booking
ADD COLUMN required_workers_total FLOAT UNSIGNED NOT NULL after required_workers_heads;

UPDATE booking
SET required_workers_total = total;

-- Add new column 'required_supervisors_heads' with JSON datatype and set default values
ALTER TABLE booking
ADD COLUMN required_supervisors_heads JSON NOT NULL after required_workers_total;

UPDATE booking
SET required_supervisors_heads = '{"1": "0", "2": "0", "3": "0", "4": "0", "5": "0", "6": "0", "7": "0"}';


-- Add new column 'required_supervisors_total' with FLOAT datatype and set default value
ALTER TABLE booking
ADD COLUMN required_supervisors_total FLOAT UNSIGNED NOT NULL DEFAULT 0 after required_supervisors_heads;

UPDATE booking
SET required_supervisors_total = 0;


------------------------------------------

-- Rename column 'requested_heads' to 'requested_workers_heads' with JSON datatype
ALTER TABLE booking_association
CHANGE COLUMN requested_heads requested_workers_heads JSON NOT NULL;

-- Add new column 'requested_workers_total' with FLOAT datatype and copy values from 'requested_total' column
ALTER TABLE booking_association
ADD COLUMN requested_workers_total FLOAT NOT NULL after requested_workers_heads;

UPDATE booking_association
SET requested_workers_total = requested_total;


-- Add new column 'requested_supervisors_heads' with JSON datatype and set default values
ALTER TABLE booking_association
ADD COLUMN requested_supervisors_heads JSON NOT NULL after requested_workers_total;


UPDATE booking_association
SET requested_supervisors_heads = '{"1": "0", "2": "0", "3": "0", "4": "0", "5": "0", "6": "0", "7": "0"}';

-- Add new column 'requested_supervisors_total' with FLOAT datatype and set default value
ALTER TABLE booking_association
ADD COLUMN requested_supervisors_total FLOAT NOT NULL after requested_supervisors_heads;

UPDATE booking_association
SET requested_supervisors_total = 0;


-- Rename column 'fulfilled_heads' to 'fulfilled_workers_heads' with JSON datatype and set DEFAULT NULL
ALTER TABLE booking_association
CHANGE COLUMN fulfilled_heads fulfilled_workers_heads JSON DEFAULT NULL;

-- Add new column 'fulfilled_workers_total' with FLOAT datatype and set DEFAULT NULL
ALTER TABLE booking_association
ADD COLUMN fulfilled_workers_total FLOAT DEFAULT NULL after fulfilled_workers_heads;

UPDATE booking_association
SET fulfilled_workers_total = fulfilled_total;

-- Add new column 'fulfilled_supervisors_heads' with JSON datatype and set DEFAULT NULL
ALTER TABLE booking_association
ADD COLUMN fulfilled_supervisors_heads JSON DEFAULT NULL after fulfilled_workers_total;


-- Add new column 'fulfilled_supervisors_total' with FLOAT datatype and set DEFAULT NULL
ALTER TABLE booking_association
ADD COLUMN fulfilled_supervisors_total FLOAT DEFAULT NULL after fulfilled_supervisors_heads;

ALTER TABLE booking_association
CHANGE COLUMN requested_total requested_total float NOT NULL AFTER requested_supervisors_total;

SET SQL_SAFE_UPDATES = 1;

