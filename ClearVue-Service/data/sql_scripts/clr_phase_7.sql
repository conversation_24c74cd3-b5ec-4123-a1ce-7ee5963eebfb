ALTER TABLE credit_dues
MODIFY performance_number decimal(10,2) NULL;

ALTER TABLE workers
ADD COLUMN is_returning_worker TINYINT(1) DEFAULT 0,
ADD COLUMN historical_employee_ids TEXT DEFAULT NULL;


CREATE TABLE ftp_credentials (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `protocol` enum('FTP', 'SFTP') NOT NULL,
  `ftp_host` VARCHAR(255) NOT NULL,
  `ftp_port` INT DEFAULT 21 NOT NULL,
  `ftp_username` VARCHAR(100) NOT NULL,
  `ftp_password` VARCHAR(100) NOT NULL,
  `remote_directory` VARCHAR(255) NOT NULL,
  `notification_email` varchar(250) DEFAULT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON><PERSON><PERSON> (`id`),
  <PERSON><PERSON><PERSON> `fk_ftp_credentials_client_id` (`client_id`),
  KEY `fk_ftp_credentials_created_by` (`created_by`),
  KEY `fk_ftp_credentials_updated_by` (`updated_by`),
  CONSTRAINT `fk_ftp_credentials_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_ftp_credentials_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_ftp_credentials_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;


CREATE TABLE ftp_configurations (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `ftp_script_type` ENUM('TNA', 'WORKERS_UPLOAD') NOT NULL,
  `cron_expression` VARCHAR(100) NOT NULL,
  `sqs_queue_name` VARCHAR(255) DEFAULT NULL,
  `sqs_queue_url` VARCHAR(255) DEFAULT NULL,
  `ftp_credential_id` bigint unsigned NOT NULL,
  `created_by` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_ftp_configurations_client_id` (`client_id`),
  KEY `fk_ftp_configurations_created_by` (`created_by`),
  KEY `fk_ftp_configurations_updated_by` (`updated_by`),
  KEY `fk_ftp_configurations_ftp_credential_id` (`ftp_credential_id`),
  CONSTRAINT `fk_ftp_configurations_client_id` FOREIGN KEY (`client_id`) REFERENCES `client_details` (`id`),
  CONSTRAINT `fk_ftp_configurations_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_ftp_configurations_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_ftp_configurations_ftp_credential_id` FOREIGN KEY (`ftp_credential_id`) REFERENCES `ftp_credentials` (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;


CREATE TABLE ftp_process_logs (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ftp_configuration_id` bigint unsigned NOT NULL,
  `process_status_id` TINYINT NOT NULL COMMENT '0->INITIAL, 1->PRODUCING, 2->PRODUCING_DONE, 3->CONSUMING, 4->CONSUMING_DONE, 5->READY_FOR_REPORT_GENERATION, 6->REPORT_GENERATION_IN_PROGRESS, 7->REPORT_GENERATION_COMPLETED, 8->READY_FOR_NOTIFICATION, 9->PROCESSING_NOTIFICATION, 10->NOTIFICATION_SENT',
  `error` TEXT DEFAULT NULL,
  `report_generation_status` BOOLEAN DEFAULT FALSE,
  `generated_report_filepath` VARCHAR(512) DEFAULT NULL, 
  `lock_timestamp` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;


CREATE TABLE ftp_sqs_message_logs (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ftp_process_log_id` bigint unsigned NOT NULL,
  `file_name` VARCHAR(255) NOT NULL,
  `sqs_message_payload` JSON NOT NULL,
  `status` ENUM('IN_QUEUE', 'PROCESSING', 'SUCCEED', 'FAILED') NOT NULL,
  `api_response` TEXT NULL COMMENT 'Response from the API',
  `payroll_meta_id` bigint unsigned DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_ftp_sqs_message_logs_ftp_process_log_id` (`ftp_process_log_id`),
  CONSTRAINT `fk_ftp_sqs_message_logs_ftp_process_log_id` FOREIGN KEY (`ftp_process_log_id`) REFERENCES `ftp_process_logs` (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;

-- Staging Queries
"""
DROP table ftp_config;

ALTER TABLE ftp_process_logs
DROP FOREIGN KEY fk_ftp_process_logs_ftp_configuration_id;

ALTER TABLE ftp_process_logs
DROP INDEX fk_ftp_process_logs_ftp_configuration_id;

# Verify the structure of the table to confirm the foreign key has been removed:
SHOW CREATE TABLE ftp_process_logs;

ALTER TABLE ftp_sqs_message_logs
ADD COLUMN payroll_meta_id BIGINT UNSIGNED DEFAULT NULL after api_response;

ALTER TABLE ftp_process_logs
ADD COLUMN report_generation_status BOOLEAN DEFAULT FALSE AFTER error,
ADD COLUMN generated_report_filepath VARCHAR(512) DEFAULT NULL AFTER report_generation_status;

SET SQL_SAFE_UPDATES = 0;

update ftp_process_logs SET process_status_id = 10 where process_status_id = 7;

SET SQL_SAFE_UPDATES = 1;
"""

-- ENV Variable to & ALL and follwing one
FLASK_SERVICE_URL='http://clearvue-python-service:5008'

-- Add .env at Clearvue-Python-Service
Replace with => CORE_SERVICE_URL="http://clearvue-app:5000/api"

-- Production user, Give SQS Access