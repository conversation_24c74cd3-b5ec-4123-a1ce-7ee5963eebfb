# 6
from creds import get_connection


def add_answer_id_column(conn):
    cursor = conn.cursor()

    # 1. Delete 'name' column and rename 'name_json' to 'name'
    # TODO: Suggest keep it name_json now, after successful deploy in prod, you can run exact below queryyyyy..
    rename_column_query = (
        "ALTER TABLE survey DROP COLUMN name, CHANGE name_json name json DEFAULT NULL"
    )
    cursor.execute(rename_column_query)

    # 2. Delete 'name' column and rename 'name_json' to 'name'
    # TODO: Suggest keep it questions_json now, after successful deploy in prod, you can drop `question_text`.
    rename_column_query_2 = "ALTER TABLE survey_questions DROP COLUMN question_text"
    cursor.execute(rename_column_query_2)

    # 4 Delete 'answer' column keep 'answer_id'
    rename_column_query_4 = "ALTER TABLE survey_answer DROP COLUMN answer"
    cursor.execute(rename_column_query_4)

    # 5 drop `options`.keep it `options_json`
    rename_column_query_3 = "ALTER TABLE survey_questions DROP COLUMN options"
    cursor.execute(rename_column_query_3)

    conn.commit()


def main():
    conn = get_connection()
    add_answer_id_column(conn)
    conn.close()


if __name__ == "__main__":
    main()
