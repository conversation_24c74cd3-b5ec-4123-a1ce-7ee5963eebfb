import json
from creds import get_connection
from deep_translator import GoogleTranslator
import logging

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)


def translate_to_desired_language(text_to_translate, source_language):
    lang = ['sq', 'bg', 'cs', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
            'sl', 'ur']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate(text_to_translate)

    return result

def fetch_messages(connection):
    with connection.cursor() as cursor:
        
        cursor.execute("SELECT id, body FROM message WHERE type = 'SYSTEM_DEFAULT'")

        #TODO: comment above line && uncomment below line ... to run script again for 'message_system_default' table
        # cursor.execute("SELECT id, body FROM message_system_default")

        return cursor.fetchall()


def update_translated_message_body(connection, message_ids, body):
    with connection.cursor() as cursor:

        update_query = "UPDATE message SET body = %s WHERE id IN ({})".format(
            ','.join(['%s'] * len(message_ids))
        )

        #TODO: comment above line && uncomment below line ... to run script again for 'message_system_default' table
        # update_query = "UPDATE message_system_default SET body = %s WHERE id IN ({})".format(
        #     ','.join(['%s'] * len(message_ids))
        # )

        cursor.execute(update_query, [json.dumps(body)] + message_ids)
        connection.commit()


def main():
    connection = get_connection()
    messages = fetch_messages(connection)

    # Group messages by body value
    message_groups = {}
    for message_id, message_body in messages:
        if message_body not in message_groups:
            message_groups[message_body] = []
        message_groups[message_body].append(message_id)

    # Translate and update each message group
    count_break_point = 0
    for message_body, message_ids in message_groups.items():
        # 0 because first it starts with 0, it connection breaks at count = 13 then replace >= 13 below.
        if count_break_point >= 0:
            message_body_load = json.loads(message_body)
            body_text_data = message_body_load[0][0]["data"]
            translated_message_object = translate_to_desired_language(body_text_data, "en")

            # Check if the list contains an object with "type" equal to "translate"
            updated = False
            for obj in message_body_load[0]:
                if obj.get("type") == "translate":
                    obj["data"] = translated_message_object
                    updated = True

            # If no object with "type" equal to "translate" was found, add a new one
            if not updated:
                message_body_load[0].append({"data": translated_message_object, "type": "translate"})

            update_translated_message_body(connection, message_ids, message_body_load)
        count_break_point += 1
        print("count: ", count_break_point)

    connection.close()


if __name__ == "__main__":
    main()
