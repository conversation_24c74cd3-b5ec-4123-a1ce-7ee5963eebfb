# 4
from creds import get_connection

def add_answer_id_column(conn):
    cursor = conn.cursor()
    cursor.execute("ALTER TABLE survey_answer ADD COLUMN answer_id INTEGER AFTER answer")
    cursor.execute(
        "ALTER TABLE survey_answer ADD CONSTRAINT fk_answer_id FOREIGN KEY (answer_id) REFERENCES survey_mcq_options(id)")
    cursor.execute("UPDATE survey_answer SET answer_id = (SELECT id FROM survey_mcq_options WHERE options = answer)")
    cursor.execute("ALTER TABLE survey_answer MODIFY answer varchar(250) NULL")
    cursor.execute("ALTER TABLE survey_answer ALTER COLUMN answer SET DEFAULT NULL")
    conn.commit()


def main():
    conn = get_connection()
    add_answer_id_column(conn)
    conn.close()


if __name__ == "__main__":
    main()
