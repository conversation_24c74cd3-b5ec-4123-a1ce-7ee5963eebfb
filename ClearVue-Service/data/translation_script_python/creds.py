import mysql.connector
import os

# ## Export below vars into environment
# export MYSQL_DATABASE_HOST=
# export MYSQL_DATABASE_USER=
# export MYSQL_DATABASE_PASSWORD=
# export MYSQL_DATABASE_DB=

creds_from_env = {
    "host": os.getenv("MYSQL_DATABASE_HOST"),
    "user": os.getenv("MYSQL_DATABASE_USER"),
    "password": os.getenv("MYSQL_DATABASE_PASSWORD"),
    "database": os.getenv("MYSQL_DATABASE_DB"),
}

# Setup current database creds
current = creds_from_env


def get_connection():
    connection = mysql.connector.connect(
        host=current["host"],
        user=current["user"],
        password=current["password"],
        database=current["database"],
    )
    return connection
