# 3
from creds import get_connection

if __name__ == '__main__':

    # Connect to the database
    conn = get_connection()
    # Create a cursor object to execute queries
    cursor = conn.cursor()

    # Create the `survey_mcq_answers` table
    table_query = """
    CREATE TABLE survey_mcq_options (
      id int NOT NULL AUTO_INCREMENT,
      options varchar(200) DEFAULT NULL,
      PRIMARY KEY (id)
    ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    """
    cursor.execute(table_query)

    # Get the list of distinct answers
    answer_query = """
    SELECT distinct(answer) FROM survey_answer;
    """
    cursor.execute(answer_query)
    answers = cursor.fetchall()

    # Insert the answers into the `survey_mcq_answers` table
    insert_query = """
    INSERT INTO survey_mcq_options (options) VALUES (%s);
    """
    for answer in answers:
        cursor.execute(insert_query, (answer[0],))

    # Commit the changes
    conn.commit()

    # Close the cursor and the connection
    cursor.close()
    conn.close()
