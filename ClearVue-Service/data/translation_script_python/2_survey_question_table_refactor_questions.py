# 2

import json
from creds import get_connection
from deep_translator import GoogleTranslator
import logging

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)


def translate_to_desired_language(text_to_translate, source_language):
    lang = ['sq', 'bg', 'cs', 'en', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
            'sl', 'ur']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate(text_to_translate)
    LOG.info("r %s", result)
    return result


def fetch_questions_group_by_question_text(conn):
    cursor = conn.cursor()
    query = "SELECT question_text FROM survey_questions group by question_text"
    cursor.execute(query)
    return cursor.fetchall()


def update_translated_question_text_in_minimum_query(connection, question_text, translated_text):
    cursor = connection.cursor()
    update_query = "UPDATE survey_questions SET question_json = %s WHERE question_text = %s"
    cursor.execute(update_query, (json.dumps(translated_text), question_text))
    connection.commit()


def main():
    connection = get_connection()
    cursor = connection.cursor()

    # Phase 1
    questions_group = fetch_questions_group_by_question_text(connection)
    questions_translation_mapping = {}
    for question_text in questions_group:
        translated_text = translate_to_desired_language(question_text[0], "en")
        questions_translation_mapping[question_text[0]] = translated_text

    print("here")

    # Create new column 'name_json' with datatype 'json'
    create_column_query = "ALTER TABLE survey_questions ADD question_json json DEFAULT NULL AFTER id"
    cursor.execute(create_column_query)
    connection.commit()

    print("new column created")

    for text, translate in questions_translation_mapping.items():
        LOG.info("text group to update %s", text)
        update_translated_question_text_in_minimum_query(connection, text, translate)
        LOG.info("Updated")

    cursor.close()
    connection.close()


if __name__ == "__main__":
    main()
