import json
from creds import get_connection
from deep_translator import GoogleTranslator
import logging

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)


def translate_to_desired_language(text_to_translate, source_language):
    lang = ['sq', 'bg', 'cs', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
            'sl', 'ur']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate(text_to_translate)

    return result


def fetch_messages(connection):
    with connection.cursor() as cursor:
        # TODO: Do changes in both "message" & "message_system_default" table
        #     : Uncomment below any one of the below at a time to run script 2 time
        cursor.execute("SELECT id, title FROM message WHERE type in ('SYSTEM_DEFAULT', 'SYSTEM')")
        # cursor.execute("SELECT id, title FROM message_system_default")
        return cursor.fetchall()


def update_translated_message_body(connection, message_ids, title_translations):
    with connection.cursor() as cursor:
        # TODO: Do changes in both "message" & "message_system_default" table
        #     : after 1st run, user "message_system_default" for 2nd run
        update_query = "UPDATE message SET title_translations = %s WHERE id IN ({})".format(
            ','.join(['%s'] * len(message_ids))
        )
        cursor.execute(update_query, [json.dumps(title_translations)] + message_ids)
        connection.commit()


def main():
    connection = get_connection()
    messages = fetch_messages(connection)

    # Group messages by body value
    message_groups = {}
    for message_id, message_body in messages:
        if message_body not in message_groups:
            message_groups[message_body] = []
        message_groups[message_body].append(message_id)

    # Translate and update each message group
    count_break_point = 0
    for message_title, message_ids in message_groups.items():
        # 0 because first it starts with 0, if connection breaks at count = 13 then replace >= 13 below.
        if count_break_point >= 0:
            translated_message_object = translate_to_desired_language(message_title, "en")

            update_translated_message_body(connection, message_ids, translated_message_object)
        count_break_point += 1
        print("count: ", count_break_point)

    connection.close()


if __name__ == "__main__":
    main()
