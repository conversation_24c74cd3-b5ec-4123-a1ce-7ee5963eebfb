# 5
import json

from deep_translator import GoogleTranslator
import logging
from creds import get_connection

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)

# Connect to the database
cnx = get_connection()
cursor = cnx.cursor()


def translate_to_desired_language(text_to_translate, source_language):
    lang = ['sq', 'bg', 'cs', 'en', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
            'sl', 'ur']
    # lang = ['sq', 'bg', 'cs', 'en']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate_batch(text_to_translate)
        print(result[la])

    return result


def get_updated_value(_option_ids, _translated_value):
    return {i: {_option_ids[value.index(j)]: j for j in value} for i, value in _translated_value.items()}


if __name__ == '__main__':
    # Fetch the data from the survey_mcq_options table
    select_mcq_options_query = "SELECT id, options FROM survey_mcq_options"
    cursor.execute(select_mcq_options_query)

    # Store the data in a dictionary
    mcq_options = {}
    for (id, option) in cursor:
        mcq_options[option] = id

    # Create new column 'options_json' with datatype 'json'
    create_column_query = "ALTER TABLE survey_questions ADD options_json json DEFAULT NULL AFTER options"
    cursor.execute(create_column_query)
    cnx.commit()

    # Use the dictionary to look up the id of an option
    select_questions_query = "SELECT id, options FROM survey_questions where option_type = 'Mcq'"
    cursor.execute(select_questions_query)

    questions_list = []
    for (_id, options) in cursor:
        questions_list.append((_id, options))

    for (_id, options) in questions_list:
        options = options.strip("[]").replace("\"", "").split(", ")
        option_ids = [str(mcq_options[option]) for option in options]

        translated_value = translate_to_desired_language(options, "auto")
        value_to_update = get_updated_value(option_ids, translated_value)
        update_options_query = "UPDATE survey_questions SET options_json = %s WHERE id = %s".format()
        cursor.execute(update_options_query, (json.dumps(value_to_update), _id))
        print(value_to_update)

    # Commit the changes and close the cursor
    cnx.commit()
    cursor.close()
    cnx.close()
