# 1
import json
from creds import get_connection
from deep_translator import GoogleTranslator
import logging

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)


def translate_to_desired_language(text_to_translate, source_language):
    lang = ['sq', 'bg', 'cs', 'en', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
            'sl', 'ur']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate(text_to_translate)

    return result


def fetch_surveys(connection):
    cursor = connection.cursor()
    cursor.execute("SELECT id, name FROM survey")
    return cursor.fetchall()


def update_translated_survey_name(connection, survey_id, translated_name):
    cursor = connection.cursor()
    update_query = "UPDATE survey SET name_json = %s WHERE id = %s"
    cursor.execute(update_query, (json.dumps(translated_name), survey_id))
    connection.commit()


def main():
    connection = get_connection()

    # Create new column 'name_json' with datatype 'json'
    cursor = connection.cursor()
    create_column_query = "ALTER TABLE survey ADD name_json json DEFAULT NULL AFTER id"
    cursor.execute(create_column_query)
    connection.commit()

    surveys = fetch_surveys(connection)
    for survey in surveys:
        survey_id, survey_name = survey
        translated_name = translate_to_desired_language(survey_name, "en")
        update_translated_survey_name(connection, survey_id, translated_name)

    cursor.close()
    connection.close()


if __name__ == "__main__":
    main()

