from translation_script_python.creds import get_connection
import re


def process_value(value):
    # Remove special characters, spaces, and convert to lowercase
    processed_value = re.sub(r'[^a-zA-Z]', '', str(value)).lower()
    return None if processed_value == 'na' else processed_value


if __name__ == "__main__":
    nationalitiesPossibleValues = {
        "afghanistan": "Afghan",
        "afghan": "Afghan",
        "afg": "Afghan",
        "af": "Afghan",
        "afgh": "Afghan",
        "albania": "Albanian",
        "albanian": "Albanian",
        "alb": "Albanian",
        "al": "Albanian",
        "alba": "Albanian",
        "algeria": "Algerian",
        "algerian": "Algerian",
        "dza": "Algerian",
        "dz": "Algerian",
        "alg": "Algerian",
        "americansamoa": "American Samoan",
        "americansamoan": "American Samoan",
        "asm": "American Samoan",
        "as": "American Samoan",
        "asmo": "American Samoan",
        "andorra": "Andorran",
        "andorran": "Andorran",
        "and": "Andorran",
        "ad": "Andorran",
        "ando": "Andorran",
        "angola": "Angolan",
        "angolan": "Angolan",
        "ago": "Angolan",
        "ao": "Angolan",
        "ango": "Angolan",
        "anguilla": "Anguillan",
        "anguillan": "Anguillan",
        "aia": "Anguillan",
        "ai": "Anguillan",
        "angu": "Anguillan",
        "antiguaandbarbuda": "Antiguan, Barbudan",
        "antiguanbarbudan": "Antiguan, Barbudan",
        "atg": "Antiguan, Barbudan",
        "ag": "Antiguan, Barbudan",
        "atgu": "Antiguan, Barbudan",
        "argentina": "Argentine",
        "argentine": "Argentine",
        "arg": "Argentine",
        "ar": "Argentine",
        "arge": "Argentine",
        "armenia": "Armenian",
        "armenian": "Armenian",
        "arm": "Armenian",
        "am": "Armenian",
        "arme": "Armenian",
        "aruba": "Aruban",
        "aruban": "Aruban",
        "abw": "Aruban",
        "aw": "Aruban",
        "arub": "Aruban",
        "ascension": "Ascension Islander",
        "ascensionislander": "Ascension Islander",
        "asc": "Ascension Islander",
        "ac": "Ascension Islander",
        "ascn": "Ascension Islander",
        "australia": "Australian",
        "australian": "Australian",
        "aus": "Australian",
        "au": "Australian",
        "aust": "Australian",
        "australiancapitalterritory": "Australian",
        "act": "Australian",
        "austria": "Austrian",
        "austrian": "Austrian",
        "aut": "Austrian",
        "at": "Austrian",
        "azerbaijan": "Azerbaijani",
        "azerbaijani": "Azerbaijani",
        "aze": "Azerbaijani",
        "az": "Azerbaijani",
        "azer": "Azerbaijani",
        "bahamas": "Bahamian",
        "bahamian": "Bahamian",
        "bhs": "Bahamian",
        "bs": "Bahamian",
        "bahm": "Bahamian",
        "bahrain": "Bahraini",
        "bahraini": "Bahraini",
        "bhr": "Bahraini",
        "bh": "Bahraini",
        "bhrn": "Bahraini",
        "bangladesh": "Bangladeshi",
        "bangladeshi": "Bangladeshi",
        "bgd": "Bangladeshi",
        "bd": "Bangladeshi",
        "bang": "Bangladeshi",
        "barbados": "Barbadian",
        "barbadian": "Barbadian",
        "brb": "Barbadian",
        "bb": "Barbadian",
        "brba": "Barbadian",
        "belarus": "Belarusian",
        "belarusian": "Belarusian",
        "blr": "Belarusian",
        "by": "Belarusian",
        "bela": "Belarusian",
        "belgium": "Belgian",
        "belgian": "Belgian",
        "bel": "Belgian",
        "be": "Belgian",
        "belg": "Belgian",
        "belize": "Belizean",
        "belizean": "Belizean",
        "blz": "Belizean",
        "bz": "Belizean",
        "beli": "Belizean",
        "benin": "Beninese",
        "beninese": "Beninese",
        "ben": "Beninese",
        "bj": "Beninese",
        "beni": "Beninese",
        "bermuda": "Bermudian",
        "bermudian": "Bermudian",
        "bmu": "Bermudian",
        "bm": "Bermudian",
        "berm": "Bermudian",
        "bhutan": "Bhutanese",
        "bhutanese": "Bhutanese",
        "btn": "Bhutanese",
        "bt": "Bhutanese",
        "bhut": "Bhutanese",
        "bolivia": "Bolivian",
        "bolivian": "Bolivian",
        "bol": "Bolivian",
        "bo": "Bolivian",
        "boli": "Bolivian",
        "bosniaandherzegovina": "Bosnian, Herzegovinian",
        "bosnianherzegovinian": "Bosnian, Herzegovinian",
        "bih": "Bosnian, Herzegovinian",
        "ba": "Bosnian, Herzegovinian",
        "bosn": "Bosnian, Herzegovinian",
        "botswana": "Motswana",
        "motswana": "Motswana",
        "bwa": "Motswana",
        "bw": "Motswana",
        "bots": "Motswana",
        "brazil": "Brazilian",
        "brazilian": "Brazilian",
        "bra": "Brazilian",
        "br": "Brazilian",
        "braz": "Brazilian",
        "britishvirginislands": "British Virgin Islander",
        "britishvirginislander": "British Virgin Islander",
        "vgb": "British Virgin Islander",
        "vg": "British Virgin Islander",
        "bvi": "British Virgin Islander",
        "brunei": "Bruneian",
        "bruneian": "Bruneian",
        "brn": "Bruneian",
        "bn": "Bruneian",
        "brun": "Bruneian",
        "bulgaria": "Bulgarian",
        "bulgarian": "Bulgarian",
        "bgr": "Bulgarian",
        "bg": "Bulgarian",
        "bulg": "Bulgarian",
        "burkinafaso": "Burkinabe",
        "burkinabe": "Burkinabe",
        "bfa": "Burkinabe",
        "bf": "Burkinabe",
        "burk": "Burkinabe",
        "burma": "Burmese",
        "burmese": "Burmese",
        "mmr": "Burmese",
        "mm": "Burmese",
        "myan": "Burmese",
        "burundi": "Burundian",
        "burundian": "Burundian",
        "bdi": "Burundian",
        "bi": "Burundian",
        "buru": "Burundian",
        "cambodia": "Cambodian",
        "cambodian": "Cambodian",
        "khm": "Cambodian",
        "kh": "Cambodian",
        "camb": "Cambodian",
        "cameroon": "Cameroonian",
        "cameroonian": "Cameroonian",
        "cmr": "Cameroonian",
        "cm": "Cameroonian",
        "came": "Cameroonian",
        "canada": "Canadian",
        "canadian": "Canadian",
        "can": "Canadian",
        "ca": "Canadian",
        "cana": "Canadian",
        "capeverde": "Cape Verdean",
        "capeverdean": "Cape Verdean",
        "cpv": "Cape Verdean",
        "cv": "Cape Verdean",
        "cape": "Cape Verdean",
        "caymanislands": "Caymanian",
        "caymanian": "Caymanian",
        "cym": "Caymanian",
        "ky": "Caymanian",
        "caym": "Caymanian",
        "centralafricanrepublic": "Central African",
        "centralafrican": "Central African",
        "caf": "Central African",
        "cf": "Central African",
        "cent": "Central African",
        "chad": "Chadian",
        "chadian": "Chadian",
        "tcd": "Chadian",
        "td": "Chadian",
        "chile": "Chilean",
        "chilean": "Chilean",
        "chl": "Chilean",
        "cl": "Chilean",
        "chil": "Chilean",
        "china": "Chinese",
        "chinese": "Chinese",
        "chn": "Chinese",
        "cn": "Chinese",
        "chin": "Chinese",
        "colombia": "Colombian",
        "colombian": "Colombian",
        "col": "Colombian",
        "co": "Colombian",
        "colo": "Colombian",
        "comoros": "Comoran",
        "comoran": "Comoran",
        "com": "Comoran",
        "km": "Comoran",
        "como": "Comoran",
        "cookislands": "Cook Islander",
        "cookislander": "Cook Islander",
        "cok": "Cook Islander",
        "ck": "Cook Islander",
        "cook": "Cook Islander",
        "costarica": "Costa Rican",
        "costarican": "Costa Rican",
        "cri": "Costa Rican",
        "cr": "Costa Rican",
        "cost": "Costa Rican",
        "ctedivoire": "Ivorian",
        "ivorian": "Ivorian",
        "civ": "Ivorian",
        "ci": "Ivorian",
        "ivor": "Ivorian",
        "croatia": "Croatian",
        "croatian": "Croatian",
        "hrv": "Croatian",
        "hr": "Croatian",
        "croa": "Croatian",
        "cuba": "Cuban",
        "cuban": "Cuban",
        "cub": "Cuban",
        "cu": "Cuban",
        "cyprus": "Cypriot",
        "cypriot": "Cypriot",
        "cyp": "Cypriot",
        "cy": "Cypriot",
        "cypr": "Cypriot",
        "czechrepublic": "Czech",
        "czech": "Czech",
        "cze": "Czech",
        "cz": "Czech",
        "czeh": "Czech",
        "democraticrepublicofthecongo": "Congolese",
        "congolese": "Congolese",
        "cod": "Congolese",
        "cd": "Congolese",
        "cond": "Congolese",
        "denmark": "Danish",
        "danish": "Danish",
        "dnk": "Danish",
        "dk": "Danish",
        "denm": "Danish",
        "diegogarcia": "Diego Garcian",
        "diegogarcian": "Diego Garcian",
        "dga": "Diego Garcian",
        "dg": "Diego Garcian",
        "dieg": "Diego Garcian",
        "djibouti": "Djiboutian",
        "djiboutian": "Djiboutian",
        "dji": "Djiboutian",
        "dj": "Djiboutian",
        "djib": "Djiboutian",
        "dominica": "Dominican",
        "dominican": "Dominican",
        "dma": "Dominican",
        "dm": "Dominican",
        "domi": "Dominican",
        "dominicanrepublic": "Dominican",
        "dom": "Dominican",
        "do": "Dominican",
        "ecuador": "Ecuadorian",
        "ecuadorian": "Ecuadorian",
        "ecu": "Ecuadorian",
        "ec": "Ecuadorian",
        "ecua": "Ecuadorian",
        "egypt": "Egyptian",
        "egyptian": "Egyptian",
        "egy": "Egyptian",
        "eg": "Egyptian",
        "egyp": "Egyptian",
        "elsalvador": "Salvadoran",
        "salvadoran": "Salvadoran",
        "slv": "Salvadoran",
        "sv": "Salvadoran",
        "salv": "Salvadoran",
        "equatorialguinea": "Equatorial Guinean",
        "equatorialguinean": "Equatorial Guinean",
        "gnq": "Equatorial Guinean",
        "gq": "Equatorial Guinean",
        "eqgu": "Equatorial Guinean",
        "eritrea": "Eritrean",
        "eritrean": "Eritrean",
        "eri": "Eritrean",
        "er": "Eritrean",
        "erit": "Eritrean",
        "estonia": "Estonian",
        "estonian": "Estonian",
        "est": "Estonian",
        "ee": "Estonian",
        "esto": "Estonian",
        "ethiopia": "Ethiopian",
        "ethiopian": "Ethiopian",
        "eth": "Ethiopian",
        "et": "Ethiopian",
        "ethi": "Ethiopian",
        "falklandislandsislasmalvinas": "Falkland Islander",
        "falklandislander": "Falkland Islander",
        "flk": "Falkland Islander",
        "fk": "Falkland Islander",
        "falk": "Falkland Islander",
        "faroeislands": "Faroese",
        "faroese": "Faroese",
        "fro": "Faroese",
        "fo": "Faroese",
        "faro": "Faroese",
        "federatedstatesofmicronesia": "Micronesian",
        "micronesian": "Micronesian",
        "fsm": "Micronesian",
        "fm": "Micronesian",
        "micr": "Micronesian",
        "fiji": "Fijian",
        "fijian": "Fijian",
        "fji": "Fijian",
        "fj": "Fijian",
        "finland": "Finnish",
        "finnish": "Finnish",
        "fin": "Finnish",
        "fi": "Finnish",
        "finl": "Finnish",
        "france": "French",
        "french": "French",
        "fra": "French",
        "fr": "French",
        "fran": "French",
        "frenchdepartmentsandterritoriesintheindianocean": "French",
        "fxx": "French",
        "fx": "French",
        "fren": "French",
        "frenchguiana": "French Guianese",
        "frenchguianese": "French Guianese",
        "guf": "French Guianese",
        "gf": "French Guianese",
        "guin": "French Guianese",
        "frenchpolynesia": "French Polynesian",
        "frenchpolynesian": "French Polynesian",
        "pyf": "French Polynesian",
        "pf": "French Polynesian",
        "fpol": "French Polynesian",
        "gabon": "Gabonese",
        "gabonese": "Gabonese",
        "gab": "Gabonese",
        "ga": "Gabonese",
        "gabo": "Gabonese",
        "gambia": "Gambian",
        "gambian": "Gambian",
        "gmb": "Gambian",
        "gm": "Gambian",
        "gam": "Gambian",
        "georgia": "Georgian",
        "georgian": "Georgian",
        "geo": "Georgian",
        "ge": "Georgian",
        "geor": "Georgian",
        "germany": "German",
        "german": "German",
        "deu": "German",
        "de": "German",
        "germ": "German",
        "ghana": "Ghanaian",
        "ghanaian": "Ghanaian",
        "gha": "Ghanaian",
        "gh": "Ghanaian",
        "ghan": "Ghanaian",
        "gibraltar": "Gibraltarian",
        "gibraltarian": "Gibraltarian",
        "gib": "Gibraltarian",
        "gi": "Gibraltarian",
        "gibr": "Gibraltarian",
        "greece": "Greek",
        "greek": "Greek",
        "grc": "Greek",
        "gr": "Greek",
        "greenland": "Greenlander",
        "greenlander": "Greenlander",
        "grl": "Greenlander",
        "gl": "Greenlander",
        "grenada": "Grenadian",
        "grenadian": "Grenadian",
        "grd": "Grenadian",
        "gd": "Grenadian",
        "gren": "Grenadian",
        "guadeloupe": "Guadeloupean",
        "guadeloupean": "Guadeloupean",
        "glp": "Guadeloupean",
        "gp": "Guadeloupean",
        "guad": "Guadeloupean",
        "guam": "Guamanian",
        "guamanian": "Guamanian",
        "gum": "Guamanian",
        "gu": "Guamanian",
        "guatemala": "Guatemalan",
        "guatemalan": "Guatemalan",
        "gtm": "Guatemalan",
        "gt": "Guatemalan",
        "guat": "Guatemalan",
        "guinea": "Guinean",
        "guinean": "Guinean",
        "gin": "Guinean",
        "gn": "Guinean",
        "gui": "Guinean",
        "guineabissau": "Guinean",
        "gnb": "Guinean",
        "gw": "Guinean",
        "gbs": "Guinean",
        "guyana": "Guyanese",
        "guyanese": "Guyanese",
        "guy": "Guyanese",
        "gy": "Guyanese",
        "guya": "Guyanese",
        "haiti": "Haitian",
        "haitian": "Haitian",
        "hti": "Haitian",
        "ht": "Haitian",
        "hait": "Haitian",
        "holyseevaticancity": "Vatican Citizen",
        "vaticancitizen": "Vatican Citizen",
        "vat": "Vatican Citizen",
        "va": "Vatican Citizen",
        "vati": "Vatican Citizen",
        "honduras": "Honduran",
        "honduran": "Honduran",
        "hnd": "Honduran",
        "hn": "Honduran",
        "hond": "Honduran",
        "hongkong": "Hong Konger",
        "hongkonger": "Hong Konger",
        "hkg": "Hong Konger",
        "hk": "Hong Konger",
        "hong": "Hong Konger",
        "hungary": "Hungarian",
        "hungarian": "Hungarian",
        "hun": "Hungarian",
        "hu": "Hungarian",
        "hung": "Hungarian",
        "iceland": "Icelander",
        "icelander": "Icelander",
        "isl": "Icelander",
        "is": "Icelander",
        "ices": "Icelander",
        "india": "Indian",
        "indian": "Indian",
        "ind": "Indian",
        "in": "Indian",
        "indi": "Indian",
        "indonesia": "Indonesian",
        "indonesian": "Indonesian",
        "idn": "Indonesian",
        "id": "Indonesian",
        "indo": "Indonesian",
        "iran": "Iranian",
        "iranian": "Iranian",
        "irn": "Iranian",
        "ir": "Iranian",
        "iraq": "Iraqi",
        "iraqi": "Iraqi",
        "irq": "Iraqi",
        "iq": "Iraqi",
        "ireland": "Irish",
        "irish": "Irish",
        "irl": "Irish",
        "ie": "Irish",
        "irel": "Irish",
        "israel": "Israeli",
        "israeli": "Israeli",
        "isr": "Israeli",
        "il": "Israeli",
        "isra": "Israeli",
        "italy": "Italian",
        "italian": "Italian",
        "ita": "Italian",
        "it": "Italian",
        "ital": "Italian",
        "jamaica": "Jamaican",
        "jamaican": "Jamaican",
        "jam": "Jamaican",
        "jm": "Jamaican",
        "jama": "Jamaican",
        "japan": "Japanese",
        "japanese": "Japanese",
        "jpn": "Japanese",
        "jp": "Japanese",
        "japa": "Japanese",
        "jordan": "Jordanian",
        "jordanian": "Jordanian",
        "jor": "Jordanian",
        "jo": "Jordanian",
        "jord": "Jordanian",
        "kazakhstan": "Kazakhstani",
        "kazakhstani": "Kazakhstani",
        "kaz": "Kazakhstani",
        "kz": "Kazakhstani",
        "kaza": "Kazakhstani",
        "kenya": "Kenyan",
        "kenyan": "Kenyan",
        "ken": "Kenyan",
        "ke": "Kenyan",
        "keny": "Kenyan",
        "kiribati": "I-Kiribati",
        "ikiribati": "I-Kiribati",
        "kir": "I-Kiribati",
        "ki": "I-Kiribati",
        "kiri": "I-Kiribati",
        "koreanorth": "North Korean",
        "northkorean": "North Korean",
        "prk": "North Korean",
        "kp": "North Korean",
        "koreasouth": "South Korean",
        "southkorean": "South Korean",
        "kor": "South Korean",
        "kr": "South Korean",
        "kuwait": "Kuwaiti",
        "kuwaiti": "Kuwaiti",
        "kwt": "Kuwaiti",
        "kw": "Kuwaiti",
        "kuwa": "Kuwaiti",
        "kyrgyzstan": "Kyrgyzstani",
        "kyrgyzstani": "Kyrgyzstani",
        "kgz": "Kyrgyzstani",
        "kg": "Kyrgyzstani",
        "kyrg": "Kyrgyzstani",
        "laos": "Laotian",
        "laotian": "Laotian",
        "lao": "Laotian",
        "la": "Laotian",
        "latvia": "Latvian",
        "latvian": "Latvian",
        "lva": "Latvian",
        "lv": "Latvian",
        "latv": "Latvian",
        "lebanon": "Lebanese",
        "lebanese": "Lebanese",
        "lbn": "Lebanese",
        "lb": "Lebanese",
        "leb": "Lebanese",
        "lesotho": "Mosotho",
        "mosotho": "Mosotho",
        "lso": "Mosotho",
        "ls": "Mosotho",
        "leso": "Mosotho",
        "liberia": "Liberian",
        "liberian": "Liberian",
        "lbr": "Liberian",
        "lr": "Liberian",
        "libe": "Liberian",
        "libya": "Libyan",
        "libyan": "Libyan",
        "lby": "Libyan",
        "ly": "Libyan",
        "liby": "Libyan",
        "liechtenstein": "Liechtensteiner",
        "liechtensteiner": "Liechtensteiner",
        "lie": "Liechtensteiner",
        "li": "Liechtensteiner",
        "liec": "Liechtensteiner",
        "lithuania": "Lithuanian",
        "lithuanian": "Lithuanian",
        "ltu": "Lithuanian",
        "lt": "Lithuanian",
        "lith": "Lithuanian",
        "luxembourg": "Luxembourgish",
        "luxembourgish": "Luxembourgish",
        "lux": "Luxembourgish",
        "lu": "Luxembourgish",
        "luxe": "Luxembourgish",
        "macau": "Macanese",
        "macanese": "Macanese",
        "mac": "Macanese",
        "mo": "Macanese",
        "maca": "Macanese",
        "macedoniafyrom": "Macedonian",
        "macedonian": "Macedonian",
        "mkd": "Macedonian",
        "mk": "Macedonian",
        "macd": "Macedonian",
        "madagascar": "Malagasy",
        "malagasy": "Malagasy",
        "mdg": "Malagasy",
        "mg": "Malagasy",
        "mada": "Malagasy",
        "malawi": "Malawian",
        "malawian": "Malawian",
        "mw": "Malawian",
        "malaysia": "Malaysian",
        "malaysian": "Malaysian",
        "mys": "Malaysian",
        "my": "Malaysian",
        "maldives": "Maldivian",
        "maldivian": "Maldivian",
        "mdv": "Maldivian",
        "mv": "Maldivian",
        "mald": "Maldivian",
        "mali": "Malian",
        "malian": "Malian",
        "mli": "Malian",
        "ml": "Malian",
        "malta": "Maltese",
        "maltese": "Maltese",
        "mlt": "Maltese",
        "mt": "Maltese",
        "malt": "Maltese",
        "marshallislands": "Marshallese",
        "marshallese": "Marshallese",
        "mhl": "Marshallese",
        "mh": "Marshallese",
        "mhls": "Marshallese",
        "martinique": "Martinican",
        "martinican": "Martinican",
        "mtq": "Martinican",
        "mq": "Martinican",
        "mart": "Martinican",
        "mauritania": "Mauritanian",
        "mauritanian": "Mauritanian",
        "mrt": "Mauritanian",
        "mr": "Mauritanian",
        "mauritius": "Mauritian",
        "mauritian": "Mauritian",
        "mus": "Mauritian",
        "mu": "Mauritian",
        "mayotte": "Mahoran",
        "mahoran": "Mahoran",
        "myt": "Mahoran",
        "yt": "Mahoran",
        "mayo": "Mahoran",
        "mexico": "Mexican",
        "mexican": "Mexican",
        "mex": "Mexican",
        "mx": "Mexican",
        "mexi": "Mexican",
        "moldova": "Moldovan",
        "moldovan": "Moldovan",
        "mda": "Moldovan",
        "md": "Moldovan",
        "mold": "Moldovan",
        "monaco": "Monegasque",
        "monegasque": "Monegasque",
        "mco": "Monegasque",
        "mc": "Monegasque",
        "mona": "Monegasque",
        "mongolia": "Mongolian",
        "mongolian": "Mongolian",
        "mng": "Mongolian",
        "mn": "Mongolian",
        "mong": "Mongolian",
        "montenegro": "Montenegrin",
        "montenegrin": "Montenegrin",
        "mne": "Montenegrin",
        "me": "Montenegrin",
        "mont": "Montenegrin",
        "montserrat": "Montserratian",
        "montserratian": "Montserratian",
        "msr": "Montserratian",
        "ms": "Montserratian",
        "mons": "Montserratian",
        "morocco": "Moroccan",
        "moroccan": "Moroccan",
        "mar": "Moroccan",
        "ma": "Moroccan",
        "moro": "Moroccan",
        "mozambique": "Mozambican",
        "mozambican": "Mozambican",
        "moz": "Mozambican",
        "mz": "Mozambican",
        "moza": "Mozambican",
        "namibia": "Namibian",
        "namibian": "Namibian",
        "nam": "Namibian",
        "nami": "Namibian",
        "na": "Namibian",
        "nauru": "Nauruan",
        "nauruan": "Nauruan",
        "nru": "Nauruan",
        "nr": "Nauruan",
        "naur": "Nauruan",
        "nepal": "Nepali",
        "nepali": "Nepali",
        "npl": "Nepali",
        "np": "Nepali",
        "nepa": "Nepali",
        "netherlands": "Dutch",
        "dutch": "Dutch",
        "nld": "Dutch",
        "nl": "Dutch",
        "neth": "Dutch",
        "netherlandsantilles": "Dutch",
        "ant": "Dutch",
        "an": "Dutch",
        "neta": "Dutch",
        "newcaledonia": "New Caledonian",
        "newcaledonian": "New Caledonian",
        "ncl": "New Caledonian",
        "nc": "New Caledonian",
        "ncld": "New Caledonian",
        "newzealand": "New Zealander",
        "newzealander": "New Zealander",
        "nzl": "New Zealander",
        "nz": "New Zealander",
        "newz": "New Zealander",
        "nicaragua": "Nicaraguan",
        "nicaraguan": "Nicaraguan",
        "nic": "Nicaraguan",
        "ni": "Nicaraguan",
        "nica": "Nicaraguan",
        "niger": "Nigerien",
        "nigerien": "Nigerien",
        "ner": "Nigerien",
        "ne": "Nigerien",
        "nigeria": "Nigerian",
        "nigerian": "Nigerian",
        "nga": "Nigerian",
        "ng": "Nigerian",
        "niue": "Niuean",
        "niuean": "Niuean",
        "niu": "Niuean",
        "nu": "Niuean",
        "northernterritory": "Australian",
        "norway": "Norwegian",
        "norwegian": "Norwegian",
        "nor": "Norwegian",
        "no": "Norwegian",
        "norw": "Norwegian",
        "oman": "Omani",
        "omani": "Omani",
        "omn": "Omani",
        "om": "Omani",
        "pakistan": "Pakistani",
        "pakistani": "Pakistani",
        "pak": "Pakistani",
        "pk": "Pakistani",
        "paki": "Pakistani",
        "palau": "Palauan",
        "palauan": "Palauan",
        "plw": "Palauan",
        "pw": "Palauan",
        "pala": "Palauan",
        "panama": "Panamanian",
        "panamanian": "Panamanian",
        "pan": "Panamanian",
        "pa": "Panamanian",
        "pana": "Panamanian",
        "papuanewguinea": "Papua New Guinean",
        "papuanewguinean": "Papua New Guinean",
        "png": "Papua New Guinean",
        "pg": "Papua New Guinean",
        "papn": "Papua New Guinean",
        "paraguay": "Paraguayan",
        "paraguayan": "Paraguayan",
        "pry": "Paraguayan",
        "py": "Paraguayan",
        "para": "Paraguayan",
        "peru": "Peruvian",
        "peruvian": "Peruvian",
        "per": "Peruvian",
        "pe": "Peruvian",
        "philippines": "Filipino",
        "filipino": "Filipino",
        "phl": "Filipino",
        "ph": "Filipino",
        "phil": "Filipino",
        "poland": "Polish",
        "polish": "Polish",
        "pol": "Polish",
        "pl": "Polish",
        "pola": "Polish",
        "portugal": "Portuguese",
        "portuguese": "Portuguese",
        "prt": "Portuguese",
        "pt": "Portuguese",
        "port": "Portuguese",
        "puertorico": "Puerto Rican",
        "puertorican": "Puerto Rican",
        "pri": "Puerto Rican",
        "pr": "Puerto Rican",
        "puer": "Puerto Rican",
        "qatar": "Qatari",
        "qatari": "Qatari",
        "qat": "Qatari",
        "qa": "Qatari",
        "qata": "Qatari",
        "republicofthecongo": "Congolese",
        "cog": "Congolese",
        "cg": "Congolese",
        "congo": "Congolese",
        "reserved": "N/A",
        "res": "N/A",
        "romania": "Romanian",
        "romanian": "Romanian",
        "rou": "Romanian",
        "ro": "Romanian",
        "roma": "Romanian",
        "russia": "Russian",
        "russian": "Russian",
        "rus": "Russian",
        "ru": "Russian",
        "russ": "Russian",
        "rwanda": "Rwandan",
        "rwandan": "Rwandan",
        "rwa": "Rwandan",
        "rw": "Rwandan",
        "rwan": "Rwandan",
        "sainthelena": "Saint Helenian",
        "sainthelenian": "Saint Helenian",
        "sh": "Saint Helenian",
        "shel": "Saint Helenian",
        "saintkittsandnevis": "Kittitian, Nevisian",
        "kittitiannevisian": "Kittitian, Nevisian",
        "kna": "Kittitian, Nevisian",
        "kn": "Kittitian, Nevisian",
        "kitt": "Kittitian, Nevisian",
        "saintlucia": "Saint Lucian",
        "saintlucian": "Saint Lucian",
        "lca": "Saint Lucian",
        "lc": "Saint Lucian",
        "luci": "Saint Lucian",
        "saintpierreandmiquelon": "Saint-Pierrais, Miquelonnais",
        "saintpierraismiquelonnais": "Saint-Pierrais, Miquelonnais",
        "spm": "Saint-Pierrais, Miquelonnais",
        "pm": "Saint-Pierrais, Miquelonnais",
        "spmi": "Saint-Pierrais, Miquelonnais",
        "saintvincentandthegrenadines": "Vincentian",
        "vincentian": "Vincentian",
        "vct": "Vincentian",
        "vc": "Vincentian",
        "vin": "Vincentian",
        "samoa": "Samoan",
        "samoan": "Samoan",
        "wsm": "Samoan",
        "ws": "Samoan",
        "samo": "Samoan",
        "sanmarino": "Sammarinese",
        "sammarinese": "Sammarinese",
        "smr": "Sammarinese",
        "sm": "Sammarinese",
        "sanm": "Sammarinese",
        "saotomeandprincipe": "Santomean",
        "santomean": "Santomean",
        "stp": "Santomean",
        "st": "Santomean",
        "saot": "Santomean",
        "saudiarabia": "Saudi Arabian",
        "saudiarabian": "Saudi Arabian",
        "sau": "Saudi Arabian",
        "sa": "Saudi Arabian",
        "saud": "Saudi Arabian",
        "senegal": "Senegalese",
        "senegalese": "Senegalese",
        "sen": "Senegalese",
        "sn": "Senegalese",
        "sene": "Senegalese",
        "serbia": "Serbian",
        "serbian": "Serbian",
        "srb": "Serbian",
        "rs": "Serbian",
        "serb": "Serbian",
        "seychelles": "Seychellois",
        "seychellois": "Seychellois",
        "syc": "Seychellois",
        "sc": "Seychellois",
        "seyc": "Seychellois",
        "sierraleone": "Sierra Leonean",
        "sierraleonean": "Sierra Leonean",
        "sle": "Sierra Leonean",
        "sl": "Sierra Leonean",
        "sier": "Sierra Leonean",
        "singapore": "Singaporean",
        "singaporean": "Singaporean",
        "sgp": "Singaporean",
        "sg": "Singaporean",
        "sing": "Singaporean",
        "slovakia": "Slovakian",
        "slovakian": "Slovakian",
        "svk": "Slovakian",
        "sk": "Slovakian",
        "slovenia": "Slovenian",
        "slovenian": "Slovenian",
        "svn": "Slovenian",
        "si": "Slovenian",
        "solomonislands": "Solomon Islander",
        "solomonislander": "Solomon Islander",
        "slb": "Solomon Islander",
        "sb": "Solomon Islander",
        "solo": "Solomon Islander",
        "somalia": "Somali",
        "somali": "Somali",
        "som": "Somali",
        "so": "Somali",
        "soma": "Somali",
        "southafrica": "South African",
        "southafrican": "South African",
        "zaf": "South African",
        "za": "South African",
        "soaf": "South African",
        "spain": "Spanish",
        "spanish": "Spanish",
        "esp": "Spanish",
        "es": "Spanish",
        "span": "Spanish",
        "srilanka": "Sri Lankan",
        "srilankan": "Sri Lankan",
        "lka": "Sri Lankan",
        "lk": "Sri Lankan",
        "srla": "Sri Lankan",
        "sudan": "Sudanese",
        "sudanese": "Sudanese",
        "sdn": "Sudanese",
        "sd": "Sudanese",
        "suda": "Sudanese",
        "suriname": "Surinamese",
        "surinamese": "Surinamese",
        "sur": "Surinamese",
        "sr": "Surinamese",
        "surm": "Surinamese",
        "swaziland": "Swazi",
        "swazi": "Swazi",
        "swz": "Swazi",
        "sz": "Swazi",
        "swaz": "Swazi",
        "sweden": "Swedish",
        "swedish": "Swedish",
        "swe": "Swedish",
        "se": "Swedish",
        "swed": "Swedish",
        "switzerland": "Swiss",
        "swiss": "Swiss",
        "che": "Swiss",
        "ch": "Swiss",
        "swit": "Swiss",
        "syria": "Syrian",
        "syrian": "Syrian",
        "syr": "Syrian",
        "sy": "Syrian",
        "syri": "Syrian",
        "taiwan": "Taiwanese",
        "taiwanese": "Taiwanese",
        "twn": "Taiwanese",
        "tw": "Taiwanese",
        "taiw": "Taiwanese",
        "tajikistan": "Tajik",
        "tajik": "Tajik",
        "tjk": "Tajik",
        "tj": "Tajik",
        "taji": "Tajik",
        "tanzania": "Tanzanian",
        "tanzanian": "Tanzanian",
        "tza": "Tanzanian",
        "tz": "Tanzanian",
        "tanz": "Tanzanian",
        "thailand": "Thai",
        "thai": "Thai",
        "tha": "Thai",
        "th": "Thai",
        "timorleste": "Timorese",
        "timorese": "Timorese",
        "tls": "Timorese",
        "tl": "Timorese",
        "tmor": "Timorese",
        "togo": "Togolese",
        "togolese": "Togolese",
        "tgo": "Togolese",
        "tg": "Togolese",
        "tokelau": "Tokelauan",
        "tokelauan": "Tokelauan",
        "tkl": "Tokelauan",
        "tk": "Tokelauan",
        "toke": "Tokelauan",
        "tonga": "Tongan",
        "tongan": "Tongan",
        "ton": "Tongan",
        "to": "Tongan",
        "tong": "Tongan",
        "trinidadandtobago": "Trinidadian, Tobagonian",
        "trinidadiantobagonian": "Trinidadian, Tobagonian",
        "tto": "Trinidadian, Tobagonian",
        "tt": "Trinidadian, Tobagonian",
        "trin": "Trinidadian, Tobagonian",
        "tristandacunha": "Tristanian",
        "tristanian": "Tristanian",
        "ta": "Tristanian",
        "tris": "Tristanian",
        "tunisia": "Tunisian",
        "tunisian": "Tunisian",
        "tun": "Tunisian",
        "tn": "Tunisian",
        "tuni": "Tunisian",
        "turkey": "Turkish",
        "turkish": "Turkish",
        "tur": "Turkish",
        "tr": "Turkish",
        "turk": "Turkish",
        "turkmenistan": "Turkmen",
        "turkmen": "Turkmen",
        "tkm": "Turkmen",
        "tm": "Turkmen",
        "tmen": "Turkmen",
        "turksandcaicosislands": "Turks and Caicos Islander",
        "turksandcaicosislander": "Turks and Caicos Islander",
        "tca": "Turks and Caicos Islander",
        "tc": "Turks and Caicos Islander",
        "turc": "Turks and Caicos Islander",
        "tuvalu": "Tuvaluan",
        "tuvaluan": "Tuvaluan",
        "tuv": "Tuvaluan",
        "tv": "Tuvaluan",
        "tuva": "Tuvaluan",
        "uganda": "Ugandan",
        "ugandan": "Ugandan",
        "uga": "Ugandan",
        "ug": "Ugandan",
        "ugan": "Ugandan",
        "ukraine": "Ukrainian",
        "ukrainian": "Ukrainian",
        "ukr": "Ukrainian",
        "ua": "Ukrainian",
        "ukra": "Ukrainian",
        "unitedarabemirates": "Emirati",
        "emirati": "Emirati",
        "are": "Emirati",
        "ae": "Emirati",
        "uare": "Emirati",
        "unitedkingdom": "British",
        "british": "British",
        "gbr": "British",
        "gb": "British",
        "unki": "British",
        "unitedstates": "American",
        "american": "American",
        "usa": "American",
        "us": "American",
        "amer": "American",
        "uruguay": "Uruguayan",
        "uruguayan": "Uruguayan",
        "ury": "Uruguayan",
        "uy": "Uruguayan",
        "urug": "Uruguayan",
        "uzbekistan": "Uzbek",
        "uzbek": "Uzbek",
        "uzb": "Uzbek",
        "uz": "Uzbek",
        "uzbe": "Uzbek",
        "vanuatu": "Ni-Vanuatu",
        "nivanuatu": "Ni-Vanuatu",
        "vut": "Ni-Vanuatu",
        "vu": "Ni-Vanuatu",
        "vanu": "Ni-Vanuatu",
        "venezuela": "Venezuelan",
        "venezuelan": "Venezuelan",
        "ven": "Venezuelan",
        "ve": "Venezuelan",
        "vene": "Venezuelan",
        "vietnam": "Vietnamese",
        "vietnamese": "Vietnamese",
        "vnm": "Vietnamese",
        "vn": "Vietnamese",
        "viet": "Vietnamese",
        "virginislands": "Virgin Islander",
        "virginislander": "Virgin Islander",
        "vir": "Virgin Islander",
        "vi": "Virgin Islander",
        "virg": "Virgin Islander",
        "wallisandfutuna": "Wallisian, Futunan",
        "wallisianfutunan": "Wallisian, Futunan",
        "wlf": "Wallisian, Futunan",
        "wf": "Wallisian, Futunan",
        "wall": "Wallisian, Futunan",
        "yemen": "Yemeni",
        "yemeni": "Yemeni",
        "yem": "Yemeni",
        "ye": "Yemeni",
        "yeme": "Yemeni",
        "zambia": "Zambian",
        "zambian": "Zambian",
        "zmb": "Zambian",
        "zm": "Zambian",
        "zamb": "Zambian",
        "zimbabwe": "Zimbabwean",
        "zimbabwean": "Zimbabwean",
        "zwe": "Zimbabwean",
        "zw": "Zimbabwean",
        "zimb": "Zimbabwean"
    }
    unmatteched = []
    matteched = []
    # Connect to the database
    connection = get_connection()
    try:
        cursor = connection.cursor()

        # Fetch distinct nationalities from the Workers table
        cursor.execute("SELECT DISTINCT nationality FROM workers")
        distinct_nationalities = cursor.fetchall()

        # Update the nationality for each row in the Workers table
        for nationality in distinct_nationalities:
            if nationality:
                old_process_nationality = process_value(
                    nationality[0])  # Convert to lowercase to match the keys in nationalitiesPossibleValues
                if old_process_nationality:
                    new_nationality = nationalitiesPossibleValues.get(old_process_nationality)
                    if new_nationality:
                        matteched.append({nationality[0]: new_nationality})
                        cursor.execute("UPDATE workers SET nationality = %s WHERE nationality = %s",
                                       (new_nationality, nationality[0]))
                    else:
                        unmatteched.append(nationality[0])
                else:
                    unmatteched.append(nationality[0])

        # Commit the changes
        connection.commit()

        print("Nationality update successful!: ", matteched)
        print("un: ", unmatteched)

        # TODO: update list based on client comment response(Duplicate values), try first on dump database
        #     : For Update "DAN", "BLG", "BNG"
        # patches = {"UK": "British", "ROM": "Romanian", "LIT": "Lithuanian", "Warehouse Operative": "", "unknown": "",
        #            "nationality": "", "BUL": "Bulgarian", "BRIT": "British", "SLO": "Slovakian", "NIG": "Nigerian",
        #            "LAT": "Latvian", "GHN": "", "CRO": "Croatian", "CHINES": "Chinese", "MOL": "Moldovan", "RWN": "",
        #            "FIJ": "Fijian", "GRE": "Greek", "FIL": "Filipino", "SUD": "Sudanese", "NED": "Dutch",
        #            "SRL": "Sri Lankan", "CON": "", "IVO": "Ivorian", "MAL": "", "MYM": "", "EYG": "Eygptian",
        #            "Kosovo": "", "Umunyarwanda": "", "Somalian": "Somali", "Cyprian": "", "evcatogoineana": "",
        #            "Maldova": "", "SLN": "", "KOSO": "", "COLUMBIAN": "Colombian", "MAROCCO": "", "Slovak": "Slovakian",
        #            "Philippine": "Filipino", "British National (Overseas)  Hong Kong": "", "Zarean": "",
        #            "GER": "German", "ZAM": "Zambian", "SWS": "", "IRI": "Irish", "POR": "Portuguese", "STATELESS": "",
        #            "MORROCO": "", "GHANIAN": "", "MLW": "", "NONA": "", "Palestinian": "", "Sao Toume": "Santomean",
        #            "Angolian": "Angolan", "Tailand": "Thai", "Zimbambwean": "Zimbabwean", "SCOTTISH": "British",
        #            "Nepalese": "Nepali", "ROMANINAN": "Romanian", "Afganistan": "Afghan", "DAN": "", "BLG": "", "BNG": "",
        #            "Portugese": "Portuguese", "Cameron": "Cameroonian", "Zimbwawe": "Zimbabwean", "Dominicana": "Dominican",
        #            "Honk Kong": "Hong Konger", "DAN": "Danish", "BLG": "Bulgarian", "BNG": ""}
        #
        # stillNotMatch = []
        # for unm in unmatteched:
        #     nationality = patches.get(unm)
        #     if nationality:
        #         cursor.execute("UPDATE workers SET nationality = %s WHERE nationality = %s",
        #                        (nationality, unm))
        #     elif unm in patches:  # if key exists with '' none or empty value
        #         cursor.execute("UPDATE workers SET nationality = %s WHERE nationality = %s",
        #                        (None, unm))
        #     else:  # If key not in patches
        #         stillNotMatch.append(unm)
        # connection.commit()
        # print("stillNotMatch: (Updated with NULL value)", stillNotMatch)

    except Exception as error:
        print("Error while connecting to the database:", error)

    finally:
        # Close the database connection
        if connection.is_connected():
            cursor.close()
            connection.close()
