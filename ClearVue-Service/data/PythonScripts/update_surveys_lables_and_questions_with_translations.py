
# TODO: Run below query first

# ALTER TABLE survey_questions
# ADD COLUMN new_question_json json DEFAULT NULL AFTER question_json,
# ADD COLUMN new_label varchar(250) DEFAULT NULL AFTER label;

import json
from deep_translator import GoogleTranslator
from translation_script_python.creds import get_connection
import time

# Define the array of objects
data = [
    {
        "Current": "Identification",
        "New": "Health & Safety",
        "Text": "My working conditions are safe",
        "Association": "SITE"
    },
    {
        "Current": "Training",
        "New": "Training",
        "Text": "I receive sufficient training to do my work well",
        "Association": "SITE"
    },
    {
        "Current": "Leadership",
        "New": "Leadership",
        "Text": "Supervisors at work treat me fairly",
        "Association": "SITE"
    },
    {
        "Current": "Engagement",
        "New": "Engagement",
        "Text": "I feel included at work",
        "Association": "SITE"
    },
    {
        "Current": "Recognition",
        "New": "Recognition",
        "Text": "I am recognised by my line manager when I perform well in my duties",
        "Association": "SITE"
    },
    {
        "Current": "Identification",
        "New": "Recommendation",
        "Text": "I would recommend my agency to family members and friends",
        "Association": "AGENCY"
    },
    {
        "Current": "Payroll",
        "New": "Payroll",
        "Text": "My wages are correct and paid on time",
        "Association": "AGENCY"
    },
    {
        "Current": "Leadership",
        "New": "Leadership",
        "Text": "My agency supervisors treat me with respect and we are all treated equally ",
        "Association": "AGENCY"
    },
    {
        "Current": "Engagement",
        "New": "Engagement",
        "Text": "I feel included by my agency",
        "Association": "AGENCY"
    },
    {
        "Current": "Role Expectations",
        "New": "Recognition",
        "Text": "I am recognised by my agency when I perform well in my duties",
        "Association": "AGENCY"
    }
]
# Define the target languages
languages = ['sq', 'bg', 'cs', 'en', 'et', 'hi', 'hu', 'it', 'lv', 'ne', 'pl', 'pt', 'ro', 'es', 'uk', 'fr', 'lt', 'sk',
             'sl', 'ur']


def custom_retry(func, arg, max_retries=5, delay_seconds=5, backoff_factor=2):
    retries = 0
    while retries < max_retries:
        try:
            result = func(arg)
            return result  # If the function succeeds, return the result
        except ConnectionResetError as e:
            print(f"Retry {retries + 1}/{max_retries} after {delay_seconds} seconds")
            time.sleep(delay_seconds)
            delay_seconds *= backoff_factor  # Increase delay with backoff
            retries += 1
    raise Exception("Max retries exceeded, operation failed")


# Function to translate
# text into multiple languages and return as JSON
def translate_text(text):
    translations = {}
    for lang in languages:
        translation = GoogleTranslator(source='auto', target=lang).translate(text)
        translations[lang] = translation
    return json.dumps(translations)


# Function to update the survey_questions table
def update_survey_questions(connection, label, new_label, new_question_json, association):
    try:
        cursor = connection.cursor()

        # Construct the SQL update query
        update_query = """
            UPDATE survey_questions
            SET
                new_question_json = %s,
                new_label = %s
            WHERE
                label = %s
                AND belongs_to = %s
                AND survey_id IN (1, 2, 3, 4, 5, 7, 9)
                AND created_by = 1
        """

        # Define the parameters for the query
        update_values = (new_question_json, new_label, label, association)

        # Execute the SQL query
        cursor.execute(update_query, update_values)

        # Commit the changes to the database
        connection.commit()

    except Exception as e:
        connection.rollback()


if __name__ == '__main__':
    i = 0
    data_to_iterate = data  # For Staging

    # Iterate through the data and translate the "Text" field
    for item in data_to_iterate:
        item["new_text_json"] = custom_retry(translate_text, item["Text"])
        print("count-> ", i)
        i += 1

    # Print or save the result JSON as needed
    print(data_to_iterate)

    # Define the connection details for your database
    db_connection = get_connection()  # Replace with your database connection function

    # Iterate through the data array and update the database
    for item in data_to_iterate:
        label = item["Current"]
        new_label = item["New"]
        new_question_json = item["new_text_json"]
        association = item["Association"]

        update_survey_questions(db_connection, label, new_label, new_question_json, association)

    # Close the database connection
    db_connection.close()


# TODO: Run below 2 queries, these are already mentioned in 1.5.2_changes.sql file, so don't execute it 2nd time.

# 1
# UPDATE survey_questions
# SET
#     question_json = COALESCE(new_question_json, question_json),
#     label = COALESCE(new_label, label)
# WHERE
#     new_question_json IS NOT NULL
#     AND new_label IS NOT NULL
#     AND created_by = 1
#     AND survey_id IN (1, 2, 3, 4, 5, 7, 9);

# 2
# ALTER TABLE survey_questions
# DROP COLUMN new_question_json,
# DROP COLUMN new_label;
