from translation_script_python.creds import get_connection
import csv

if __name__ == '__main__':

    # List of worker_ids for which you want to find the latest data
    worker_ids = [63882]  # Add more worker IDs if needed

    # Initialize the MySQL connection
    db_connection = get_connection()  #
    cursor = db_connection.cursor(dictionary=True)
    start_date = "2023-09-24"
    end_date = "2023-10-07"

    # SQL query to retrieve the latest time and attendance data for each worker
    sql_query = f"""
        SELECT 
        employee_id,
        workers.id as workers_id,
        workers.user_id,
        workers.first_name,
        workers.last_name,
        workers.start_date AS worker_start_date,
        workers.assignment_date AS worker_assignment_date,
        IF(workers.device_token IS NOT NULL, 'YES', 'NO') is_app_downloaded,
        user.email,
        type,
        workers.client_id,
        workers.agency_id,
        MAX(time_and_attendance_data.created_at) AS inactivated_at,
        MAX(time_and_attendance_data.start_date) AS tna_start_date,
        MAX(time_and_attendance_data.end_date) AS tna_end_date
    FROM
        workers
            LEFT JOIN
        user ON user.id = workers.user_id
            LEFT JOIN
        time_and_attendance_data ON time_and_attendance_data.worker_id = workers.id
    WHERE
        workers.assignment_date < '2023-10-08' AND
        workers.id IN (SELECT DISTINCT
                workers.id
            FROM
                workers
                    INNER JOIN
                user ON user.id = workers.user_id
            WHERE
                workers.id NOT IN (SELECT DISTINCT
                        (worker_id)
                    FROM
                        time_and_attendance_data
                    WHERE
                        total_charge > 0
                            AND start_date >= '2023-10-08'
                            AND end_date <= '2023-10-21')
                    AND workers.id IN (SELECT 
                        MAX(id)
                    FROM
                        workers
                    GROUP BY user_id)
                    AND workers.type = 'TEMPORARY'
                    AND workers.is_active = 1
                    AND workers.client_id = 40)
    GROUP BY workers.id
    Order By inactivated_at desc;
    """

    try:
        # Execute the SQL query
        cursor.execute(sql_query)

        # Fetch the results
        results = cursor.fetchall()

        # Define the path for the CSV file
        csv_file_path = '/tmp/inactivate_workers_8_21_Filter.csv'

        # Write the results to a CSV file
        with open(csv_file_path, 'w', newline='') as csv_file:
            fieldnames = results[0].keys()
            csv_writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
            csv_writer.writeheader()
            csv_writer.writerows(results)

        print(f"CSV file saved at: {csv_file_path}")

    except Exception as err:
        print(f"Error: {err}")

    finally:
        # Close the cursor and connection
        cursor.close()
        db_connection.close()
