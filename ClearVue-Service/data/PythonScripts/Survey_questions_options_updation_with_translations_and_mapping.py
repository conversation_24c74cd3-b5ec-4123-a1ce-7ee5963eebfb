
# CLR-1349 | Payquery survey update options and translations  (November 6th, 2023 7:57 PM) 

from deep_translator import GoogleTranslator
import logging

LOG = logging.getLogger(__file__)
logging.basicConfig(format='%(levelname)s %(message)s', level=logging.INFO)

lang = ['bg', 'cs', 'en', 'es', 'et', 'fr', 'hi', 'hu', 'it', 'lt', 'lv', 'ne', 'pl', 'pt', 'ro', 'sk', 'sl', 'sq',
        'uk', 'ur']

# lang = ['sq', 'bg', 'cs', 'en']


def translate_to_desired_language(text_to_translate, source_language):
    # lang = ['sq', 'bg', 'cs', 'en']
    result = {}

    for la in lang:
        result[la] = GoogleTranslator(source=source_language or 'auto', target=la).translate_batch(text_to_translate)
        print(result[la])

    return result


def get_updated_value(_option_ids, _translated_value):
    return {i: {_option_ids[value.index(j)]: j for j in value} for i, value in _translated_value.items()}


if __name__ == '__main__':
    input_data = {
        "6": "Missing hours pay",
        "8": "Incorrect pay rate",
        "9": "Missing holiday pay",
        "16": "Tax Issue",
        "17": "Pension Issue",
        "19": "Unauthorised wage deduction (Work Finders Fee)",
        "20": "Unauthorized wage deduction (Paid a deposit)",
        "21": "Unauthorized wage deduction (Other)",
        "18": "Other"
    }

    source_language = 'en'  # Change this to the source language if needed

    output = {}
    option_ids = [option for option in input_data]


    translated_texts = translate_to_desired_language(list(input_data.values()), source_language)

    lang_result = get_updated_value(option_ids, translated_texts)


    print(lang_result)
