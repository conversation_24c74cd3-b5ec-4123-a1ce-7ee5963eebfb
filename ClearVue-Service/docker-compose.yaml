version: "3.7"
services:
  # Run the node application
  clearvue-app:
    container_name: clearvue-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    build:
      context: ./
    networks:
      - app-network

  # Run the caddy server
  caddy:
    image: caddy/caddy:2.4.1-alpine
    container_name: caddy-service
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - $PWD/Caddyfile:/etc/caddy/Caddyfile
      - $PWD/site:/srv
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - app-network

networks:
  app-network:
    external: true

volumes:
  caddy_data:
  caddy_config:
