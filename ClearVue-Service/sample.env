# =============================================================================
# ClearVue Service Environment Configuration
# =============================================================================
# This is a sample environment file for the ClearVue backend service.
#
# SETUP INSTRUCTIONS:
# 1. Copy this file to .env: cp sample.env .env
# 2. Update all placeholder values with your actual configuration
# 3. Never commit the .env file to version control
# 4. Keep this sample.env file updated when adding new environment variables
#
# SECURITY WARNING:
# - Never commit actual credentials to version control!
# - All values in this file are placeholders - replace with real values
# - Use strong, unique passwords and API keys
# - Rotate credentials regularly
#
# PLACEHOLDER PATTERNS:
# - your-* : Replace with your actual values
# - *-here : Replace with actual API keys/tokens
# - xxxxxxxx : Replace with actual IDs/templates
# =============================================================================

# -----------------------------------------------------------------------------
# Application Configuration
# -----------------------------------------------------------------------------
PRODUCT_NAME=ClearVue
APP_NAME=ClearVue
SERVICE_PORT=5001
ERROR_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
API_HOSTS='*'
ENVIRONMENT=development
NODE_ENV=development
PORT=5001

# -----------------------------------------------------------------------------
# Authentication & Security
# -----------------------------------------------------------------------------
# Generate a strong random key for JWT signing
JWT_TOKEN_KEY=your-super-secret-jwt-key-here-change-this

# Session timeout configurations
USER_ACCESS_TOKEN_EXPIRE_TIME=3600s
USER_REFRESH_TOKEN_EXPIRE_TIME=28800s
USER_INACTIVITY_TIMEOUT_FIREBASE=300
USER_MAX_SESSION_DURATION_FIREBASE=28800
RESET_PASSWORD_LINK_EXPIRE_TIME=1d

# Login attempt protection
MAX_WRONG_ATTEMPTS=5
LAST_ATTEMPTS=5
BLOCKED_TIME=30

# -----------------------------------------------------------------------------
# Frontend Configuration
# -----------------------------------------------------------------------------
CUSTOMER_PORTAL_HOST_URL=http://localhost:3000

# -----------------------------------------------------------------------------
# External Services
# -----------------------------------------------------------------------------
# Flask/Python service URL
FLASK_SERVICE_URL=http://localhost:5008

# Firebase Configuration
FIREBASE_SERVER_KEY=your-firebase-server-key-here
FIREBASE_WEB_API_KEY=your-firebase-web-api-key-here
FIREBASE_AUTH_ENABLED=1
FIREBASE_TOTP_MFA_ENABLED=1

# SendGrid Email Service
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
FROM_EMAIL=*******

# Cron Job Security
CRONJOB_ACCESS_TOKEN=your-secure-cronjob-token-here
SEND_WORKERS_MOBILE_INACTIVITY_NOTIFICATION=0


# -----------------------------------------------------------------------------
# Email Configuration
# -----------------------------------------------------------------------------
FROM_EMAIL=*******
FORGOT_PASSWORD_EMAIL_TEMPLATE=d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
INVITE_USER_EMAIL_TEMPLATE=d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
BOOKING_NOTIFICATION_EMAIL_TEMPLATE=d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
WORKER_INVITE_EMAIL_TEMPLATE=d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
WORKER_INACTIVATION_EMAIL_TEMPLATE=d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx


# -----------------------------------------------------------------------------
# Logging & Monitoring
# -----------------------------------------------------------------------------
ENABLE_CONSOLE_LOGGER=1
ENABLE_SLACK_ERROR_NOTIFICATION=0
ENABLE_FILE_LOGGER=1

# GrayLog configurations
ENABLE_GRAYLOG=0
GRAYLOG_HOST=your-graylog-host
GRAYLOG_PORT=12201

# Bugsnag Error Tracking
BUGSNAG_API_KEY=your-bugsnag-api-key-here
ENABLE_BUGSNAG_ERROR_LOGGING=0

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_redis_password

# MySQL Database
MYSQL_DATABASE_HOST=localhost
MYSQL_DATABASE_USER=your_db_user
MYSQL_DATABASE_PASSWORD=your_secure_db_password
MYSQL_DATABASE_DB=clearvue_db

# -----------------------------------------------------------------------------
# API & Business Configuration
# -----------------------------------------------------------------------------
ENABLE_API_DOCS=1
MAX_REPEAT_BOOKING_ALLOWED=12

# Survey Configuration
EXIT_SURVEY_ID=9
EXIT_SURVEY_QUESTION_ID=77
GENERAL_SURVEY_ID=7

# -----------------------------------------------------------------------------
# AWS S3 Configuration
# -----------------------------------------------------------------------------
# AWS Credentials
ACCESS_KEY_ID=your-aws-access-key-id
SECRET_ACCESS_KEY=your-aws-secret-access-key
S3_REGION=eu-west-2


# S3 Bucket Configuration
BUCKET_URL=https://your-bucket-name.s3.your-region.amazonaws.com
BUCKET_NAME=your-s3-bucket-name
WORKER_BUCKET_FOLDER=workers-sample-csv
PROFILE_BUCKET_FOLDER=profile-images
TOTAL_AGENCY_PAY_FOLDER=total-agency-pay-sheets
TAP_SHEETS_BUCKET_FOLDER=total-agency-pay-sheets
TNA_SHEETS_BUCKET_FOLDER=time-and-attendance-sheets
TAP_SAMPLE_FILE_BUCKET_FOLDER=total-agency-pay-sample
TAP_UPLOAD_SAMPLE_FILE_BUCKET_KEY=sample_total_agency_pay_file.csv
WORKER_PERFORMANCE_FOLDER=workers-performance-sheets
WORKER_PERFORMANCE_SAMPLE_SHEET_FOLDER=workers-performance-example-sheets
WORKER_PERFORMANCE_CLIENT_SAMPLE_SHEET=demo_client_worker_performance_to_upload.csv
WORKER_PERFORMANCE_AGENCY_SAMPLE_SHEET=demo_agency_worker_performance_to_upload.csv

RATE_CARD_FOLDER=rate-card-sheets
RATE_CARD_SAMPLE_SHEET_FOLDER=rate-card-example-sheet
RATE_CARD_SAMPLE_SHEET_NAME=demo_rate_card_to_upload.csv

PERMANENT_WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY=demo_client_worker_data_to_upload.csv
WORKER_SAMPLE_DOWNLOAD_BUCKET_KEY=demo_agency_worker_data_to_upload.csv



WORKER_UPDATE_CLIENT_SAMPLE_FILE_NAME=demo_client_worker_data_to_update.csv
WORKER_UPDATE_AGENCY_SAMPLE_FILE_NAME=demo_agency_worker_data_to_update.csv
TIME_AND_ATTENDANCE_FOLDER=time-and-attendance-sheets
DEFAULT_IMAGE=default_user_profile.jpg
MAX_IMAGE_SIZE=5000000
DOCUMENTS_FOLDER=worker-documents


# -----------------------------------------------------------------------------
# Business Configuration
# -----------------------------------------------------------------------------
# National Insurance Configuration
NI_PERCENT=15
NI_THRESHOLD=96

# VAT Configuration
VAT_RATE=20
VAT_CODE=S

# File Storage Configuration
PAYROLL_REPORT_FOLDER=payroll-report

# Login Security Configuration
MAX_WRONG_ATTEMPTS=5
LAST_ATTEMPTS=5
BLOCKED_TIME=30

# -----------------------------------------------------------------------------
# Google Cloud Translation Service
# -----------------------------------------------------------------------------
TRANSLATE_PROJECT_ID=your-google-cloud-project-id
TRANSLATE_KEY_DATA={"type":"service_account","project_id":"your-project-id","private_key_id":"your-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT_HERE\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com","universe_domain":"googleapis.com"}

# -----------------------------------------------------------------------------
# Firebase Configuration
# -----------------------------------------------------------------------------
# Firebase Admin SDK Service Account Key (JSON format)
FIREBASE_AUTH_KEY_JSON={"type":"service_account","project_id":"your-firebase-project-id","private_key_id":"your-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_FIREBASE_PRIVATE_KEY_CONTENT_HERE\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com","universe_domain":"googleapis.com"}

# Firebase Web API Key
FIREBASE_WEB_API_KEY=your-firebase-web-api-key-here

# Firebase Feature Flags
FIREBASE_AUTH_ENABLED=1
FIREBASE_TOTP_MFA_ENABLED=1

# -----------------------------------------------------------------------------
# Application Environment
# -----------------------------------------------------------------------------
NODE_ENV=development
PORT=5001

# =============================================================================
# END OF CONFIGURATION
# =============================================================================