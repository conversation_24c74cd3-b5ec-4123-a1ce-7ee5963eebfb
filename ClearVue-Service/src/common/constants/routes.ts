/**
 * All the routes which are supported into the system. Separate that by version.
 */

export enum userAuthentication {
    RENEW_TOKEN = "/v1/token",
    LOG_IN = "/v1/login",
    FORGOT_PASSWORD = "/v1/forgot-password",
    RESET_PASSWORD = "/v1/reset-password",
}

export enum clientDetails {
    ADD_OR_GET_CLIENT = "/v1/client",
    GET_OR_UPDATE_CLIENT_DETAILS_BY_ID = "/v1/client/:clientId",
    ADD_CLIENT_USERS = "/v1/client/users",
    UPDATE_CLIENT_USERS = "/v1/client/users/:userId",
    CLIENT_USERS = "/v1/client-users",
    CLIENT_RATINGS = "/v1/client-ratings",
    TYPE_WISE_CLIENT_RATINGS = "/v1/type-wise-client-rating",
    BLOCK_WORKER_INVITE_EMAIL = "/v1/client/worker-invite-email/:clientId",
    WORKER_TRAINING_RULES = "/v1/client/:clientId/workers-training/rules",
    WORKER_TRAINING_RULE_BY_ID = "/v1/client/:clientId/workers-training/rules/:trainingRuleId",
    WORKER_START_DATE_YEARLY_RULES = "/v1/client/:clientId/start-date-yearly/rules",
    WORKER_START_DATE_YEARLY_RULE_BY_ID = "/v1/client/:clientId/start-date-yearly/rules/:startDateYearlyRuleId",
    WORKER_FINANCIAL_RULES = "/v1/client/:clientId/financial-rules",
    WORKER_FINANCIAL_RULE_BY_ID = "/v1/client/:clientId/financial-rules/:financialRuleId",
}

export enum agency {
    CREATE_AND_GET_LIST_OF_AGENCY = "/v1/agency",
    UPDATE_AND_GET_AGENCY = "/v1/agency/:agencyId",
    AGENCY_RATINGS = "/v1/agency-ratings",
    AGENCY_RATINGS_DETAILS = "/v1/agency-ratings/details",
    ADD_AGENCY_USERS = "/v1/agency/users",
    UPDATE_AGENCY_USERS = "/v1/agency/users/:userId",
    AGENCY_USERS = "/v1/agency-users"
}

export enum worker {
    CREATE_OR_GET_WORKERS = "/v1/workers",
    BULK_UPLOAD_OR_UPDATE_WORKERS = "/v1/workers/bulk",
    // BULK_UPDATE_WORKERS = "/v1/workers/bulk/updates",
    WORKER_SEARCH_PROFILE = "/v1/workers/search",
    GET_SAMPLE_SHEET = "/v1/get-sample-sheet",
    UPDATE_WORKER_SAMPLE_SHEET = "/v1/worker/get-sample-sheet",
    GET_AND_UPDATE_WORKER_DETAILS = "/v1/workers/:workerId",
    LOGIN = "/v1/worker/login",
    DOCUMENTS = "/v1/worker/documents",
    GET_WORKER_LISTING = "/v1/workers-listing",
    SIGN_UP = "/v1/worker/sign-up",
    SIGN_UP_V2 = "/v2/worker/sign-up",
    UPDATE_PROFILE_V2 = "/v2/worker",
    PROFILE = "/v1/worker/user/:userId",
    PROFILE_V2 = "/v2/worker/user/:userId",
    WORKER_PROFILE = "/v1/workers/users/:userId",
    GET_WORKER_GROUPS = "/v1/workers-groups",
    TRACK_TRAINING = "/v1/messages/:messageId/training-completed",
    WORKER_NATIONALITYT_LIST = "/v1/workers-nationality",
    SET_WORKER_LANGUAGE = "/v1/workers/language",
    WORKER_PERFORMANCE = "/v1/workers-performance",
    WORKER_PERFORMANCE_SAMPLE_SHEET = "/v1/workers-performance/get-sample-sheet",
    FTP_WORKERS_BULK_UPLOAD_MANUAL_TRIGGER = "/v1/ftp-workers-upload-lookup-trigger/:clientId",
}

export enum timeAndAttendance {
    UPLOAD_TIME_AND_ATTENDANCE = "/v1/time-and-attendance-upload",
    FTP_MANUAL_UPLOAD_TNA_TRIGGER = "/v1/ftp-tna-lookup-trigger/:clientId",
    // GET_LIST_OF_TIME_AND_ATTENDANCE = "/v1/time-and-attendance",
    // GET_DETAIL_OF_TIME_AND_ATTENDANCE = "/v1/time-and-attendance/:id",
    GET_TIME_AND_ATTENDANCE_SAMPLE_SHEET = "/v1/time-and-attendance/get-sample-sheet",
    DOWNLOAD_UPLOADED_TAP_SHEET = "/v1/total-agency-pay/download",
    GET_TAP_SAMPLE_SHEET = "/v1/total-agency-pay/get-sample-sheet",
    UPLOAD_TOTAL_AGENCY_PAY = "/v1/total-agency-pay/upload",
    DELETE_TOTAL_AGENCY_PAY = "/v1/total-agency-pay/delete",
    GET_ADJUSTMENT_ROWS = "/v1/time-and-attendance/adjustment-rows/:tnaId",
}

export enum region {
    REGION = "/v1/region",
    GET_REGION = "/v1/region",
    DROPDOWN = "/v1/region-drop-down",
    UPDATE_REGION = "/v1/region/:regionId"
}

export enum department {
    CREATE_AND_GET_LIST_OF_DEPARTMENT = "/v1/department",
    UPDATE_DEPARTMENT = "/v1/department/:departmentId"
};

export enum sector {
    CREATE_AND_GET_LIST_OF_SECTOR = "/v1/sector",
    UPDATE_SECTOR = "/v1/sector/:sectorId"
};

export enum site {
    SITE = "/v1/site",
    SITE_DROPDOWN = "/v1/site-drop-down",
    UPDATE_SITE = "/v1/site/:siteId",
    SITE_RATINGS = "/v1/site-ratings",
    SITE_RATINGS_DETAILS = "/v1/site-ratings/details",
    TYPE_WISE_SITE_RATINGS = "/v1/type-wise-site-rating",
    AGENCY_RESTRICTED_SITES = "/v1/site-restrictions",
}

export enum rateCard {
    CREATE_AND_GET_LIST_OF_RATE_CARD = "/v1/rate-card",
    RATE_CARD_BY_ID = "/v1/rate-card/:rateCardId",
    RATE_CARD_SAMPLE_SHEET = "/v1/rate-card/sample-sheet",
}

export enum job {
    CREATE_AND_GET_LIST_OF_JOB = "/v1/job",
    BULK_UPLOAD_JOBS = "/v1/job/bulk",
    UPDATE_JOB = "/v1/job/:jobId",
    GET_JOBS_DROPDOWN_BY_SITE = "/v1/job/site/:siteId",
    GET_JOBS_NAME_DROPDOWN_BY_SITE = "/v1/job-drop-down/site/:siteId"
};

export enum agencyClientAssociation {
    CREATE_AND_GET_LIST_OF_ASSOCIATION = "/v1/agency-client",
    UPDATE_ASSOCIATION = "/v1/agency-client/:associationId",
    RESTRICT_AGENCY = "/v1/agency-client/restrict/:associationId",
    RESTRICT_COMMENTS = "/v1/agency-client/restrict-comments/:associationId",
    TOTAL_ASSIGNMENT_PAY = "/v1/agency-client/tap/:associationId",
};

export enum user {
    ADD_NEW_USER = "/v1/user",
    GET_ADMIN_USERS = "/v1/admin-users",
    RESEND_INVITATION = "/v1/user/resend-invitation/:userId",
    REVOKE_USER = "/v1/user/:userId/revoke",
    USER_STATUS = "/v1/user/status/:userId",
}

export enum shift {
    SHIFT = "/v1/shift",
    EDIT_SHIFT = "/v1/shift/:shiftId",
}

export enum booking {
    GET_OR_CREATE_BOOKING = "/v1/bookings",
    CREATE_BULK_BOOKING = "/v1/bookings/bulk",
    BOOKING_DETAILS = "/v1/bookings/:bookingId",
    GET_BOOKING_SAMPLE_SHEET = "/v1/bookings/download/dynamic-sample-sheet",
    UPDATE_BOOKING = "/v1/booking",
    GET_OPEN_BOOKING = "/v1/booking/open",
    FULFILL_BULK_OPEN_BOOKING = "/v1/shift-booking/fulfillment",
}

export enum payroll {
    PAYROLL_AND_SUMMARY = "/v1/payroll",
    DOWNLOAD_PAYROLL_CSV = "/v1/payroll-download/:payrollMetaId",
    GET_PAYROLL_SAMPLE_SHEET = "/v1/payroll-get-sample-sheet",
    GET_CREDIT_DUE = "/v1/credit-due",
    DOWNLOAD_PAYROLL_DETAILED_SUMMARY = "/v1/payroll/detailed-summary-download/:payrollMetaId",
}

export enum agencyManagement {
    GET_SUPERVISORS_WEEKLY_DATA = "/v1/agency-management/supervisors-data",
    GET_SUPERVISOR_WORKERS_DATA = "/v1/agency-management/supervisors-data/:payrollMetaId",
}

// CLearvue Admin  Dashboard APIs
export enum masterAdminDashboard {
    DASHBOARD_SECTOR_LIST = "/v1/dashboard/sectors",
    DASHBOARD_ANALYTICS = "/v1/dashboard/analytics",
    DASHBOARD_CLIENT_LIST = "/v1/dashboard/clients",
    DASHBOARD_AGENCY_LIST = "/v1/dashboard/agencies",
    DASHBOARD_PAYROLL_LIST = "/v1/dashboard/payroll"
}

export enum compliancesDashboard {
    DASHBOARD_COMPLIANCE_COUNTS = "/v1/dashboard/client/:clientId/compliances",
    DASHBOARD_COMPLIANCES = "/v1/dashboard/client/:clientId/compliances/:complianceCardId",
    DASHBOARD_COMPLIANCES_APPROVAL = "/v1/dashboard/client/:clientId/compliances/:complianceCardId/approval"
}

export enum dashboardWorkForce {
    DASHBOARD_WORKER_DEMOGRAPHICS = "/v1/dashboard/worker-demographics",
    DASHBOARD_WORKER_LENGTH_OF_SERVICE = "/v1/dashboard/worker-service-length",
    DASHBOARD_WORKER_SHIFT_UTILIZATION = "/v1/dashboard/workforce/day-wise-shift-utilization",
    POOL_UTILIZATION = "/v1/dashboard/workforce/pool-utilization"
}

export enum dashboardWorkForceBottomDeck {
    DASHBOARD_WORKER_DEMOGRAPHICS = "/v1/dashboard/workforce/agency-wise-worker-demographics",
    DASHBOARD_WORKER_LENGTH_OF_SERVICE = "/v1/dashboard/workforce/agency-wise-length-of-service",
    DASHBOARD_WORKER_SHIFT_UTILIZATION = "/v1/dashboard/workforce/agency-wise-shift-utilization",
    HEAD_COUNT = "/v1/dashboard/activity/head-count",
}

export enum dashboardLeaversTopDeck {
    LEAVERS_LENGTH_OF_SERVICE = "/v1/dashboard/leaver-service-length",
    LEAVERS_COUNT_AND_STARTER_RETENTION = "/v1/dashboard/leaver-count-and-starter-retention",
    LEAVERS_SHIFT_UTILIZATION = "/v1/dashboard/leaver-shift-utilization",
    POOL_UTILIZATION = "/v1/dashboard/pool-utilization"
}

export enum dashboardLeaversBottonDeck {
    AGENCY_WISE_LEAVERS_LENGTH_OF_SERVICE = "/v1/dashboard/agency-wise-leaver-service-length",
    AGENCY_WISE_LEAVERS_COUNT_AND_STARTER_RETENTION = "/v1/dashboard/agency-wise-new-starter-retention",
    AGENCY_WISE_LEAVERS_SHIFT_UTILIZATION = "/v1/dashboard/agency-wise-leaver-shift-utilization",
    LEAVERS = "/v1/dashboard/leavers-data"
}

export enum activityAllStats {
    ALL_STATS = "/v1/dashboard/activity-stats"
}

export enum activityBottomDeck {
    SPEND = "/v1/dashboard/activity/spend",
    AVERAGE_HOURS = "/v1/dashboard/activity/average-hours",
    SHIFT_DETAILS = "/v1/dashboard/activity/shift-details",
}

export enum header {
    HEADER_STATS = "/v1/dashboard/header-stats",
    LEAVERS_ANALYSIS = "/v1/dashboard/leavers-analysis"
}

export enum dashboardDemographics {
    GENDER = "/v1/dashboard/gender",
    PROXIMITY = "/v1/dashboard/proximity",
    AGE = "/v1/dashboard/age"
}

// TODO: Refactor code to remove unused :userId path parameter from message API, which is no longer needed. 
// However, it is being kept for now to maintain support for the old mobile app version.
export enum message {
    MESSAGE = "/v1/messages",
    MESSAGE_DETAILS = "/v1/messages/:messageId",
    WORKER_SIDE_MESSAGES_LIST = "/v1/users/:userId/messages",
    WORKER_SIDE_MESSAGE_REACTION = "/v1/users/:userId/messages/:messageId/reaction",
    WORKER_SIDE_MESSAGE_COMMENT = "/v1/users/:userId/messages/:messageId/comment",
    WORKER_SIDE_MESSAGE_COMMENTS = "/v1/users/:userId/messages/:messageId/comments",
    TEMPLATES = "/v1/templates",
    TEMPLATE_BY_ID = "/v1/templates/:templateId",
    TRANSLATE_TEMPLATES_TO_MULTIPLE_LANGUAGE = "/v1/templates/translate/multiple",
    TRAINING_MESSAGE_DETAILS = "/v1/workers/training/messages/:messageId",
    UPDATE_MESSAGE_STATUS = "/v1/message-read/:messageId",
    TRANSLATE_MESSAGE = "/v1/messages/translate",
    TRANSLATE_MESSAGE_TO_MULTIPLE_LANGUAGE = "/v1/messages/translate/multiple",
    MESSAGE_TRANSLATION = "/v1/messages/translate/:messageId"
}

export enum survey {
    GET_SURVEY_CATEGORY = "/v1/survey/categories",
    GET_SURVEY_ANALYSIS = "/v1/survey/analysis/:surveyId",
    DOWNLOAD_SURVEY_ANALYSIS = "/v1/survey/analysis-download/:surveyId",
    GET_SURVEY_QUESTIONS = "/v1/survey/questions/:surveyId",
    ADD_SURVEY_RESPONSE = "/v1/survey/response"
}

export enum reporting {
    GET_PERFORMANCE_SHIFTS_BLOCKS = "/v1/reporting/performance-shifts-blocks",
    GET_PERFORMANCE_NEW_STARTERS_GRAPH = "/v1/reporting/performance-new-starters-graph",
    GET_PERFORMANCE_BY_TENURE = "/v1/reporting/performance-by-tenure",
    GET_SHIFT_BOOKINGS_GRAPH = "/v1/reporting/shift-bookings-graph",
    GET_SITE_STATS_SHIFT_FULFILMENT = "/v1/reporting/site-stats/shift-fulfilment",
    GET_SITE_STATS_AVE_HOURS = "/v1/reporting/site-stats/ave-hours",
    GET_SITE_STATS_POOL_UTILISATION = "/v1/reporting/site-stats/pool-utilisation",
    GET_SITE_STATS_LEAVERS = "/v1/reporting/site-stats/leavers",
    GET_SITE_STATS_PERFORMANCE = "/v1/reporting/site-stats/performance",
    GET_SITE_STATS_SHIFT_UTILISATION = "/v1/reporting/site-stats/shift-utilisation",
    GET_SITE_STATS_SPEND_HOURS = "/v1/reporting/site-stats/spend-hours"
}

export enum automatedMessages {
    TIMELINE_MESSAGES = "/v1/scheduler/timeline/messages",
    BIRTHDAY_MESSAGES = "/v1/scheduler/birthday/messages",
    WORKER_INACTIVE_MESSAGES = "/v1/scheduler/worker-inactive/messages",
    FIRST_DAY_WELCOME_MESSAGE = "/v1/scheduler/first-day-welcome/messages",
    FTP_TNA_UPLOADS = "/v1/scheduler/time-and-attendance-upload",
    FTP_WORKERS_UPLOAD = "/v1/scheduler/workers-bulk-upload",
    FTP_GET_PAYROLL_INVOICE = "/v1/scheduler/payroll-invoice",
    FTP_GET_PAYROLL_SUMMARY = "/v1/scheduler/payroll-download/:payrollMetaId",
    FTP_GET_PAYROLL_DETAILED_SUMMARY = "/v1/scheduler/payroll-detailed-summary/:payrollMetaId",
}

export enum trendsAnalysis {
    GET_STANDARD_OVERTIME_SPEND = "/v1/dashboard/trends/spend",
    GET_STANDARD_OVERTIME_HOURS = "/v1/dashboard/trends/hours",
    GET_TOTAL_HEADS = "/v1/dashboard/trends/heads",
    GET_LEAVERS_ANALYSIS = "/v1/dashboard/trends/leavers",
    SITE_RATING = "/v1/dashboard/trends/site-rating",
    AGENCY_RATING = "/v1/dashboard/trends/agency-rating",
    COMPANY_RATING = "/v1/dashboard/trends/company-rating",
}

export enum faq {
    GET_LIST_OF_FAQ = "/v1/support/:type",
};

export enum mobileVersion {
    GET_MOBILE_VERSION = "/v1/app/mobile-version",
};

export enum margins {
    MARGINS = "/v1/margins",
    MARGIN_BY_ID = "/v1/margins/:marginId",
}

export enum mfa {
    STATUS = "/v1/mfa/status",
    USER_STATUS = "/v1/mfa/user-status",
    ENROLLMENT_START = "/v1/mfa/enrollment/start",
    ENROLLMENT_COMPLETE = "/v1/mfa/enrollment/complete",
    SIGNIN_START = "/v1/mfa/signin/start",
    SIGNIN_COMPLETE = "/v1/mfa/signin/complete",
    DELETE_USER_ENROLLMENT = "/v1/mfa/enrollment/delete",
}