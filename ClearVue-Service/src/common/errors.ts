/**
 * All the custom errors with the status code for the same.
 */

import { invalid } from "joi";
import { config } from "../configurations";

export class GeneralError extends Error {
	error: string
	constructor(error: string, message: string) {
		super();
		this.message = message;
		this.error = error
	}

	getCode() {
		switch (true) {
			// Status code for the Custom Errors
			case this instanceof BadRequestError: return 400
			case this instanceof ResourceNotFoundError: return 404;
			case this instanceof UnauthorizedError: return 401;
			case this instanceof ForbiddenError: return 403;
			case this instanceof ConflictError: return 409;
			default: return 500;
		}
	}
}


// Declare and export Custom Errors
export class BadRequestError extends GeneralError { };
export class ResourceNotFoundError extends GeneralError { };
export class UnauthorizedError extends GeneralError { };
export class ForbiddenError extends GeneralError { };
export class ConflictError extends GeneralError { };
export class InternalServerError extends GeneralError { };


// Array of custom exceptions for which exception logs will be ignored.
const customExceptionsIgnoreLogsArray = [
	BadRequestError, UnauthorizedError, ForbiddenError, ResourceNotFoundError
];


/**
 * Check error log should be ignored or not.
 * @param  {any} err
 */
export const shouldIgnoreCustomeErrorLog = (err: any) => {
	for (let customException of customExceptionsIgnoreLogsArray) {
		if (err instanceof customException) return true;
	}
	return false;
};


/**
 * Error Responses
 */

export const ErrorResponse = {
	// Invalid JWT token
	Unauthorized: {
		"status": 401,
		"ok": false,
		"message": "Your session has expired. Please login again.",
		"error": "UNAUTHORIZED"
	},
	UnauthorizedWithForcedRelogin: {
		"status": 401,
		"ok": false,
		"message": "Your session has expired. Please login again.",
		"error": "UNAUTHORIZED",
		"force_relogin": true
	},
	// Missing JWT token in request header
	Forbidden: {
		"status": 403,
		"ok": false,
		"message": "Request is missing required authorization.",
		"error": "PERMISSION_DENIED"
	},

	// Invalid Password for the user
	InvalidCredentials: {
		"status": 401,
		"ok": false,
		"message": "Invalid email or password.",
		"error": "INVALID_CREDENTIAL_ERROR"
	},
	InvalidCredentialsWithForcedRelogin: {
		"status": 401,
		"ok": false,
		"message": "Invalid email or password.",
		"error": "INVALID_CREDENTIAL_ERROR",
		"force_relogin": true
	},
	// Invalid Password for the user
	WorkerInvalidCredentials: {
		"status": 401,
		"ok": false,
		"message": "Invalid email or password.",
		"error": "INVALID_CREDENTIAL_ERROR"
	},

	// Email not found for the user
	UserNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! We couldn't find your account. Please check you are using the correct credentials.",
		"error": "USER_NOT_FOUND",
	},
	UserAccessRevoked: {
		"status": 403,
		"ok": false,
		"message": "Your account is not active please contact your administrator.",
		"error": "USER_ACCOUNT_REVOKED",
	},

	// resource not found
	ResourceNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! That doesn't exist please check the spelling and try again or contact the admin.",
		"error": "RESOURCE_NOT_FOUND",
	},

	// resource not found
	ResourceNotFoundWithoutAnyUserInput: {
		"status": 404,
		"ok": false,
		"message": "Oops! That doesn't exist try again or contact the admin.",
		"error": "RESOURCE_NOT_FOUND",
	},

	// Only Automated(SYSTEM_DEFAULT) message can be updatable.
	InvalidMessageToUpdate: {
		"status": 400,
		"ok": false,
		"message": "Only messages with a type = `SYSTEM_DEFAULT` can be updatable.",
		"error": "BAD_REQUEST",
	},

	// User does not have permission for this action
	PermissionDenied: {
		"status": 403,
		"ok": false,
		"message": "You are not authorized for this action",
		"error": "PERMISSION_DENIED",
	},

	// Missing required data
	BadRequestError: {
		"status": 400,
		"ok": false,
		"message": "Missing data for required fields.",
		"error": "BAD_REQUEST",
	},

	InvalidFileTypeError: {
		"status": 400,
		"ok": false,
		"message": "Invalid file type. Please use '.CSV'",
		"error": "BAD_REQUEST",
	},

	InvalidFileTypeErrorNonCsv: {
		"status": 400,
		"ok": false,
		"message": "Invalid file type.",
		"error": "BAD_REQUEST",
	},

	InvalidFileSize: {
		"status": 400,
		"ok": false,
		"message": "File size exceed the 5Mb limit. Please reduce the file size and try again.",
		"error": "BAD_REQUEST",
	},

	InvalidRepeatBookingCount: {
		"status": 400,
		"ok": false,
		"message": `No more than ${config.MAX_REPEAT_BOOKING_ALLOWED} weeks can be repeated for a shift booking.`,
		"error": "BAD_REQUEST",
	},
	// For Duplicate worker
	WorkerAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested worker already exists.",
		"error": "WORKER_ALREADY_EXISTS"
	},
	// For Duplicate worker
	EmailAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Oops! The email you provided cannot be associated with this account. Please enter a different email address.",
		"error": "EMAIL_ALREADY_EXISTS"
	},
	ClientAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested client already exists.",
		"error": "CLIENT_ALREADY_EXISTS"
	},
	UserAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested user already exists.",
		"error": "USER_ALREADY_EXISTS"
	},
	UserAlreadyExistsCustom: {
		"status": 409,
		"ok": false,
		"message": `Requested user already exists as $CustomValues`,
		"error": "USER_ALREADY_EXISTS"
	},
	RegionAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested region already exists.",
		"error": "REGION_ALREADY_EXISTS"
	},

	AdminAlreadyAssignToSite: {
		"status": 409,
		"ok": false,
		"message": "Admin is already assigned to other site.",
		"error": "ADMIN_ALREADY_ASSIGN_TO_SITE"
	},

	AdminAlreadyAssignToRegion: {
		"status": 409,
		"ok": false,
		"message": "Admin is already assigned to other region.",
		"error": "ADMIN_ALREADY_ASSIGN_TO_REGION"
	},

	// For invalid token for the password reset API
	InvalidResetPasswordCodeError: {
		'ok': false,
		'error': 'INVALID_PASSWORD_LINK',
		'message': "Reset password link has expired. Please request another reset.",
		'status': 404,
	},
	UnprocessableEntity: {
		"status": 422,
		"ok": false,
		"message": "Invalid data for required fields.",
		"error": "UNPROCESSABLE_ENTITY",
	},
	AssociationAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested association already exists.",
		"error": "ASSOCIATION_ALREADY_EXISTS"
	},
	AssocationNotFound: {
		"status": 404,
		"ok": false,
		"message": "Client Agency association with given id not found.",
		"error": "ASSOCIATION_NOT_FOUND"
	},
	AssociationPermissionDenied: {
		"status": 403,
		"ok": false,
		"message": "You do not have permission to create association.",
		"error": "ASSOCIATION_PERMISSION_DENIED"
	},
	// For Duplicate worker email
	WorkerEmailAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested worker email already exists.",
		"error": "WORKER_EMAIL_ALREADY_EXISTS"
	},
	//
	FileAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Data for the week already exists.",
		"error": "FILE_ALREADY_EXISTS"
	},
	WrongDateRange: {
		"status": 400,
		"ok": false,
		"message": "The difference between start_date and end_date should be exactly 6 days.",
		"error": "WRONG_DATE_RANGE"
	},
	InvalidStartDate: {
		"status": 400,
		"ok": false,
		"message": "Weekday of the selected start_date is not matched with the client-specific start weekday",
		"error": "INVALID_START_DATE"
	},
	DepartmentAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested Department name already exists.",
		"error": "DEPARTMENT_ALREADY_EXISTS"
	},
	ShiftAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested Shift name already exists.",
		"error": "SHIFT_ALREADY_EXISTS"
	},
	InvalidShiftBookingRequest: {
		"status": 409,
		"ok": false,
		"message": "No. of Shift Booking Request must be at least 1 and less than or equals to " + config.MAX_BULK_WORKER_UPLOAD_LIMIT.toString() + ".",
		"error": "SHIFT_ALREADY_EXISTS"
	},
	InvalidJobsNumbersRequest: {
		"status": 409,
		"ok": false,
		"message": "No. of jobs must be greater than 1 but no more than " + config.MAX_BULK_JOB_UPLOAD_LIMIT.toString() + ".",
		"error": "BAD_REQUEST"
	},
	InvalidWorkersNumbersRequest: {
		"status": 409,
		"ok": false,
		"message": "No. of workers must be at least 1 and less than or equals to " + config.MAX_BULK_WORKER_UPLOAD_LIMIT.toString() + ".",
		"error": "BAD_REQUEST"
	},
	WorkerClockReportNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find the worker clock report for the given week, please upload that first.",
		"error": "WORKER_CLOCK_REPORT_NOT_FOUND",
	},
	TapFileNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find TAP sheet for the given week, please upload that first.",
		"error": "TAP_File_NOT_FOUND",
	},
	WorkerPasswordAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "You are already registered. Please try to login to the system again or request new password.",
		"error": "WORKER_ALREADY_EXISTS"
	},
	WorkerNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find your account.",
		"error": "WORKER_NOT_FOUND",
	},
	InvalidSiteIds: {
		"status": 403,
		"ok": false,
		"message": "Invalid site IDs in associations.",
		"error": "INVALID_SITE_IDS",
	},
	// No workers available while sending the message
	WorkersNotFoundForSendingMessage: {
		"status": 404,
		"ok": false,
		"message": "Oops! There are no workers available to send message as per selected filters!",
		"error": "WORKERS_NOT_AVAILABLE",
	},
	InvalidBookingWorkers: {
		"status": 400,
		"ok": false,
		"message": `Please assign the remaining workers to another agency to complete the booking.`,
		"error": "BAD_REQUEST",
	},

	InvalidBookingSumNotMacthing: {
		"status": 400,
		"ok": false,
		"message": `The total number of heads doesn't match the sum of day-wise heads for at least one value.`,
		"error": "BAD_REQUEST",
	},

	InvalidBookingTotalRequestedNotMacthing: {
		"status": 400,
		"ok": false,
		"message": `The sum of 'total' for agency requested doesn't match the sum of 'Required Workers Total' and 'Required Supervisors Total'`,
		"error": "BAD_REQUEST",
	},

	InvalidBookingTotalAgencyRequestedNotMacthing: {
		"status": 400,
		"ok": false,
		"message": `The value of 'total' doesn't match the sum of 'Requested Workers Total' and 'Requested Supervisors Total' for one of the agencies.`,
		"error": "BAD_REQUEST",
	},

	InvalidFulfilmentTotalAgencyRequestedNotMacthing: {
		"status": 400,
		"ok": false,
		"message": `The value of 'total' doesn't match the sum of 'Fulfilled Workers Total' and 'Fulfilled Supervisors Total' for one of the agencies.`,
		"error": "BAD_REQUEST",
	},

	InvalidBookingDeletionRequest: {
		"status": 400,
		"ok": false,
		"message": `Bookings with FULFILLED status cannot be deleted.`,
		"error": "BAD_REQUEST",
	},

	InvalidWorkersAssignment: {
		"status": 400,
		"ok": false,
		"message": `Error! Can not create booking if workers are not assigned to all agencies!`,
		"error": "BAD_REQUEST",
	},
	InvalidFulfillmentOperation: {
		"status": 400,
		"ok": false,
		"message": `Fulfillment not allowed after 'Time and Attendance' uploads for selected site and week.`,
		"error": "BAD_REQUEST",
	},
	InvalidWorkersAssignmentBulk: {
		"status": 400,
		"ok": false,
		"message": "Booking creation failed! The total number of heads must not be 0 for any agencies.",
		"error": "BAD_REQUEST",
	},

	MessageNotFound: {
		"status": 400,
		"ok": false,
		"message": `Oops! Couldn't find message.`,
		"error": "MESSAGE_NOT_AVAILABLE",
	},

	CronJobUnauthorizedError: {
		"status": 401,
		"ok": false,
		"message": "Missing required authorization details.",
		"error": "UNAUTHORIZED"
	},

	NationalInsuranceNumberExistsError: {
		'ok': false,
		'error': 'NATIONAL_INSURANCE_NUMBER_EXISTS',
		'message': "National Insurance number already exists.",
		'status': 422,
	},
	NationalInsuranceNumberExistsErrorCustom: {
		'ok': false,
		'error': 'NATIONAL_INSURANCE_NUMBER_EXISTS',
		'message': `The Following National Insurance number already exists in database: [ $CustomValues ] `,
		'status': 422,
	},
	EmployeeIdExistsCustom: {
		'ok': false,
		'error': 'EMPLOYEE_ID_ALREADY_EXISTS',
		'message': `The Following Employee Id(s) already exists in database: [ $CustomValues ] `,
		'status': 422,
	},
	// For Duplicate template name
	TemplateNameAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested template name already exists.",
		"error": "TEMPLATE_ALREADY_EXISTS"
	},
	SurveyAlreadyFilled: {
		"status": 409,
		"ok": false,
		"message": "Requested survey has already been completed.",
		"error": "SURVEY_ALREADY_FILLED"
	},
	BookingEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "The booking has been successful but emails have not been sent.",
		"error": "BOOKING_EMAIL_NOT_SENT"
	},
	BookingAmendEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "Booking amendment has been successful but email updates have not been sent.",
		"error": "BOOKING_EMAIL_NOT_SENT"
	},
	UserInviteEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "User has been created but email has not been successful. Please click to re-invite user.",
		"error": "BOOKING_EMAIL_NOT_SENT"
	},
	WorkerInviteEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "Worker uploads are complete, but sending emails was unsuccessful. Please get in touch with the admin for assistance.",
		"error": "WORKER_INVITE_EMAIL_NOT_SENT"
	},
	ForgotPasswordEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "Forgot Password email not sent. Please try again later.",
		"error": "BOOKING_EMAIL_NOT_SENT"
	},
	ResendInvitationEmailNotSent: {
		"status": 409,
		"ok": false,
		"message": "Resend invitation email not sent. Please try again later.",
		"error": "BOOKING_EMAIL_NOT_SENT"
	},
	WorkerIsNotRegistered: {
		"status": 404,
		"ok": false,
		"message": "Thank you for downloading The ClearVue app. It looks like your agency haven't registered you yet. Please try again later or contact your agency representative.",
		"error": "WORKER_IS_NOT_REGISTERED",
	},
	UserIsDeactivated: {
		"status": 404,
		"ok": false,
		"message": "Your account is no longer active. Please contact your administrator if you feel this has been done in error.",
		"error": "USER_ACCOUNT_DEACTIVATED",
	},
	UnauthorizedForInvalidPath: {
		"status": 401,
		"ok": false,
		"message": "Requested path does not exist. Please verify path params.",
		"error": "UNAUTHORIZED"
	},
	BlockedAccount: {
		"status": 401,
		"ok": false,
		"message": "Your account is blocked, please try again later",
		"error": "ACCOUNT_BLOCKED"
	},
	UnprocessableEntityForFile: {
		"status": 422,
		"ok": false,
		"message": "Oops! We could not process the file due to special characters within the file. Please remove these and try again.",
		"error": "UNPROCESSABLE_ENTITY",
	},
	DuplicateKeyInReqObject: {
		"status": 403,
		"ok": false,
		"message": "Duplicate Key found in both query and body parameters",
		"error": "PERMISSION_DENIED"
	},
	UnauthorizedUser: {
		"status": 403,
		"ok": false,
		"message": "Your user account is not authorised to access this resource.",
		"error": "PERMISSION_DENIED"
	},
	JobNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find job for the requested site",
		"error": "JOB_NOT_FOUND",
	},
	InvalidShiftName: {
		"status": 401,
		"ok": false,
		"message": "Only alphanumeric characters, spaces, and special characters(! `@ # $ % ^ & * ( ) _ + - = [ ] { } | ; : , . < > / ?) are allowed for the field name.",
		"error": "INVALID_SHIFT_ERROR"
	},
	BookingNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find shift booking for the given week, please create that first.",
		"error": "TAP_File_NOT_FOUND",
	},
	InvalidTapSHeetDeletionRequest: {
		"status": 400,
		"ok": false,
		"message": `Cannot delete TAP file. T&A data exists for the selected week.`,
		"error": "BAD_REQUEST",
	},
	InvalidTapUploadOperation: {
		"status": 400,
		"ok": false,
		"message": `Uploading the TAP file is not allowed after the 'Time and Attendance' file is uploaded for the selected site and week.`,
		"error": "BAD_REQUEST",
	},
	InvalidRequestedHeads: {
		"status": 400,
		"ok": false,
		"message": `The headcount must be a whole number for all days. Please review your input and try again.`,
		"error": "BAD_REQUEST",
	},
	WorkerPerformanceAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Data for this week already exists. Please delete the existing data and then re-upload.",
		"error": "WORKER_PERFORMANCE_FILE_ALREADY_EXISTS"
	},
	WorkerPerformanceFileNotFound: {
		"status": 404,
		"ok": false,
		"message": "Oops! Couldn't find worker performance sheet for the given week, please upload that first.",
		"error": "WORKER_PERFORMANCE_FILE_NOT_FOUND",
	},
	InvalidWorkerPerformanceDeleteRequest: {
		"status": 400,
		"ok": false,
		"message": "Deleting worker performance uploaded by client is restricted.",
		"error": "BAD_REQUESTS"
	},
	InvalidWeekNumbers: {
		"status": 400,
		"ok": false,
		"message": "The threshold week cannot be greater than the 'check week'. Please check the input values and try again.",
		"error": "BAD_REQUEST",
	},
	TrainingRuleAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "A training rule already exists for the selected Site. Please choose a different site or update the existing rule.",
		"error": "TRAINING_RULE_ALREADY_EXISTS"
	},
	RateCardAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Rate card data already exists. Please delete the existing data and then re-upload.",
		"error": "RATE_CARD_FILE_ALREADY_EXISTS"
	},
	InvalidWorkerAssignmentDates: {
		"status": 400,
		"ok": false,
		"message": "The assignment date can't be earlier than the start date.",
		"error": "INVALID_ASSIGNMENT_DATE"
	},
	DuplicateEntriesForIdSupervisorFlag: {
		"status": 400,
		"ok": false,
		"message": `The sheet contains duplicate entries for combinations of 'id' and 'supervisor_flag' for the following Ids : \n [ $CustomValues ]. \n Please remove the duplicate values and try to reupload.`,
		"error": "BAD_REQUEST"
	},
	DuplicateEntriesForEmployeeId: {
		"status": 400,
		"ok": false,
		"message": `The sheet contains duplicate 'employee_id' entries for the following Employee Ids : \n [ $CustomValues ]. \n Please remove the duplicate values and try to reupload.`,
		"error": "BAD_REQUEST"
	},
	DuplicateEntriesForEmails: {
		"status": 400,
		"ok": false,
		"message": `The sheet contains duplicate 'email' entries for the following Emails : \n [ $CustomValues ]. \n Please remove the duplicate values and try to reupload.`,
		"error": "BAD_REQUEST"
	},
	DuplicateEntriesForNI: {
		"status": 400,
		"ok": false,
		"message": `The sheet contains duplicate 'national_insurance_number' entries for the following National Insurance Numbers: \n [ $CustomValues ]. \n Please remove the duplicate values and try to reupload.`,
		"error": "BAD_REQUEST"
	},
	InvalidRoleTypes: {
		"status": 400,
		"ok": false,
		"message": `Role type must be Full Time-5 Days, Full Time-4 on 4 off, Full Time-4 on 3 off, Full Time-3 on 3 off, Part Time and Weekend. Error on line numbers: $CustomValues`,
		"error": "BAD_REQUEST"
	},
	InvalidShifts: {
		"status": 404,
		"ok": false,
		"message": `Oops! Couldn't find following Shift(s): $CustomValues  `,
		"error": "SHIFT_NOT_FOUND",
	},
	InvalidDepartments: {
		"status": 404,
		"ok": false,
		"message": `Oops! Couldn't find following Department(s): $CustomValues  `,
		"error": "DEPARTMENT_NOT_FOUND",
	},
	InvalidAgencyIdNameCombo: {
		"status": 400,
		"ok": false,
		"message": `Oops! Invalid Agency ID and Agency Name Combinations(s): $CustomValues  `,
		"error": "BAD_REQUEST",
	},
	WorkerEmailAlreadyExistsCustom: {
		"status": 409,
		"ok": false,
		"message": `Requested 'Worker' already exists: $CustomValues`,
		"error": "WORKER_EMAIL_ALREADY_EXISTS"
	},
	EmailAlreadyExistsCustom: {
		"status": 409,
		"ok": false,
		"message": `The Following Email(s) already exists: $CustomValues `,
		"error": "EMAIL_ALREADY_EXISTS"
	},
	InvalidWorkerPerformanceDownloadRequest: {
		"status": 400,
		"ok": false,
		"message": "Downloading worker performance uploaded by $CustomValues is restricted.",
		"error": "BAD_REQUESTS"
	},
	PerformanceHighLowEmpty: {
		"status": 400,
		"ok": false,
		"message": `The following rows have either performance_low or performance_high set without the other, or one of them is missing: : \n [ $CustomValues ].`,
		"error": "BAD_REQUEST"
	},
	InvalidPerformanceHighLow: {
		"status": 400,
		"ok": false,
		"message": `The following rows have performance_low greater than or equal to performance_high : \n [ $CustomValues ].`,
		"error": "BAD_REQUEST"
	},
	DuplicatePayChargeSupervisorCombo: {
		"status": 400,
		"ok": false,
		"message": `The following rows in the sheet have duplicate combinations of 'pay', 'charge', 'supervisor_rate', and 'pay_type': \n [ $CustomValues ]. \n Please update the sheet to ensure that these combinations are unique.`,
		"error": "BAD_REQUEST"
	},
	InvalidSupervisorRate: {
		"status": 400,
		"ok": false,
		"message": `ERROR: Only Supervisor/Internal pay types can be associated with supervisor rates. Please update the following rows in the sheet : \n [ $CustomValues ].`,
		"error": "BAD_REQUEST"
	},
	InvalidPaytypeSupervisorCombo: {
		"status": 400,
		"ok": false,
		"message": `ERROR: Supervisor/Internal pay types can only be associated with supervisor rates. Please update the following rows in the sheet : \n [ $CustomValues ].`,
		"error": "BAD_REQUEST"
	},
	SupervisorStatusAlreadySet: {
		"status": 422,
		"ok": false,
		"message": `The worker's supervisor status is already set as [ $CustomValues ]. \nIt cannot be modified at this time.`,
		"error": "SUPERVISOR_STATUS_ALREADY_SET"
	},
	WorkersNotUpdated: {
		"status": 400,
		"ok": false,
		'message': `All workers on the sheet were updated successfully except for the following Employee Id(s) :\n [ $CustomValues ]`,
		"error": "BAD_REQUEST"
	},
	WorkerInviteEmailAlreadySet: {
		"status": 400,
		"ok": false,
		'message': `The action cannot be performed because the worker invite email setting is $CustomValues.`,
		"error": "BAD_REQUEST"
	},
	ResourceAlreadyExists: {
		"status": 409,
		"ok": false,
		"message": "Requested resource already exists.",
		"error": "RESOURCE_ALREADY_EXISTS"
	},
	SiteNotAllowedForMarginCreation: {
		"status": 403,
		"ok": false,
		"message": "The site is not allowed to create margins.",
		"error": "SITE_NOT_ALLOWED"
	},
	MissingBankDetialsError: {
		"status": 401,
		"ok": false,
		"message": "Missing 'sort_code' or 'account_number'. Both must either be empty or both must have values together.",
		"error": "MISSING_BANK_DETAILS"
	},
	RuleOverlapsExistingRules: {
		status: 409,
		ok: false,
		message: "A rule already exists for the selected period. Please choose a different date range or update the existing rule.",
		error: "RULE_OVERLAPS"
	},
	InvalidWeekdayStart: {
		status: 400,
		ok: false,
		message: "The start date does not match the client's required weekday start.",
		error: "INVALID_WEEKDAY_START"
	},
	InvalidStartDateMonth: {
		status: 400,
		ok: false,
		message: "Start date must be in March or April.",
		error: "INVALID_START_DATE_MONTH"
	},
	InvalidEndDateMonth: {
		status: 400,
		ok: false,
		message: "End date must be in March or April.",
		error: "INVALID_END_DATE_MONTH"
	},
	InvalidEndDateYear: {
		status: 400,
		ok: false,
		message: "End date must be exactly one year after start date.",
		error: "INVALID_END_DATE_YEAR"
	},
	InvalidRuleOrder: {
		status: 400,
		ok: false,
		message: "Rules must be added in chronological order.",
		error: "INVALID_RULE_ORDER"
	},
	InvalidTotalWeeks: {
		status: 400,
		ok: false,
		message: "Total weeks calculation is incorrect.",
		error: "INVALID_TOTAL_WEEKS"
	},
	InvalidStartDateShouldBeInFuture: {
		status: 400,
		ok: false,
		message: "Start date must be in the future.",
		error: "INVALID_START_DATE"
	},
	InvalidEndDate: {
		status: 400,
		ok: false,
		message: "End date must result in a full week difference. Partial weeks are not allowed.",
		error: "INVALID_END_DATE"
	},
	InvalidDates: {
		status: 400,
		ok: false,
		message: "End date must be bigger than start date.",
		error: "INVALID_DATES"
	},
	RuleNotFoundForTNAUpload: {
		status: 404,
		ok: false,
		message: "Rule for this week TNA upload does not exist. There must be a rule available for the financial year you are trying to upload TNA, as there are prior rules that were in use.",
		error: "RULE_NOT_FOUND"
	},
	WrongWeekNumber: {
		status: 400,
		ok: false,
		message: "Provided week number is invalid. Please contact the admin.",
		error: "INVALID_WEEK_NUMBER"
	},
	DetailedSummaryNotExist: {
		status: 404,
		ok: false,
		message: "Detailed summary report is not available for the requested date range. Please select a more recent week.",
		error: "DETAILED_SUMMARY_NOT_FOUND"
	},
	InvalidParams: {
		status: 400,
		ok: false,
		message: "Invalid path/body parameters provided.",
		error: "INVALID_PARAMS"
	},
	ApprovalStatusAlredySet: {
		status: 400,
		ok: false,
		message: "The approval status has already been set to the provided one.",
		error: "APPROVAL_STATUS_ALREADY_SET"
	},
}
