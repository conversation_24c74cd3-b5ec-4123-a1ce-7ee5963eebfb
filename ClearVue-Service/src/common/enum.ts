/**
 * All the enum type objects.
 */

export enum HttpMethods {
    POST = 'POST',
    PUT = 'PUT',
    GET = 'GET',
    DELETE = 'DELETE'
};

export enum FeatureList {
    AGENCY = "agency",
    CLIENT = "client",
    REGIONAL_CLIENT_ADMIN = "regional_client_admin",
    SITE_CLIENT_ADMIN = "site_client_admin",
    DEPARTMENT = "department",
    REGION = "region",
    SITE = "site",
    JOB = "job",
    RATE_CARD = "rate_card",
    SHIFT_TYPE = "shift_type",
    WORKER = "worker",
    SECTOR = "sector",
    MANAGE_ASSOCIATIONS = "manage_associations",
    TIME_AND_ATTENDANCE = "time_and_attendance",
    USER = "user",
    SHIFT_BOOKING = "shift_booking",
    MASTER_ADMIN_DASHBOARD = "master_admin_dashboard",
    RESEND_INVITATION = "resend_invitation"
};

export enum RedirectURLs {
    RESET_PASSWORD = '/reset-password'
};

export enum UserType {
    CLEARVUE_ADMIN = 1,
    CLIENT_ADMIN = 2,
    AGENCY_ADMIN = 3,
    CLIENT_REGIONAL = 4,
    CLIENT_SITE = 5,
    AGENCY_WORKER = 6,
    AGENCY_SITE = 7,
    AGENCY_REGIONAL = 8,
    MESSAGE_ADMIN = 9,
}

export enum AccessType {
    VIEW = 1,
    CREATE = 2,
    UPDATE = 3,
    DELETE = 4
}


export enum MessageActions {
    CREATE_CLIENT = "Client has been added successfully.",
    CREATE_CLIENT_USER = "Client user has been added successfully.",
    UPDATE_CLIENT_USER = "Client user details has been updated successfully.",
    UPDATE_CLIENT = "Client details has been updated successfully.",
    CREATE_SITE = "Site has been added successfully.",
    CREATE_REGION = "Region has been added successfully.",
    CREATE_AGENCY = "Agency has been added successfully.",
    UPDATE_AGENCY = "Agency details updated successfully.",
    CREATE_AGENCY_ASSOCIATION = "Agency Association has been added successfully.",
    UPDATE_AGENCY_ASSOCIATION = "Agency Association has been updated successfully.",
    CREATE_DEPARTMENT = "Department has been added successfully.",
    UPDATE_DEPARTMENT = "Department details has been updated successfully.",
    UPDATE_REGION = "Region details has been updated successfully.",
    UPDATE_SITE = "Site details has been updated successfully.",
    CREATE_JOB = "Job has been added successfully.",
    UPDATE_JOB = "Job details has been updated successfully.",
    CREATE_RATE_CARD = "Rate has been card added successfully.",
    CREATE_SECTOR = "Sector has been added successfully.",
    UPDATE_SECTOR = "Sector details has been updated successfully.",
    CREATE_WORKER = "Worker has been added successfully.",
    CREATE_WORKERS = "Workers has been added successfully.",
    UPDATE_WORKER = "Worker has been updated successfully.",
    CREATE_TIME_AND_ATTENDANCE = "Time and attendance data has been added successfully.",
    CREATE_PAYROLL = "Payroll Report has been added successfully.",
    UPDATE_WORKERS = "Workers data has been updated successfully.",
    UPDATE_SINGLE_WORKERS = "Worker has been updated successfully.",
    REGISTRATION_SUCCESS = "Worker registration successful",
    CREATE_USER = "User has been added successfully",
    UPDATE_USER = "User data has been updated successfully.",
    CREATE_SHIFT = "Shift has been added successfully.",
    UPDATE_SHIFT = "Shift has been updated successfully.",
    RESEND_INVITATION = "Invitation has been resend successfully.",
    CANCEL_BOOKING = "Booking has been cancelled.",
    UPDATE_BOOKING = "Booking has updated successfully.",
    BULK_BOOKING = "Bookings has been added successfully.",
    NO_CANCEL_BOOKING = "Booking has not been cancelled.",
    REVOKE_USER = "User access revoked.",
    UPDATE_WORKER_PROFILE = "Details are updated successfully.",
    SURVEY_RESPONSE = "Response is submitted successfully.",
    MESSAGE_SENT = "Message is sent successfully!",
    NON_ENGLISH_MESSAGE_TRANSLATIONS_WARNING = "We have detected some non-english text. The translate all feature works best when the base language is English. Check your text and amend if required. If your text is non-english then ensure you verify the translated text",
    TRANSLATIONS_SUCCESS = "Translation has been done successfully",
    TEMPLATE = "Template is added successfully!",
    UPDATE_TEMPLATE = "Template is updated successfully!",
    UPDATE_QUESTION = "Questions are updated successfully!",
    USER_STATUS = "User access status is updated successfully!",
    UPDATE_AUTOMATED_MESSAGE = "Message Updated successfully!",
    DELETE_PAYROLL = "Payroll data has been deleted successfully.",
    CREATE_TOTAL_AGENCY_PAY_FOLDER = "Total agency pay data has been added successfully.",
    CREATE_WORKER_PERFORMANCE_FOLDER = "Worker performance data has been added successfully.",
    DELETE_TOTAL_AGENCY_PAY = "Total agency pay data has been deleted successfully.",
    DELETE_SHIFT_BOOKING = "Booking has been deleted successfully.",
    DELETE_WORKER_PERFORMANCE = "Worker Performance, related T&A upload and training data has been deleted sucessfully.",
    DELETE_RATE_CARD = "Rate card data has been deleted successfully.",
    CREATE_TRAINING_RULE = "Training rule has been added successfully.",
    UPDATE_TRAINING_RULE = "Training rule has been updated successfully.",
    DELETE_TRAINING_RULE = "Training rule has been deleted successfully.",
    CREATE_MARGINS = 'Margins created successfully',
    UPDATE_MARGINS = 'Margins updated successfully',
    DELETE_MARGINS = 'Margins deleted successfully',
    UPDATE_SITE_RESTRICTIONS = 'Site restrictions updated successfully',
    CREATE_YEARLY_RULE = "Client yearly rule has been added successfully.",
    UPDATE_YEARLY_RULE = "Client yearly rule has been updated successfully.",
    DELETE_YEARLY_RULE = "Client yearly rule has been deleted successfully.",
    CREATE_FINANCIAL_RULE = "Client financial rule has been added successfully.",
    UPDATE_FINANCIAL_RULE = "Client financial rule has been updated successfully.",
    DELETE_FINANCIAL_RULE = "Client financial rule has been deleted successfully."
}

export enum MimeType {
    CSV = "text/csv",
    JPG = 'image/jpeg',
    PNG = 'image/png',
    XLS = 'application/vnd.ms-excel',
    XLSX = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    PDF = 'application/pdf',
    GIF = 'image/gif',
    DOC = 'application/msword',
    DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    PPT = 'application/vnd.ms-powerpoint',
    PPTX = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
}

export enum TimeAndAttendanceStatus {
    APPROVED = "Approved",
    PROCESSING = "Processing",
    PROCESSED = "Processed"
}

export enum PayrollAssumptions {
    NI_THRESHOLD = 170,
    NI_RATE = 0.138,
    PENSION_THRESHOLD = 120,
    PENSION_CONTRIBUTION = 0.03,
    APPRENTICESHIP_LEVY = 0.005,
    HOLIDAY_RATE = 0.1207,
    HOLIDAY_ENTITLEMENT = 28,
    STANDARD_WORKING_HOURS = 37.5,
}

export enum RoleTypeForCSV {
    'FULLTIME5DAYS' = 1,
    'PARTTIME' = 2,
    'WEEKEND' = 3,
    'FULLTIME4ON4OFF' = 4,
    'FULLTIME4ON3OFF' = 5,
    'FULLTIME3ON3OFF' = 6
}

export enum RoleType {
    'FULL-TIME-5 DAYS' = 1,
    'PART-TIME' = 2,
    'WEEKEND' = 3,
    'FULL-TIME-4 ON 4 OFF' = 4,
    'FULL-TIME-4 ON 3 OFF' = 5,
    'FULL-TIME-3 ON 3 OFF' = 6
}

export enum BookingStatus {
    'OPEN' = 0,
    'FULFILLED' = 1,
    'AMENDED' = 2
}

export enum PayType {
    STANDARD = 'standard',
    OVERTIME = 'overtime',
}

export enum MessageType {
    GENERAL = "GENERAL",
    BADGE = "BADGE",
    RECOGNITION = "RECOGNITION",
    AWARD = "AWARD",
    REWARD = "REWARD",
    TRAINING = "TRAINING",
    SYSTEM = "SYSTEM",
    SYSTEM_DEFAULT = "SYSTEM_DEFAULT"
}

export enum MessageBodyContentType {
    TEXT = "text",
    MEDIA = "media",
    LINK = "link",
    TRANSLATE = "translate",
    SURVEY = "survey"
}

export enum ComplinaceCardsType {
    WORKER_SIXTY_PLUS_HOURS = 1,
    MULTIPLE_OCCUPANCY = 2,
    STUDENT_VISA = 3,
    CONSECUTIVE_DAYS = 4,
    UNDER_18 = 5,
    BANKING = 6,
    LIMITED_HOURS = 7,
}

export enum MessageUpdationType {
    OTHER = "other",
    TRANSLATION_DATA_ONLY = "translate"
}

export enum MessageReceiverGroups {
    WORKERS = 'workers',
    JOB = 'job',
    SHIFT = 'shift',
    DEPARTMENT = 'department',
    NATIONALITY = 'nationality'
}

export enum HallOfFameTypes {
    RECOGNITION = "recognition",
    BADGE = "badge",
    AWARD = "award"
}


export enum WorkerSideMessagesType {
    GENERAL = "general",
    RECOGNITION = "recognition",
    AWARD = "award",
    TRAINING = "training",
    BADGE = "badge",
    FEED = "feed",
    KUDOS = "kudos"
}

export enum WorkerSideMessagesScreenViewType {
    CLIENT = "client",
    AGENCY = "agency",
    GENERAL = "general"
}

export enum AutomatedMessagesLabels {
    NEW_STARTER_WEEK_1 = "new_starter_week_1",
    NEW_STARTER_WEEK_2 = "new_starter_week_2",
    NEW_STARTER_WEEK_4 = "new_starter_week_4",
    NEW_STARTER_WEEK_8 = "new_starter_week_8",
    NEW_STARTER_WEEK_12 = "new_starter_week_12",
    NEW_STARTER_WEEK_26 = "new_starter_week_26",
    NEW_STARTER_WEEK_39 = "new_starter_week_39",
    NEW_STARTER_WEEK_52 = "new_starter_week_52",
    ANNUAL_WORK_ANNIVERSARY = "annual_work_anniversary",
    BIRTHDAY_MESSAGES = "birthday_wishes",
    UNASSINGED_WORKER = "unassign_worker",
    ZERO_HOURS_MESSAGE = "zero_hours",
    FIRST_DAY_WELCOME_MESSAGE = "first_day_welcome"
}

export enum FaqUrlType {
    FAQ = "faq",
    LINK_TO_SUPPORT = "link-to-support"
}

export enum FaqDatabaseType {
    FAQ = "FAQ",
    LINK_TO_SUPPORT = "LINK_TO_SUPPORT"
}

export enum WorkerTypes {
    TEMPORARY = "TEMPORARY",
    PERMANENT = "PERMANENT"
}

export enum WorkerAvailability {
    'FULL-TIME' = 1,
    'PART-TIME' = 2,
}

export enum Languages {
    ALBANIAN = 'sq',
    BULGARIAN = 'bg',
    CZECH = 'cs',
    ENGLISH = 'en',
    ESTONIAN = 'et',
    FRENCH = 'fr',
    HINDI = 'hi',
    HUNGARIAN = 'hu',
    ITALIAN = 'it',
    LATVIAN = 'lv',
    LITHUANIAN = 'lt',
    NEPALI = 'ne',
    URDU = 'ur',
    POLISH = 'pl',
    PORTUGUESE = 'pt',
    ROMANIAN = 'ro',
    SLOVAK = 'sk',
    SLOVANIAN = 'sl',
    SPANISH = 'es',
    UKRAINIAN = 'uk',
    TIGRINYA = 'ti'
}

export enum SchemaValidationErrorCode {
    UNKNOWN_COLUMN = "object.unknown",
    MIN_LIMIT = "string.min",
    REGEX = "string.pattern.base",
    ALTERNATIVES = "alternatives.match",
    INVALID = "any.invalid",
    DATE_FORMAT = "date.format",
    DATE_MAX_TODAY = "date.max",
    ALPHANUMERIC = "string.alphanum",
    EMAIL = "string.email",
    REQUIRED = "any.required",
    ANY_ONLY = "any.only",
    NUMBER_BASE = "number.base",
    CUSTOM_BANKDETAILS = "any.custom.bankdetials",
    STUDENT_VISA_LIMITED_HOURS = "any.custom.studentVisa",
}

export enum WeekDays {
    SUNDAY = "SUN",
    MONDAY = "MON",
    TUESDAY = "TUE",
    WEDNESDAY = "WED",
    THURSDAY = "THU",
    FRIDAY = "FRI",
    SATURDAY = "SAT"
}

export enum PayTypes {
    STANDARD = "Standard",
    OVERTIME = "Overtime",
    HOLIDAY = "Holiday",
    WEEKEND = "Weekend",
    BH = "Bh",
    SUSPENSION = "Suspension",
    INTERNAL_STANDARD = "Internal standard",
    SUPERVISOR_STANDARD = "Supervisor standard",
    NSP = "Nsp",
    INDUCTION_TRAINING = "Induction training",
    TRAINING = "Training",
    SP = "Sp",
    STANDARD_BONUS = "Standard bonus",
    SPECIAL_BONUS = "Special bonus",
    INTERNAL_OVERTIME = "Internal overtime",
    INTERNAL_PERMANENT = "Internal permanent",
    SUPERVISOR_OVERTIME = "Supervisor overtime",
    SUPERVISOR_PERMANENT = "Supervisor permanent",
    COVID = "Covid",
    EXPENSES = "Expenses",
}

export enum PensionStatus {
    OPTED_OUT = "OPTED_OUT",
    OPTED_IN = "OPTED_IN"
}

export enum BookingFormat {
    HEADS = "HEADS",
    HOURS = "HOURS"
}

/**
 * Enumeration representing different calculation pathways.
 * Each pathway consists of two flags: (i.e. "n1|n2")
 * - n1: Value of holiday_activation flag
 * - n2: Value of holiday_cost_removed flag
 */
export enum CalculationPathWays {
    YESYES = "1|1", // Both holiday_activation and holiday_cost_removed flags are activated
    YESNO = "1|0",  // Only holiday_activation flag is activated
    NOYES = "0|1",  // Only holiday_cost_removed flag is activated
    NONO = "0|0",   // Neither holiday_activation nor holiday_cost_removed flags are activated
}

export enum WorkerSupervisorStatus {
    FRONT_OFFICE = "FRONT_OFFICE",
    BACK_OFFICE = "BACK_OFFICE"
}

export enum AuthType {
    LEGACY = "legacy",
    FIREBASE = "firebase"
}