/**
 * All the common Utility methods which are used in the service.
 */
import * as winston from "winston";
const AWS = require('aws-sdk');
import { logger } from './logger';
import { BadRequestError } from '../common/errors';
import { config } from '../configurations';
import { MimeType, ErrorResponse, SchemaValidationErrorCode, DeviceTokensByLanguage, Languages, dateTimeFormates } from '../common';
import { getAllSites } from '../services';
let moment = require('moment');
const jwt = require("jsonwebtoken");
const s3 = new AWS.S3({
    accessKeyId: config.ACCESS_KEY_ID,
    secretAccessKey: config.SECRET_ACCESS_KEY,
    region: config.S3_REGION
});
const JoiBase = require("joi");
const Bugsnag = require('@bugsnag/js');
const crypto = require('crypto');

import { getConnection } from 'typeorm';
import { BookingFormat, CalculationPathWays, PayTypes } from "../common/enum";

// AWS.config.update()

// Add interface at the top of the file
interface LogMetadata {
    req: {
        headers: {
            'correlation_id'?: string;
            'user_id'?: string;
            'user-agent'?: string;
        };
        url?: string;
        method?: string;
    };
    correlation_id?: string;
    user_id?: string;
    path_info?: string;
    user_agent?: string;
    method?: string;
}

interface LogInfo extends winston.Logform.TransformableInfo {
    metadata: LogMetadata;
}

// Update the function signature
export const addCustomData = winston.format((info: LogInfo) => {
    try {
        info.metadata.correlation_id = info.metadata.req.headers['correlation_id'] || '-'
        info.metadata.user_id = info.metadata.req.headers['user_id'] || '-'
        info.metadata.path_info = info.metadata.req.url || '-'
        info.metadata.user_agent = info.metadata.req.headers['user-agent'] || '-'
        info.metadata.method = info.metadata.req.method || '-'
    } catch (error) {
        logger.debug("Correlation ID not found", { 'error': error });
    }
    return info;
});


/**
 * Ignore log messages if they have { private: true }
 * @param  {} (info
 * @param  {} opts
 */
export const ignorePrivate = winston.format((info, opts) => {
    if (info.private) { return false; }
    return info;
});


/**
 * Return response for system status APIs.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const service_status = (req, res, next) => {
    res.status(204).json();
}


/**
 * Validate Payload with the Joi schema
 * @param  {} joiSchema
 * @param  {} payload
 */
export const validateRequestData = async (joiSchema, payload, isArrayValidation = false) => {
    let validatedData = isArrayValidation ? await JoiBase.array().items(joiSchema).validate(payload) : joiSchema.validate(payload);

    // Return validated data if validated data doen't contain any error.
    if (!validatedData.error) return validatedData.value;

    // Throw bad request if schema validation gets failed.
    throw new BadRequestError(
        "BAD_REQUEST",
        validatedData.error.details ? fetchJoiSchemaValidationErrors(validatedData.error.details) : validatedData.error.message
    );
};


/**
 * Remove extra fields context and type from Joi schema error details
 * @param  {} data
 */
let fetchJoiSchemaValidationErrors = (data) => {
    data.forEach((obj) => {
        delete obj.context;
        delete obj.type;
    });
    return data;
};


/**
 * Return object with required headers
 * @param  {} correlation_id
 * @param  {} request_user_id
 */
export let getHeaders = (correlation_id, request_user_id) => {
    return {
        'CORRELATION_ID': correlation_id,
        'USER_ID': request_user_id.toString(),
        'Content-Type': "application/json",
    }
}


/**
 * Return whole API endpoint with combining port, host and endpoint
 * @param  {} endpoint
 * @param  {} host
 * @param  {} port
 */
export const getEndpoint = (endpoint, host, port) => {
    return `http://${host}:${port}/${endpoint}`
}


/**
 * Validate token and return decoded token data.
 * @param  {string} token
 */
export const verifyJwtToken = (token: string) => {
    try {
        token = token.replace("Bearer ", "");
        let tokenData = jwt.verify(token, config.JWT_TOKEN_KEY);

        // Remove extra JWT token keys
        delete tokenData.iat;
        delete tokenData.exp;

        // Return user object data
        return tokenData;
    } catch (err) {
        return null;
    }
};

/**
 * Enhanced JWT token verification that distinguishes between expired and invalid tokens
 * @param  {string} token
 * @returns {object} { valid: boolean, expired: boolean, data?: any, error?: string }
 */
export const verifyJwtTokenDetailed = (token: string): {
    valid: boolean;
    expired: boolean;
    data?: any;
    error?: string;
} => {
    try {
        token = token.replace("Bearer ", "");
        let tokenData = jwt.verify(token, config.JWT_TOKEN_KEY);

        // Remove extra JWT token keys
        delete tokenData.iat;
        delete tokenData.exp;

        // Return successful verification
        return {
            valid: true,
            expired: false,
            data: tokenData
        };
    } catch (err) {
        // Check if the error is specifically due to token expiration
        if (err.name === 'TokenExpiredError') {
            return {
                valid: false,
                expired: true,
                error: 'Token expired'
            };
        }

        // Check if it's a JWT format error (not a JWT token at all)
        if (err.name === 'JsonWebTokenError') {
            return {
                valid: false,
                expired: false,
                error: 'Invalid token format'
            };
        }

        // Other JWT errors (signature verification failed, etc.)
        return {
            valid: false,
            expired: false,
            error: err.message || 'Token verification failed'
        };
    }
};

export const getSignedUrlForGetObject = async (bucket, folder, fileName) => {
    try {
        const params = {
            Bucket: bucket,
            Key: `${folder}/${fileName}`,
            Expires: config.SIGNED_URL_EXPIRE_TIME
        };
        const url = s3.getSignedUrl('getObject', params);
        return {
            url,
        };
    } catch (error) {
        notifyBugsnag(error);
        throw error;
    }
}

export const getObject = async (bucket, folder, fileName) => {
    try {
        const params = {
            Bucket: bucket,
            Key: `${folder}/${fileName}`,
        };
        return (await (s3.getObject(params).promise())).Body;
    } catch (error) {
        notifyBugsnag(error);
        throw error;
    }
}

export const getSignedUrlForPutObject = async (bucket, folder, fileName, fileType, expireSeconds = 1800, ACL = 'bucket-owner-full-control') => {
    try {
        const contentType = MimeType[fileType.toUpperCase()];
        const params = {
            Bucket: bucket,
            Key: `${folder}/${fileName}`,
            Expires: expireSeconds,
            ACL,
            ContentType: contentType,
        };
        const promise = new Promise((resolve, reject) => {
            s3.getSignedUrl('putObject', params, (err, uri) => (err ? reject(err) : resolve(uri)));
        });
        const url = await promise;
        return {
            url,
            contentType,
        };
    } catch (error) {
        notifyBugsnag(error);
        throw error;
    }
}

export const uploadFileOnS3 = async (bucket, folder, fileName, fileType, fileContent) => {
    try {
        const contentType = MimeType[fileType.toUpperCase()];
        const params = {
            Bucket: bucket,
            Key: `${folder}/${fileName}`,
            Body: fileContent,
            ContentType: contentType,
        };
        const promise = new Promise((resolve, reject) => {
            s3.upload(params, (err, data) => (err ? reject(err) : resolve(data.Location)));
        });
        const s3Data = await promise;
        return {
            location: s3Data,
            contentType,
        };
    } catch (error) {
        notifyBugsnag(error);
        throw error;
    }
}

export const deleteObject = async (bucket, folder, fileName) => {
    try {
        const params = {
            Bucket: bucket,
            Key: `${folder}/${fileName}`,
        };
        const promise = new Promise((resolve, reject) => {
            s3.deleteObject(params, (err, data) => (err ? reject(err) : resolve(data)));
        });
        await promise;
    } catch (error) {
        notifyBugsnag(error);
        throw error;
    }
}

export const snakeCaseToCamelCase = (key) => {
    return key.toLowerCase().replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase());
}

export const getOffsetAsPerPage = (page: number, limit: number) => {
    return (page - 1) * limit + 1
}

export const getWeeksOfTwoDates = (startDate: string, endDate: string = moment.utc()) => {
    return moment(endDate || moment.utc()).diff(startDate, "week") + 1;
}

export const dayRangeAsPerDayCount = (count: number) => {
    switch (true) {
        case (1 <= count && count <= 2): {
            return '1-2'
        }
        case (3 <= count && count <= 4): {
            return '3-4'
        }
        case (5 <= count && count <= 8): {
            return '5-8'
        }
        case (9 <= count && count <= 12): {
            return '9-12'
        }
        case (13 <= count && count <= 16): {
            return '13-16'
        }
        case (17 <= count && count <= 26): {
            return '17-26'
        }
        case (27 <= count && count <= 52): {
            return '27-52'
        }
        case (52 <= count): {
            return '52+'
        }
    }
};

export const removeKeyFromObject = (existingKey: string, newKey: string, objectValue: any) => {
    objectValue[newKey] = objectValue[existingKey];
    delete objectValue[existingKey];
    return objectValue
}

// will remove in future
export const objectToMySQLConditionString = (obj: any, whereClauseString: string = "") => {
    for (let key in obj) {
        whereClauseString += key + "=" + obj[key] + " AND "
    }
    return whereClauseString ? whereClauseString.substring(0, whereClauseString.length - 4) : "";
};

export const objectToMySQLWhereClause = (obj: any, whereClauseString: string = "", whereClauseValue: any = {}) => {
    for (let key in obj) {
        let queryKey = key.replace(/\./g, "_");
        whereClauseString += `${key} = :${queryKey} AND `;
        whereClauseValue[queryKey] = obj[key];
    }
    return {
        "whereClause": whereClauseString ? whereClauseString.substring(0, whereClauseString.length - 4) : "",
        "whereClauseValue": whereClauseValue
    };
};

export const getWeekWiseWorkingDays = arr => {
    let arrayLength = arr.filter(x => x > 0).length;
    return (1 <= arrayLength && arrayLength <= 3) ? '1-3' : (arrayLength >= 4) ? '4+' : null
}


export const notifyBugsnag = (error, url = '') => {
    logger.info("error", {
        message: error.message
    })
    console.error("\nnotifyBugsnag: error :--> ", error.message);
    const errorKeys = Object.keys(ErrorResponse);
    const errorCodes = [];
    errorKeys.map((key) => {
        errorCodes.push(ErrorResponse[key].error);
    });
    if (!errorCodes.includes(error.error)) {
        Bugsnag.addMetadata('request', { url: url })
        Bugsnag.notify(error);
    }
}

export const snakeCaseToPascalCase = (key) => {
    let camelCase = snakeCaseToCamelCase(key);
    return camelCase[0].toUpperCase() + camelCase.substr(1);
}

/**
 * Convert array to object
 * @param  {} arr
 * @param  {} key
 */
export const arrayToObject = (arr, key) => Object.assign({}, ...arr.map(item => ({ [item[key]]: item })))


/**
 * Convert array of objects to objects
 * @param  {} payload
 * @param  {} key
 * @param  {} valueKey
 */
export const arrayOfObjectsToObject = (payload, key, valueKey) => {
    return Object.assign({}, ...payload.map(state => {
        return { [state[key]]: state[valueKey] }
    }))
}

export const getWeeklabels = (numberOfWeeks) => {
    let weeks = [];
    for (let i = 1; i <= numberOfWeeks; i++) {
        weeks.push("Week " + i);
    }
    return weeks;
}

/**
 * Cast value to the number and return 0 if value is not valid num
 * @param  {} value
 */
export const getNumberValue = (value) => {
    return parseFloat(value) || 0;
}


export const addTrendsAnalysisRegion = async (selectQuery, region_id, client_id) => {
    if (region_id) {
        let siteDetails: any = await getAllSites(client_id, region_id);
        let site = siteDetails[1].sites;
        site.map((key) => {
            selectQuery = selectQuery.andWhere(`site_id = ('${key.id}')`)
        });
    }
    return selectQuery;
}

// will remove in future
export const objectToMySQLConditionStrings = (obj: any, whereClauseString: string = "") => {
    for (let key in obj) {
        if (key == 'client_id') {
            whereClauseString += "time_and_attendance_data." + key + "=" + obj[key] + " AND "
        } else if (key == 'region_id') {
            whereClauseString += "site." + key + "=" + obj[key] + " AND "
        } else {
            whereClauseString += key + "=" + obj[key] + " AND "
        }
    }
    return whereClauseString ? whereClauseString.substring(0, whereClauseString.length - 4) : "";
};

// will remove in future
export const objectToMySQLConditionStringForTAData = (obj: any, whereClauseString: string = "") => {
    for (let key in obj) {
        whereClauseString += "tadata." + key + "=" + obj[key] + " AND "
    }
    return whereClauseString ? whereClauseString.substring(0, whereClauseString.length - 4) : "";
};

export const objectToMySQLConditionStringForTADataWhereClause = (obj: any, whereClauseString: string = "", whereClauseValue: any = {}) => {
    for (let key in obj) {
        let queryKey = key.replace(/\./g, "_");
        whereClauseString += `tadata.${key} = :${queryKey} AND `;
        whereClauseValue[queryKey] = obj[key];
    }
    return {
        "whereClauseForTA": whereClauseString ? whereClauseString.substring(0, whereClauseString.length - 4) : "",
        "whereClauseValueForTA": whereClauseValue
    };
};

export const createTaxYearPayload = (obj: any) => {
    let tax_year_data = {};
    for (let i = 2; i <= 8; i++) {
        tax_year_data['202' + i + '-' + '202' + (i + 1)] = obj;
    }
    tax_year_data['2029-2030'] = obj;
    for (let i = 0; i <= 1; i++) {
        tax_year_data['203' + i + '-' + '203' + (i + 1)] = obj;
    }
    return tax_year_data;
};


export const getCurrentFinancialYear = (start_date) => {
    var check = moment(start_date, 'YYYY/MM/DD');
    var month = parseInt(check.format('M'));
    var day = parseInt(check.format('D'));
    var year = parseInt(check.format('YYYY'));

    let tax_year;
    let financial_year;

    if (month >= 1 && month <= 3) {
        tax_year = year - 1;
    } else if (month >= 5 && month <= 12) {
        tax_year = year + 1;
    } else {
        if (day < 6) {
            tax_year = year - 1;
        } else {
            tax_year = year + 1;
        }
    }

    if (tax_year > year) {
        financial_year = year.toString() + '-' + tax_year.toString();
    } else {
        financial_year = tax_year.toString() + '-' + year.toString();
    }
    return financial_year
}



/**
 * Provide difference between given date and today in YEAR.
 * date format: YYYY-MM-DD
 */
export const diffBetweenGivenDateAndTodayInYear = (date: string) => {
    let start_date = new Date(date);
    let today = new Date();
    start_date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    let diff_in_years = (today.getTime() - start_date.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    return Math.floor(diff_in_years);
};

/**
 * Provide difference between given 2 dates in Days
 * date format: YYYY-MM-DD
 * endDate - startDate
 */
export const diffBetweenGivenTwoDatesInDays = (startDate: string, endDate: string, dateFormate = dateTimeFormates.YYYYMMDD) => {
    // Old
    // let start_date = new Date(startDate);
    // let end_date = new Date(endDate);
    // return Math.floor((end_date.getTime() - start_date.getTime()) / (1000 * 60 * 60 * 24));

    // New
    const startDateMoment = moment(startDate, dateFormate);
    const endDateMoment = moment(endDate, dateFormate);
    return endDateMoment.diff(startDateMoment, 'days');
};

/**
 * Provide Today's Date
 * date format: YYYY-MM-DD
 */
export const getTodaysDate = () => {
    return moment(new Date()).utc().format(dateTimeFormates.YYYYMMDD);
}

export const getUserByUserTypeId = (userTypeId) => {
    let userType = {
        '1': "Clearvue Admin",
        '2': "Client Admin",
        '3': "Agency Admin",
        '4': "Client Regional Admin",
        '5': "Client Site Admin",
        '6': "Agency Worker",
        '7': "Agency Site Admin",
        '8': "Agency Regional Admin",
        '9': "Message Admin"
    }
    return userType[userTypeId]
};

export const extractValuesFromString = (str: string, object: any) => {
    let tempStr = str;
    const matches = str.match(/:[a-zA-Z_]+/g) || []; // find all words starting with ":"
    const values = [];
    for (const match of matches) {
        const key = match.slice(1); // remove the ":" from the key
        if (object.hasOwnProperty(key)) {
            values.push(object[key]);
            tempStr = tempStr.replace(match, '?'); // replace the matched string with "?"
        }
    }
    return [tempStr, values];
};

export const applyCommonFiltersToQuery = (query, client_id, agency_id, site_id, start_date) => {
    return query
        .where('clientId = :client_id', { client_id })
        .andWhere('agencyId = :agency_id', { agency_id })
        .andWhere('siteId = :site_id', { site_id })
        .andWhere('startDate = :start_date', { start_date });
};


/**
 * Finds duplicate values of the 'national_insurance_number', 'email', and 'employeeId'
 * properties within a list of objects and returns them in an object with three arrays
 */
export const findDuplicatesKeyValuesInListOfObjects = (data: Array<any>, checkOnlyEmployeeId = false) => {
    const duplicates = {
        national_insurance_number: new Set(),
        email: new Set(),
        employee_id: new Set(),
    };

    const uniqueValues = {
        national_insurance_number: new Set(),
        email: new Set(),
        employee_id: new Set(),
    };

    if (checkOnlyEmployeeId) {
        for (let obj of data) {
            if (obj.employee_id !== null && obj.employee_id !== '' && uniqueValues.employee_id.has(obj.employee_id)) {
                duplicates.employee_id.add(obj.employee_id);
            } else {
                uniqueValues.employee_id.add(obj.employee_id);
            }
        }
    } else {
        for (let obj of data) {
            if (obj.national_insurance_number !== null && obj.national_insurance_number !== '' && uniqueValues.national_insurance_number.has(obj.national_insurance_number)) {
                duplicates.national_insurance_number.add(obj.national_insurance_number);
            } else {
                uniqueValues.national_insurance_number.add(obj.national_insurance_number);
            }

            if (obj.email !== null && obj.email !== '' && uniqueValues.email.has(obj.email)) {
                duplicates.email.add(obj.email);
            } else {
                uniqueValues.email.add(obj.email);
            }

            if (obj.employee_id !== null && obj.employee_id !== '' && uniqueValues.employee_id.has(obj.employee_id)) {
                duplicates.employee_id.add(obj.employee_id);
            } else {
                uniqueValues.employee_id.add(obj.employee_id);
            }
        }
    }

    return duplicates;
}

export const validateRateCardDataForDuplicates = async (objectsList) => {
    const errors = {
        performanceLowHighCheck: [],
        performanceOrderCheck: [],
        duplicatesCheck: [],
        invalidSupervisorRate: [],
        invalidWorkersRate: []
    };

    // Check for performance_low and performance_high together or both empty
    objectsList.forEach((obj, index) => {
        const hasPerformanceLow = obj.performance_low !== null && obj.performance_low !== '';
        const hasPerformanceHigh = obj.performance_high !== null && obj.performance_high !== '';

        if ((hasPerformanceLow && !hasPerformanceHigh) || (!hasPerformanceLow && hasPerformanceHigh)) {
            errors.performanceLowHighCheck.push(index + 2);
        }

        if (hasPerformanceLow && hasPerformanceHigh && obj.performance_low >= obj.performance_high) {
            errors.performanceOrderCheck.push(index + 2);
        }
    });

    if (errors.performanceLowHighCheck.length || errors.performanceOrderCheck.length) {
        delete errors.duplicatesCheck;
        delete errors.invalidSupervisorRate;
        delete errors.invalidWorkersRate;
        return errors;
    }

    // Check for duplicates based on pay, charge, supervisor_rate, and pay_type
    const seenCombinations = new Set();
    objectsList.forEach((obj, index) => {
        const combinationString = `${obj.pay}_${obj.charge}_${obj.supervisor_rate}_${obj.pay_type}`;
        if (seenCombinations.has(combinationString)) {
            errors.duplicatesCheck.push(index + 2);
        } else {
            seenCombinations.add(combinationString);
        }

        const isSupervisorPaytype = [
            PayTypes.SUPERVISOR_STANDARD, PayTypes.SUPERVISOR_OVERTIME, PayTypes.SUPERVISOR_PERMANENT,
            PayTypes.INTERNAL_STANDARD, PayTypes.INTERNAL_OVERTIME, PayTypes.INTERNAL_PERMANENT
        ].map(type => type.toLowerCase()).includes(obj.pay_type.toLowerCase())

        const supervisorRate = obj.supervisor_rate ? obj.supervisor_rate.toLowerCase() : "no";
        if (supervisorRate == "yes" && !isSupervisorPaytype) {
            errors.invalidSupervisorRate.push(index + 2);
        } else if (isSupervisorPaytype && supervisorRate == "no") {
            errors.invalidWorkersRate.push(index + 2);
        }

    });

    // Remove empty error arrays
    Object.keys(errors).forEach(key => {
        if (errors[key].length === 0) {
            delete errors[key];
        }
    });

    return errors;
};

/**
 * custom Error Message For Schema Validation With Value
 */
export const customErrorMessageForSchemaValidationWithValue = (errors) => {
    errors.forEach((err) => {
        if (err.code === SchemaValidationErrorCode.MIN_LIMIT) {
            err.message = `ValidationError: Please ensure that the '${err.local.key}' field contains at least ${err.local.limit} characters.`;
        }
        if (err.code === SchemaValidationErrorCode.UNKNOWN_COLUMN) {
            err.message = `Oops! CSV sheet contains unknown column: [ '${err.local.key}' ] `;
        }
        // err.message = `Invalid data for field '${err.local.key}': '${err.local.value}' `;
    });
    return errors;
}


/**
 * Split array with specific size
 */
export const splitArrayWithSize = (a, size) => {
    return Array.from(
        new Array(Math.ceil(a.length / size)),
        (_, i) => a.slice(i * size, i * size + size)
    );
}

export const getNewTransaction: any = () => {
    return getConnection().createQueryRunner();
};

export const stripEmojis = (str) => {
    if (str) {
        let houseNumberValue = str.replace(/[^\p{L}\p{N}\p{P}\p{Z}]/gu, '');
        return (houseNumberValue.trim() === '') ? null : houseNumberValue;
    }
    return str;
};

export const deviceTokenGroupByLanguages = async (workerDeviceTokensAndLanguage) => {
    const deviceTokensByLanguage: DeviceTokensByLanguage = workerDeviceTokensAndLanguage.reduce((tokensByLanguage, worker) => {
        const { device_token, language } = worker;

        if (device_token) {
            const lang = language === Languages.TIGRINYA ? Languages.ENGLISH : language || Languages.ENGLISH;

            if (!tokensByLanguage[lang]) {
                tokensByLanguage[lang] = [];
            }

            tokensByLanguage[lang].push(device_token);
        }

        return tokensByLanguage;
    }, {});

    return deviceTokensByLanguage
};

// check if string contains all english
export const isAllEnglish = async (text) => {
    const englishWordsRegex = /^[A-Za-z0-9\s!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]+$/;
    const isAllEnglish = englishWordsRegex.test(text);
    return isAllEnglish;
}

/**
 * Finds duplicate combinations of 'id' and 'supervisor_flag' fields values
 */
export const findDuplicatesKeyValuesInListOfObject = (data: Array<any>) => {
    const duplicates = {
        id_supervisor_flag: new Set(),
        id: new Set(),
    };

    const uniqueValues = {
        id_supervisor_flag: new Set(),
        id: new Set(),
    };

    for (let obj of data) {
        const idSupervisorFlag = `${obj.id}_${obj.supervisor_flag}`;

        if (obj.id !== null && obj.id !== '' && obj.supervisor_flag !== null && obj.supervisor_flag !== '' && uniqueValues.id_supervisor_flag.has(idSupervisorFlag)) {
            duplicates.id_supervisor_flag.add(idSupervisorFlag);
            duplicates.id.add(obj.id);
        } else {
            uniqueValues.id_supervisor_flag.add(idSupervisorFlag);
            uniqueValues.id.add(obj.id);
        }
    }

    return duplicates;
}



/**
 * Get date ranges for four weeks based on the provided or current date.
 *
 * @param {string} [inputDate] - Optional. Date in "YYYY-MM-DD" format. If not provided, defaults to the current date.
 * @returns {Object} An object containing the day of the week and date ranges for four batches.
 * 
 * @example
 * const result = getDateRanges("2024-02-09");
 * Output: {
        weekday_start: 'THU',
        w4: { start_date: '2024-01-12', end_date: '2024-01-18' }
        w3: { start_date: '2024-01-19', end_date: '2024-01-25' },
        w2: { start_date: '2024-01-26', end_date: '2024-02-01' },
        w1: { start_date: '2024-02-02', end_date: '2024-02-08' },
    }
 */
export const getDateRanges = async (lastNWeeks = 3, inputDate = null) => {
    const providedDate = inputDate ? moment(inputDate) : moment(); // Adjust providedDate to be one day less
    const dayOfWeek = providedDate.format('ddd').toUpperCase();

    const dateToUse = providedDate.subtract(1, 'days');
    let batches = { weekday_start: dayOfWeek, w1: null, w2: null, w3: null, w4: null };

    for (let i = 1; i <= lastNWeeks; i++) {
        const end = dateToUse.clone().subtract((i - 1) * 7, 'days');
        const start = dateToUse.clone().subtract(i * 7, 'days').add(1, 'days');
        batches[`w${i}`] = { start_date: start.format('YYYY-MM-DD'), end_date: end.format('YYYY-MM-DD') };
    }

    return batches;
}

/**
 * Get the week range in which the provided date falls, based on the specified starting day of the week.
 *
 * @param {string} inputDate - Required. Date in "YYYY-MM-DD" format.
 * @param {string} week_day_start - Required. Day of the week to start the week (e.g., "MON", "TUE").
 * 
 * @example
 * const result = getDateRange("2024-02-09", "MON");
 * Output: { start_date: '2024-02-05' }
 */
export const getWeekRangeForGivenDate = (inputDate, week_day_start) => {
    const providedDate = moment(inputDate); // The provided input date
    const startDayOfWeek = moment().day(week_day_start).day(); // Get numeric representation of the week start day (0-6)

    // Adjust the provided date to fall within the week that starts on the given week_day_start
    const daysDifference = (providedDate.day() - startDayOfWeek + 7) % 7; // Difference in days to move back to week start
    const startOfWeek = providedDate.clone().subtract(daysDifference, 'days'); // Start date of the week
    const endOfWeek = startOfWeek.clone().add(6, 'days'); // End date of the week (6 days after start)

    return {
        start_date: startOfWeek.format('YYYY-MM-DD'),
        end_date: endOfWeek.format('YYYY-MM-DD')
    };
};


/**
 * Find the yearly rule that matches the given date range
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {Object} yearlyRules - Object of yearly rule objects keyed by year
 * @returns {Object|null} Matching rule or null if not found
 */
export const findMatchingYearlyRule = (startDate, endDate, yearlyRules) => {
    const startMoment = moment(startDate);
    const endMoment = moment(endDate);

    // Convert object to array of values for iteration
    return Object.values(yearlyRules).find(rule => {
        const ruleStartDate = moment(rule["start_date"]);
        const ruleEndDate = moment(rule["end_date"]);

        return startMoment.isSameOrAfter(ruleStartDate) &&
            endMoment.isSameOrBefore(ruleEndDate);
    }) || null;
};


export const findMatchingFinancialRule = (startDate, endDate, financialRules) => {
    const startMoment = moment(startDate);
    const endMoment = moment(endDate);

    return financialRules.find(rule => {
        const ruleStartDate = moment(rule["start_date"]);
        const ruleEndDate = moment(rule["end_date"]);

        return startMoment.isSameOrAfter(ruleStartDate) &&
            endMoment.isSameOrBefore(ruleEndDate);
    }) || {};
};

/**
 * Calculate the number of weeks between two dates
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {Object|null} matchedRule - Matching yearly rule if exists
 * @returns {number} Number of weeks
 */
export const calculateWeeksBetweenDates = (startDate, endDate, matchedRule = null) => {
    if (matchedRule) {
        // If we have a matched rule, calculate exact weeks between dates
        const startMoment = moment(matchedRule["start_date"]);
        const endMoment = moment(endDate);
        const totalDays = endMoment.diff(startMoment, 'days') + 1;
        return totalDays / 7;
    } else {
        // Calculate weeks based on financial year logic
        const startMoment = moment(startDate);
        const year = startMoment.year();

        const firstJanuary = moment(`${year}-01-01`);
        const fifthApril = moment(`${year}-04-05`);

        const daysDifference = fifthApril.diff(firstJanuary, 'days');
        const weekCount = Math.floor(
            (daysDifference + (firstJanuary.day() + 1) + (fifthApril.day() + 1)) / 7
        );

        return startMoment.clone().subtract(weekCount, 'weeks').week();
    }
};

export const validateDateRangeWithYearlyRules = (startDate, endDate, yearlyRules) => {
    const matchedRule = findMatchingYearlyRule(startDate, endDate, yearlyRules);

    // Calculate weeks if validation passes
    const weeks = calculateWeeksBetweenDates(startDate, endDate, matchedRule);
    return [200, null, { weeks, matchedRule }];
};

// Pre-calculate weeks for all unique date pairs
export const calculateWeeksForAllDatePairs = async (allDates, yearlyRules) => {
    // Create a map to store unique date combinations
    const dateMap = new Map();

    // Collect all unique start/end date pairs
    allDates.forEach(p => {
        const key = `${p.start_date}|${p.end_date}`;
        dateMap.set(key, { startDate: p.start_date, endDate: p.end_date });
    });

    // Calculate weeks for all unique combinations
    const weekMap = new Map();
    dateMap.forEach((dates, key) => {
        const matchedRule = findMatchingYearlyRule(dates.startDate, dates.endDate, yearlyRules);
        const week = calculateWeeksBetweenDates(dates.startDate, dates.endDate, matchedRule);
        weekMap.set(key, week);
    });

    return weekMap;
};

// Helper function to format numbers according to the booking format
export const formatNumber = (number, format) => {
    return format !== BookingFormat.HEADS ? parseFloat(parseFloat(number).toFixed(2)) : parseInt(number, 10);
}

// Helper function to format numbers according to the booking format
export const findCalculatedPathway = async (holiday_activation, holiday_cost_removed) => {
    if (holiday_activation == 1 && holiday_cost_removed == 0) {
        return CalculationPathWays.YESNO;
    } else if (holiday_activation == 1 && holiday_cost_removed == 1) {
        return CalculationPathWays.YESYES;
    } else if (holiday_activation == 0 && holiday_cost_removed == 1) {
        return CalculationPathWays.NOYES;
    } else {
        return CalculationPathWays.NONO;
    }
}


export const dynamicErrorObject = async (objectOfError, values) => {
    return {
        ...objectOfError,
        "message": objectOfError.message.replace("$CustomValues", values)
    };
}

export const getPreviousDateByWeeks = async (date, numberOfWeeks) => {
    // Parse the given date
    const givenDate = moment(date, dateTimeFormates.YYYYMMDD);

    // Subtract the number of weeks from the given date
    const previousDate = givenDate.subtract(numberOfWeeks, 'weeks');

    // Return the new date formatted as 'YYYY-MM-DD'
    return previousDate.format(dateTimeFormates.YYYYMMDD);
};

export const getApplicableMargin = (margins, lengthOfService) => {
    if (!margins || !margins.length) {
        return null;
    }

    // Sort margins by specificity
    const defaultAssociationMargin = margins.find(m => !m.siteId && !m.los);
    const defaultSiteMargin = margins.find(m => m.siteId && !m.los);
    const losMargins = margins.filter(m => m.siteId && m.los)
        .sort((a, b) => parseFloat(a.los) - parseFloat(b.los));

    // If no LOS margins exist, return site default or association default
    if (!losMargins.length) {
        return defaultSiteMargin || defaultAssociationMargin;
    }

    // Find applicable LOS margin
    const years = lengthOfService.years + (lengthOfService.months / 12);

    for (let i = 0; i < losMargins.length; i++) {
        const currentLos = parseFloat(losMargins[i].los);
        const nextLos = losMargins[i + 1] ? parseFloat(losMargins[i + 1].los) : Infinity;

        if (years >= currentLos && years < nextLos) {
            return losMargins[i];
        }
    }

    // If worker's LOS is higher than all defined LOS margins, use the highest LOS margin
    if (years >= parseFloat(losMargins[losMargins.length - 1].los)) {
        return losMargins[losMargins.length - 1];
    }

    // If worker's LOS is lower than all defined LOS margins, use site default or association default
    return defaultSiteMargin || defaultAssociationMargin;
};

/**
 * startDate and endDate are in "YYYY-MM-DD" format 
 */
export const getDurationBetweenDates = (startDate, endDate) => {
    let duration = moment.duration(moment.max(moment(endDate)).diff(moment.min(moment(startDate))));
    return {
        "years": duration.years() || 0,
        "months": duration.months() || 0,
        "weeks": duration.weeks() || 0,
        "asYears": duration.asYears() || 0, // Total years
        "asMonths": duration.asMonths() || 0, // Total months
        "asWeeks": duration.asWeeks() || 0 // Total weeks
    };
}
export const generateMaskedAccountIdentifier = (sortCode, accountNumber, partiallyMasked = false) => {
    if (!sortCode || !accountNumber) {
        return null;
    }

    if (partiallyMasked) {
        const maskedSortCode = `${sortCode[0]}****${sortCode[5]}`;
        const maskedAccountNumber = `${accountNumber[0]}${'*'.repeat(accountNumber.length - 2)}${accountNumber[accountNumber.length - 1]}`
        return `${maskedSortCode}-${maskedAccountNumber}`;
    }

    // Concatenate sort code and account number before hashing
    const combinedBankDetails = `${sortCode}:${accountNumber}`;

    // Use SHA-256 for consistent, one-way hash generation
    const hashedValue = crypto.createHash('sha256')
        .update(combinedBankDetails)
        .digest('hex');

    // Optional: Take first 16 characters for shorter representation
    return hashedValue.substring(0, 16);
}

/**
 * Converts internal pay types to their supervisor counterparts
 * @param {string} payType - The pay type to convert
 * @returns {string} The converted pay type or original if no conversion needed
 */
export const convertInternalPayTypeToSupervisor = async (payType) => {
    const payTypeLower = payType?.toLowerCase();
    switch (payTypeLower) {
        case PayTypes.INTERNAL_STANDARD.toLowerCase():
            return PayTypes.SUPERVISOR_STANDARD;
        case PayTypes.INTERNAL_OVERTIME.toLowerCase():
            return PayTypes.SUPERVISOR_OVERTIME;
        case PayTypes.INTERNAL_PERMANENT.toLowerCase():
            return PayTypes.SUPERVISOR_PERMANENT;
        default:
            return payType;
    }
};