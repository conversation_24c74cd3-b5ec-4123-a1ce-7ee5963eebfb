const sgMail = require("@sendgrid/mail");
import { BadRequestError, SendgridEmailTemplateDTO, SendgridBulkEmailTemplateDTO } from "../common";
import { config } from "../configurations";

/**
 * Method to send template Email from the configured Sendgrid account.
 * @param  {SendgridEmailTemplateDTO} payload
 */
export const sendTemplateEmail = async (payload: SendgridEmailTemplateDTO) => {
    try {
        let toEmail: any;
        if (Array.isArray(payload.toEmailId)) {
            toEmail = payload.toEmailId;
        } else {
            toEmail = { email: payload.toEmailId }
        }
        const message = {
            from: config.Sendgrid.FROM_EMAIL,
            template_id: payload.templateId,
            personalizations: [{
                to: toEmail,
                dynamic_template_data: payload.dynamicTemplateData
            }],

        };

        await sgMail.setApiKey(config.Sendgrid.API_KEY);
        await sgMail.send(message);
    }
    catch (err) {
        throw new BadRequestError("SENDGRID_BAD_REQUEST", "Email not sent.")
    }
}


/**
 * Method for Sending the same email to multiple recipients with dynamic template data
 * @param  {SendgridBulkEmailTemplateDTO} payload
 */
export const sendTemplateEmailInBulk = async (payload: SendgridBulkEmailTemplateDTO) => {
    try {
        const { personalizations } = payload;
        const chunkSize = 1000;
        const totalChunks = Math.ceil(personalizations.length / chunkSize);

        await sgMail.setApiKey(config.Sendgrid.API_KEY);

        for (let i = 0; i < totalChunks; i++) {
            const chunk = personalizations.slice(i * chunkSize, (i + 1) * chunkSize);
            const message = {
                from: config.Sendgrid.FROM_EMAIL,
                template_id: payload.templateId,
                personalizations: chunk
            };
            await sgMail.send(message);
        }
    }
    catch (err) {
        throw new BadRequestError("SENDGRID_BAD_REQUEST", "Email not sent.");
    }
};
