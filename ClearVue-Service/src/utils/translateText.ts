const { Translate } = require("@google-cloud/translate").v2;
import { config } from "../configurations";
import { Languages } from '../common';
import { isAllEnglish } from '../utils';

const translate = new Translate({
    projectId: config.TRANSLATE_PROJECT_ID,
    credentials: JSON.parse(config.TRANSLATE_KEY_DATA),
});

// Translate message
export const translateText = async (message, from, to) => {
    try {
        // Translates text into the target language
        if (from === to) {
            return message;
        }
        const [translation] = await translate.translate(message, { from: from, to: to });
        return translation;
    } catch (error) {
        console.error(`Error: ${error}`);
        return error;
    }
}

// Translate message
export const translateTextToListOfLanguages = async (message: string, from: string, toLanguages: Array<string>) => {
    try {

        const translationPromises = toLanguages.map(async (to) => {
            // Skip translation if source and target languages are the same
            if (from === to) {
                return { language: to, translation: message };
            }
            const [translation] = await translate.translate(message, { from, to });
            return { language: to, translation };
        });

        // Execute all translation requests concurrently
        const translations = await Promise.all(translationPromises);

        const translationObject = translations.reduce((obj, { language, translation }) => {
            obj[language] = translation;
            return obj;
        }, {});

        return translationObject;
    } catch (error) {
        console.error(`Error: ${error}`);
        return error;
    }
};


export const detectNonEnglishText = async (strings: Array<string>) => {

    const filteredList = strings.filter(item => item.trim() !== '');

    // Initialize the Google Cloud Translation client
    try {
        // Detect the language for each string in the list
        const detections = await Promise.all(
            filteredList.map(async (text) => {
                const [detection] = await translate.detect(text);
                return { text, language: detection.language, confidence: detection.confidence, };
            })
        );

        let nonEnglishFlag = 0;
        for (const obj of detections) {
            if (obj.language !== Languages.ENGLISH) {
                const numberOfWords = obj.text.split(/\s+|[,.;?!]+/).filter(word => word !== "").length;

                if (numberOfWords > 3 || !await isAllEnglish(obj.text)) {
                    nonEnglishFlag = 1;
                    break;
                }
            }
            else if (obj.confidence !== 1 && !await isAllEnglish(obj.text)) {
                nonEnglishFlag = 1;
                break;
            }
        }

        return nonEnglishFlag;
    } catch (error) {
        console.error("Error detecting languages:", error);
        throw error;
    }
}