import { default as sqlstring } from "sqlstring";


/**
 * Method escape sqlstring values in the request URL params and body payload:
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const sqlEscape = (req, res, next) => {

    // Escape URL params
    if (req.query) {
        Object.keys(req.query).forEach(key => {
            if (req.query[key]) {
                req.query[key] = escapeSql(req.query[key]);
            }
        });
    };

    // Escape body payload
    if (req.body) {
        Object.keys(req.body).forEach(key => {
            if (req.body[key]) {
                req.body[key] = escapeSql(req.body[key]);
            }
        });
    };

    return next();
};


export const escapeSql = (value: any): string => {
    if (typeof value === 'string') {
        return sqlstring.escape(value)
            .replace(/\\\\([_%])/g, '\\$1') // escape backslashes before _ and % characters
            .replace(/\\\\/g, '\\\\\\\\') // escape backslashes
            .replace(/\\'/g, "'")
            .slice(1, -1); // Remove the extra quotes
    } else {
        return value;
    }
};
