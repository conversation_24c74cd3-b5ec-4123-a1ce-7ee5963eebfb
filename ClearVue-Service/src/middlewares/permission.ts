import { ErrorResponse } from "../common/errors";
import { getPermissionsByUserTypeAndFeatureId, getUserById } from "./../models";
import { UserType } from "../common/enum";


/**
 * Method to check user permission
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const checkPermission = (allowedUserTypes) => {
    return async function (req, res, next) {
        const loggedInUser = req.user;

        /* Check whether the user is exist or not. if not then throw user not found. */
        let userDetails = await getUserById(loggedInUser.user_id);
        if (!userDetails) {
            return [403, ErrorResponse.PermissionDenied]
        }

        if (allowedUserTypes.includes(parseInt(loggedInUser.user_type_id))) {
            return next();
        }
        return res.status(403).send(ErrorResponse.PermissionDenied);
    }
};