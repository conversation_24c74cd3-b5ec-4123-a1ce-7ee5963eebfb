import { ErrorResponse } from "../common/errors";
import { getAgencyClientsByUserId } from "./../models";
import { UserType } from "../common/enum";

/**
 * Middleware to check client authorization
 */
export const checkClientAuth = () => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      const userTypeId = Number(user.user_type_id);
      const clientIdFromUser = Number(user.client_id);
    //   const clientIdFromParam = Number(req.params.clientId) || Number(req.query.client_id);
      const paramClientIdRaw =
        req.params?.clientId ?? req.query?.client_id;
      const clientIdFromRequest = paramClientIdRaw != null ? Number(paramClientIdRaw) : null;

      // 1. ClearVue Admin: Full Access
      if (userTypeId === UserType.CLEARVUE_ADMIN) {
        return next();
      }

      // 2. Client Users: Must match clientId directly
      const clientRoles = [
        UserType.CLIENT_ADMIN,
        UserType.CLIENT_REGIONAL,
        UserType.CLIENT_SITE,
      ];
      if (clientRoles.includes(userTypeId) && clientIdFromUser === clientIdFromRequest) {
        return next();
      }

      // 3. Agency Users: Check association via DB
      const agencyRoles = [
        UserType.AGENCY_ADMIN,
        UserType.AGENCY_REGIONAL,
        UserType.AGENCY_SITE,
      ];
      if (agencyRoles.includes(userTypeId)) {
        const associatedClients = await getAgencyClientsByUserId(user.user_id);

        if (!Array.isArray(associatedClients) || associatedClients.length === 0) {
          return res.status(403).json(ErrorResponse.PermissionDenied);
        }

        for (const row of associatedClients) {
          if (Number(row.client_id) === clientIdFromRequest) {
            return next();
          }
        }

        return res.status(403).json(ErrorResponse.PermissionDenied);
      }

      // 4. Default Deny
      return res.status(403).json(ErrorResponse.PermissionDenied);
    } catch (error) {
      console.error("Auth Middleware Error:", error);
      return res.status(500).json({ message: "Unexpected error" });
    }
  };
};
