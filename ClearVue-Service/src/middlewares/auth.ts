import { ErrorResponse } from "../common/errors";
import { verifyJwtToken, verifyJwtTokenDetailed } from "../utils";
import { config } from "../configurations";
import * as admin from 'firebase-admin';
import { getUserByEmail } from "../models";
import { isMfaEnabled, getUserMfaStatus } from "../services/firebaseMfa";
import { Config } from "aws-sdk";
import {
	updateUserActivity,
	checkUserActivityTimeouts
} from "../models";
import { mfa } from "../common/constants/routes";

/**
 * Method to verify Firebase ID token
 */
const verifyFirebaseToken = async (token: string) => {
	try {
		if (!config.FIREBASE_AUTH_ENABLED) {
			return [true, null];
		}

		const decodedToken = await admin.auth().verifyIdToken(token);

		// If custom claims are missing, look up user in database
		let userData;
		if (!decodedToken.user_id || decodedToken.user_id === decodedToken.uid) {
			// Custom claims missing or incorrect, look up user in database
			try {
				const dbUser = await getUserByEmail(decodedToken.email);
				if (dbUser) {
					userData = {
						user_id: dbUser.id,
						user_type_id: dbUser.userTypeId,
						user_name: dbUser.name,
						client_id: dbUser.clientId,
						agency_id: dbUser.agencyId,
						email: dbUser.email,
						firebase_uid: decodedToken.uid,
						iat: decodedToken.iat,
						auth_time: decodedToken.auth_time, // Include auth_time for MFA checks
					};
				} else {
					console.error('User not found in database for email:', decodedToken.email);
					return [true, null];
				}
			} catch (error) {
				console.error('Error looking up user in database:', error);
				return [true, null];
			}
		} else {
			// Use custom claims from token
			userData = {
				user_id: decodedToken.user_id,
				user_type_id: decodedToken.user_type_id,
				user_name: decodedToken.user_name || decodedToken.name || decodedToken.email,
				client_id: decodedToken.client_id,
				agency_id: decodedToken.agency_id,
				email: decodedToken.email,
				firebase_uid: decodedToken.uid,
				iat: decodedToken.iat,
				auth_time: decodedToken.auth_time, // Include auth_time for MFA checks
			};
		}

		return [false, userData];
	} catch (error) {
		if (error.code === 'auth/id-token-expired') {
			return [true, 'auth/id-token-expired'];
		}
		console.error('Firebase token verification failed:', error);
		return [true, null];
	}
};

/**
 * Method to verify JWT token (supports both Firebase and Legacy tokens)
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const authorizeJwtToken = async (req, res, next) => {
	let token = req.headers["authorization"];

	// Return Forbidden error response if token is not provided
	if (!token) {
		return res.status(403).send(ErrorResponse.Forbidden);
	}

	token = token.replace("Bearer ", "");

	// If Firebase is enabled, try Firebase token verification first
	if (config.FIREBASE_AUTH_ENABLED) {
		const [isError, firebaseTokenData] = await verifyFirebaseToken(token);
		if (!isError && firebaseTokenData) {

			const currentTime = Math.floor(Date.now() / 1000);
			const iat = firebaseTokenData.iat;
			const authTime = firebaseTokenData.auth_time;

			const userId = firebaseTokenData.user_id;
			const sessionId = firebaseTokenData.firebase_uid; // Use Firebase UID as session identifier

			// Check if this is an MFA-related endpoint that should skip activity timeout checks
			const isMfaEndpoint = req.path && (req.path.includes(mfa.ENROLLMENT_START));

			// Skip activity timeout checks for MFA endpoints to prevent deadlock
			if (!isMfaEndpoint) {
				// Get timeout configurations
				const inactivityTimeoutSeconds = Number(config.USER_INACTIVITY_TIMEOUT_FIREBASE);
				const maxSessionDurationSeconds = Number(config.USER_MAX_SESSION_DURATION_FIREBASE);

				try {
					// OPTIMIZED: Single database call to check both timeouts
					const timeoutChecks = await checkUserActivityTimeouts(
						userId,
						sessionId,
						inactivityTimeoutSeconds,
						maxSessionDurationSeconds
					);

					// Check inactivity timeout
					if (timeoutChecks.inactivityTimeout.isTimedOut) {
						return res.status(401).send({
							...ErrorResponse.UnauthorizedWithForcedRelogin,
							message: "Session expired due to inactivity. Please log in again.",
						});
					}

					// Check maximum session duration
					if (timeoutChecks.sessionDurationTimeout.isTimedOut) {
						return res.status(401).send(ErrorResponse.UnauthorizedWithForcedRelogin);
					}

					// Update user activity for this request (only if we have an activity record)
					if (timeoutChecks.activityRecord) {
						await updateUserActivity(
							userId,
							sessionId,
							new Date(authTime * 1000), // Convert Firebase timestamp to Date
							false // This is just an activity update, not a fresh login
						);
					}

				} catch (activityError) {
					console.error('Error checking user activity:', activityError);

					// Fallback to token-based checks if database activity tracking fails
					// Check token freshness (inactivity timeout) - fallback method
					if (iat && (currentTime - iat > inactivityTimeoutSeconds)) {
						return res.status(401).send({
							...ErrorResponse.UnauthorizedWithForcedRelogin,
							message: "Session expired due to inactivity. Please log in again."
						});
					}

					// Check maximum session duration - fallback method
					if (authTime && (currentTime - authTime > maxSessionDurationSeconds)) {
						return res.status(401).send(ErrorResponse.UnauthorizedWithForcedRelogin);
					}
				}
			} else {
				// For MFA endpoints, still update activity if possible but don't enforce timeouts
				try {
					await updateUserActivity(
						userId,
						sessionId,
						new Date(authTime * 1000), // Convert Firebase timestamp to Date
						false // This is just an activity update, not a fresh login
					);
				} catch (activityError) {
					// Silently fail for MFA endpoints - don't block the MFA flow
					console.warn('Could not update user activity for MFA endpoint:', activityError);
				}
			}

			// Standard token expiration check (for token refresh logic)
			if (currentTime - iat > Number(config.USER_ACCESS_TOKEN_EXPIRE_TIME.replace("s", ""))) {
				return res.status(401).send(ErrorResponse.Unauthorized);
			}

			req.user = firebaseTokenData;
			return next();
		} else if (isError && firebaseTokenData === 'auth/id-token-expired') {
			return res.status(401).send(ErrorResponse.Unauthorized);
		}

		// If Firebase is enabled but token verification failed, check if it's a legacy token
		const legacyTokenResult = verifyJwtTokenDetailed(token);

		if (legacyTokenResult.valid || legacyTokenResult.expired) {
			// Valid/Expired legacy token found while Firebase is enabled - force re-login
			return res.status(401).send(ErrorResponse.UnauthorizedWithForcedRelogin);
		}

		// Invalid token format (not a JWT token) - could be malformed Firebase token
		// Return standard unauthorized without force_relogin
		return res.status(401).send(ErrorResponse.Unauthorized);
	} else {
		// Firebase disabled, use legacy JWT verification
		let tokenData = verifyJwtToken(token);
		if (!tokenData) {
			return res.status(401).send(ErrorResponse.Unauthorized);
		}
		req.user = tokenData;
		return next();
	}
};


/**
 * Method to verify required authentication for scheduler events
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const schedulerEventsAuth = (req, res, next) => {
	let token = req.headers["authorization"];

	// Return Forbidden error response if token is not provided
	if (!token) {
		return res.status(403).send(ErrorResponse.Forbidden);
	}

	if (token !== config.CRONJOB_ACCESS_TOKEN) {
		return res.status(401).send(ErrorResponse.CronJobUnauthorizedError);
	}
	return next();
};
