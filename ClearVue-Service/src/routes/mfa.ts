/**
 * MFA routes for Firebase TOTP MFA operations
 */
import { authorizeJwtToken } from '../middlewares/auth';
import { checkPermission } from '../middlewares/permission';
import {
    startMfaEnrollmentAPI, completeMfaEnrollmentAPI,
    startMfaSignInAPI, completeMfaSignInAPI,
    getMfaStatusAPI, checkMfaRequiredAPI, deleteUserMfaEnrollmentAPI
} from '../api/mfa';
import { mfa } from '../common/constants/routes';
import { UserType } from '../common';

const express = require('express');
export const router = express.Router();

// Public routes (no authentication required)
router.get(mfa.STATUS, checkMfaRequiredAPI);
router.get(mfa.USER_STATUS, getMfaStatusAPI);

// Protected routes (require authentication)
router.post(mfa.ENROLLMENT_START, authorizeJwtToken, startMfaEnrollmentAPI);
router.post(mfa.ENROLLMENT_COMPLETE, completeMfaEnrollmentAPI);
router.post(mfa.SIGNIN_START, startMfaSignInAPI);
router.post(mfa.SIGNIN_COMPLETE, completeMfaSignInAPI);

// Admin only routes (require admin permissions)
router.delete(mfa.DELETE_USER_ENROLLMENT, authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN]), deleteUserMfaEnrollmentAPI);
