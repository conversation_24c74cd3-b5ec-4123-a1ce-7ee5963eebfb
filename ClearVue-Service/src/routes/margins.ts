import express from 'express';
export const router = express.Router();

import { checkPermission } from '../middlewares/permission';
import { authorizeJwtToken } from '../middlewares/auth';
import { margins, UserType } from '../common';
import {
    createMargins,
    getMarginsList,
    updateMargins,
    deleteMargins
} from '../api';

// APIs
router.route(margins.MARGINS)
    .post(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN]), createMargins)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getMarginsList)
    .delete(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN]), deleteMargins);

router.route(margins.MARGIN_BY_ID)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN]), updateMargins)