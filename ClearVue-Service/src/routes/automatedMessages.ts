/**
 * All the Automated Messages related APIs.
 */

const express = require('express');
export const router = express.Router();

import { schedulerEventsAuth } from './../middlewares/auth';
import { automatedMessages } from './../common';
import {
    sendTimelineCompletionMessages, sendBirthdayMessages, sendWorkerInactiveMessages,
    sendFirstDayWelcomeMessage, ftpUploadTimeAndAttendance,
    downloadPayrollSummary,
    bulkUploadWorkers,
    ftpGetPayrollInvoice,
    downloadPayrolDetialedSummary
} from '../api';

const upload = require("express-fileupload");

// APIs
router.route(automatedMessages.TIMELINE_MESSAGES)
    .post(schedulerEventsAuth, sendTimelineCompletionMessages);

router.route(automatedMessages.BIRTHDAY_MESSAGES)
    .post(schedulerEventsAuth, sendBirthdayMessages);

router.route(automatedMessages.WORKER_INACTIVE_MESSAGES)
    .post(schedulerEventsAuth, sendWorkerInactiveMessages);

router.route(automatedMessages.FIRST_DAY_WELCOME_MESSAGE)
    .post(schedulerEventsAuth, sendFirstDayWelcomeMessage);

router.route(automatedMessages.FTP_TNA_UPLOADS)
    .post(schedulerEventsAuth, upload(), ftpUploadTimeAndAttendance);

router.route(automatedMessages.FTP_GET_PAYROLL_SUMMARY)
    .get(schedulerEventsAuth, downloadPayrollSummary);

router.route(automatedMessages.FTP_WORKERS_UPLOAD)
    .post(schedulerEventsAuth, upload(), bulkUploadWorkers);

router.route(automatedMessages.FTP_GET_PAYROLL_INVOICE)
    .post(schedulerEventsAuth, ftpGetPayrollInvoice);

router.route(automatedMessages.FTP_GET_PAYROLL_DETAILED_SUMMARY)
    .get(schedulerEventsAuth, downloadPayrolDetialedSummary);