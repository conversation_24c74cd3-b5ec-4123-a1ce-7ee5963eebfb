/**
 * All the TIME AND ATTENDANCE related APIs.
 */

const express = require('express');
export const router = express.Router();

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { timeAndAttendance, UserType } from './../common';
import { uploadTimeAndAttendance, getListOfTimeAndAttendance, getDetailOfTimeAndAttendance, downloadTimeAndAttendanceSampleFile, uploadTotalAgencyPay, downloadTotalAgencyPayFile, downloadTAPSampleFile, deleteTAPFile, triggerFtpLookupTna, getAdjustmentRows } from '../api';
const upload = require("express-fileupload");
// APIs

router.route(timeAndAttendance.UPLOAD_TIME_AND_ATTENDANCE)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), upload(), uploadTimeAndAttendance);

// router.route(timeAndAttendance.GET_LIST_OF_TIME_AND_ATTENDANCE)
//     .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE]), getListOfTimeAndAttendance);

router.route(timeAndAttendance.GET_TIME_AND_ATTENDANCE_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadTimeAndAttendanceSampleFile);

router.route(timeAndAttendance.GET_TAP_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadTAPSampleFile);

router.route(timeAndAttendance.DOWNLOAD_UPLOADED_TAP_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadTotalAgencyPayFile);

// router.route(timeAndAttendance.GET_DETAIL_OF_TIME_AND_ATTENDANCE)
//     .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE]), getDetailOfTimeAndAttendance);

router.route(timeAndAttendance.UPLOAD_TOTAL_AGENCY_PAY)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), upload(), uploadTotalAgencyPay);

router.route(timeAndAttendance.DELETE_TOTAL_AGENCY_PAY)
    .delete(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), deleteTAPFile);

router.route(timeAndAttendance.FTP_MANUAL_UPLOAD_TNA_TRIGGER)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN]), triggerFtpLookupTna);

router.route(timeAndAttendance.GET_ADJUSTMENT_ROWS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN, UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_SITE]), getAdjustmentRows);