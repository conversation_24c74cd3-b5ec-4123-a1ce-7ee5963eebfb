export { router as userAuthenticationRouter } from './userAuthentication';
export { router as serviceStatusRoutes } from './serviceStatusRoutes';
export { router as swaggerRoutes } from './swaggerRoutes';
export { router as clientDetails } from './clientDetails';
export { router as agencyRoute } from './agency';
export { router as workerRoutes } from './worker';
export { router as timeAndAttendanceRoutes } from './timeAndAttendance';
export { router as regionRoute } from './region';
export { router as departmentRoute } from './department';
export { router as siteRoute } from './site';
export { router as rateCardRoute } from './rateCard';
export { router as jobRoutes } from './job';
export { router as sectorRoutes } from './sector';
export { router as agencyClientAssociationRoute } from './agencyClientAssociation';
export { router as userRoutes } from './user';
export { router as shiftRoutes } from './shift';
export { router as bookingRoutes } from './booking';
export { router as payrollRouters } from './payroll';
export { router as masterAdminDadhboardRouter } from './masterAdminDashboard';
export { router as dashboardRouter } from './dashboard';
export { router as messageRouter } from './messages';
export { router as surveyRouter } from './survey';
export { router as automatedMessagesRouter } from './automatedMessages';
export { router as faqRouter } from './faq';
export { router as mobileVersionRouter } from './mobileVersion';
export { router as agencyManagementRouter } from './agencyManagement';
export { router as marginsRouter } from './margins';
export { router as reportingRouter } from './reporting';
export { router as mfaRouter } from './mfa';
