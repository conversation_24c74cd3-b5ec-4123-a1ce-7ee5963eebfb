/**
 * All the PAYROLL related APIs.
 */

const express = require('express');
export const router = express.Router();
const upload = require("express-fileupload");

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { payroll, UserType } from './../common';
import { getPayrollSummary, downloadPayrollSummary, downloadPayrollSampleFile, deletePayroll, getWorkersCreditDues, downloadPayrolDetialedSummary } from '../api';

// APIs
router.route(payroll.PAYROLL_AND_SUMMARY)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_ADMIN, UserType.CLEARVUE_ADMIN, UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getPayrollSummary)
    .delete(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), deletePayroll);

router.route(payroll.DOWNLOAD_PAYROLL_CSV)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadPayrollSummary);

router.route(payroll.GET_PAYROLL_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadPayrollSampleFile);

router.route(payroll.GET_CREDIT_DUE)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getWorkersCreditDues);

router.route(payroll.DOWNLOAD_PAYROLL_DETAILED_SUMMARY)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadPayrolDetialedSummary);