/**
 * All the agency related APIs.
 */

const express = require('express');
export const router = express.Router();
const upload = require("express-fileupload");

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { booking, UserType } from './../common';
import { createBooking, getBookings, getBookingDetails, updateBookingDetails, updateBooking, createBulkBooking, deleteBooking, downloadBookingDynamicSampleSheet, getOpenBookings, bulkFulFillBookings } from '../api';

// APIs
router.route(booking.GET_OR_CREATE_BOOKING)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE]), createBooking)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getBookings);

router.route(booking.CREATE_BULK_BOOKING)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE]), upload(), createBulkBooking)

router.route(booking.BOOKING_DETAILS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getBookingDetails)
    .put(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), updateBookingDetails) // Fulfilment
    .delete(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE]), deleteBooking)

router.route(booking.UPDATE_BOOKING)
    .put(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE]), updateBooking) // Update booking heads/hours

router.route(booking.GET_OPEN_BOOKING)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getOpenBookings);

router.route(booking.FULFILL_BULK_OPEN_BOOKING)
    .put(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), upload(), bulkFulFillBookings);

router.route(booking.GET_BOOKING_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE]), downloadBookingDynamicSampleSheet);
