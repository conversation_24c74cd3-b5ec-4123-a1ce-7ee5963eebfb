/**
 * All the messages related APIs.
 */

const express = require('express');
export const router = express.Router();

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { message, UserType } from './../common';
import {
    sendMessageToWorkers, getSentMessagesList, getWorkerSideMessagesList, updateMessageTemplate, createMessageTemplate,
    getTrainingMessageDetails, updateMessageStatus, getMessageDetails, getMessageTemplateList, getMessageTemplate, translateMessage,
    addMessageReaction, addMessageComment, getMessageComments, updateAutomatedMessage, translateMessageToMultipleLanguages, getMessageTranslations, updateAutomatedMessageTranslations, translateTemplateToMultipleLanguages, deleteMessageTemplate
} from '../api';

// APIs
router.route(message.MESSAGE)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), sendMessageToWorkers)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), getSentMessagesList)


router.route(message.MESSAGE_DETAILS)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), getMessageDetails)
    .put(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), updateAutomatedMessage)

router.route(message.WORKER_SIDE_MESSAGES_LIST)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), getWorkerSideMessagesList)

router.route(message.WORKER_SIDE_MESSAGE_REACTION)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), addMessageReaction)

router.route(message.WORKER_SIDE_MESSAGE_COMMENT)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), addMessageComment)

router.route(message.WORKER_SIDE_MESSAGE_COMMENTS)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), getMessageComments)

router.route(message.TEMPLATES)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), createMessageTemplate)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), getMessageTemplateList)

router.route(message.TEMPLATE_BY_ID)
    .put(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), updateMessageTemplate)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), getMessageTemplate)
    .delete(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), deleteMessageTemplate)


router.route(message.TRAINING_MESSAGE_DETAILS)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), getTrainingMessageDetails)

router.route(message.UPDATE_MESSAGE_STATUS)
    .put(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), updateMessageStatus)

router.route(message.TRANSLATE_MESSAGE)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN]), translateMessage)

router.route(message.TRANSLATE_TEMPLATES_TO_MULTIPLE_LANGUAGE)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN]), translateTemplateToMultipleLanguages)

router.route(message.TRANSLATE_MESSAGE_TO_MULTIPLE_LANGUAGE)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN]), translateMessageToMultipleLanguages)

router.route(message.MESSAGE_TRANSLATION)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), getMessageTranslations)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), updateAutomatedMessageTranslations)