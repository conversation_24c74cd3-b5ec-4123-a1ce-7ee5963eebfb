/**
 * All the rate card related APIs.
 */

const express = require('express');
export const router = express.Router();

import { createRateCard, getRateCardList, rateCardDelete, downloadRatecardSampleFile } from '../api';
import { rateCard, UserType } from './../common';
import { authorizeJwtToken } from './../middlewares/auth';
import { checkPermission } from './../middlewares/permission';
const upload = require("express-fileupload");

// APIs
router.route(rateCard.CREATE_AND_GET_LIST_OF_RATE_CARD)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), upload(), createRateCard)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), getRateCardList);

router.route(rateCard.RATE_CARD_BY_ID)
    .delete(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), rateCardDelete);

router.route(rateCard.RATE_CARD_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), downloadRatecardSampleFile);