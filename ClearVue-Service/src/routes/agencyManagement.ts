/**
 * All the job related APIs.
 */

const express = require('express');
export const router = express.Router();

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { agencyManagement, UserType } from './../common';
import { getSupervisorsWeeklyData, getSupervisorsWorkersData } from '../api';


router.route(agencyManagement.GET_SUPERVISORS_WEEKLY_DATA)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_ADMIN, UserType.CLEARVUE_ADMIN, UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getSupervisorsWeeklyData);

router.route(agencyManagement.GET_SUPERVISOR_WORKERS_DATA)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_ADMIN, UserType.CLEARVUE_ADMIN, UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getSupervisorsWorkersData);