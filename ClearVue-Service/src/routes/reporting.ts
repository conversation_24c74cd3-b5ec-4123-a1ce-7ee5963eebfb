/**
 * All the mobile app related APIs.
 */
import express from 'express';
export const router = express.Router();

import { checkPermission } from '../middlewares/permission';
import { authorizeJwtToken } from '../middlewares/auth';
import { reporting, UserType } from '../common';
import {
    getPerformanceShiftsBlocks,
    getPerformanceNewStartersGraph,
    getPerformanceByTenureGraph,
    getShiftBookingGraph,
    getSiteStatsShiftFulfilment,
    getSiteStatsAveHours,
    getSiteStatsPoolUtilisation,
    getSiteStatsLeavers,
    getSiteStatsPerformance,
    getSiteStatsShiftUtilisation,
    getSiteStatsSpendHours
} from '../api';

router.route(reporting.GET_PERFORMANCE_SHIFTS_BLOCKS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getPerformanceShiftsBlocks);

router.route(reporting.GET_PERFORMANCE_NEW_STARTERS_GRAPH)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getPerformanceNewStartersGraph);

router.route(reporting.GET_PERFORMANCE_BY_TENURE)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getPerformanceByTenureGraph);

router.route(reporting.GET_SHIFT_BOOKINGS_GRAPH)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getShiftBookingGraph);

const allowedUserTypes = [
    UserType.CLIENT_ADMIN,
    UserType.CLIENT_SITE,
    UserType.CLIENT_REGIONAL,
    UserType.AGENCY_ADMIN,
    UserType.AGENCY_REGIONAL,
    UserType.AGENCY_SITE
];

router.route(reporting.GET_SITE_STATS_SHIFT_FULFILMENT)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsShiftFulfilment);

router.route(reporting.GET_SITE_STATS_AVE_HOURS)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsAveHours);

router.route(reporting.GET_SITE_STATS_POOL_UTILISATION)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsPoolUtilisation);

router.route(reporting.GET_SITE_STATS_LEAVERS)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsLeavers);

router.route(reporting.GET_SITE_STATS_PERFORMANCE)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsPerformance);

router.route(reporting.GET_SITE_STATS_SHIFT_UTILISATION)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsShiftUtilisation);

router.route(reporting.GET_SITE_STATS_SPEND_HOURS)
    .get(authorizeJwtToken, checkPermission(allowedUserTypes), getSiteStatsSpendHours);
