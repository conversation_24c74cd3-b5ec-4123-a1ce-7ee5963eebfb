/**
 * All the worker related APIs.
 */

const express = require('express');
export const router = express.Router();

import { checkPermission } from './../middlewares/permission';
import { authorizeJwtToken } from './../middlewares/auth';
import { worker, UserType } from './../common';
import {
    addNewWorker,
    bulkUploadWorkers,
    updateSingleWorkersActivityStatus,
    downloadSampleFile,
    getWorkersList,
    getWorkerDetailsByWorkerId,
    workerRegistrationAPI,
    workerLogin,
    getWorkersListWithoutPagination,
    workerDocumentsUpload,
    workerProfileAPI,
    workerProfileAPIV2,
    updateWorkerProfileByUserId,
    getWorkerGroupDetails,
    updateWorkerDetailByWorkerId,
    trackWorkerTrainingAPI, getWorkersNationality, deleteWorkerAccount, workerRegistrationAPIV2, updateWorkerDetailByWorkerIdV2, searchWorkers, bulkUpdateWorker, downloadSampleFileToUpdateWorker, updateWorkerLanguageCode, uploadWorkerPerformance, downloadWorkerPerformanceFile,
    downloadWorkerPerformanceSampleSheet,
    triggerFtpLookupWorkersUpload
} from '../api';
import { deleteWorkerPerformanceFile } from '../api/worker';

const upload = require("express-fileupload");
// APIs
router.route(worker.CREATE_OR_GET_WORKERS)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), addNewWorker)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN]), getWorkersList)
    .delete(authorizeJwtToken, (checkPermission([UserType.AGENCY_WORKER])), deleteWorkerAccount)

router.route(worker.WORKER_SEARCH_PROFILE)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN]), searchWorkers)

router.route(worker.BULK_UPLOAD_OR_UPDATE_WORKERS)
    .post(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), upload(), bulkUploadWorkers)
    .put(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), updateSingleWorkersActivityStatus)  // Active / Inactive Worker

router.route(worker.GET_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN]), downloadSampleFile)

router.route(worker.GET_AND_UPDATE_WORKER_DETAILS)
    .get(authorizeJwtToken, (checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN])), getWorkerDetailsByWorkerId)
    .put(authorizeJwtToken, (checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN])), updateWorkerDetailByWorkerId)

router.route(worker.LOGIN).post(workerLogin)

router.route(worker.DOCUMENTS)
    .post(authorizeJwtToken, checkPermission([
        UserType.AGENCY_ADMIN, UserType.AGENCY_WORKER, UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL,
        UserType.CLIENT_SITE, UserType.CLEARVUE_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN
    ]), upload(), workerDocumentsUpload)

router.route(worker.GET_WORKER_LISTING)
    .get(authorizeJwtToken, (checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN])), getWorkersListWithoutPagination)

router.route(worker.SIGN_UP).post(workerRegistrationAPI)

router.route(worker.PROFILE)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), workerProfileAPI)

router.route(worker.PROFILE_V2)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), workerProfileAPIV2)

router.route(worker.WORKER_PROFILE)
    .put(authorizeJwtToken, (checkPermission([UserType.AGENCY_WORKER])), updateWorkerProfileByUserId)

router.route(worker.GET_WORKER_GROUPS)
    .get(authorizeJwtToken, (checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE])), getWorkerGroupDetails)

router.route(worker.TRACK_TRAINING)
    .put(authorizeJwtToken, (checkPermission([UserType.AGENCY_WORKER])), trackWorkerTrainingAPI)

router.route(worker.WORKER_NATIONALITYT_LIST)
    .get(authorizeJwtToken, (checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_SITE, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN])), getWorkersNationality)


router.route(worker.SIGN_UP_V2).post(workerRegistrationAPIV2)

router.route(worker.UPDATE_PROFILE_V2).put(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), updateWorkerDetailByWorkerIdV2)

router.route(worker.SET_WORKER_LANGUAGE).post(authorizeJwtToken, checkPermission([UserType.AGENCY_WORKER]), updateWorkerLanguageCode)

// router.route(worker.BULK_UPDATE_WORKERS)
//     .put(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.CLIENT_ADMIN, UserType.MESSAGE_ADMIN]), upload(), bulkUpdateWorker)

router.route(worker.UPDATE_WORKER_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.AGENCY_ADMIN, UserType.CLIENT_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE, UserType.MESSAGE_ADMIN]), downloadSampleFileToUpdateWorker)


router.route(worker.WORKER_PERFORMANCE)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), upload(), uploadWorkerPerformance)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadWorkerPerformanceFile)
    .delete(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE]), deleteWorkerPerformanceFile)

router.route(worker.WORKER_PERFORMANCE_SAMPLE_SHEET)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), downloadWorkerPerformanceSampleSheet)


router.route(worker.FTP_WORKERS_BULK_UPLOAD_MANUAL_TRIGGER)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN]), triggerFtpLookupWorkersUpload);

