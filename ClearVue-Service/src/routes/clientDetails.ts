
const express = require('express');

import { clientDetails, UserType } from './../common';
import { addNewClients, updateClients, getClientById, getClients, getClientUsers, addClientUsers, updateClientUsers, clientRatingsAPI, workerTypeWiseClientRatingsAPI, restrictWorkerInviteEmail, addNewTrainingRule, getTrainingRule, updateTrainingRule, deleteTrainingRule, addNewStartDateYearlyRule, getStartDateYearlyRule, updateStartDateYearlyRule, deleteStartDateYearlyRule, getFinancialRule, addNewFinancialRule, updateFinancialRule, deleteFinancialRule } from '../api';
import { authorizeJwtToken } from './../middlewares/auth';
import { checkPermission } from '../middlewares/permission';
import { checkClientAuth } from '../middlewares/clientAuth';
const upload = require("express-fileupload");


export const router = express.Router();

router.route(clientDetails.ADD_OR_GET_CLIENT)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getClients)
    .post(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), addNewClients);

router.route(clientDetails.GET_OR_UPDATE_CLIENT_DETAILS_BY_ID)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL, UserType.AGENCY_ADMIN, UserType.AGENCY_SITE]), checkClientAuth(), getClientById)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN]), upload(), updateClients);

router.route(clientDetails.CLIENT_USERS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), getClientUsers);

router.route(clientDetails.ADD_CLIENT_USERS)
    .post(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), addClientUsers);

router.route(clientDetails.UPDATE_CLIENT_USERS)
    .put(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), updateClientUsers);

router.route(clientDetails.CLIENT_RATINGS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), clientRatingsAPI);

router.route(clientDetails.TYPE_WISE_CLIENT_RATINGS)
    .get(authorizeJwtToken, checkPermission([UserType.CLIENT_ADMIN]), workerTypeWiseClientRatingsAPI);

router.route(clientDetails.BLOCK_WORKER_INVITE_EMAIL)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), restrictWorkerInviteEmail);


router.route(clientDetails.WORKER_TRAINING_RULES)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), getTrainingRule)
    .post(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), addNewTrainingRule);

router.route(clientDetails.WORKER_TRAINING_RULE_BY_ID)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), updateTrainingRule)
    .delete(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), deleteTrainingRule);

router.route(clientDetails.WORKER_START_DATE_YEARLY_RULES)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getStartDateYearlyRule)
    .post(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), addNewStartDateYearlyRule);

router.route(clientDetails.WORKER_START_DATE_YEARLY_RULE_BY_ID)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), updateStartDateYearlyRule)
    .delete(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), deleteStartDateYearlyRule);

router.route(clientDetails.WORKER_FINANCIAL_RULES)
    .get(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN, UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE, UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]), getFinancialRule)
    .post(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), addNewFinancialRule);

router.route(clientDetails.WORKER_FINANCIAL_RULE_BY_ID)
    .put(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), updateFinancialRule)
    .delete(authorizeJwtToken, checkPermission([UserType.CLEARVUE_ADMIN]), deleteFinancialRule);