import { validateRequestData, notifyBugsnag, getSignedUrlForGetObject } from './../utils';
import { QueryParamsSchemaWithIdOnly, payrollReportCsvSchema, QueryParamsForPayrollSummary, deletePayrollSchema, payrollMetaIdSchema, QueryParamsForCreditDues, QueryParamsForPayrollInvoice } from './../common';
import { getPayrollSummaryService, downloadPayrollSummaryService, deletePayrollDataService, getWorkersCreditDuesService, downloadPayrollDetialedSummaryService, getPayrollInvoiceService } from '../services'
import { config } from '../configurations';
const path = require('path');
const csvParser = require('csvtojson');

/**
 * API to get the Payroll Summary.
 * @param req
 * @param res
 * @param next
 */
export const getPayrollSummary = async (req, res, next) => {
    try {
        await validateRequestData(QueryParamsForPayrollSummary, req.query);
        let response = await getPayrollSummaryService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to get the Payroll Summary for FTP service.
 * @param req
 * @param res
 * @param next
 */
export const ftpGetPayrollInvoice = async (req, res, next) => {
    try {
        await validateRequestData(QueryParamsForPayrollInvoice, req.body);
        let response = await getPayrollInvoiceService(req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download the Payroll Summary.
 * @param req
 * @param res
 * @param next
 */
export const downloadPayrollSummary = async (req, res, next) => {
    try {
        await validateRequestData(payrollMetaIdSchema, req.params);
        let response = await downloadPayrollSummaryService(req.params.payrollMetaId);
        res.setHeader('Content-Type', 'text/csv');
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download the Payroll Detialed Summary.
 * @param req
 * @param res
 * @param next
 */
export const downloadPayrolDetialedSummary = async (req, res, next) => {
    try {
        await validateRequestData(payrollMetaIdSchema, req.params);
        let response = await downloadPayrollDetialedSummaryService(req.params.payrollMetaId);
        res.setHeader('Content-Type', 'text/csv');
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download sample document for Payroll.
 * @param req
 * @param res
 * @param next
 */
export const downloadPayrollSampleFile = async (req, res, next) => {
    try {
        let link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.PAYROLL_REPORT_SAMPLE_FOLDER, config.PAYROLL_REPORT_SAMPLE_FILE_NAME);
        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Delete payroll data
 * @param req Request
 * @param res Response
 * @param next
 */
export const deletePayroll = async (req, res, next) => {
    try {
        // Validate request body
        let payload = await validateRequestData(deletePayrollSchema, req.body);
        let response = await deletePayrollDataService(payload, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * API to get the Credit dues.
 * @param req
 * @param res
 * @param next
 */
export const getWorkersCreditDues = async (req, res, next) => {
    try {
        await validateRequestData(QueryParamsForCreditDues, req.query);
        let response = await getWorkersCreditDuesService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}