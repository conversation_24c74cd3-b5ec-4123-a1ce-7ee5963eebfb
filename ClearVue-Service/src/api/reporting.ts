import { validateRequestData, notifyBugsnag } from './../utils';
import { getPerformanceShiftsBlocksService, getPerformanceNewStartersGraphService, getPerformanceByTenureGraphService, getShiftBookingGraphService, siteStatsShiftFulfilment, siteStatsAveHours, siteStatsPoolUtilisation, siteStatsLeavers, siteStatsPerformance, siteStatsShiftUtilisation, siteStatsSpendHours } from '../services';
import { performanceShiftsBlocksSchema } from '../common';
import { siteStatsSchema } from '../common/schema';

export const getPerformanceShiftsBlocks = async (req, res, next) => {
    try {
        // Validate request parameters
        await validateRequestData(performanceShiftsBlocksSchema, req.query);

        // Call service function
        const response = await getPerformanceShiftsBlocksService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getPerformanceNewStartersGraph = async (req, res, next) => {
    try {
        // Validate request parameters
        await validateRequestData(performanceShiftsBlocksSchema, req.query);

        // Call service function
        const response = await getPerformanceNewStartersGraphService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getPerformanceByTenureGraph = async (req, res, next) => {
    try {
        // Validate request parameters
        await validateRequestData(performanceShiftsBlocksSchema, req.query);

        // Call service function
        const response = await getPerformanceByTenureGraphService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getShiftBookingGraph = async (req, res, next) => {
    try {
        // Validate request parameters
        await validateRequestData(performanceShiftsBlocksSchema, req.query);

        // Call service function
        const response = await getShiftBookingGraphService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


export const getSiteStatsShiftFulfilment = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsShiftFulfilment(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsAveHours = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsAveHours(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsPoolUtilisation = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsPoolUtilisation(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsLeavers = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsLeavers(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsPerformance = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsPerformance(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsShiftUtilisation = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsShiftUtilisation(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getSiteStatsSpendHours = async (req, res, next) => {
    try {
        await validateRequestData(siteStatsSchema, req.query);
        const response = await siteStatsSpendHours(req.query, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};