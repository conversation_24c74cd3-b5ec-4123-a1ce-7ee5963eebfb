import { validateRequestData, notifyBugsnag } from '../utils';
import { MarginsRequestSchema, MarginsQuerySchema, MarginIdSchema } from '../common';
import {
    createMarginsService,
    getMarginsListService,
    updateMarginsService,
    deleteMarginsService
} from '../services';

export const createMargins = async (req, res, next) => {
    try {
        await validateRequestData(MarginsRequestSchema, req.body);
        await validateRequestData(MarginsQuerySchema, req.query);
        const response = await createMarginsService(req.query, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const getMarginsList = async (req, res, next) => {
    try {
        await validateRequestData(MarginsQuerySchema, req.query);
        const response = await getMarginsListService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const updateMargins = async (req, res, next) => {
    try {
        await validateRequestData(MarginIdSchema, req.params);
        await validateRequestData(MarginsRequestSchema, req.body);
        const response = await updateMarginsService(req.params.marginId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

export const deleteMargins = async (req, res, next) => {
    try {
        await validateRequestData(MarginsQuerySchema, req.query);
        const response = await deleteMarginsService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}; 