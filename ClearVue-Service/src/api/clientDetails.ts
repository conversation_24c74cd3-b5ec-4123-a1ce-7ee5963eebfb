import { validateRequestData, notifyBugsnag } from '../utils';
import { AddNewClientSchema, ErrorResponse, MimeType, setTrainingRule, UserType, setStartDateYearlyRule, setFinancialRule } from '../common';
import {
    addClient, updateClient, getClientDetailsById, getAllClientDetails, getClientUsersService, addClientUsersService, updateClientUserService, siteAndClientRatingsService, workerTypeWiseSiteAndClientRatingService, restrictWorkerInviteEmailService, addTrainingRuleService, getTrainingRuleService, updateTrainingRuleService, deleteTrainingRuleService,
    addStartDateYearlyRuleService, getStartDateYearlyRuleService, updateStartDateYearlyRuleService, deleteStartDateYearlyRuleService, addFinancialRuleService, getFinancialRuleService, updateFinancialRuleService, deleteFinancialRuleService,
} from '../services'
import { AddClientUserSchema, clientRatingsSchema, GetClientsPaginationSchema, updateClientParamsSchema, UpdateClientSchemaForClientAdmin, UpdateClientSchema, UpdateClientUserSchema, siteRatingsSchema, clientIdSchema, userIdSchema, RestrictWorkerInviteEmailSchema, detailedAgencyRatingsSchema, trainingRuleIdClientIdSchema, startDateYearlyRuleIdSchema, startDateYearlyRuleIdClientIdSchema, financialRuleIdClientIdSchema } from '../common/schema';

import { config } from '../configurations';
const path = require("path");


/**
 * Renew access token as per the refresh-token
 * @param req Request
 * @param res Response
 */

export const addNewClients = async (req, res, next) => {
    try {
        let payload = await validateRequestData(AddNewClientSchema, req.body);
        payload["user_id"] = req.user.user_id;

        let response = await addClient(payload);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to update the client details.
 * @param req Request
 * @param res Response
 */
export const updateClients = async (req, res, next) => {
    try {
        let image = null;
        if (req.files) {
            if (req.files.profile.mimetype !== MimeType.JPG &&
                req.files.profile.mimetype !== MimeType.PNG) {
                return res.status(400).json(ErrorResponse.InvalidFileTypeErrorNonCsv)
            }
            if (req.files.profile.size > config.MAX_IMAGE_SIZE) {
                return res.status(400).json(ErrorResponse.InvalidFileSize)
            }
            let ext = path.extname(req.files.profile.name);
            image = {
                data: req.files.profile.data,
                mime: req.files.profile.mimetype,
                extension: ext
            };
        }
        let payload = req.body;
        payload["client_id"] = req.params.clientId;
        if (req.user.user_type_id == UserType.CLEARVUE_ADMIN) {
            await validateRequestData(UpdateClientSchema, payload);
        } else {
            await validateRequestData(UpdateClientSchemaForClientAdmin, payload);
        }
        let response = await updateClient(payload, image, req.user);
        res.status(response[0]).json(response[1])
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Get particular client details.
 * @param {any} res
 * @param {any} next
 * @returns {any}
 */
export const getClientById = async (req, res, next) => {
    try {
        await validateRequestData(clientIdSchema, req.params);
        let response = await getClientDetailsById(req.params.clientId, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Get list of all client details.
 * @param {any} res
 * @param {any} next
 * @returns {any}
 */
export const getClients = async (req, res, next) => {
    try {
        await validateRequestData(GetClientsPaginationSchema, req.query);
        let response = await getAllClientDetails(req.user, req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Get list of users created by client.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getClientUsers = async (req, res, next) => {
    try {
        let response = await getClientUsersService(req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to add the client sub-users for Site and Region.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const addClientUsers = async (req, res, next) => {
    try {
        await validateRequestData(AddClientUserSchema, req.body);
        let response = await addClientUsersService(req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to amend the client sub-users profile details.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateClientUsers = async (req, res, next) => {
    try {
        await validateRequestData(userIdSchema, req.params);
        await validateRequestData(UpdateClientUserSchema, req.body);
        let response = await updateClientUserService(req.params.userId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to fetch the ratings of the client.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const clientRatingsAPI = async (req, res, next) => {
    try {
        await validateRequestData(clientRatingsSchema, req.query);
        let response = await siteAndClientRatingsService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to fetch the rating of client by worker type wise.
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const workerTypeWiseClientRatingsAPI = async (req, res, next) => {
    try {
        await validateRequestData(siteRatingsSchema, req.query);
        let response = await workerTypeWiseSiteAndClientRatingService(req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to block worker invite email
 * @param req Request
 * @param res Response
 */
export const restrictWorkerInviteEmail = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(clientIdSchema, req.params);
        // Validate request body
        await validateRequestData(RestrictWorkerInviteEmailSchema, req.body);
        let response = await restrictWorkerInviteEmailService(req.params.clientId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 *
 * @param req Request
 * @param res Response
 */

export const addNewTrainingRule = async (req, res, next) => {
    try {
        await validateRequestData(clientIdSchema, req.params);
        let payload = await validateRequestData(setTrainingRule, req.body);
        payload["user_id"] = req.user.user_id;
        payload["client_id"] = req.params.clientId;
        let response = await addTrainingRuleService(payload);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */

export const getTrainingRule = async (req, res, next) => {
    try {
        let payload = await validateRequestData(clientIdSchema, req.params);
        payload["user_id"] = req.user.user_id;
        payload["client_id"] = payload.clientId;

        let response = await getTrainingRuleService(payload);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */

export const updateTrainingRule = async (req, res, next) => {
    try {
        await validateRequestData(trainingRuleIdClientIdSchema, req.params);

        let body = await validateRequestData(setTrainingRule, req.body);
        body["user_id"] = req.user.user_id;
        body["client_id"] = req.params.clientId;

        let response = await updateTrainingRuleService(body, req.params.trainingRuleId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */

export const deleteTrainingRule = async (req, res, next) => {
    try {
        await validateRequestData(trainingRuleIdClientIdSchema, req.params);

        let response = await deleteTrainingRuleService(req.params);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const addNewStartDateYearlyRule = async (req, res, next) => {
    try {
        await validateRequestData(clientIdSchema, req.params);
        let payload = await validateRequestData(setStartDateYearlyRule, req.body);
        payload["client_id"] = req.params.clientId;
        let response = await addStartDateYearlyRuleService(payload, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const getStartDateYearlyRule = async (req, res, next) => {
    try {
        let payload = await validateRequestData(clientIdSchema, req.params);
        payload["client_id"] = payload.clientId;

        let response = await getStartDateYearlyRuleService(payload);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const updateStartDateYearlyRule = async (req, res, next) => {
    try {
        await validateRequestData(startDateYearlyRuleIdClientIdSchema, req.params);

        let body = await validateRequestData(setStartDateYearlyRule, req.body);
        body["user_id"] = req.user.user_id;
        body["client_id"] = req.params.clientId;

        let response = await updateStartDateYearlyRuleService(body, req.params.startDateYearlyRuleId, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const deleteStartDateYearlyRule = async (req, res, next) => {
    try {
        await validateRequestData(startDateYearlyRuleIdClientIdSchema, req.params);

        let response = await deleteStartDateYearlyRuleService(req.params);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 *
 * @param req Request
 * @param res Response
 */
export const addNewFinancialRule = async (req, res, next) => {
    try {
        await validateRequestData(clientIdSchema, req.params);
        let payload = await validateRequestData(setFinancialRule, req.body);
        payload["client_id"] = req.params.clientId;
        let response = await addFinancialRuleService(payload, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const getFinancialRule = async (req, res, next) => {
    try {
        let payload = await validateRequestData(clientIdSchema, req.params);
        payload["client_id"] = payload.clientId;

        let response = await getFinancialRuleService(payload);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const updateFinancialRule = async (req, res, next) => {
    try {
        await validateRequestData(financialRuleIdClientIdSchema, req.params);

        let body = await validateRequestData(setFinancialRule, req.body);
        body["user_id"] = req.user.user_id;
        body["client_id"] = req.params.clientId;

        let response = await updateFinancialRuleService(body, req.params.financialRuleId, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 *
 * @param req Request
 * @param res Response
 */
export const deleteFinancialRule = async (req, res, next) => {
    try {
        await validateRequestData(financialRuleIdClientIdSchema, req.params);

        let response = await deleteFinancialRuleService(req.params);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};
