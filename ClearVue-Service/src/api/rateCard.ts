import { validateRequestData, notifyBugsnag, validateRateCardDataForDuplicates, getSignedUrlForGetObject, dynamicErrorObject } from './../utils';
import { CreateRateCardRequestSchema, rateCardIdSchema, siteIdQueryParams, dropDownSchema, rateCardBodySchema, ErrorResponse } from './../common';
import { createRateCardService, getRateCardListService, rateCardDeleteService } from '../services'
import { config } from '../configurations';
const path = require('path');
const csvParser = require('csvtojson');

/**
 * create Rate Card
 * @param req Request
 * @param res Response
 */
export const createRateCard = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.rate_card.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }
        let csv = String(req.files.rate_card.data)
        let csvData = await csvParser().fromString(req.files.rate_card.data.toString('utf8'));

        let payload = await validateRequestData(CreateRateCardRequestSchema, csvData, true);


        if (!payload.length) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }


        let body = await validateRequestData(rateCardBodySchema, req.body);

        const errors = await validateRateCardDataForDuplicates(payload);
        if (Object.keys(errors).length !== 0) {
            let errorObj;
            if (errors.performanceLowHighCheck && errors.performanceLowHighCheck.length > 0) {
                errorObj = await dynamicErrorObject(ErrorResponse.PerformanceHighLowEmpty, errors.performanceLowHighCheck.join(', '))
            } else if (errors.performanceOrderCheck && errors.performanceOrderCheck.length > 0) {
                errorObj = await dynamicErrorObject(ErrorResponse.InvalidPerformanceHighLow, errors.performanceOrderCheck.join(', '))
            } else if (errors.duplicatesCheck && errors.duplicatesCheck.length > 0) {
                errorObj = await dynamicErrorObject(ErrorResponse.DuplicatePayChargeSupervisorCombo, errors.duplicatesCheck.join(', '))
            } else if (errors.invalidSupervisorRate && errors.invalidSupervisorRate.length) {
                errorObj = await dynamicErrorObject(ErrorResponse.InvalidSupervisorRate, errors.invalidSupervisorRate.join(', '))
            } else {
                errorObj = await dynamicErrorObject(ErrorResponse.InvalidPaytypeSupervisorCombo, errors.invalidWorkersRate.join(', '))
            }

            return res.status(400).json(errorObj);
        }

        let response = await createRateCardService(csv, payload, body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * get list of Rate Card
 * @param req Request
 * @param res Response
 */
export const getRateCardList = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(siteIdQueryParams, req.query);
        let response = await getRateCardListService(req.user.client_id, req.query.site_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to Delete rate-card
 * @param req
 * @param res
 * @param next
 */
export const rateCardDelete = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(rateCardIdSchema, req.params);
        let response = await rateCardDeleteService(req.params.rateCardId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to download sample Ratecard Sheet.
 * @param req
 * @param res
 * @param next
 */
export const downloadRatecardSampleFile = async (req, res, next) => {
    try {
        let link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.RATE_CARD_SAMPLE_SHEET_FOLDER, config.RATE_CARD_SAMPLE_SHEET_NAME);
        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}