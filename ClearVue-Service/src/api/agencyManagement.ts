import { validateRequestData, notifyBugsnag } from './../utils';
import { QueryParamsForSupervisorsWeeklyData, payrollMetaIdSchema, QueryParamsForSupervisorWorkersData } from './../common';
import { getPayrollSummaryService, getSupervisorsWorkersDataService } from '../services'


/**
 * API to get SupervisorsWeeklyData
 * @param req
 * @param res
 * @param next
 */
export const getSupervisorsWeeklyData = async (req, res, next) => {
    try {
        await validateRequestData(QueryParamsForSupervisorsWeeklyData, req.query);
        let response = await getPayrollSummaryService(req.query, req.user, true);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to get the SupervisorsWorkersData
 * @param req
 * @param res
 * @param next
 */
export const getSupervisorsWorkersData = async (req, res, next) => {
    try {
        await validateRequestData(payrollMetaIdSchema, req.params);
        await validateRequestData(QueryParamsForSupervisorWorkersData, req.query);
        let response = await getSupervisorsWorkersDataService(req.params.payrollMetaId, req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}