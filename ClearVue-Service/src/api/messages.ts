import { validateRequestData } from './../utils';
import {
    SendMessageRequestSchema, SendMessageRequestParamsSchema, GetSentMessageListSchema, CreateMessageTemplateSchema,
    QueryParamsSchemaWithIdOnly, getWorkerSideMessagesListSchema, GeTemnplateListSchema, GetMessageRequestParamsSchema, eitherClientOrAgencyId,
    addMessageReactionSchema, SocialFeedPathParamSchema, addMessageCommentSchema, getMessageCommentsSchema, editAutomatedMessageSchema, TranslateMessageSchema, messageIdSchema, templateIdSchema, WorkerSideMessagesType, ErrorResponse, TranslateMessageToMultipleLanguageSchema, updateAutomatedMessageTranslationSchema, MessageUpdationType, TranslateTemplateToMultipleLanguageSchema, UpdateMessageTemplateSchema, GetTrainingMessageDetailesRequestParamsSchema
} from './../common';
import {
    sendMessageToWorkersService, getSentMessagesListService, createMessageTemplateService, updateMessageTemplateService,
    getWorkerSideMessagesListService, getWorkerTrainingMessageDetailsService, updateMessageStatusService,
    getMessageDetailsService, getTemplateListService, getMessageTemplateService, translateMessageService,
    addMessageReactionService, addMessageCommentService, getMessageCommentsService, updateAutomatedMessageService, translateMessageToMultipleLanguageService, getAutomatedMessageTranslationService, translateTemplateToMultipleLanguageService, deleteMessageTemplateService
} from '../services';
import { notifyBugsnag } from '../utils';

/**
 * Send new message to workers
 * @param req Request
 * @param res Response
 */
export const sendMessageToWorkers = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(SendMessageRequestParamsSchema, req.query);
        await validateRequestData(SendMessageRequestSchema, req.body);
        let response = await sendMessageToWorkersService(req.body, req.user.user_id, req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Get list of sent messages 
 * @param req Request
 * @param res Response
 */
export const getSentMessagesList = async (req, res, next) => {
    try {
        // Validate request body
        let response = await getSentMessagesListService(
            req.user.user_id,
            await validateRequestData(GetSentMessageListSchema, req.query)
        );
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Update an Automated('System Default') message for a specific client or agency
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateAutomatedMessage = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(messageIdSchema, req.params);
        await validateRequestData(eitherClientOrAgencyId, req.query);

        // Validate request body
        await validateRequestData(editAutomatedMessageSchema, req.body);

        const updateType = MessageUpdationType.OTHER
        let response = await updateAutomatedMessageService(req.params.messageId, req.query, req.body, updateType, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Update an Automated('System Default') message TRANSLATION only for a specific client or agency
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateAutomatedMessageTranslations = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(messageIdSchema, req.params);

        // Validate request body
        await validateRequestData(updateAutomatedMessageTranslationSchema, req.body);
        await validateRequestData(eitherClientOrAgencyId, req.query);
        const updateType = MessageUpdationType.TRANSLATION_DATA_ONLY
        let response = await updateAutomatedMessageService(req.params.messageId, req.query, req.body, updateType, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Get list of messages for the workers to display in mobile application
 * @param req Request
 * @param res Response
 */
export const getWorkerSideMessagesList = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        // Validate request body
        let params = await validateRequestData(getWorkerSideMessagesListSchema, req.query);
        params.sort_by = "created_at"
        params.sort_type = "DESC"
        if (params.type === WorkerSideMessagesType.KUDOS) {
            params.type = WorkerSideMessagesType.RECOGNITION;
        }
        let response = await getWorkerSideMessagesListService(req.user.user_id, params);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Get training message details
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getTrainingMessageDetails = async (req, res, next) => {
    try {
        await validateRequestData(messageIdSchema, req.params);
        await validateRequestData(GetTrainingMessageDetailesRequestParamsSchema, req.query);        // Validate query params
        let response = await getWorkerTrainingMessageDetailsService(req.params.messageId, req.query.language_code, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Get message details to display that into the mobile app 
 * @param req Request
 * @param res Response
 */
export const getMessageDetails = async (req, res, next) => {
    try {
        await validateRequestData(messageIdSchema, req.params);          // Validate path params
        await validateRequestData(GetMessageRequestParamsSchema, req.query);        // Validate query params
        let response = await getMessageDetailsService(req.params.messageId, req.query.message_receiver_worker_id, req.query.language_code, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Get message translations from message id
 * @param req Request
 * @param res Response
 */
export const getMessageTranslations = async (req, res, next) => {
    try {
        await validateRequestData(messageIdSchema, req.params);         // Validate path params
        await validateRequestData(eitherClientOrAgencyId, req.query);
        let response = await getAutomatedMessageTranslationService(req.params.messageId, req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * create a new message template
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const createMessageTemplate = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(CreateMessageTemplateSchema, req.body);
        let response = await createMessageTemplateService(req.body, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Update existing templates
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateMessageTemplate = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(templateIdSchema, req.params);
        // Validate request body
        await validateRequestData(UpdateMessageTemplateSchema, req.body);
        let response = await updateMessageTemplateService(req.params.templateId, req.body, req.user.user_id);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * get list of available templates
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getMessageTemplateList = async (req, res, next) => {
    try {
        let response = await getTemplateListService(
            req.user.user_id,
            await validateRequestData(GeTemnplateListSchema, req.query)
        );
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Update available templates
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateMessageStatus = async (req, res, next) => {
    try {
        await validateRequestData(messageIdSchema, req.params);
        let response = await updateMessageStatusService(req.params.messageId, req.user);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Get existing template details
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const getMessageTemplate = async (req, res, next) => {
    try {
        await validateRequestData(templateIdSchema, req.params);
        let response = await getMessageTemplateService(req.params.templateId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Delete existing template by Id
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const deleteMessageTemplate = async (req, res, next) => {
    try {
        await validateRequestData(templateIdSchema, req.params);
        let response = await deleteMessageTemplateService(req.params.templateId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Translate message
 * @param req Request
 * @param res Response
 */
export const translateMessage = async (req, res, next) => {
    try {
        await validateRequestData(TranslateMessageSchema, req.body);
        let response = await translateMessageService(req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Translate Template to multiple lang.
 * @param req Request
 * @param res Response
 */
export const translateTemplateToMultipleLanguages = async (req, res, next) => {
    try {
        await validateRequestData(TranslateTemplateToMultipleLanguageSchema, req.body);
        let response = await translateTemplateToMultipleLanguageService(req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Translate message
 * @param req Request
 * @param res Response
 */
export const translateMessageToMultipleLanguages = async (req, res, next) => {
    try {
        await validateRequestData(TranslateMessageToMultipleLanguageSchema, req.body);
        let response = await translateMessageToMultipleLanguageService(req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Add reaction(LIKE, DISLIKE) to message (reaction by worker)
 * @param req Request
 * @param res Response
 */
export const addMessageReaction = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        // Validate request body
        let params = await validateRequestData(SocialFeedPathParamSchema, req.params);
        await validateRequestData(addMessageReactionSchema, req.body);
        let response = await addMessageReactionService(req.user.user_id, params.messageId, req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Add comment to message (Comment by worker)
 * @param req Request
 * @param res Response
 */
export const addMessageComment = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        // Validate request body
        let params = await validateRequestData(SocialFeedPathParamSchema, req.params);
        await validateRequestData(addMessageCommentSchema, req.body);
        let response = await addMessageCommentService(req.user.user_id, params.messageId, req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Get list of all message specific comments
 * @param req Request
 * @param res Response
 */
export const getMessageComments = async (req, res, next) => {
    try {

        if (req.params.userId != req.user.user_id) {
            return res.status(403).send(ErrorResponse.PermissionDenied);
        }

        // Validate request body
        let params = await validateRequestData(SocialFeedPathParamSchema, req.params);
        let body = await validateRequestData(getMessageCommentsSchema, req.body);
        let response = await getMessageCommentsService(params.messageId, body.page, body.limit);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};
