/**
 * MFA API endpoints for Firebase TOTP MFA operations
 */
import { validateRequestData, notifyBugsnag } from '../utils';
import {
    isMfaEnabled, startMfaEnrollment, completeMfaEnrollment,
    startMfaSignIn, completeMfaSignIn, getUserMfaStatus, deleteUserMfaEnrollmentByEmail
} from '../services';
import { ErrorResponse, DeleteUserMfaEnrollmentSchema } from '../common';
import { config } from '../configurations';
import { getUserByEmail, updateUser, initializeUserActivityForFreshLogin } from '../models';
import { verifyFirebaseUserExists } from '../services/firebaseAuth';

/**
 * Start MFA enrollment process
 * @param req Request
 * @param res Response
 */
export const startMfaEnrollmentAPI = async (req, res, next) => {
    try {
        if (!isMfaEnabled()) {
            return res.status(400).json({
                ok: false,
                message: 'MFA is not enabled'
            });
        }

        // Get token from Authorization header (extracted by authorizeJwtToken middleware)
        const authHeader = req.headers["authorization"];
        if (!authHeader) {
            return res.status(400).json({
                ok: false,
                message: 'Authorization header is required'
            });
        }

        const idToken = authHeader.replace("Bearer ", "");
        if (!idToken) {
            return res.status(400).json({
                ok: false,
                message: 'ID token is required'
            });
        }

        const result = await startMfaEnrollment(idToken);

        if (result.success) {
            res.status(200).json({
                ok: true,
                sessionInfo: result.sessionInfo,
                secretKey: result.secretKey,
                qrCodeUrl: result.qrCodeUrl,
                totpUri: result.totpUri,
                message: 'MFA enrollment started successfully'
            });
        } else {
            res.status(400).json({
                ok: false,
                message: result.error || 'Failed to start MFA enrollment'
            });
        }
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Complete MFA enrollment process
 * @param req Request
 * @param res Response
 */
export const completeMfaEnrollmentAPI = async (req, res, next) => {
    try {
        if (!isMfaEnabled()) {
            return res.status(400).json({
                ok: false,
                message: 'MFA is not enabled'
            });
        }

        const { idToken, sessionInfo, totpCode, email } = req.body;

        if (!idToken || !sessionInfo || !totpCode || !email) {
            return res.status(400).json({
                ok: false,
                message: 'ID token, session info, TOTP code, and email are required'
            });
        }

        const result = await completeMfaEnrollment(idToken, sessionInfo, totpCode, email);

        if (result.success) {
            // Update user's MFA status in database and initialize activity tracking
            try {
                const userDetails = await getUserByEmail(email);
                if (userDetails) {
                    await updateUser(userDetails.id, {
                        mfaEnabled: true,
                        mfaEnrolledAt: new Date(),
                        forceMfaSetup: false
                    });

                    // Initialize user activity tracking for successful MFA enrollment
                    try {
                        const firebaseUserCheck = await verifyFirebaseUserExists(email);
                        if (firebaseUserCheck.exists && firebaseUserCheck.uid) {
                            // Use fresh login initializer to ensure clean session tracking
                            await initializeUserActivityForFreshLogin(
                                userDetails.id.toString(),
                                firebaseUserCheck.uid,
                                new Date() // Current time as auth_time for fresh MFA enrollment
                            );
                        }
                    } catch (activityError) {
                        console.error('Error initializing user activity tracking after MFA enrollment:', activityError);
                        // Continue with response even if activity tracking fails
                    }
                }
            } catch (dbError) {
                console.error('Error updating user MFA status:', dbError);
                // Continue with response even if DB update fails
            }

            res.status(200).json({
                ok: true,
                idToken: result.idToken,
                refreshToken: result.refreshToken,
                message: 'MFA enrollment completed successfully'
            });
        } else {
            res.status(400).json({
                ok: false,
                message: result.error || 'Failed to complete MFA enrollment'
            });
        }
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Start MFA sign-in process
 * @param req Request
 * @param res Response
 */
export const startMfaSignInAPI = async (req, res, next) => {
    try {
        if (!isMfaEnabled()) {
            return res.status(400).json({
                ok: false,
                message: 'MFA is not enabled'
            });
        }

        const { mfaPendingCredential, mfaEnrollmentId } = req.body;

        if (!mfaPendingCredential) {
            return res.status(400).json({
                ok: false,
                message: 'MFA pending credential is required'
            });
        }

        const result = await startMfaSignIn(mfaPendingCredential, mfaEnrollmentId);

        if (result.success) {
            res.status(200).json({
                ok: true,
                sessionInfo: result.sessionInfo,
                message: 'MFA sign-in started successfully'
            });
        } else {
            res.status(400).json({
                ok: false,
                message: result.error || 'Failed to start MFA sign-in'
            });
        }
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Complete MFA sign-in process
 * @param req Request
 * @param res Response
 */
export const completeMfaSignInAPI = async (req, res, next) => {
    try {
        if (!isMfaEnabled()) {
            return res.status(400).json({
                ok: false,
                message: 'MFA is not enabled'
            });
        }

        const { mfaPendingCredential, mfaEnrollmentId, totpCode } = req.body;

        if (!mfaPendingCredential || !mfaEnrollmentId || !totpCode) {
            return res.status(400).json({
                ok: false,
                message: 'MFA pending credential, enrollment ID, and TOTP code are required'
            });
        }

        const result = await completeMfaSignIn(mfaPendingCredential, mfaEnrollmentId, totpCode);

        if (result.success) {
            // After successful MFA verification, initialize user activity tracking
            try {
                // Decode the new idToken to get user information
                const admin = require('firebase-admin');
                const decodedToken = await admin.auth().verifyIdToken(result.idToken);

                if (decodedToken && decodedToken.email) {
                    const userDetails = await getUserByEmail(decodedToken.email);
                    if (userDetails) {
                        // Initialize user activity tracking for successful MFA sign-in
                        try {
                            // Use fresh login initializer to ensure clean session tracking
                            await initializeUserActivityForFreshLogin(
                                userDetails.id.toString(),
                                decodedToken.uid,
                                new Date(decodedToken.auth_time * 1000) // Convert Firebase timestamp to Date
                            );
                        } catch (activityError) {
                            console.error('Error initializing user activity tracking after MFA sign-in:', activityError);
                            // Continue with response even if activity tracking fails
                        }
                    }
                }

                // Since the frontend already has the user data from initial login,
                // we can return a simplified response and let the frontend handle the user data
                // The frontend should use the stored loginResult data
                res.status(200).json({
                    ok: true,
                    idToken: result.idToken,
                    refreshToken: result.refreshToken,
                    message: 'MFA sign-in completed successfully',
                    // Signal to frontend to use stored user data
                    useStoredUserData: true
                });
            } catch (error) {
                console.error('Error processing MFA completion:', error);
                // Fallback to just returning tokens
                res.status(200).json({
                    ok: true,
                    idToken: result.idToken,
                    refreshToken: result.refreshToken,
                    message: 'MFA sign-in completed successfully'
                });
            }
        } else {
            res.status(400).json({
                ok: false,
                message: result.error || 'Failed to complete MFA sign-in'
            });
        }
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Get user's MFA status
 * @param req Request
 * @param res Response
 */
export const getMfaStatusAPI = async (req, res, next) => {
    try {
        if (!isMfaEnabled()) {
            return res.status(200).json({
                ok: true,
                mfaEnabled: false,
                mfaRequired: false,
                message: 'MFA is not enabled'
            });
        }

        const { email } = req.query;
        if (!email) {
            return res.status(400).json({
                ok: false,
                message: 'Email is required'
            });
        }

        // Get user from database
        const userDetails = await getUserByEmail(email as string);
        if (!userDetails) {
            return res.status(404).json({
                ok: false,
                message: 'User not found'
            });
        }

        // Check if user has Firebase UID and get Firebase MFA status
        let firebaseMfaStatus = { mfaEnabled: false };
        if (userDetails.firebaseUid) {
            firebaseMfaStatus = await getUserMfaStatus(userDetails.firebaseUid);
        }

        res.status(200).json({
            ok: true,
            mfaEnabled: userDetails.mfaEnabled || false,
            mfaRequired: isMfaEnabled(),
            forceMfaSetup: userDetails.forceMfaSetup || false,
            firebaseMfaEnabled: firebaseMfaStatus.mfaEnabled,
            mfaEnrolledAt: userDetails.mfaEnrolledAt,
            message: 'MFA status retrieved successfully'
        });
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Check if MFA is required for the system
 * @param req Request
 * @param res Response
 */
export const checkMfaRequiredAPI = async (req, res, next) => {
    try {
        res.status(200).json({
            ok: true,
            mfaRequired: isMfaEnabled(),
            firebaseAuthEnabled: config.FIREBASE_AUTH_ENABLED,
            firebaseTotpMfaEnabled: config.FIREBASE_TOTP_MFA_ENABLED,
            message: 'MFA requirement status retrieved successfully'
        });
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * Delete user MFA enrollment by email (Admin only)
 * @param req Request
 * @param res Response
 */
export const deleteUserMfaEnrollmentAPI = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(DeleteUserMfaEnrollmentSchema, req.body);

        const { email } = req.body;

        // Call the service function
        const result = await deleteUserMfaEnrollmentByEmail(email);

        if (result.success) {
            res.status(200).json({
                ok: true,
                message: result.message
            });
        } else {
            res.status(400).json({
                ok: false,
                message: result.error || 'Failed to delete MFA enrollment'
            });
        }
    } catch (err) {
        console.error('Error in deleteUserMfaEnrollmentAPI:', err);
        notifyBugsnag(err);
        next(err);
    }
};
