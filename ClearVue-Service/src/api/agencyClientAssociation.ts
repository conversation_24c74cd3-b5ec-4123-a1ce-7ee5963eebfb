import { validateRequestData, notifyBugsnag } from './../utils';
import { CreateAgencyAssociationRequestSchema, UpdateAgencyAssociationRequestSchema, QueryParamsSchemaWithIdOnly, RestrictAgencyAssociationRequestSchema, RestrictCommentsForAgencyAssociationSchema, AgencyClientAssociationSchema, associationIdSchema, setTotalAssignmentPayFlagSchema, UpdateAgencyAssociationSuperAdminRequestSchema, UserType } from './../common';
import { createAgencyAssociationService, updateAgencyAssociationService, getAgencyAssociationListService, restrictAgencyAssociationService, restrictCommentsForAgencyAssociationService, setTotalAssignmentPayFlagService } from '../services'

/**
 * create agency
 * @param req Request
 * @param res Response
 */
export const createAgencyAssociation = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(CreateAgencyAssociationRequestSchema, req.body);
        let response = await createAgencyAssociationService(req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * update agency
 * @param req Request
 * @param res Response
 */
export const updateAgencyAssociation = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(associationIdSchema, req.params);

        // Validate request body
        if (UserType.CLEARVUE_ADMIN == parseInt(req.user.user_type_id)) {
            await validateRequestData(UpdateAgencyAssociationRequestSchema.concat(UpdateAgencyAssociationSuperAdminRequestSchema), req.body);
        } else {
            await validateRequestData(UpdateAgencyAssociationRequestSchema, req.body);
        }
        let response = await updateAgencyAssociationService(req.params.associationId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * get list of agency
 * @param req Request
 * @param res Response
 */
export const getAgencyAssociationList = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(AgencyClientAssociationSchema, req.query);
        let response = await getAgencyAssociationListService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * restrict agency
 * @param req Request
 * @param res Response
 */
export const restrictAgencyAssociation = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(associationIdSchema, req.params);
        // Validate request body
        await validateRequestData(RestrictAgencyAssociationRequestSchema, req.body);
        let response = await restrictAgencyAssociationService(req.params.associationId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * restrict comments for agency
 * @param req Request
 * @param res Response
 */
export const restrictCommentsForAgencyAssociation = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(associationIdSchema, req.params);
        // Validate request body
        await validateRequestData(RestrictCommentsForAgencyAssociationSchema, req.body);
        let response = await restrictCommentsForAgencyAssociationService(req.params.associationId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * set total assignment pay flag for agency
 * @param req Request
 * @param res Response
 */
export const setTotalAssignmentPayFlag = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(associationIdSchema, req.params);
        // Validate request body
        await validateRequestData(setTotalAssignmentPayFlagSchema, req.body);
        let response = await setTotalAssignmentPayFlagService(req.params.associationId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};
