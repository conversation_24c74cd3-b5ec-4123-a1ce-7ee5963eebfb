import { updateBookingForSiteAdminSchema, createBookingSchema, getBookingSchema, updateBookingByAgencySchema, workerProfileSchema, bookingIdSchema, getOpenBookingSchema, bulkFulFillBookingsCsvSchema, bulkFulFillBookingsCsvSchemaWithoutPagination, createBulkBookingCSVSchema, ErrorResponse } from "../common";
import { createBookingService, getBookingService, getBookingDetailsService, updateBookingDetailsService, updateBookingService, getOpenBookingService, updateBulkShiftBookings, createBulkShiftBookingService, deleteBookingAndAssociations, downloadBookingDynamicSampleSheetService } from "../services";
import { validateRequestData, notifyBugsnag, findDuplicatesKeyValuesInListOfObject, dynamicErrorObject } from "../utils";
const path = require('path');
const csvParser = require('csvtojson');
import { config } from '../configurations';
const moment = require('moment');
import { dateTimeFormates } from "../common/constants";



/**
 * API to add create booking.
 * @param req Request
 * @param res Response
 */
export const createBooking = async (req, res, next) => {
    try {
        await validateRequestData(createBookingSchema, req.body);
        let response = await createBookingService(req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to add create bulk booking.
 * @param req Request
 * @param res Response
 */
export const createBulkBooking = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.bookings.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }

        let csvData = await csvParser().fromString(req.files.bookings.data.toString('utf8'));

        let payload = await validateRequestData(createBulkBookingCSVSchema, csvData, true);


        if (payload.length > config.SHIFT_BOOKING_BULK_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidShiftBookingRequest);
        }

        let response = await createBulkShiftBookingService(payload, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};


/**
 * Listing for agency
 * Listing for site admin
 * Listing for client admin
 * @param req
 * @param res
 * @param next
 */
export const getBookings = async (req, res, next) => {
    try {
        await validateRequestData(getBookingSchema, req.query);
        let response = await getBookingService(req.query, req.user);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Booking Details for the agency admin user
 * Listing for site admin user
 * Listing for client admin user
 * @param req
 * @param res
 * @param next
 */
export const getBookingDetails = async (req, res, next) => {
    try {
        await validateRequestData(bookingIdSchema, req.params);
        await validateRequestData(getBookingSchema, req.query);
        let response = await getBookingDetailsService(req.query, req.params.bookingId, req.user);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Amend booking details by agency.
 * @param req
 * @param res
 * @param next
 */
export const updateBookingDetails = async (req, res, next) => {
    try {
        await validateRequestData(bookingIdSchema, req.params)
        await validateRequestData(updateBookingByAgencySchema, req.body);
        let response = await updateBookingDetailsService(req.params.bookingId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API for booking Updation
 * @param  {} req
 * @param  {} res
 * @param  {} next
 */
export const updateBooking = async (req, res, next) => {
    try {
        await validateRequestData(updateBookingForSiteAdminSchema, req.body);
        let response = await updateBookingService(req.user, req.body);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * @param req
 * @param res
 * @param next
 */
export const getOpenBookings = async (req, res, next) => {
    try {
        await validateRequestData(getOpenBookingSchema, req.query);
        let response = await getOpenBookingService(req.query);
        res.setHeader('Content-Type', 'text/csv');
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API for booking Request Deletion 
 * @param  {} req
 * @param  {} res
 * @param  {} nextMtech
 * 
 */
export const deleteBooking = async (req, res, next) => {
    try {
        await validateRequestData(bookingIdSchema, req.params);
        let response = await deleteBookingAndAssociations(req.params.bookingId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to fill bulk shift booking data from CSV
 * @param  req Request
 * @param  res Response
 * @param  next Next
 */
export const bulkFulFillBookings = async (req, res, next) => {
    try {
        await validateRequestData(getOpenBookingSchema, req.query);

        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.bookings.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }

        let csvData = await csvParser().fromString(req.files.bookings.data.toString('utf8'));

        let payload;
        payload = await validateRequestData(bulkFulFillBookingsCsvSchema, csvData, true);

        if (payload.length > config.SHIFT_BOOKING_BULK_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidShiftBookingRequest);
        }
        const duplicates = findDuplicatesKeyValuesInListOfObject(payload);
        if (duplicates.id_supervisor_flag.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForIdSupervisorFlag, Array.from(duplicates.id).join(', '))
            return res.status(400).json(errorObj);
        };

        const bulk_shiftbooking_data_list = payload.map(({
            id,
            client_name,
            site,
            department,
            region,
            shift,
            requested,
            start_date,
            end_date,
            supervisor_flag,
            sun,
            mon,
            tue,
            wed,
            thu,
            fri,
            sat,
            total_fulfillment
        }) => ({
            id: id,
            client_name,
            site,
            department,
            region,
            shift,
            requested_value_in_csv: requested,
            start_date: moment(start_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD),
            end_date: moment(end_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD),
            supervisor_flag,
            sun: Number(sun) || 0,
            mon: Number(mon) || 0,
            tue: Number(tue) || 0,
            wed: Number(wed) || 0,
            thu: Number(thu) || 0,
            fri: Number(fri) || 0,
            sat: Number(sat) || 0,
            total_fulfillment
        }));

        let response = await updateBulkShiftBookings(bulk_shiftbooking_data_list, req.user, req.query);
        res.status(response[0]).send(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download the Payroll Summary.
 * @param req
 * @param res
 * @param next
 */
export const downloadBookingDynamicSampleSheet = async (req, res, next) => {
    try {
        let response = await downloadBookingDynamicSampleSheetService(req.user);
        res.setHeader('Content-Type', 'text/csv');
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}
