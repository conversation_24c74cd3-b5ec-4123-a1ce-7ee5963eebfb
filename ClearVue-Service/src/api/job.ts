import { validateRequestData, notifyBugsnag, dynamicErrorObject } from './../utils';
import { CreateJobRequestSchema, jobIdSchema, PaginationSchemaWithClientId, UpdateJobRequestSchema, bulkUploadJobCsvSchema, RoleTypeForCSV, siteIdSchema, ErrorResponse } from './../common';
import { createJobService, updateJobService, getJobListService, getJobListingForDropDownService, getJobNameListingForDropDownService, addBulkJobs } from '../services'
import { config } from '../configurations';

const path = require('path');
const csvParser = require('csvtojson');

/**
 * create job
 * @param req Request
 * @param res Response
 */
export const createJob = async (req, res, next) => {
    try {
        // Validate request body
        await validateRequestData(CreateJobRequestSchema, req.body);
        let response = await createJobService(req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * update job
 * @param req Request
 * @param res Response
 */
export const updateJob = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(jobIdSchema, req.params);
        // Validate request body
        await validateRequestData(UpdateJobRequestSchema, req.body);
        let response = await updateJobService(req.params.jobId, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * get list of job
 * @param req Request
 * @param res Response
 */
export const getJobList = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(PaginationSchemaWithClientId, req.query);
        let response = await getJobListService(req.query);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to get the Job Listing for the dropdown.
 * @param req
 * @param res
 * @param next
 */
export const getJobListForDropDown = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(siteIdSchema, req.params);
        let response = await getJobListingForDropDownService(req.params.siteId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to get the job name listing for the drop down.
 * @param req
 * @param res
 * @param next
 */
export const getJobNameListForDropDown = async (req, res, next) => {
    try {
        // Validate query params
        await validateRequestData(siteIdSchema, req.params);
        let response = await getJobNameListingForDropDownService(req.params.siteId);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to upload bulk jobs data from CSV
 * @param  req Request
 * @param  res Response
 * @param  next Next
 */
export const bulkUploadJobs = async (req, res, next) => {
    try {
        if (!req.files || (!req.query.client_id)) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.jobs.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }

        let csvData = await csvParser().fromString(req.files.jobs.data.toString('utf8'));
        let payload = await validateRequestData(bulkUploadJobCsvSchema, csvData, true);

        if (payload.length > config.MAX_BULK_JOB_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidJobsNumbersRequest);
        }

        let role_flag = 0;
        let line_number = 1;
        let error_role_type = [];
        const bulk_job_data_list = payload.map(({
            job_name,
            role_type,
            shift_name,
            hours_per_week,
            site_name,
            department_name,
        }) => {
            line_number++;
            if (RoleTypeForCSV[role_type.trim().toUpperCase().replace(/-|_|\s/g, "")]) {
                return {
                    jobName: job_name,
                    type: RoleTypeForCSV[role_type.trim().toUpperCase().replace(/-|_|\s/g, "")],
                    shiftName: shift_name,
                    hoursPerWeek: hours_per_week,
                    siteName: site_name,
                    departmentName: department_name,
                }
            } else {
                role_flag = 1;
                error_role_type.push(line_number);
            }
        }
        );

        if (role_flag) {
            const errorObj = await dynamicErrorObject(ErrorResponse.InvalidRoleTypes, error_role_type)
            return res.status(400).json(errorObj);
        }

        if (payload.length > config.MAX_BULK_JOB_UPLOAD_LIMIT || payload.length < 1) {
            return res.status(400).json(ErrorResponse.InvalidJobsNumbersRequest);
        }

        let response = await addBulkJobs(
            bulk_job_data_list,
            parseInt(req.user.user_id),
            parseInt(req.query.client_id) || null
        );
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}
