import { validateRequestData, getSignedUrlForGetObject, notifyBugsnag, findDuplicatesKeyValuesInListOfObjects, dynamicErrorObject } from './../utils';
import { QueryParamsSchemaWithIdOnly, timeAndAttendanceCsvSchema, timeAndAttendanceIdsSchema, totalAgencyPayCsvSchema, totalAgencyPaySchema, deleteTAPSchema, UserType, ErrorResponse, ftpTimeAndAttendanceIdsSchema, clientIdSchema, tnaIdSchema } from './../common';
import { addTimeAndAttendance, getListOfTimeAndAttendanceService, getDetailOfTimeAndAttendanceService, addTotalAgencyPay, downloadTotalAgencyPayFileService, deleteTAPDataService, triggerFtpLookupPythonService, getAdjustmentRowsService } from '../services';
import { config } from '../configurations';
const path = require('path');
const csvParser = require('csvtojson');
const { execFile } = require('child_process');

/**
 * API to upload and process the Time and Attendance data.
 * @param req
 * @param res
 * @param next
 */
export const uploadTimeAndAttendance = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.timeAndAttendance.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }
        let csv = String(req.files.timeAndAttendance.data)
        let csvData = await csvParser().fromString(req.files.timeAndAttendance.data.toString('utf8'));
        let payload = await validateRequestData(timeAndAttendanceCsvSchema, csvData, true);

        if (!payload.length) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }

        await validateRequestData(timeAndAttendanceIdsSchema, req.body);
        // req.files.timeAndAttendance.name,
        let response = await addTimeAndAttendance(csv, payload, req.body, req.user, false);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


export const ftpUploadTimeAndAttendance = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.timeAndAttendance.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }
        let csv = String(req.files.timeAndAttendance.data)
        let csvData = await csvParser().fromString(req.files.timeAndAttendance.data.toString('utf8'));
        let payload = await validateRequestData(timeAndAttendanceCsvSchema, csvData, true);

        if (!payload.length) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        await validateRequestData(ftpTimeAndAttendanceIdsSchema, req.body);

        let response = await addTimeAndAttendance(csv, payload, req.body, { user_id: 1 }, true);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


export const triggerFtpLookupTna = async (req, res, next) => {
    try {

        await validateRequestData(clientIdSchema, req.params);
        let response = await triggerFtpLookupPythonService(req.params.clientId, req.user, 'TNA');
        res.status(response[0]).json(response[1]);

    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};

/**
 * API to GET the list of Time and Attendance Data.
 * @param req
 * @param res
 * @param next
 */
export const getListOfTimeAndAttendance = async (req, res, next) => {
    try {
        let response = await getListOfTimeAndAttendanceService(req.user.client_id, req.query.page, req.query.limit, req.query.sort_by, req.query.sort_type);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to GET the list of Adjustment rows from tna.
 * @param req
 * @param res
 * @param next
 */
export const getAdjustmentRows = async (req, res, next) => {
    try {
        await validateRequestData(tnaIdSchema, req.params);
        let response = await getAdjustmentRowsService(req.params.tnaId, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to GET the details of the Time and Attendance.
 * @param req
 * @param res
 * @param next
 */
export const getDetailOfTimeAndAttendance = async (req, res, next) => {
    try {
        await validateRequestData(QueryParamsSchemaWithIdOnly, req.params);
        let response = await getDetailOfTimeAndAttendanceService(req.params.id, req.query.page, req.query.limit, req.query.sort_by, req.query.sort_type);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download sample time and attendance.
 * @param req
 * @param res
 * @param next
 */
export const downloadTimeAndAttendanceSampleFile = async (req, res, next) => {
    try {
        let link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.TIME_AND_ATTENDANCE_SAMPLE_FOLDER, config.TIME_AND_ATTENDANCE_SAMPLE_FILE_NAME);
        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * API to download uploaded TAP file by agencies 
 * @param req
 * @param res
 * @param next
 */
export const downloadTotalAgencyPayFile = async (req, res, next) => {
    try {
        await validateRequestData(totalAgencyPaySchema, req.query);
        let response = await downloadTotalAgencyPayFileService(req.user, req.query);
        res.status(response[0]).json(response[1]);
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/**
 * API to download sample TAP Sheet.
 * @param req
 * @param res
 * @param next
 */
export const downloadTAPSampleFile = async (req, res, next) => {
    try {
        let link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.TAP_SAMPLE_FILE_BUCKET_FOLDER, config.TAP_UPLOAD_SAMPLE_FILE_BUCKET_KEY);
        res.status(200).json({
            ok: true,
            "resource_url": link.url,
        })
    }
    catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}


/* API to upload and process the Total Agency Pay data */
export const uploadTotalAgencyPay = async (req, res, next) => {
    try {
        if (!req.files) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }
        let extension = path.extname(req.files.totalAgencyPay.name);
        if (extension !== ".csv") {
            return res.status(400).json(ErrorResponse.InvalidFileTypeError)
        }
        let csv = String(req.files.totalAgencyPay.data)
        let csvData = await csvParser().fromString(req.files.totalAgencyPay.data.toString('utf8'));
        let payload = await validateRequestData(totalAgencyPayCsvSchema, csvData, true);

        if (!payload.length) {
            return res.status(400).json(ErrorResponse.BadRequestError);
        }

        await validateRequestData(timeAndAttendanceIdsSchema, req.body);

        const duplicates = findDuplicatesKeyValuesInListOfObjects(payload, true);
        if (duplicates.employee_id.size > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.DuplicateEntriesForEmployeeId, Array.from(duplicates.employee_id).join(', '))
            return res.status(400).json(errorObj);
        };

        let response = await addTotalAgencyPay(csv, payload, req.body, req.user);
        res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
}

/**
 * Delete TAP file
 * @param req Request
 * @param res Response
 * @param next
 */
export const deleteTAPFile = async (req, res, next) => {
    try {
        // Validate request body
        let payload = await validateRequestData(deleteTAPSchema, req.body);
        let response = await deleteTAPDataService(payload, req.user);
        return res.status(response[0]).json(response[1]);
    } catch (err) {
        notifyBugsnag(err);
        next(err);
    }
};
