import e from 'express';
import {
    ErrorCodes, ErrorResponse, HallOfFameTypes, MessageActions, MessageReceiverGroups, MessageType,
    WorkerSideMessagesScreenViewType, WorkerSideMessagesType, AutomatedMessagesLabels, site, message, Languages, MessageBodyContentType, MessageUpdationType, DeviceTokensByLanguage
} from '../common';
import {
    addRecordInMessageReceiverGroup, addWorkerTraining, createMessage, createMessageTemplate,
    getAgencyById, getClientsById, getSentMessageList, getTrainingMessageDetails,
    getWorkerAssociatedSiteAndAgency, getWorkerDeviceTokens, getWorkersAsPerSelectedGroups,
    getWorkerSideMessagesListFromDatabase, updateHallOfFameDataForWorkers, updateMessageTemplate,
    updateMessageReadStatusHelper, getMessageDetailsModel, getTemplateList, getMessageTemplateDetails, getDefaultMessageTemplate,
    getDepartmentNames, getJobNames, getShiftNames, getSiteNamesById, getWorkerNamesByIds, getWorkerIdfromUserId, getMessageDetailsById, createMessageReaction, getMessageReactionByWorkerIdMessageId, updateMessageReaction,
    createMessageComment, getMessageComments, getMessageReactionCount, getMessageWorkerReactions, getSystemDefaultMessageList, updateAutomatedMessage, getWorkerIdFromMessageId, getUserById, getAutomatedMessageDetails, deleteMessageTemplateById, getWorkersByIds
} from '../models';
import { notifyBugsnag, removeKeyFromObject, sendNotificationsToMobiles, arrayOfObjectsToObject, translateText, splitArrayWithSize, translateTextToListOfLanguages, deviceTokenGroupByLanguages, detectNonEnglishText } from '../utils';
const { EasyPromiseAll } = require('easy-promise-all');
const _ = require('lodash');


/**
 * Send messages to the workers who qualify in selected criteria by the agency admin or site admin
 * @param  {any} payload
 * @param  {number} loggedInUser
 * @param  {any} params
 */
export const sendMessageToWorkersService = async (payload: any, loggedInUser: number, params: any) => {
    /*
    1. Get list of worker-ids
    2. Remove duplicate worker-ids
    3. Add record in message table
    4. Update records as per message type:
        - Increment hall of fame count
        - Training table insert
    5. Add record in message_receiver_workers table
    6. Fetch device tokens for the workers and send notifications to them
    */

    try {
        let shiftIds = [];
        let departmentIds = [];
        let jobIds = [];
        let workers = [];
        let nationalityList = [];
        let hallOfFameFieldName = "";
        let groupWorkers = [];

        // Get list of workers as per the selected criteria
        payload.send_to.forEach((element) => {
            if (element.type === MessageReceiverGroups.DEPARTMENT) {
                departmentIds.push(...element.data);
            }
            else if (element.type === MessageReceiverGroups.JOB) {
                jobIds.push(...element.data);
            }
            else if (element.type === MessageReceiverGroups.SHIFT) {
                shiftIds.push(...element.data);
            }
            else if (element.type === MessageReceiverGroups.WORKERS) {
                workers.push(...element.data);
            }
            else if (element.type === MessageReceiverGroups.NATIONALITY) {
                nationalityList.push(...element.data);
            }
        })

        if (shiftIds.length || jobIds.length || departmentIds.length || nationalityList.length || !workers.length) {
            groupWorkers = await getWorkersAsPerSelectedGroups(
                params.site_id, params.client_id, params.agency_id, shiftIds, jobIds, departmentIds, nationalityList, params.type
            )
        }

        groupWorkers.forEach((element) => {
            workers.push(element.worker_id);
        })

        // Remove duplicate workers
        workers = [...new Set(workers)];

        if (!workers.length) {
            return [404, ErrorResponse.WorkersNotFoundForSendingMessage];
        }

        // Add new message into the database
        let messageId = await createMessage(payload, loggedInUser, params);


        /* Update records as per message type:
            - Increment hall of fame count
            - Training table insert
        */
        hallOfFameFieldName = payload.type === MessageType.AWARD ? HallOfFameTypes.AWARD : (
            payload.type === MessageType.BADGE ? HallOfFameTypes.BADGE : (
                payload.type === MessageType.RECOGNITION ? HallOfFameTypes.RECOGNITION : ""))

        if (hallOfFameFieldName) {
            await updateHallOfFameDataForWorkers(hallOfFameFieldName, loggedInUser, workers);
        }

        if (payload.type === MessageType.TRAINING) {
            await addWorkerTraining(messageId, workers, loggedInUser);
        }

        // send notifiction in chunk of size 1000
        let arrays = await splitArrayWithSize(workers, 1000);
        Array.from(arrays).forEach(async (workers) => {
            // Add record in message_receiver_workers table
            await addRecordInMessageReceiverGroup(messageId, workers, loggedInUser);

            let workerDetails = await getWorkersByIds(workers);
            const deviceTokensByLanguage: DeviceTokensByLanguage = await deviceTokenGroupByLanguages(workerDetails);

            // Send notification to the workers asynchronously
            if (Object.keys(deviceTokensByLanguage).length > 0) {
                for (let lang in deviceTokensByLanguage) {
                    const languageTokens = deviceTokensByLanguage[lang];

                    sendMessageNotification(
                        workers,
                        payload,
                        messageId,
                        params,
                        [...new Set(languageTokens)], // Pass the language-specific tokens
                        lang
                    );
                }
            }
        });

        return [
            200,
            {
                ok: true,
                message: MessageActions.MESSAGE_SENT
            },
        ];
    } catch (err) {
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};


/**
 * Send notification to the workers
 * @param  {Array<number>} workerIds
 * @param  {any} payload
 * @param  {number} messageId
 * @param  {any} params
 * @param  {string} deviceTokens
 * @param  {string} languageCode
 */
export const sendMessageNotification = async (workerIds: Array<number>, payload: any, messageId: number, params: any, deviceTokens: Array<string> = [], languageCode: string = Languages.ENGLISH) => {
    let workerDeviceTokens = [];

    if (deviceTokens.length) {
        workerDeviceTokens = deviceTokens;
    } else {
        let workerDeviceTokensResult = await getWorkerDeviceTokens(workerIds);
        workerDeviceTokensResult.forEach((workerDetail) => {
            if (workerDetail.deviceToken) workerDeviceTokens.push(workerDetail.deviceToken)
        })
    }

    let text = (payload.body[0].find(obj => obj.type === MessageBodyContentType.TEXT) || {}).data || "";
    let title = payload.title;

    // Send worker's app language specific notifications
    if (languageCode !== Languages.ENGLISH && languageCode !== Languages.TIGRINYA) {
        const translationObject = payload.body[0].find(obj => obj.type === MessageBodyContentType.TRANSLATE);
        text = translationObject ? translationObject.data[languageCode] : text;
        title = (payload.titleTranslations || payload.title_translations)?.[languageCode] || title;
    }

    let image = (payload.body[0].find(obj => obj.type === MessageBodyContentType.MEDIA) || {}).data || "";

    let customData = {
        type: payload.type === MessageType.REWARD ? WorkerSideMessagesType.GENERAL : payload.type.toLowerCase(),
        message_id: messageId,
        msg_id: messageId,
        send_by: params.agencyId || params.agency_id ? "agency" : "client"
    }
    await sendNotificationsToMobiles(workerDeviceTokens, title, text, image, customData);
}


/**
 * Get list of sent messages to display to the site or agency admin
 * @param  {number} loggedInUser
 * @param  {any} params
 */
export const getSentMessagesListService = async (loggedInUser: number, params: any) => {
    const { "sort_by": sortBy, "sort_type": sortType, "limit": limit, "page": page, ...otherParams } = params;

    let response, result;
    if (otherParams.type == "SYSTEM_DEFAULT") {
        response = await getSystemDefaultMessageList(otherParams, sortBy, sortType, page, limit);
        result = response[0].map((record) => {
            record.body = JSON.parse(record.body);
            record.title_translations = record.title_translations ? JSON.parse(record.title_translations) : null;
            return record
        })
    }
    else {
        response = await getSentMessageList(loggedInUser, otherParams, sortBy, sortType, page, limit);
        result = response[0].map((record) => {
            record.body = JSON.parse(record.body);
            record.title_translations = record.title_translations ? JSON.parse(record.title_translations) : null;
            record.receiver = JSON.parse(record.receiver);
            record.sent_to = record.receiver.length == 1 && record.receiver[0].type == "workers" && record.receiver[0].data.length ? "Individual" : record.receiver[0].data.length < 1 || record.receiver.length > 1 ? "Group" : "Other";
            return record
        })
    };

    // sorting of results by "sent_to" filed.
    if (sortBy.toLowerCase() == "sent_to") {
        const sortOrder = sortType.toLowerCase() == 'asc' ? 1 : -1;
        result.sort((a, b) => a.sent_to.localeCompare(b.sent_to) * sortOrder);
    }

    return [200, {
        ok: true,
        result: result,
        count: response[1]
    }];
};


/**
 * Update existing SYSTEM_DEFAULT message from `message` table for the given messageId
 * @param  {number} messageId
 * @param  {any} payload
 * @param  {string} updateType
 * @param  {number} loggedInUser
 */
export const updateAutomatedMessageService = async (messageId: number, queryParam: any, payload: any, updateType: string, loggedInUser: number) => {
    let message = await getAutomatedMessageDetails(messageId, queryParam);
    if (!message || message.type != MessageType.SYSTEM_DEFAULT) {
        return [404, ErrorResponse.ResourceNotFound]
    }

    const currentBody = JSON.parse(message.body);

    // Update only Translation
    if (updateType === MessageUpdationType.TRANSLATION_DATA_ONLY && currentBody) {
        const newBody = currentBody[0].filter(obj => obj.type !== MessageBodyContentType.TRANSLATE);
        const newTranslationObject = {
            data: payload.data,
            type: MessageBodyContentType.TRANSLATE
        };
        newBody.push(newTranslationObject);
        currentBody[0] = newBody;
        payload.body = currentBody;
    }
    // update other data except Translation 
    else if (updateType === MessageUpdationType.OTHER && currentBody) {
        const translate = currentBody[0].filter(obj => obj.type === MessageBodyContentType.TRANSLATE);
        const survey = currentBody[0].filter(obj => obj.type === MessageBodyContentType.SURVEY);
        payload.body[0] = payload.body[0].concat(survey, translate);
    }

    let updateResponse = await updateAutomatedMessage(messageId, payload, loggedInUser);
    if (!updateResponse.affected) {
        return [404, ErrorResponse.InvalidMessageToUpdate]
    }

    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AUTOMATED_MESSAGE,
    }];
};


/**
 * Get list of messages to display to the workers in mobile application
 * @param  {number} loggedInUser
 * @param  {any} params
 */
export const getWorkerSideMessagesListService = async (loggedInUser: number, params: any) => {
    /**
    1. Generate where condition as per the type of screen
        * General - General, Recognition, Award, Badge, Reward (Only assigned messages)
        * Social Feed - General, Recognition, Award, Badge (All the messages for agency/site without considering message read status)
        * Recognition - Recognition (Only assigned messages)
        * Award - Award (Only assigned messages)
        * Badge - Badge (Only assigned messages)
        * Training - Training (Only assigned messages)
    2. Fetch records as per the generated where condition from the database
    3. Parse Response Data
    4. Return Response
    */
    const { "sort_by": sortBy, "sort_type": sortType, "limit": limit, "page": page, ...otherParams } = params;
    let whereConditionString = "";
    let messageTypes = [];
    let groupByString = "";

    switch (true) {
        case (params.type === WorkerSideMessagesType.GENERAL): {
            messageTypes = [
                MessageType.AWARD, MessageType.BADGE, MessageType.REWARD, MessageType.RECOGNITION, MessageType.SYSTEM
            ];

            if (params.view === WorkerSideMessagesScreenViewType.AGENCY) {
                whereConditionString = " (message.agency_id IS NOT NULL OR message.type = 'SYSTEM')";
            } else if (params.view === WorkerSideMessagesScreenViewType.CLIENT) {
                whereConditionString = " message.agency_id IS NULL";
            }
            break;
        }

        case (params.type === WorkerSideMessagesType.RECOGNITION): {
            messageTypes = [
                MessageType.RECOGNITION
            ];
            break;
        }

        case (params.type === WorkerSideMessagesType.AWARD): {
            messageTypes = [
                MessageType.AWARD
            ];
            break;
        }

        case (params.type === WorkerSideMessagesType.TRAINING): {
            messageTypes = [
                MessageType.TRAINING
            ];
            break;
        }

        case (params.type === WorkerSideMessagesType.BADGE): {
            messageTypes = [
                MessageType.BADGE, MessageType.TRAINING
            ];
            whereConditionString += "(worker_training.is_training_completed IS NULL OR worker_training.is_training_completed=1)";
            break;
        }

        case (params.type === WorkerSideMessagesType.FEED): {
            messageTypes = [
                MessageType.GENERAL, MessageType.RECOGNITION, MessageType.AWARD
            ];

            groupByString = "message.id";
            let metaDataResponse = await getWorkerAssociatedSiteAndAgency(loggedInUser);

            if (!_.size(metaDataResponse)) {
                return [200, {
                    ok: true,
                    result: null,
                    count: null,
                    logo: null,
                    unread_message_count: null
                }];
            }

            whereConditionString += getMessageReceiverWorkerDBCondition(metaDataResponse, params.view)

            break;
        }
    }

    let dbResponse = await getWorkerSideMessagesListFromDatabase(
        whereConditionString, sortBy, sortType, page, limit, loggedInUser, messageTypes, params.type, groupByString
    );

    let parsedResponse = await parseWorkerMessages(dbResponse[1], params.view, params.type, params.language_code, loggedInUser);

    return [200, {
        ok: true,
        result: parsedResponse[0],
        count: parseInt(dbResponse[0]),
        logo: parsedResponse[1],
        unread_message_count: parseInt(dbResponse[2].unread_message_count)
    }];
};

const getMessageReceiverWorkerDBCondition = (dbRecords: any, viewType: any) => {
    let condition = "";

    dbRecords.forEach((workerDetails) => {
        if (viewType === WorkerSideMessagesScreenViewType.AGENCY) {
            condition += ` (message.client_id = ${workerDetails.client_id} AND message.agency_id = ${workerDetails.agency_id} AND site_id=${workerDetails.site_id}) OR `;
        } else if (viewType === WorkerSideMessagesScreenViewType.CLIENT) {
            condition += ` (message.client_id = ${workerDetails.client_id} AND site_id=${workerDetails.site_id} AND message.agency_id IS NULL) OR `;
        } else {
            condition += ` (message.client_id = ${workerDetails.client_id} AND site_id=${workerDetails.site_id} AND (message.agency_id = ${workerDetails.agency_id} OR message.agency_id IS NULL)) OR `;
        }
    })

    return condition.slice(0, -3);;
}


/**
 * It will get message Reacitons Counts. [i.e. "LIKE": <n>, "DISLIKE": <n>]
 * @param  {Array<any>} messageIdList
 */
const getMessageReactionCountService = async (messageIdList: Array<any>) => {
    let MessageReactionsCount = {};
    if (messageIdList.length == 0) {
        return MessageReactionsCount;
    }

    let MessageReactionCountRaw = await getMessageReactionCount(messageIdList);
    for await (const reaction of MessageReactionCountRaw) {
        let message_id = reaction.message_id
        if (!MessageReactionsCount[message_id]) {
            MessageReactionsCount[message_id] = {};
        };
        MessageReactionsCount[message_id][reaction.reaction] = Number(reaction.reaction_counts);
    }
    return MessageReactionsCount;
}


/**
 * It will get Worker specific message reaction [i.e. CurrentUserReaction] 
 * @param  {Array<any>} messageIdList
 * @param  {number} loggedInUser
 */
const getMessageWorkerReactionsService = async (messageIdList: Array<any>, loggedInUser: number) => {
    let messageWorkerReactions = {};
    if (messageIdList.length == 0) {
        return messageWorkerReactions;
    }

    let messageWorkerSpecificReaction = await getMessageWorkerReactions(messageIdList, loggedInUser);
    for await (const reaction of messageWorkerSpecificReaction) {
        let message_id = reaction.message_id
        messageWorkerReactions[message_id] = { "current_user_reaction": reaction.reaction };
    }
    return messageWorkerReactions;
}


const parseMessageBodySelectTranslation = async (messageBody: string, languageCode: string) => {
    try {
        const body = JSON.parse(messageBody);

        if (languageCode && languageCode !== Languages.ENGLISH && languageCode !== Languages.TIGRINYA) {
            const textObject = body[0].find(obj => obj.type === MessageBodyContentType.TEXT);
            const translationObject = body[0].find(obj => obj.type === MessageBodyContentType.TRANSLATE);

            // Process only if translations are available
            if (translationObject) {
                const translations = translationObject.data[languageCode]
                if (textObject) {
                    textObject.data = translations;
                }
            }
        }
        return body;
    } catch (error) {
        console.error(`Error: ${error}`);
        return error;
    }
};


const parseWorkerMessages = async (messageList: Array<any>, view: string = null, type: string, languageCode: string, loggedInUser: number) => {
    let response = [];
    let id = null;
    let logo = "";
    let shiftIdObj = {};
    let departmentIdObj = {};
    let jobIdObj = {};
    let workersObj = {};
    let messageReactions = {};
    let messageWorkerSpecificReaction = {};
    let siteObj;

    if (type === WorkerSideMessagesType.FEED) {
        const [workersList, siteList, jobIdList, departmentIdList, shiftIdList] = getMessageReceiverList(messageList);

        let { shiftIdDetail, departmentIdDetail, jobIdDetail, workersDetail, siteDetail } = await EasyPromiseAll({
            shiftIdDetail: shiftIdList.length ? await getShiftNames(shiftIdList) : [],
            departmentIdDetail: departmentIdList.length ? getDepartmentNames(departmentIdList) : [],
            jobIdDetail: jobIdList.length ? getJobNames(jobIdList) : [],
            workersDetail: workersList.length ? getWorkerNamesByIds(workersList) : [],
            siteDetail: siteList.length ? getSiteNamesById(siteList) : [],
        });

        shiftIdObj = shiftIdDetail && arrayOfObjectsToObject(shiftIdDetail, "id", "name");
        departmentIdObj = departmentIdDetail && arrayOfObjectsToObject(departmentIdDetail, "id", "name");
        jobIdObj = jobIdDetail && arrayOfObjectsToObject(jobIdDetail, "id", "name");
        workersObj = workersDetail && arrayOfObjectsToObject(workersDetail, "id", "user_name");
        siteObj = siteDetail && arrayOfObjectsToObject(siteDetail, "id", "name");
    }

    // Create List Of Message IDs
    let messageIdList = [];
    for await (const message of messageList) {
        messageIdList.push(message.message_id)
    }

    if (messageIdList) {
        messageReactions = await getMessageReactionCountService(messageIdList)
        messageWorkerSpecificReaction = await getMessageWorkerReactionsService(messageIdList, loggedInUser)
    }

    for await (const message of messageList) {
        if (view) {
            if (view === WorkerSideMessagesScreenViewType.CLIENT && !id) {
                id = message.client_id;
            } else if (view === WorkerSideMessagesScreenViewType.AGENCY && !id) {
                id = message.agency_id;
            }
        }

        let profile_pic = message.agency_id ? (await getAgencyById(message.agency_id, false))['resource'] :
            message.client_id ? (await getClientsById(message.client_id))['resource'] : null;
        response.push({
            id: message.message_id,
            sender: message.client_id ? "C" : "A",
            profile_pic: profile_pic,
            from: message.message_from,
            title: message.message_title_translations ? JSON.parse(message.message_title_translations)[languageCode] || message.message_title : message.message_title,
            body: message.message_body ? await parseMessageBodySelectTranslation(message.message_body, languageCode) : message.message_body,
            created_at: message.message_receiver_workers_created_at,
            is_message_read: Boolean(message.is_message_read),
            user_name: (message.message_type !== MessageType.GENERAL && type === WorkerSideMessagesType.FEED) ? getReceiverName(
                message.message_receiver || [], message.site_id, shiftIdObj, departmentIdObj, jobIdObj, workersObj, siteObj
            ) : "",
            message_receiver_worker_id: message.message_receiver_workers_id,
            type: message.message_type.toLowerCase(),
            reactions: {
                "LIKE": messageReactions.hasOwnProperty(message.message_id) ? messageReactions[message.message_id]["LIKE"] || 0 : 0,
                "DISLIKE": messageReactions.hasOwnProperty(message.message_id) ? messageReactions[message.message_id]["DISLIKE"] || 0 : 0,
                "current_user_reaction": messageWorkerSpecificReaction.hasOwnProperty(message.message_id) ? messageWorkerSpecificReaction[message.message_id]["current_user_reaction"] : null,
            },
            is_comment_allowed: message.is_comment_allowed
        })
    }

    if (view === WorkerSideMessagesScreenViewType.CLIENT && id) {
        logo = (await getClientsById(id))['resource'];
    } else if (view === WorkerSideMessagesScreenViewType.AGENCY && id) {
        logo = (await getAgencyById(id, false))['resource'];
    }
    return [response, logo];
}


/**
 * Get receiver name to display in mobile application
 * @param  {} receiver
 */
const getReceiverName = (receiver, siteId, shiftIdObj, departmentIdObj, jobIdObj, workersObj, siteObj) => {
    let receiverString = "";

    if (!receiver || !receiver.length) {
        return ""
    } else {
        if (receiver[0].type == "workers" && (!receiver[0].data || !receiver[0].data.length)) {
            receiverString = siteObj[siteId] ? `Site: ${siteObj[siteId]}` : ""
        } else if (receiver[0].type == "workers") {
            let data = [];
            receiver[0].data.forEach((workerId) => {
                workersObj[workerId] && data.push(workersObj[workerId]);
            })
            receiverString = data.join(", ");
        } else {
            let groupReceivers = []
            receiver.forEach((group) => {

                if (group.type === "department") {
                    let data = [];
                    group.data.forEach((id) => {
                        departmentIdObj[id] && data.push(departmentIdObj[id]);
                    });
                    groupReceivers.push(`Department: ${data.join(", ")}`);
                } else if (group.type === "shift") {
                    let data = [];
                    group.data.forEach((id) => {
                        shiftIdObj[id] && data.push(shiftIdObj[id]);
                    });
                    groupReceivers.push(`Shift: ${data.join(", ")}`);
                } else if (group.type === "job") {
                    let data = [];
                    group.data.forEach((id) => {
                        jobIdObj[id] && data.push(jobIdObj[id]);
                    });
                    groupReceivers.push(`Role: ${data.join(", ")}`);
                } else if (group.type === "nationality") {
                    groupReceivers.push(`Nationality: ${group.data.join(", ")}`);
                }
            })
            receiverString = groupReceivers.join(" | ")
        }
        return receiverString;
    }
}

/**
 * Get message receiver groups
 * @param  {Array<any>} messageList
 */
const getMessageReceiverList = (messageList: Array<any>) => {
    let shiftIdList = [];
    let departmentIdList = [];
    let jobIdList = [];
    let workersList = [];
    let siteList = [];

    messageList.forEach(message => {
        message.message_receiver = message.message_receiver ? JSON.parse(message.message_receiver) : []
        if (!message.message_receiver || (message.message_receiver[0] && (!message.message_receiver[0].data || !message.message_receiver[0].data.length))) {
            siteList.push(message.site_id)
        }
        (message.message_receiver).forEach(element => {
            if (element.type === "workers") {
                workersList.push(...element.data);
            } else if (element.type === "department") {
                departmentIdList.push(...element.data);
            } else if (element.type === "shift") {
                shiftIdList.push(...element.data);
            } else if (element.type === "job") {
                jobIdList.push(...element.data);
            }
        });
    });

    return [
        [...new Set(workersList)],
        [...new Set(siteList)],
        [...new Set(jobIdList)],
        [...new Set(departmentIdList)],
        [...new Set(shiftIdList)]
    ]
}

/**
 * create a new message template for sending messages
 * @param  {any} payload
 * @param  {number} loggedInUser
 */
export const createMessageTemplateService = async (payload: any, loggedInUser: number) => {
    try {
        // Add new message template into the database
        let templateInsert = [{
            name: payload.name,
            title: payload.title,
            titleTranslations: payload.title_translations || null,
            type: payload.type,
            from: payload.from,
            body: payload.body,
            language: payload.languages.label,
            code: payload.languages.value,
            createdBy: loggedInUser.toString(),
            modifiedBy: loggedInUser.toString()
        }]
        let messageTemplate = await createMessageTemplate(templateInsert);
        return [
            201,
            {
                ok: true,
                id: messageTemplate[0].id,
                message: MessageActions.TEMPLATE
            },
        ];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.TemplateNameAlreadyExists]    // Return 409 if template name already exists
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * update existing templates by templateId
 * @param  {number} id
 * @param  {any} payload
 * @param  {number} loggedInUser
 */
export const updateMessageTemplateService = async (id: number, payload: any, loggedInUser: number) => {
    try {
        let updateResponse = await updateMessageTemplate(id, payload, loggedInUser);
        if (!updateResponse.affected) {
            return [404, ErrorResponse.ResourceNotFound]        // Return 404 if any foreign key contraint is not available in DB
        }
        return [200, {
            ok: true,
            message: MessageActions.UPDATE_TEMPLATE,
        }];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.TemplateNameAlreadyExists]    // Return 409 if template name already exists
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * get list of available templates
 * @param  {Number} loggedInUser
 */
export const getTemplateListService = async (loggedInUser: number, params: any) => {
    let templates = await getTemplateList(loggedInUser, params);
    return [200, {
        ok: true,
        templates
    }];
};

/**
 * Get training message details for the worker
 * @param  {number} loggedInUser
 * @param  {string} languageCode
 * @param  {number} messageId
 */
export const getWorkerTrainingMessageDetailsService = async (messageId: number, languageCode: string, loggedInUser: number) => {
    let message = await getTrainingMessageDetails(messageId, loggedInUser);
    if (!message) return [404, ErrorResponse.ResourceNotFound]

    // Get list of workers who recieved the particular message
    // To check loggedIn worker has access to read training message
    let workerIdList = await getWorkerIdFromMessageId(messageId);
    const latestWorkerId = Math.max(...(await getWorkerIdfromUserId({ userId: loggedInUser })).map(({ id }) => id));
    if (!latestWorkerId) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    if (!workerIdList.includes(latestWorkerId)) {
        return [403, ErrorResponse.PermissionDenied]
    }

    return [200, {
        ok: true,
        result: {
            id: message.message_id,
            sender: message.client_id ? "C" : "A",
            profile_pic: message.agency_id ? (await getAgencyById(message.agency_id, false))['resource'] : (await getClientsById(message.client_id))['resource'],
            from: message.message_from,
            title: message.message_title_translations ? JSON.parse(message.message_title_translations)[languageCode] || message.message_title : message.message_title,
            body: message.message_body ? await parseMessageBodySelectTranslation(message.message_body, languageCode) : message.message_body,
            created_at: message.message_created_at,
            user_name: message.user_name,
            is_training_completed: Boolean(message.is_training_completed),
            training_completed_at: message.training_completed_at,
            require_more_training: Boolean(message.require_more_training),
            require_training_updated_at: message.require_training_updated_at
        },
    }];
};

/**
 * Update the message read status
 * @param  loggedInUser
 * @param  {number} messageId
 */
export const updateMessageStatusService = async (messageId: number, loggedInUser) => {
    let message = await getMessageDetailsModel(messageId);
    if (!message) return [404, ErrorResponse.ResourceNotFound]

    // Get list of workers who recieved the particular message
    // To check loggedIn worker has access to update message status
    let workerIdList = await getWorkerIdFromMessageId(messageId);
    const latestWorkerId = Math.max(...(await getWorkerIdfromUserId({ userId: loggedInUser.user_id })).map(({ id }) => id));
    if (!latestWorkerId) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    if (!workerIdList.includes(latestWorkerId)) {
        return [403, ErrorResponse.PermissionDenied]
    }

    await updateMessageReadStatusHelper(messageId, parseInt(loggedInUser.user_id))
    return [200, { ok: true }]
};


/**
 * Get message details
 * @param  {number} loggedInUser
 * @param  {number} messageId
 * @param  {string} languageCode
 * @param  {number} messageReceiverWorkerId
 */
export const getMessageDetailsService = async (messageId: number, messageReceiverWorkerId: number, languageCode: string, loggedInUser: number) => {
    let message = await getMessageDetailsModel(messageId, messageReceiverWorkerId);
    if (!message) return [404, ErrorResponse.ResourceNotFound]

    // Get list of workers who recieved the particular message
    // To check loggedIn worker has access to read message
    let workerIdList = await getWorkerIdFromMessageId(messageId);
    const latestWorkerId = Math.max(...(await getWorkerIdfromUserId({ userId: loggedInUser })).map(({ id }) => id));
    if (!latestWorkerId) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    if (!workerIdList.includes(latestWorkerId)) {
        return [403, ErrorResponse.PermissionDenied]
    }

    let profile_pic = message.agency_id ? (await getAgencyById(message.agency_id, false))['resource'] :
        message.client_id ? (await getClientsById(message.client_id))['resource'] : null;

    return [200, {
        ok: true,
        result: {
            id: message.message_id,
            sender: message.client_id ? "C" : "A",
            profile_pic: profile_pic,
            from: message.message_from,
            title: message.message_title_translations ? JSON.parse(message.message_title_translations)[languageCode] || message.message_title : message.message_title,
            body: message.message_body ? await parseMessageBodySelectTranslation(message.message_body, languageCode) : message.message_body,
            created_at: message.createdAt ? message.createdAt : message.message_created_at,
            type: message.message_type.toLowerCase()
        },
    }];
};

/**
 * Get system_default message translations only
 * @param  {number} messageId
 * @param  {any} params
 */
export const getAutomatedMessageTranslationService = async (messageId: number, params: any) => {
    let message = await getAutomatedMessageDetails(messageId, params);
    if (!message) return [404, ErrorResponse.ResourceNotFound]

    let translations = {};
    if (message.body) {
        const body = JSON.parse(message.body);
        const translationObject = body[0].find(obj => obj.type === MessageBodyContentType.TRANSLATE);
        if (translationObject) {
            translations = translationObject.data
        }
    }

    return [200, {
        ok: true,
        result: {
            id: message.id,
            data: translations,
            type: message.type.toLowerCase()
        },
    }];
};

/**
 * Get message template details by templateId
 * @param  {number} templateId
 */
export const getMessageTemplateService = async (templateId: number) => {
    let template = await getMessageTemplateDetails(templateId);
    if (!template) return [404, ErrorResponse.ResourceNotFound]
    template.body = JSON.parse(template.body)          //parse body from JSON string
    template.title_translations = JSON.parse(template.title_translations)          //parse title_translate from JSON string
    template.is_master_template = template.title_translations != null;
    template.languages = { label: template.language, value: template.code };
    delete template.language;
    delete template.code;
    return [200, {
        ok: true,
        template
    }];
};

/**
 * Delete message template details by templateId
 * @param  {number} templateId
 */
export const deleteMessageTemplateService = async (templateId: number) => {
    let template = await deleteMessageTemplateById(templateId);
    if (template.affected === 0) return [404, ErrorResponse.ResourceNotFound]
    return [200, {
        ok: true,
        template
    }];
};

export const sendDefaultMessageTemplate = async (id: number) => {
    const payload = await getDefaultMessageTemplate();

    let templateInsert = []
    templateInsert = payload.map(payload => {
        return {
            name: payload.name,
            title: payload.title,
            type: payload.type,
            from: payload.from,
            body: JSON.parse(payload.body),
            createdBy: id.toString(),
            modifiedBy: id.toString()
        }
    })
    await createMessageTemplate(templateInsert);
}

/**
 * Translate message
 * @param  {any} payload
 */
export const translateMessageService = async (payload: any) => {
    // Invoke translate function
    let translatedMessage = await translateText(payload.message, payload.from, payload.to);
    return [
        200,
        {
            ok: true,
            translatedMessage
        },
    ];
}


/**
 * Translate tempate to multiple languages
 * @param  {any} payload
 */
export const translateTemplateToMultipleLanguageService = async (payload: any) => {

    // Step 1: Translate Messages
    const translatedMessagePromises = payload.message.map(async (messageText) => {
        const translations = await translateTextToListOfLanguages(
            messageText,
            payload.from,
            payload.to_languages
        );

        return {
            data: translations,
            type: "translate"
        };
    });

    const translatedMessage = await Promise.all(translatedMessagePromises); // message translations concurrently

    // Step 2: Translate Title
    const titleTranslations = await translateTextToListOfLanguages(
        payload.title,
        payload.from,
        payload.to_languages
    );

    // Organize the output
    const result = {
        message: translatedMessage,
        title_translate: titleTranslations
    };

    const nonEnglishFlag = await detectNonEnglishText(payload.message.concat(payload.title))
    return [
        200,
        {
            ok: true,
            message: nonEnglishFlag === 1 ? MessageActions.NON_ENGLISH_MESSAGE_TRANSLATIONS_WARNING : MessageActions.TRANSLATIONS_SUCCESS,
            result
        },
    ];
}


/**
 * Translate message to multiple languages
 * @param  {any} payload
 */
export const translateMessageToMultipleLanguageService = async (payload: any) => {
    // Invoke translate function
    let translatedMessage = await translateTextToListOfLanguages(payload.message, payload.from, payload.to_languages);
    const nonEnglishFlag = await detectNonEnglishText([payload.message])

    return [
        200,
        {
            ok: true,
            message: nonEnglishFlag === 1 ? MessageActions.NON_ENGLISH_MESSAGE_TRANSLATIONS_WARNING : MessageActions.TRANSLATIONS_SUCCESS,
            translatedMessage
        },
    ];
}


/**
 * update existing templates by templateId
 * @param  {number} userId
 * @param  {number} messageId
 * @param  {any} reqPayload
 */
export const addMessageReactionService = async (userId: number, messageId: number, reqPayload: any) => {
    let workerId = reqPayload.worker_id.toString();
    let reaction = reqPayload.reaction;

    let allWorkerIds = await getWorkerIdfromUserId({ userId });
    if (!allWorkerIds.map((_obj) => _obj.id).includes(workerId)) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    let message = await getMessageDetailsById(messageId);
    if (!message) {
        return [401, ErrorResponse.UnauthorizedForInvalidPath]
    }
    let payload = {
        messageId: messageId,
        workerId: workerId,
        reaction: reaction
    }
    let isMessageReactionExist = await getMessageReactionByWorkerIdMessageId(messageId, workerId);
    if (isMessageReactionExist) {
        payload['updatedAt'] = new Date();
        await updateMessageReaction(isMessageReactionExist.id, payload);
    } else {
        payload['createdAt'] = new Date();
        await createMessageReaction(payload);
    }
    return [200, { ok: true }]
};

/**
 * update existing templates by templateId
 * @param  {number} userId
 * @param  {number} messageId
 * @param  {any} reqPayload
 */
export const addMessageCommentService = async (userId: number, messageId: number, reqPayload: any) => {
    let workerId = reqPayload.worker_id.toString();
    let comment = reqPayload.comment;

    let allWorkerIds = await getWorkerIdfromUserId({ userId });
    if (!allWorkerIds.map((_obj) => _obj.id).includes(workerId)) {
        return [404, ErrorResponse.WorkerNotFound]
    }

    let message = await getMessageDetailsById(messageId);
    if (!message) {
        return [401, ErrorResponse.UnauthorizedForInvalidPath]
    }
    let payload = {
        messageId: messageId,
        workerId: workerId,
        comment: comment
    }
    await createMessageComment(payload);
    return [200, { ok: true }]
};

/**
 * Get message comments service by message id
 * @param  {number} messageId
 * @param  {number} page
 * @param  {number} limit
 */
export const getMessageCommentsService = async (messageId: number, page: number, limit: number) => {

    let message = await getMessageDetailsById(messageId);
    if (!message) {
        return [401, ErrorResponse.UnauthorizedForInvalidPath]
    }

    let [count, comments] = await getMessageComments(messageId, page, limit);
    return [200, {
        ok: true,
        count: count,
        comments: comments
    }]
};
