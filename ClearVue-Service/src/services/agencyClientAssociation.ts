/**
 * All the service layer methods for the Agency Client Association.
 */
import { CreateAgencyAssociationDTO, UpdateAgencyAssociationDTO, ErrorResponse, MessageActions } from "./../common";
import {
    createAgencyAssociation, updateAgencyAssociation, getAgencyAssociationList,
    getAgencyAssociationById, insert<PERSON>ar<PERSON>,
    getMarginsListHelper,
    updateMargin
} from "./../models";
import { UserType } from '../common';
import { notifyBugsnag } from '../utils'
import { getManager } from "typeorm";

/**
 * create agencyAssociation.
 * @param  {CreateAgencyAssociationDTO} payload
 */
export const createAgencyAssociationService = async (payload, loggedInUser) => {

    try {
        // Prepare payloads
        const agencyAssociationPayload = {
            clientId: payload.client_id,
            agencyId: payload.agency_id,
            currency: payload.currency,
            holidayActivation: payload.holiday_activation,
            holidayCostRemoved: payload.holiday_cost_removed,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
        };

        const marginsPayload = {
            margin: payload.margin,
            overtimeMargin: payload.overtime_margin || 0.00,
            transportFee: payload.transport_fee || 0.00,
            ssp: payload.ssp || 0.00,
            inductionTrainingMargin: payload.induction_training_margin || 0.00,
            trainingMargin: payload.training_margin || 0.00,
            bhMargin: payload.bh_margin || 0.00,
            nspMargin: payload.nsp_margin || 0.00,
            supervisorStandardMargin: payload.supervisor_standard_margin || 0.00,
            supervisorOvertimeMargin: payload.supervisor_overtime_margin || 0.00,
            supervisorPermanentMargin: payload.supervisor_permanent_margin || 0.00,
            suspensionMargin: payload.suspension_margin || 0.00,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
            siteId: null,
            los: null
        };

        const entityManager = getManager();
        let association_id = null
        await entityManager.transaction(async (transactionalEntityManager) => {
            // This insert generates an ID immediately, even before commit
            const agencyAssociation = await createAgencyAssociation(transactionalEntityManager, agencyAssociationPayload);
            // We can safely use the ID here
            association_id = agencyAssociation.id;
            marginsPayload['agencyClientAssociationId'] = association_id;

            // Both records will be committed or rolled back together
            await insertMargins(transactionalEntityManager, marginsPayload);
        });

        return [201, {
            ok: true,
            message: MessageActions.CREATE_AGENCY_ASSOCIATION,
            agency_association_id: association_id
        }];
    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message];
    }
};


/**
 * update agencyAssociation.
 * @param  {id}
 * @param  {UpdateAgencyAssociationDTO} payload
 */
export const updateAgencyAssociationService = async (id: string, payload, loggedInUser) => {
    let agencytoUpdate = await getAgencyAssociationById(id);
    if (!agencytoUpdate) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    const agencyAssociationPayload: any = {
        clientId: agencytoUpdate.client_id,
        agencyId: agencytoUpdate.agency_id,
        currency: agencytoUpdate.currency,
        updatedBy: loggedInUser.user_id,
    }

    if (UserType.CLEARVUE_ADMIN == parseInt(loggedInUser.user_type_id)) {
        agencyAssociationPayload.holidayActivation = payload.holiday_activation;
        agencyAssociationPayload.holidayCostRemoved = payload.holiday_cost_removed;
        agencyAssociationPayload.updatedBy = loggedInUser.user_id;
    }

    // Prepare margins payload
    const marginsPayload = {
        margin: payload.margin,
        overtimeMargin: payload.overtime_margin || 0.00,
        ssp: payload.ssp || 0.00,
        transportFee: payload.transport_fee || 0.00,
        inductionTrainingMargin: payload.induction_training_margin || 0.00,
        trainingMargin: payload.training_margin || 0.00,
        bhMargin: payload.bh_margin || 0.00,
        nspMargin: payload.nsp_margin || 0.00,
        supervisorStandardMargin: payload.supervisor_standard_margin || 0.00,
        supervisorOvertimeMargin: payload.supervisor_overtime_margin || 0.00,
        supervisorPermanentMargin: payload.supervisor_permanent_margin || 0.00,
        suspensionMargin: payload.suspension_margin || 0.00,
        siteId: null,
        los: null,
        agencyClientAssociationId: id
    };

    const entityManager = getManager();
    let agencyAssociation;

    await entityManager.transaction(async (transactionalEntityManager) => {
        // Update agency association if it exists
        if (Object.keys(agencyAssociationPayload).length > 0) {
            agencyAssociation = await updateAgencyAssociation(id, agencyAssociationPayload);
        }

        // If site_id and los match existing record, it will update, otherwise insert new record
        // Check if margin record already exists
        const existingMargins = await getMarginsListHelper({
            agencyClientAssociationId: id,
            siteId: null,
            los: null
        });
        if (existingMargins && existingMargins.length > 0) {
            // Update existing margin record
            await updateMargin(existingMargins[0].id, marginsPayload, transactionalEntityManager);
        } else {
            // Insert new margin record if none exists
            await insertMargins(transactionalEntityManager, marginsPayload);
        }
    });

    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AGENCY_ASSOCIATION,
        agency_association_id: agencyAssociation.id
    }];
};

/**
 * get agencyAssociation list.
 */
export const getAgencyAssociationListService = async (params, loggedInUser) => {
    let whereClause = '';
    if (params.agency_id && params.client_id) {
        whereClause = `agency_client_association.agency_id = :agency_id AND agency_client_association.client_id = :client_id`;
    } else if (params.client_id) {
        whereClause = `agency_client_association.client_id = :client_id`;
    } else if (params.agency_id) {
        whereClause = `agency_client_association.agency_id = :agency_id`;
    }

    const whereClauseValue = { "agency_id": params.agency_id, "client_id": params.client_id };
    const agencyAssociationList = await getAgencyAssociationList(params.page, params.limit, params.sort_by, params.sort_type, whereClause, whereClauseValue);

    const count = agencyAssociationList.count;

    return [200, {
        "ok": true,
        count,
        "agency_association_list": agencyAssociationList
    }];
};


/**
 * update agencyAssociation.
 */
export const restrictAgencyAssociationService = async (id, payload, loggedInUser) => {
    let agencytoUpdate = await getAgencyAssociationById(id);
    if (!agencytoUpdate) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    let agencyAssociation = await updateAgencyAssociation(id, { isRestricted: payload.is_restricted, updatedBy: loggedInUser.user_id });
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AGENCY_ASSOCIATION,
        agency_association_id: agencyAssociation.id
    }];
};


/**
 * update comments restrictions for agencyAssociation.
 */
export const restrictCommentsForAgencyAssociationService = async (id, payload, loggedInUser) => {
    let agencytoUpdate = await getAgencyAssociationById(id);
    if (!agencytoUpdate) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    let agencyAssociation = await updateAgencyAssociation(id, { commentRestricted: payload.comment_restricted, updatedBy: loggedInUser.user_id });
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AGENCY_ASSOCIATION,
        agency_association_id: agencyAssociation.id
    }];
};

/**
 * update total assignment pay flag for agencyAssociation.
 */
export const setTotalAssignmentPayFlagService = async (id, payload, loggedInUser) => {
    let agencytoUpdate = await getAgencyAssociationById(id);
    if (!agencytoUpdate) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    let agencyAssociation = await updateAgencyAssociation(id, { totalAssignmentPay: payload.total_assignment_pay, updatedBy: loggedInUser.user_id });
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AGENCY_ASSOCIATION,
        agency_association_id: agencyAssociation.id
    }];
};
