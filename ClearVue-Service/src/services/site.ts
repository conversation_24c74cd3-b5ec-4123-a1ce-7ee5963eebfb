const _ = require('lodash')
import { Integer } from "aws-sdk/clients/apigateway";
import { AddAndUpdateSiteDTO, ErrorResponse, UserType } from "../common";
import {
    addSite, getSites, getSiteById, updateSite, getClientRegion, getSitesForDropdown,
    getDashboardRatingsHelper, getSiteRatingsWithLabelHelper, getAverageSiteRatings, getAssociatedClients, getSiteWiseReviewsCount, getWorkerTypeWiseDashboardRatingsHelper, addWtrData, getRuleBySiteId, deleteRuleById, addAndUpdateWtrData,
    getAgencyAssociationById,
    syncSiteRestrictions,
    getSiteRestrictionsList,
    deleteMarginsBySiteIds,
    checkAuthorisedResourceAccess,
    getClientsById
} from "../models";
import { MessageActions, selectedTaxYear, RoleType, labelWiseDefaultRatings } from "./../common";
import { createTaxYearPayload } from "../utils";
import { getManager } from "typeorm";


/**
 * Service to add new site in the site
 * @param  {AddAndUpdateSiteDTO} payload
 */
export const addNewSite = async (payload, loggedInUser) => {
    const data = {
        name: payload.name,
        regionId: payload.region_id,
        clientId: payload.client_id,
        address: {
            address_line_1: payload.address_line_1,
            address_line_2: payload.address_line_2 || '',
            address_line_3: payload.address_line_3 || ''
        },
        postCode: payload.post_code,
        city: payload.city,
        country: payload.country,
        costCentre: payload.cost_centre || null,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id
    }
    let siteDetails = await addSite(data);
    if (!siteDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    let wtrInsert = []
    wtrInsert = payload.wtr.map(dataElement => {
        return {
            clientId: payload.client_id,
            siteId: siteDetails.id,
            name: dataElement.rule_name,
            roleType: dataElement.role_type,
            startTaxYear: dataElement.start_tax_year,
            payType: dataElement.pay_type,
            preTwelveWeek: dataElement.pre_twelve_week,
            postTwelveWeek: dataElement.post_twelve_week,
            los: dataElement.los,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id
        }
    })
    await addWtrData(wtrInsert);

    return [
        201,
        {
            ok: true,
            message: MessageActions.CREATE_SITE,
            site_id: parseInt(siteDetails.id)
        },
    ];
};

//Service to get sites for logged in client role.
export const getAllSites = async (clientId: number, regionId = null) => {
    let whereClause = `site.clientId = :client_id`;
    let whereClauseValue = { client_id: clientId }

    if (regionId) {
        whereClause += ` AND site.region_id = :region_id`
        whereClauseValue["region_id"] = regionId;
    }
    let siteDetails = await getSites(whereClause, whereClauseValue);

    if (!siteDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    if (!whereClause) {
        return [200, {
            ok: true,
            sites: []
        }];
    }
    siteDetails = _.map(siteDetails, (site) => {
        site.address = JSON.parse(site.address);
        site.los = JSON.parse(site.los);
        return site;
    });

    const transformedArray = [];
    const resultMap = new Map();

    for (const site of siteDetails) {
        if (!resultMap.has(site.id)) {
            resultMap.set(site.id, transformedArray.length);
            transformedArray.push({
                id: site.id,
                name: site.name,
                region_id: site.region_id,
                address: site.address,
                post_code: site.post_code,
                city: site.city,
                country: site.country,
                region_name: site.region_name,
                cost_centre: site.cost_centre || "",
                wtr: []
            });
        }

        const index = resultMap.get(site.id);
        transformedArray[index].wtr.push({
            rule_name: site.rule_name,
            role_type: site.role_type,
            start_tax_year: site.start_tax_year,
            pay_type: site.pay_type,
            pre_twelve_week: site.pre_twelve_week,
            post_twelve_week: site.post_twelve_week,
            los: site.los
        });
    }

    transformedArray.forEach((element) => {
        element.wtr = element.wtr.filter((item) => item.rule_name !== null);
    });

    return [
        200,
        {
            ok: true,
            sites: transformedArray

        },
    ];
};

export const getSitesDropDownService = async (data) => {

    let whereClause = `site.clientId = :client_id`;
    if (data.region_id) {
        whereClause += ` AND site.regionId = :region_id`
    }
    let whereClauseValue = { "client_id": data.client_id, "region_id": data.region_id }
    let siteDetails = await getSitesForDropdown(whereClause, whereClauseValue);
    return [
        200,
        {
            ok: true,
            sites: siteDetails
        },
    ];
};

/**
 * update site.
 * @param  {id}
 * @param  {CreateAndUpdateDepartmentDTO} payload
 */
export const updateSiteService = async (id: string, payload, loggedInUser) => {
    let siteToUpdate = await getSiteById(id);

    if (!siteToUpdate || siteToUpdate.client_id != payload.client_id) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    // Check for duplicate rules
    const repeatedRulesSet = new Set();
    const seenCombinations = new Set();

    payload.wtr.forEach(item => {
        const combinationKey = `${item.role_type}-${item.pay_type}`;
        if (seenCombinations.has(combinationKey)) {
            repeatedRulesSet.add(item.role_type);
        } else {
            seenCombinations.add(combinationKey);
        }
    });

    const repeatedRules = Array.from(repeatedRulesSet);
    const repeatedRulesArray = repeatedRules.map(rule => RoleType[Number(rule)]);
    if (_.size(repeatedRules)) {
        return [409, {
            ok: false,
            message: `Found duplicate rules for role : (${repeatedRulesArray})`
        }]
    }

    const sitePayload = {
        name: payload.name,
        regionId: payload.region_id,
        clientId: payload.client_id,
        address: {
            address_line_1: payload.address_line_1,
            address_line_2: payload.address_line_2 || '',
            address_line_3: payload.address_line_3 || ''
        },
        postCode: payload.post_code,
        city: payload.city,
        country: payload.country,
        costCentre: payload.cost_centre || null,
        updatedBy: loggedInUser.user_id,
    }
    let site = await updateSite(id, sitePayload);

    // Get rules by site
    let rules = await getRuleBySiteId(id);

    // Delete rules
    const deleteRule = rules
        .filter(rule => !payload.wtr.some(wtr => (wtr.role_type === rule.role_type && wtr.pay_type === rule.pay_type)))
        .map(rule => rule.id);
    if (_.size(deleteRule)) {
        await deleteRuleById(deleteRule);
    }

    // Create or update rules
    if (_.size(payload.wtr)) {
        let wtrInsert = []
        wtrInsert = payload.wtr.map(dataElement => {
            return {
                clientId: payload.client_id,
                siteId: id,
                name: dataElement.rule_name,
                roleType: dataElement.role_type,
                startTaxYear: dataElement.start_tax_year,
                payType: dataElement.pay_type,
                preTwelveWeek: dataElement.pre_twelve_week,
                postTwelveWeek: dataElement.post_twelve_week,
                los: dataElement.los,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id
            }
        })
        await addAndUpdateWtrData(wtrInsert);
    }

    return [200, {
        ok: true,
        message: MessageActions.UPDATE_SITE,
        site_id: site.id
    }];
};

/**
 * site ratings service.
 * @param  {data}
 * @param  {loggedInUser}
 */
export const siteAndClientRatingsService = async (data, loggedInUser) => {

    //Missing data validation
    if (!_.size(data)) {
        return [400, ErrorResponse.BadRequestError]
    }

    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;

    let { client_id, site_id, region_id, agency_id, shift_id, department_id } = updatedPayload;

    let clientDetails = await getClientsById(client_id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    const defaultRatingRespose = [200, {
        "ok": true,
        "average_rating": 0,
        "reviews_count": 0,
        "label_wise_ratings": labelWiseDefaultRatings
    }]

    if (clientDetails.hide_ratings) {
        return defaultRatingRespose;
    }

    let response: any;
    let whereClause1: string = `survey_question.belongs_to='SITE'`;
    let whereClause2: string = '';

    let whereClauseValue1 = {};
    let whereClauseValue2 = {};

    let whereClause3: string = '';
    let whereClauseValue3 = {};

    if (agency_id) {
        whereClause1 = `${whereClause1} AND survey_result.agency_id = :agency_id`;
        whereClauseValue1 = { ...whereClauseValue1, "agency_id": agency_id };
    }

    let whereClauseBase = `survey_result.client_id = :client_id AND survey_result.rating is not null`;
    let whereClauseValuesBase = { "client_id": client_id };

    if (client_id) {
        if (shift_id || department_id) {
            whereClauseValue3 = { ...whereClauseValuesBase };
            if (shift_id) {
                whereClause3 = `job.shift_id = :shift_id AND ${whereClauseBase}`;
                whereClauseValue3["shift_id"] = shift_id;
            }
            if (department_id) {
                whereClause3 = `job_association.department_id = :department_id AND ${whereClauseBase}`;
                whereClauseValue3["department_id"] = department_id;
            }

            if (site_id) {
                whereClause3 += ` AND site.id = :site_id`;
                whereClauseValue3["site_id"] = site_id;
            }
            if (region_id) {
                whereClause3 += ` AND site.region_id = :region_id`;
                whereClauseValue3["region_id"] = region_id;
            }
        } else if (!site_id && !region_id) {
            // Fetching the list of associated site id from the client_id.
            let sites = await getSites(`site.client_id= :client_id`, { "client_id": client_id });
            if (sites.length) {
                let site_id_list = sites.map(object => parseInt(object.id));
                whereClause2 = `survey_result.site_id IN (:site_id_list) AND ${whereClauseBase}`;
                whereClauseValue2 = { ...whereClauseValuesBase, "site_id_list": site_id_list };
            } else {
                return defaultRatingRespose;
            }
        } else {
            // Site ratings by site_id or region_id and client_id.
            if (site_id) {
                whereClause2 = `survey_result.site_id = :site_id AND ${whereClauseBase}`;
                whereClauseValue2 = { ...whereClauseValuesBase, "site_id": site_id };
            }
            if (region_id) {
                let sites = await getSites(`site.region_id= :region_id`, { "region_id": region_id });
                if (sites.length) {
                    let site_id_list = sites.map(object => parseInt(object.id));
                    whereClause2 = `survey_result.site_id IN (:site_id_list) AND ${whereClauseBase}`;
                    whereClauseValue2 = { ...whereClauseValuesBase, "site_id_list": site_id_list };
                } else {
                    return defaultRatingRespose;
                }
            }
        }
    } else {
        let clients = await getAssociatedClients(agency_id);
        if (clients.length) {
            let clientIdList = clients.map(element => parseInt(element.clientId));
            whereClause2 = `survey_result.client_id IN (:client_id_list) AND survey_result.rating is not null`;
            whereClauseValue2 = { "client_id_list": clientIdList };
        } else {
            return defaultRatingRespose;
        }
    }
    response = await getDashboardRatingsHelper(whereClause1, whereClauseValue1, whereClause2, whereClauseValue2, whereClause3, whereClauseValue3, loggedInUser);

    let { average_rating, label_wise_ratings } = response;
    let reviews_count = response.reviews_count.map(object => parseInt(object.reviews_count));

    if (!reviews_count.length) {
        return defaultRatingRespose
    }

    return [200, {
        "ok": true,
        average_rating: parseFloat(average_rating.ratings),
        reviews_count: _.sum(reviews_count),
        label_wise_ratings
    }]
};


/**
 * site ratings service.
 * @param  {data}
 * @param  {loggedInUser}
 */
export const detailedSiteRatingsService = async (data, loggedInUser) => {
    if (!_.size(data)) {
        return [400, ErrorResponse.BadRequestError]
    }

    const { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;
    const { client_id, agency_id, site_id, region_id, type } = updatedPayload;

    let clientDetails = await getClientsById(client_id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    if (clientDetails.hide_ratings) {
        return [200, { ok: true, 'ratings': [] }]
    }

    let sites: any;
    let whereClause = `survey_result.rating is not null AND survey_question.belongs_to='SITE' AND`
    let site_id_list: Array<Integer> = [];

    if (site_id) {
        site_id_list = [site_id]
    } else {
        if (client_id && !region_id) {
            sites = await getSites(`site.client_id=:client_id`, { "client_id": client_id });
        }
        else {
            sites = await getSites(`site.client_id=:client_id AND site.region_id= :region_id`, { "client_id": client_id, "region_id": region_id });

        }
        if (!_.size(sites)) {
            return [200, { ok: true, ratings: {} }]
        }
        site_id_list = sites.map(object => parseInt(object.id));
    }
    whereClause += ` survey_result.site_id IN (:site_id_list)`;
    let whereClauseValue = { "site_id_list": site_id_list };

    if (agency_id) {
        whereClause = `${whereClause} AND survey_result.agency_id = :agency_id`;
        whereClauseValue["agency_id"] = agency_id;
    }

    let siteWiseRatings = await getSiteRatingsWithLabelHelper(whereClause, whereClauseValue, type);
    let siteWiseAverageRatings = await getAverageSiteRatings(whereClause, whereClauseValue, type);
    let siteWiseReviewsCount = await getSiteWiseReviewsCount(whereClause, whereClauseValue, type);

    let grouped = _.groupBy(siteWiseRatings, object => object.site_name);

    let response: any = []

    Object.keys(grouped).forEach(key => {
        let reviewsCount = [];
        reviewsCount = grouped[key].map(object => parseInt(object.reviews_count))
        let label_wise_ratings = grouped[key].map(object => { return { "label": object.label, "ratings": object.ratings } })
        let obj = siteWiseReviewsCount.find(o => o.site_id === grouped[key][0].site_id);
        response.push({
            name: key,
            reviews_count: parseInt(obj.reviews_count),
            average_rating: parseFloat(siteWiseAverageRatings.find(object => object.site_name === key).ratings),
            label_wise_ratings
        })
    })

    return [200, { ok: true, 'ratings': response || [] }]
};


// Helper function to convert the array of objects into a single object for the label wise ratings.
export const modifyLabelWiseRatings = (array) => {
    return array.reduce((obj, item) => (obj[item.label] = parseFloat(item.ratings), obj), {});
}


/**
 * worker type wise client and site ratings service.
 * @param  {data}
 */
export const workerTypeWiseSiteAndClientRatingService = async (data) => {
    //Missing data validation
    if (!_.size(data)) {
        return [400, ErrorResponse.BadRequestError]
    }

    let { client_id, site_id, region_id } = data;

    let clientDetails = await getClientsById(client_id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    if (clientDetails.hide_ratings) {
        return nullRatingResponse();
    }

    let whereClause1: string = `survey_question.belongs_to='SITE'`;
    let whereClause2: string = `survey_result.client_id = :client_id AND survey_result.rating is not null `;

    let whereClauseValue1 = {};
    let whereClauseValue2 = { "client_id": client_id };
    //Get site ratings on client_id.
    if (client_id && !site_id && !region_id) {
        //Fetching the list of associated site id from the client_id.
        let sites = await getSites(`site.client_id=:client_id`, { "client_id": client_id });
        if (sites.length) {
            let site_id_list = sites.map(object => parseInt(object.id));
            whereClause2 = whereClause2 + `AND survey_result.site_id IN (:site_id_list) `;
            whereClauseValue2["site_id_list"] = site_id_list;
        }
        else {
            return nullRatingResponse();
        }
    }
    else if (client_id && region_id && !site_id) {
        //Site ratings by region_id.
        let sites = await getSites(`site.region_id= :region_id`, { "region_id": region_id });
        if (sites.length) {
            let site_id_list = sites.map(object => parseInt(object.id));
            whereClause2 = whereClause2 + `AND survey_result.site_id IN (:site_id_list) `;
            whereClauseValue2["site_id_list"] = site_id_list;
        }
        else {
            return nullRatingResponse();
        }
    }
    else if (client_id && site_id && !region_id) {
        // Site ratings by site_id and client_id.
        whereClause2 = whereClause2 + `AND survey_result.site_id = :site_id `;
        whereClauseValue2["site_id"] = site_id;
    }

    let { average_rating, reviews_count, label_wise_rating } = await getWorkerTypeWiseDashboardRatingsHelper(whereClause1, whereClauseValue1, whereClause2, whereClauseValue2);

    let grouped = _.groupBy(label_wise_rating, object => object.type);

    let response: any = []

    Object.keys(grouped).forEach(key => {
        let label_wise_ratings = () => {
            let obj = label_wise_rating.filter(object => object.type === key);
            return obj.map(object => {
                return { "label": object.label, "ratings": object.ratings }
            })
        }

        let reviews_counts = () => {
            let obj = reviews_count.filter(object => object.type === key);
            return obj.map(object => parseInt(object.reviews_count))
        }

        response.push({
            type: key,
            reviews_count: _.sum(reviews_counts()),
            average_rating: parseFloat(average_rating.find(object => object.type === key).ratings),
            label_wise_ratings: label_wise_ratings()
        })
    });

    return response.length ? [200, { "ok": true, 'ratings': response }] : nullRatingResponse()
};


export const nullRatingResponse = () => {
    return [200, {
        "ok": true,
        'ratings': [{
            "type": "",
            "average_rating": 0,
            "reviews_count": 0,
            "label_wise_ratings": labelWiseDefaultRatings
        }]
    }]
}


export const updateSiteRestrictionsService = async (payload, loggedInUser) => {
    const agencyAssociation = await getAgencyAssociationById(payload.agency_client_association_id);
    if (!agencyAssociation) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    // Check if current user's client_id is same as the client_id of the association if user is not CLERAVUE_ADMIN
    if (parseInt(loggedInUser.user_type_id) !== UserType.CLEARVUE_ADMIN && loggedInUser.client_id !== agencyAssociation.client_id) {
        return [403, ErrorResponse.Forbidden];
    }

    // Get all sites for the client
    const clientSites = await getSitesDropDownService({ "client_id": agencyAssociation.client_id });
    const clientSiteIds = clientSites[1]["sites"].map(site => site.id.toString());

    // Validate if all provided site_ids belong to the client
    const invalidSiteIds = payload.site_ids.filter(siteId =>
        !clientSiteIds.includes(siteId.toString())
    );

    if (invalidSiteIds.length > 0) {
        return [400, {
            ok: false,
            message: 'Invalid site IDs provided',
            invalid_site_ids: invalidSiteIds
        }];
    }

    // Get existing site restrictions
    const existingRestrictions = await getSiteRestrictionsList(payload.agency_client_association_id);
    const existingSiteIds = existingRestrictions.map(r => r.site.id.toString());
    // Find removed sites
    const removedSiteIds = existingSiteIds.filter(id => !payload.site_ids.map(siteId => siteId.toString()).includes(id));
    // Prepare site restrictions data
    const siteRestrictionsData = payload.site_ids.map(siteId => ({
        agencyClientAssociationId: payload.agency_client_association_id,
        siteId,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id
    }));

    // Use transaction to sync site restrictions and delete margins
    const entityManager = getManager();
    await entityManager.transaction(async transactionalEntityManager => {
        // Sync site restrictions
        await syncSiteRestrictions(transactionalEntityManager,
            payload.agency_client_association_id,
            siteRestrictionsData
        );

        // Delete margins for removed sites
        if (removedSiteIds.length > 0) {
            await deleteMarginsBySiteIds(
                transactionalEntityManager,
                removedSiteIds,
                payload.agency_client_association_id
            );
        }
    });

    return [200, {
        ok: true,
        message: MessageActions.UPDATE_SITE_RESTRICTIONS
    }];
};

export const getSiteRestrictionsService = async (params) => {
    const agencyAssociation = await getAgencyAssociationById(params.agency_client_association_id);
    if (!agencyAssociation) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    // Get all sites for the client
    const clientSites = await getSitesDropDownService({ "client_id": agencyAssociation.client_id });

    // Get existing restrictions
    const restrictions = await getSiteRestrictionsList(params.agency_client_association_id);

    // Create a Set of restricted site IDs for faster lookup
    const restrictedSiteIds = new Set(restrictions.map(r => r.site.id.toString()));

    // Map all client sites and add is_selected flag
    const sitesWithSelectionStatus = clientSites[1]["sites"].map(site => ({
        site_id: site.id,
        site_name: site.name,
        is_selected: restrictedSiteIds.has(site.id.toString())
    }));

    return [200, {
        ok: true,
        total_selected_count: restrictions.length,
        total_count: sitesWithSelectionStatus.length,
        sites: sitesWithSelectionStatus
    }];
};