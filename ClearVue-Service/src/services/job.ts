/**
 * All the service layer methods for the Department.
 */
const _ = require('lodash');
import { CreateAndUpdateJobDTO, ErrorResponse, RoleType, MessageActions } from "./../common";
import {
    createJob, updateJob, getJobById, createJobAssociation, deleteJobAssociation, getJobAssociation, jobDropDownListingHelper,
    jobNameDropDownListingHelper, getSitesWithLowerCase, getShifWithLowerCase, getDepartmentListLowerCase, getAssociatedDepartmentsWithSite, getAssociatedShiftsWithSite
} from "./../models";
import { getShiftService, getDepartmentListService } from '../services'
import { notifyBugsnag } from '../utils'
import moment from "moment";
const deepClone = require('lodash.clonedeep');
const dashboard = require('../services/dashboard');


/**
 * Checks if the user has permission based on department and shift associations.
 *
 * @param {number} clientId - The client ID.
 * @param {number} siteId - The site ID.
 * @param {number[]} departmentIdList - The list of department IDs.
 * @param {number} shiftIdLsit - The shift ID.
 * @returns {} - Returns true if permission is denied, false otherwise.
 */
const isPermissionDenied = async (clientId, siteId, departmentIdList, shiftIdLsit) => {
    const AssociateDepartmentsList = await getAssociatedDepartmentsWithSite(clientId, siteId);
    const AssociateShiftsList = await getAssociatedShiftsWithSite(clientId, siteId);

    const idsNotInDepartmentsList = departmentIdList.filter(deptId => !AssociateDepartmentsList.includes(deptId));
    const idsNotInShiftsList = shiftIdLsit.filter(shiftId => !AssociateShiftsList.includes(shiftId));

    return { status: (idsNotInDepartmentsList.length > 0 || idsNotInShiftsList.length > 0), idsNotInDepartmentsList, idsNotInShiftsList };
};

/**
 * create job.
 * @param  {CreateAndUpdateJobDTO} payload
 */
export const createJobService = async (payload: CreateAndUpdateJobDTO, loggedInUser) => {

    const permissionDenied = await isPermissionDenied(loggedInUser.client_id, payload.siteId, payload.departmentId, [payload.shiftId]);
    if (permissionDenied.status || (payload.clientId && loggedInUser.client_id != payload.clientId)) {
        return [403, ErrorResponse.PermissionDenied];
    }

    const jobPayload = {
        name: payload.name,
        type: payload.type,
        shiftId: payload.shiftId,
        hoursPerWeek: payload.hoursPerWeek,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id,
    }
    let job = await createJob(jobPayload);
    const jobAssociationData = [];
    const createJobAssociationPayload = {
        jobId: job.id,
        clientId: loggedInUser.client_id,
        siteId: payload.siteId,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id,
        createdAt: moment().utc().format(),
        updatedAt: moment().utc().format()
    }
    if (_.size(payload.departmentId)) {
        for (let i = 0; i < payload.departmentId.length; i++) {
            jobAssociationData.push(_.cloneDeep(_.extend(createJobAssociationPayload, { departmentId: payload.departmentId[i] })))
        }
    } else {
        jobAssociationData.push(createJobAssociationPayload);
    }
    await createJobAssociation(jobAssociationData);
    return [201, {
        ok: true,
        message: MessageActions.CREATE_JOB,
        job_id: job.id,
    }];
};

/**
 * update job.
 * @param  {id}
 * @param  {CreateAndUpdateJobDTO} payload
 */
export const updateJobService = async (id: string, payload: CreateAndUpdateJobDTO, loggedInUser) => {
    try {
        let jobToUpdate = await getJobById(id);
        if (!jobToUpdate || jobToUpdate.client_id != payload.clientId) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const permissionDenied = await isPermissionDenied(loggedInUser.client_id, payload.siteId, payload.departmentId, [payload.shiftId]);
        if (permissionDenied.status || (payload.clientId && loggedInUser.client_id != payload.clientId)) {
            return [403, ErrorResponse.PermissionDenied];
        }

        const jobPayload = {
            name: payload.name,
            type: payload.type,
            shiftId: payload.shiftId,
            hoursPerWeek: payload.hoursPerWeek,
            updatedBy: loggedInUser.user_id,
        }
        let job = await updateJob(id, jobPayload);
        if (payload.departmentId || payload.siteId || loggedInUser.client_id) {
            const jobAssociationData = [];
            const createJobAssociationPayload = {
                jobId: id,
                clientId: loggedInUser.client_id,
                siteId: payload.siteId,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
                createdAt: moment().utc().format(),
                updatedAt: moment().utc().format()
            }
            if (_.size(payload.departmentId)) {
                for (let i = 0; i < payload.departmentId.length; i++) {
                    jobAssociationData.push(_.cloneDeep(_.extend(createJobAssociationPayload, { departmentId: payload.departmentId[i] })))
                }
            } else {
                jobAssociationData.push(createJobAssociationPayload);
            }
            const del = await deleteJobAssociation({ jobId: id })
            await createJobAssociation(jobAssociationData);
        }
        return [200, {
            ok: true,
            message: MessageActions.UPDATE_JOB,
            job_id: job.id
        }];
    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
};

/**
 * get job list.
 */
export const getJobListService = async (data) => {
    let { page, limit, sort_by, sort_type, ...otherArgs } = data;
    let { payrollWhereClause, payrollWhereClauseValue } = dashboard.getPayrollWhereClauseString(deepClone(otherArgs), 1, 'job_association')
    let jobAssociationList = await getJobAssociation(page, limit, payrollWhereClause, payrollWhereClauseValue)
    jobAssociationList = _.groupBy(jobAssociationList, 'job_id')
    let resultantArray = [];
    for (let i in Object.keys(jobAssociationList)) {
        let groupedJob = jobAssociationList[Object.keys(jobAssociationList)[i]];
        const jobObject = _.cloneDeep(_.omit(jobAssociationList[Object.keys(jobAssociationList)[i]][0], ['department_id', 'department_name']));
        const department = [];
        for (let j of groupedJob) {
            department.push({ value: j.department_id, label: j.department_name });
        }
        let job_type_id = jobObject.job_type;
        jobObject.job_type = RoleType[jobObject.job_type];
        jobObject['job_type_id'] = job_type_id
        jobObject.department = department;
        resultantArray.push(jobObject);
    }
    return [200, {
        ok: true,
        job_list: resultantArray
    }];
};

/**
 * Service to GET job listing for drop-down.
 */
export const getJobListingForDropDownService = async (site_id) => {
    try {
        let whereClause = `job_association.siteId = :site_id`;
        let whereClauseValue = { "site_id": site_id };

        const job_list = await jobDropDownListingHelper(whereClause, whereClauseValue);
        let jobs = []
        if (job_list) {
            _.map(job_list, (job) => {
                let exisitingObj = _.find(jobs, { id: job.job_id })
                if (exisitingObj) {
                    if (!exisitingObj.name.includes('...')) {
                        exisitingObj.name += `...`
                    }
                } else {
                    jobs.push({ id: job.job_id, name: `${job.job_name} - ${job.shift_name} - ${RoleType[job.job_type]} - ${job.department_name}` })
                }
            })
        }
        return [200, { ok: true, job_list: jobs }]
    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message]
    }
}

/**
 * Service to GET the job names for drop-down.
 */
export const getJobNameListingForDropDownService = async (site_id) => {
    try {
        let whereClause = `job_association.siteId = :site_id`;
        let whereClauseValue = { "site_id": site_id };
        let job_list = await jobNameDropDownListingHelper(whereClause, whereClauseValue) || [];

        //Remove duplicate jobs
        job_list = Object.values(job_list.reduce((acc, cur) => Object.assign(acc, { [cur.name]: cur }), {}))
        return [200, { ok: true, job_list }]
    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message]
    }
}

/**
 * Service to add bulk jobs data.
 */
export const addBulkJobs = async (data, userId, client_id) => {
    let siteDetails = await getSitesWithLowerCase(client_id);

    let shiftDetails = await getShifWithLowerCase(client_id);

    let departmentList = await getDepartmentListLowerCase(client_id);

    const siteNotFound = [];
    const departmentNotFound = [];
    const shiftNotFound = [];
    let jobIds = [];
    let notFoundLine = 1;
    let siteAssociations: any = [];


    data = await Promise.all(_.map(data, async (job) => {
        let sites = _.find(siteDetails, {
            'name': job.siteName.toLowerCase()
        });
        notFoundLine++;
        if (sites) {
            job.siteId = sites.id;
            delete job.siteName;
        } else {
            siteNotFound.push(`{ 'SiteName': '${job.siteName}', 'lineNumber': ${notFoundLine} }`);
        }

        let shifts = _.find(shiftDetails, {
            'name': job.shiftName.toLowerCase()
        });
        if (shifts) {
            job.shiftId = shifts.id;
            delete job.shiftName;
        } else {
            shiftNotFound.push(`{ 'ShiftName': '${job.shiftName}', 'lineNumber': ${notFoundLine} }`);
        }

        let departments = _.find(departmentList, {
            'name': job.departmentName.toLowerCase()
        });
        if (departments) {
            job.departmentId = departments.id;
            delete job.departmentName;
        } else {
            departmentNotFound.push(`{ 'DepartmentName': '${job.departmentName}', 'lineNumber': ${notFoundLine} }`);
        }

        // Search for associated shifts and departments with the given site.
        // Create an object that represents all the shifts and departments associated with the site.
        if (sites && shifts && departments) {
            const existingItem = siteAssociations.find(item => item.site_id === job.siteId);

            const siteObject = {
                'site_id': job.siteId,
                'shifts': [job.shiftId],
                'departments': [job.departmentId],
                'sites_names': { [job.siteId]: shifts.name },
                'shifts_names': { [job.shiftId]: shifts.name },
                'departments_names': { [job.departmentId]: departments.name }
            };

            if (existingItem) {
                existingItem.shifts.push(job.shiftId);
                existingItem.departments.push(job.departmentId);
                Object.assign(existingItem.sites_names, { [job.siteId]: shifts.name });
                Object.assign(existingItem.shifts_names, { [job.shiftId]: shifts.name });
                Object.assign(existingItem.departments_names, { [job.departmentId]: departments.name });
            } else {
                siteAssociations.push(siteObject);
            }
        }

        return { siteNotFound, shiftNotFound, departmentNotFound, job, siteAssociations };
    }));

    if (_.size(data[0].siteNotFound)) {
        return [400, {
            ok: false,
            message: `Site does not exist with name(s): ${siteNotFound} `
        }]
    }
    if (_.size(data[0].shiftNotFound)) {
        return [400, {
            ok: false,
            message: `Shift does not exist with name(s): ${shiftNotFound} `
        }]
    }
    if (_.size(data[0].departmentNotFound)) {
        return [400, {
            ok: false,
            message: `Department does not exist with name(s): ${departmentNotFound} `
        }]
    }

    const deniedCombinations = await findDeniedCombinations(siteAssociations, client_id);
    if (deniedCombinations.length > 0) {
        return [403, {
            ok: false,
            message: `Following sites need associations with shifts or departments. Associate them first or check spelling. \n\n${deniedCombinations.join('\n')}\n`
        }];
    }

    data = await Promise.all(_.map(data, async (jobDetails) => {
        const jobPayload = {
            name: jobDetails.job.jobName,
            type: jobDetails.job.type,
            shiftId: jobDetails.job.shiftId,
            hoursPerWeek: jobDetails.job.hoursPerWeek,
            createdBy: userId,
            updatedBy: userId,
        }
        let job_detail_response = await createJob(jobPayload);
        jobIds.push(job_detail_response.id);
        const createJobAssociationPayload = {
            jobId: job_detail_response.id,
            clientId: client_id,
            siteId: jobDetails.job.siteId,
            departmentId: jobDetails.job.departmentId,
            createdBy: userId,
            updatedBy: userId,
            createdAt: moment().utc().format(),
            updatedAt: moment().utc().format()
        }
        await createJobAssociation(createJobAssociationPayload);
    }));

    return [201, {
        ok: true,
        message: MessageActions.CREATE_JOB,
        job_id: jobIds,
    }];
};


/**
 * Generate denied combinations based on shifts and departments that are not associated with the given sites.
 *
 * @param {Array} permissionDenied - List of objects containing denied combinations
 * @param {Array} siteAssociations - List of site associations with shifts and departments
 * @returns {Array} Denied combinations error messages
 */
const findDeniedCombinations = async (siteAssociations, client_id) => {
    const permissionDenied = await Promise.all(siteAssociations.map(async (association) => {
        const { site_id, shifts, departments } = association;
        const departmentIdList = departments.map(String);
        const shiftIdList = shifts.map(String);

        const { status, idsNotInDepartmentsList, idsNotInShiftsList } = await isPermissionDenied(client_id, site_id, departmentIdList, shiftIdList);

        const deniedCombinations = [];

        idsNotInDepartmentsList.forEach(deptId => {
            const departmentName = association.departments_names[deptId];
            deniedCombinations.push({ departmentName, siteName: association.sites_names[site_id] });
        });

        idsNotInShiftsList.forEach(shiftId => {
            const shiftName = association.shifts_names[shiftId];
            deniedCombinations.push({ shiftName, siteName: association.sites_names[site_id] });
        });

        return { status, deniedCombinations };
    }));

    const deniedCombinationsErrorMessage = [];

    permissionDenied.forEach((result) => {
        const { status, deniedCombinations } = result;

        if (status) {
            deniedCombinations.forEach(combination => {
                const entry = combination.shiftName ? `[ Site: '${combination.siteName}', Shift: '${combination.shiftName}' ]` : `[ Site: '${combination.siteName}', Department: '${combination.departmentName}' ]`;
                deniedCombinationsErrorMessage.push(entry);
            });
        }
    });

    return deniedCombinationsErrorMessage;
};