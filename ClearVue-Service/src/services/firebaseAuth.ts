/**
 * Firebase Authentication Service Layer
 * Handles Firebase user management and authentication operations
 */
import * as admin from 'firebase-admin';
import axios from 'axios';
import { config } from '../configurations';
import { notifyBugsnag } from '../utils';
import { isMfaEnabled } from './firebaseMfa';

let firebaseApp: admin.app.App | null = null;

/**
 * Initialize Firebase Admin SDK
 */
export const initializeFirebase = (): boolean => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return false;
        }

        if (!config.FIREBASE_AUTH_KEY_JSON) {
            console.warn('Firebase Auth is enabled but FIREBASE_AUTH_KEY_JSON is not provided');
            return false;
        }

        if (firebaseApp) {
            return true; // Already initialized
        }

        const serviceAccount = JSON.parse(config.FIREBASE_AUTH_KEY_JSON);

        firebaseApp = admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
        });

        return true;
    } catch (error) {
        console.error('Failed to initialize Firebase Admin SDK:', error);
        notifyBugsnag(error);
        return false;
    }
};

/**
 * Get Firebase Auth instance
 */
const getFirebaseAuth = (): admin.auth.Auth | null => {
    if (!firebaseApp) {
        if (!initializeFirebase()) {
            return null;
        }
    }
    return firebaseApp?.auth() || null;
};

/**
 * Create a new Firebase user with email and password
 */
export const createFirebaseUser = async (email: string, password: string): Promise<{ success: boolean; uid?: string; error?: string }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { success: true }; // Skip if Firebase is disabled
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        const userRecord = await auth.createUser({
            email: email,
            password: password,
            emailVerified: true, // Since we handle verification through our own system
        });

        return { success: true, uid: userRecord.uid };
    } catch (error) {
        console.error('Error creating Firebase user:', error);
        notifyBugsnag(error);

        // If user already exists, that's okay for our migration strategy
        if (error.code === 'auth/email-already-exists') {
            return { success: true, error: 'User already exists in Firebase' };
        }

        return { success: false, error: error.message };
    }
};

/**
 * Update Firebase user password
 */
export const updateFirebaseUserPassword = async (email: string, newPassword: string): Promise<{ success: boolean; error?: string }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { success: true }; // Skip if Firebase is disabled
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        // Get user by email first
        const userRecord = await auth.getUserByEmail(email);

        // Update password
        await auth.updateUser(userRecord.uid, {
            password: newPassword,
        });

        return { success: true };
    } catch (error) {
        console.error('Error updating Firebase user password:', error);
        notifyBugsnag(error);

        // If user doesn't exist, we'll create them during migration
        if (error.code === 'auth/user-not-found') {
            return { success: false, error: 'User not found in Firebase' };
        }

        return { success: false, error: error.message };
    }
};

/**
 * Authenticate user with Firebase using email and password
 * Uses Firebase REST API for server-side authentication
 */
export const authenticateWithFirebase = async (email: string, password: string): Promise<{ success: boolean; uid?: string; idToken?: string; refreshToken?: string; error?: string; mfaPendingCredential?: string; mfaInfo?: any[] }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { success: false, error: 'Firebase auth disabled' };
        }

        if (!config.FIREBASE_WEB_API_KEY) {
            return { success: false, error: 'Firebase Web API key not configured' };
        }

        // Use Firebase REST API to authenticate
        const response = await axios.post(
            `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${config.FIREBASE_WEB_API_KEY}`,
            {
                email: email,
                password: password,
                returnSecureToken: true
            }
        );

        if (response.data && response.data.localId) {
            // Check if MFA is required (mfaPendingCredential present but no idToken)
            if (response.data.mfaPendingCredential && !response.data.idToken) {
                return {
                    success: false,
                    error: 'SECOND_FACTOR_REQUIRED',
                    uid: response.data.localId,
                    mfaPendingCredential: response.data.mfaPendingCredential,
                    mfaInfo: response.data.mfaInfo || []
                };
            }

            // Normal authentication success with tokens
            if (response.data.idToken && response.data.refreshToken) {
                return {
                    success: true,
                    uid: response.data.localId,
                    idToken: response.data.idToken,
                    refreshToken: response.data.refreshToken
                };
            }

            return { success: false, error: 'Invalid response from Firebase - missing tokens' };
        } else {
            return { success: false, error: 'Invalid response from Firebase' };
        }
    } catch (error) {
        console.error('Error authenticating with Firebase:', error);
        notifyBugsnag(error);

        if (error.response) {
            const errorCode = error.response.data?.error?.message;
            if (errorCode === 'EMAIL_NOT_FOUND') {
                return { success: false, error: 'User not found in Firebase' };
            } else if (errorCode === 'INVALID_PASSWORD') {
                return { success: false, error: 'Invalid password' };
            } else if (errorCode === 'USER_DISABLED') {
                return { success: false, error: 'User account disabled' };
            } else if (errorCode === 'SECOND_FACTOR_REQUIRED') {
                // MFA is required, return the pending credential
                return {
                    success: false,
                    error: 'SECOND_FACTOR_REQUIRED',
                    mfaPendingCredential: error.response.data?.mfaPendingCredential
                };
            }
        }

        return { success: false, error: error.message };
    }
};

/**
 * Set custom claims for Firebase user
 */
export const setFirebaseUserClaims = async (uid: string, claims: any): Promise<{ success: boolean; error?: string }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { success: true }; // Skip if Firebase is disabled
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        await auth.setCustomUserClaims(uid, claims);
        return { success: true };
    } catch (error) {
        console.error('Error setting Firebase user claims:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};

/**
 * Migrate user to Firebase (create if doesn't exist)
 */
export const migrateUserToFirebase = async (email: string, password: string, userClaims?: any): Promise<{ success: boolean; uid?: string; error?: string }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { success: true }; // Skip if Firebase is disabled
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        // First, try to get the user to see if they already exist
        try {
            const userRecord = await auth.getUserByEmail(email);
            // User exists, update their password
            await auth.updateUser(userRecord.uid, {
                password: password,
            });

            // Set custom claims if provided
            if (userClaims) {
                await setFirebaseUserClaims(userRecord.uid, userClaims);
            }

            return { success: true, uid: userRecord.uid };
        } catch (getUserError) {
            if (getUserError.code === 'auth/user-not-found') {
                // User doesn't exist, create them
                const createResult = await createFirebaseUser(email, password);

                // Set custom claims if user was created successfully
                if (createResult.success && createResult.uid && userClaims) {
                    await setFirebaseUserClaims(createResult.uid, userClaims);
                }

                return createResult;
            } else {
                throw getUserError;
            }
        }
    } catch (error) {
        console.error('Error migrating user to Firebase:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};

/**
 * Check if Firebase authentication is enabled and properly configured
 */
export const isFirebaseEnabled = (): boolean => {
    return config.FIREBASE_AUTH_ENABLED && !!config.FIREBASE_AUTH_KEY_JSON;
};

/**
 * Verify Firebase user exists
 */
export const verifyFirebaseUserExists = async (email: string): Promise<{ exists: boolean; uid?: string; error?: string }> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return { exists: false };
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { exists: false, error: 'Firebase not initialized' };
        }

        const userRecord = await auth.getUserByEmail(email);
        return { exists: true, uid: userRecord.uid };
    } catch (error) {
        if (error.code === 'auth/user-not-found') {
            return { exists: false };
        }

        console.error('Error verifying Firebase user:', error);
        notifyBugsnag(error);
        return { exists: false, error: error.message };
    }
};


// // Fetch user info first
export const getFirebaseUserEmail = async (idToken: string): Promise<string | null> => {
    try {
        if (!config.FIREBASE_AUTH_ENABLED) {
            return null;
        }

        const userInfoResponse = await axios.post(
            `https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${config.FIREBASE_WEB_API_KEY}`,
            {
                idToken: idToken
            }
        );
        return userInfoResponse.data.users?.[0]?.email || null;
    } catch (error) {
        console.error('Error getting Firebase user email:', error);
        notifyBugsnag(error);
        return null;
    }
};

