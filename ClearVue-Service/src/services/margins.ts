import { count } from 'console';
import { ErrorResponse, MessageActions, UserType } from '../common';
import {
    createMargin,
    getMarginsListHelper,
    updateMargin,
    deleteMargins,
    getMarginById,
    getAgencyAssociationById,
    getSiteRestrictionsList
} from '../models';
import { IsNull, Not } from 'typeorm';

export const createMarginsService = async (query, payload, loggedInUser) => {

    let association = await getAgencyAssociationById(query.agency_client_association_id);
    if (!association) {
        return [404, ErrorResponse.AssocationNotFound];
    }

    // check if current user's client_id is same as the client_id of the association if user is not CLERAVUE_ADMIN
    if (parseInt(loggedInUser.user_type_id) !== UserType.CLEARVUE_ADMIN && loggedInUser.client_id !== association.client_id) {
        return [403, ErrorResponse.Forbidden];
    }


    // check if site is allowed for the margin creation
    const restrictions = await getSiteRestrictionsList(query.agency_client_association_id);
    const check = restrictions.find((restriction) => restriction.site.id === query.site_id);
    if (!check) {
        return [403, ErrorResponse.SiteNotAllowedForMarginCreation];
    }

    const existingMargins = await getMarginsListHelper({
        agencyClientAssociationId: query.agency_client_association_id,
        siteId: query.site_id,
        los: query.los || null
    });

    if (existingMargins.length) {
        return [409, ErrorResponse.ResourceAlreadyExists];
    }

    const marginsPayload = {
        agencyClientAssociationId: query.agency_client_association_id,
        siteId: query.site_id,
        los: query.los || null,
        margin: payload.margin,
        overtimeMargin: payload.overtime_margin || 0.00,
        transportFee: payload.transport_fee || 0.00,
        ssp: payload.ssp || 0.00,
        inductionTrainingMargin: payload.induction_training_margin || 0.00,
        trainingMargin: payload.training_margin || 0.00,
        bhMargin: payload.bh_margin || 0.00,
        nspMargin: payload.nsp_margin || 0.00,
        supervisorStandardMargin: payload.supervisor_standard_margin || 0.00,
        supervisorOvertimeMargin: payload.supervisor_overtime_margin || 0.00,
        supervisorPermanentMargin: payload.supervisor_permanent_margin || 0.00,
        suspensionMargin: payload.suspension_margin || 0.00,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id
    };

    const margins = await createMargin(marginsPayload);

    return [201, {
        ok: true,
        message: MessageActions.CREATE_MARGINS,
        margins_id: margins.id
    }];
};

export const getMarginsListService = async (params, loggedInUser) => {
    let association = await getAgencyAssociationById(params.agency_client_association_id);
    if (!association) {
        return [404, ErrorResponse.AssocationNotFound];
    }

    // check if current user's client_id is same as the client_id of the association if user is not CLERAVUE_ADMIN
    if (parseInt(loggedInUser.user_type_id) !== UserType.CLEARVUE_ADMIN && loggedInUser.client_id !== association.client_id) {
        return [403, ErrorResponse.Forbidden];
    }

    const whereConditions = {
        agencyClientAssociationId: params.agency_client_association_id,
        siteId: params.site_id
    };

    if (params.los) whereConditions['los'] = params.los;

    const marginsList = await getMarginsListHelper(whereConditions);

    return [200, {
        ok: true,
        count: marginsList.length,
        margins_list: marginsList
    }];
};

export const updateMarginsService = async (id, payload, loggedInUser) => {
    const existingMargin = await getMarginById(id);
    if (!existingMargin) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    let association = await getAgencyAssociationById(existingMargin.agencyClientAssociationId);

    if (parseInt(loggedInUser.user_type_id) !== UserType.CLEARVUE_ADMIN && loggedInUser.client_id !== association.client_id) {
        return [403, ErrorResponse.Forbidden];
    }

    const marginsPayload = {
        margin: payload.margin,
        overtimeMargin: payload.overtime_margin || 0.00,
        transportFee: payload.transport_fee || 0.00,
        ssp: payload.ssp || 0.00,
        inductionTrainingMargin: payload.induction_training_margin || 0.00,
        trainingMargin: payload.training_margin || 0.00,
        bhMargin: payload.bh_margin || 0.00,
        nspMargin: payload.nsp_margin || 0.00,
        supervisorStandardMargin: payload.supervisor_standard_margin || 0.00,
        supervisorOvertimeMargin: payload.supervisor_overtime_margin || 0.00,
        supervisorPermanentMargin: payload.supervisor_permanent_margin || 0.00,
        suspensionMargin: payload.suspension_margin || 0.00,
        updatedAt: new Date(),
        updatedBy: loggedInUser.user_id
    };

    await updateMargin(id, marginsPayload);

    return [200, {
        ok: true,
        message: MessageActions.UPDATE_MARGINS,
    }];
};

export const deleteMarginsService = async (params, loggedInUser) => {
    let association = await getAgencyAssociationById(params.agency_client_association_id);
    if (!association) {
        return [404, ErrorResponse.AssocationNotFound];
    }

    // check if current user's client_id is same as the client_id of the association if user is not CLERAVUE_ADMIN
    if (parseInt(loggedInUser.user_type_id) !== UserType.CLEARVUE_ADMIN && loggedInUser.client_id !== association.client_id) {
        return [403, ErrorResponse.Forbidden];
    }

    const whereConditions = {
        agencyClientAssociationId: params.agency_client_association_id,
        siteId: params.site_id
    };

    if (params.los) {
        whereConditions['los'] = params.los;
    } else {
        whereConditions['los'] = Not(IsNull());
    }
    const existingMargins = await getMarginsListHelper(whereConditions);
    if (!existingMargins.length) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    await deleteMargins(whereConditions);

    return [200, {
        ok: true,
        message: MessageActions.DELETE_MARGINS,
        deleted_count: existingMargins.length
    }];
}; 