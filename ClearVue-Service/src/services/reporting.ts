import moment from "moment";
import { dateTimeFormates, ErrorResponse, WeekDays } from "../common";
import {
    getWorkersGroupedByShiftCounts,
    getWorkersPerformancesData,
    checkAuthorisedResourceAccess,
    getClientsById,
    getPerformanceNewStartersGraphHelper,
    getAverageShiftCounts,
    buildPerformanceWhereCondition,
    getBookingGraphData
} from "../models";
import { getSites } from "../models";
import { diffBetweenGivenTwoDatesInDays } from "../utils";
import { clientWiseShiftFulFillment, siteWiseAveHrs, siteWiseLeaverCount, siteWiseOverSpendNHours, siteWisePerformance, siteWisePoolUtilisation, siteWiseShiftUtilisation, siteWiseSpendNHours } from "../models/sql/helper";


// Common helper functions
const getSiteIdsFromRegion = async (clientId: number, regionId: number, siteId: number | null) => {
    if (!regionId && !siteId) return [];

    if (siteId) return [siteId];

    const sites = await getSites(
        `site.client_id=:client_id AND site.region_id=:region_id`,
        { client_id: clientId, region_id: regionId }
    );
    return [...new Set(sites.map(site => site.id))];
};

// New helper function to batch performance calculations
const calculateBatchPerformance = async (
    workerGroups: number[][],
    clientId: number,
    agencyId: number | null,
    siteIds: unknown[],
    startDate: string,
    endDate: string
): Promise<{
    averagePerformances: number[];
    workerIdMap: Record<number, number[]>;
}> => {
    // Get all worker IDs
    const allWorkerIds = workerGroups.flat();
    if (allWorkerIds.length === 0) {
        return {
            averagePerformances: [],
            workerIdMap: {}
        };
    }

    // Fetch performance data for all workers in one query
    const { whereCondition, whereConditionValue } = await buildPerformanceWhereCondition(
        allWorkerIds,
        clientId,
        agencyId,
        siteIds,
        startDate,
        endDate
    );

    const allPerformanceData = await getWorkersPerformancesData(
        whereCondition,
        whereConditionValue
    );

    // Group performance data by worker groups
    const averagePerformances: number[] = [];
    const workerIdMap: Record<number, number[]> = {};

    workerGroups.forEach((group, index) => {
        const groupPerformance = allPerformanceData.filter(data =>
            group.includes(data.worker_id)
        );

        // Get unique worker IDs that had performance data
        const workersWithData = [...new Set(groupPerformance.map(data => data.worker_id))];

        const totalPerformance = groupPerformance.reduce((sum, data) =>
            sum + parseFloat(data.performance_number), 0);

        const avgPerformance = groupPerformance.length ?
            Number((totalPerformance / groupPerformance.length).toFixed(2)) : 0;

        averagePerformances.push(avgPerformance);

        // Only include workers that actually contributed to the calculation
        if (workersWithData.length > 0) {
            workerIdMap[index] = workersWithData;
        }
    });

    return {
        averagePerformances,
        workerIdMap
    };
};

export const getPerformanceShiftsBlocksService = async (requestArgs, loggedInUser) => {
    // Validate access and get payload
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const {
        start_date,
        end_date,
        client_id,
        region_id,
        site_id,
        agency_id,
        department_id,
        shift_id
    } = updatedPayload;

    const defaultResponse = { ok: true, performanceByShifts: Object.fromEntries(Array.from({ length: 7 }, (_, i) => [i + 1, {}])) };

    // Get site IDs
    const siteIds = await getSiteIdsFromRegion(client_id, region_id, site_id);
    if (region_id && siteIds.length === 0) {
        return [200, defaultResponse];
    }

    // Get workers grouped by shift counts
    const groupedWorkers = await getWorkersGroupedByShiftCounts(
        start_date,
        end_date,
        client_id,
        { regionId: region_id, siteId: site_id, agencyId: agency_id, departmentId: department_id, shiftId: shift_id }
    );

    // Prepare worker groups for batch processing
    const workerGroups = groupedWorkers.map(group => group.workers);

    // Calculate performance for all groups in one batch
    const { averagePerformances, workerIdMap } = await calculateBatchPerformance(
        workerGroups,
        client_id,
        agency_id,
        siteIds,
        start_date,
        end_date
    );

    // Build result object using reduce for better performance
    const result = groupedWorkers.reduce((acc, group, index) => {
        const shiftCount = index + 1;
        acc[shiftCount] = {
            workers: workerIdMap[index] || [], // Use worker IDs that actually contributed to performance calculation
            average_performance: averagePerformances[index] || 0
        };
        return acc;
    }, {});

    return [200, { ok: true, performanceByShifts: result }];
};

export const getPerformanceNewStartersGraphService = async (requestArgs, loggedInUser) => {
    // Validate access and get payload
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const {
        start_date,
        end_date,
        client_id,
        region_id,
        site_id,
        agency_id
    } = updatedPayload;

    // Validate date range
    if (diffBetweenGivenTwoDatesInDays(start_date, end_date) !== 6) {
        return [400, ErrorResponse.WrongDateRange];
    }

    // Run client validation and site IDs fetch in parallel
    const [clientDetails, siteIds] = await Promise.all([
        getClientsById(client_id),
        getSiteIdsFromRegion(client_id, region_id, site_id)
    ]);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(start_date).getDay()];

    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const emptyArray = Array(12).fill(0);
    const defaultResponse = {
        ok: true,
        averageShiftCounts: emptyArray,
        averagePerformance: emptyArray,
    };

    if (region_id && siteIds.length === 0) {
        return [200, defaultResponse];
    }

    // Calculate twelve weeks ago date
    const twelveWeeksAgoWeekStartDate = new Date(end_date);
    twelveWeeksAgoWeekStartDate.setDate(twelveWeeksAgoWeekStartDate.getDate() - (12 * 7) + 1);
    const twelveWeeksAgoDate = twelveWeeksAgoWeekStartDate.toISOString().split('T')[0];

    // Get grouped workers first
    const groupedWorkers = await getPerformanceNewStartersGraphHelper({
        ...updatedPayload,
        siteIds,
    }, clientDetails.weekday_start, twelveWeeksAgoDate);

    if (groupedWorkers.flat().length === 0) {
        return [200, defaultResponse];
    }

    // Then calculate average shift counts with the grouped workers
    const averageShiftCounts = await getAverageShiftCounts(updatedPayload, groupedWorkers);

    // Batch performance calculations
    const workerGroups = groupedWorkers.map(group => group.map(w => w.id));
    const { averagePerformances, workerIdMap } = await calculateBatchPerformance(
        workerGroups,
        client_id,
        agency_id,
        siteIds,
        start_date,
        end_date
    );

    return [200, {
        ok: true,
        averageShiftCounts,
        averagePerformance: averagePerformances.length ? averagePerformances : emptyArray,
    }];
};

export const getPerformanceByTenureGraphService = async (requestArgs, loggedInUser) => {
    // Validate access and get payload
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const {
        start_date,
        end_date,
        client_id,
        region_id,
        site_id,
        agency_id,
        department_id,
        shift_id
    } = updatedPayload;

    // Validate date range
    if (diffBetweenGivenTwoDatesInDays(start_date, end_date) !== 6) {
        return [400, ErrorResponse.WrongDateRange];
    }

    // Run client validation and site IDs fetch in parallel
    const [clientDetails, siteIds] = await Promise.all([
        getClientsById(client_id),
        getSiteIdsFromRegion(client_id, region_id, site_id)
    ]);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(start_date).getDay()];

    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const emptyArray = Array(6).fill(0);
    const defaultResponse = {
        ok: true,
        averageShiftCounts: emptyArray,
        averagePerformance: emptyArray,
    };

    if (region_id && siteIds.length === 0) {
        return [200, defaultResponse];
    }

    // Get grouped workers first
    const groupedWorkers = await getPerformanceNewStartersGraphHelper({
        ...updatedPayload,
        siteIds,
    }, clientDetails.weekday_start);

    if (groupedWorkers.flat().length === 0) {
        return [200, defaultResponse];
    }

    // Then calculate average shift counts with the grouped workers
    const averageShiftCounts = await getAverageShiftCounts(updatedPayload, groupedWorkers);

    // Batch performance calculations
    const workerGroups = groupedWorkers.map(group => group.map(w => w.id));
    const { averagePerformances, workerIdMap } = await calculateBatchPerformance(
        workerGroups,
        client_id,
        agency_id,
        siteIds,
        start_date,
        end_date
    );

    return [200, {
        ok: true,
        averageShiftCounts,
        averagePerformance: averagePerformances.length ? averagePerformances : emptyArray,
    }];
};

export const getShiftBookingGraphService = async (requestArgs: any, loggedInUser: any): Promise<[number, any]> => {
    // Validate access and get payload
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const {
        start_date,
        end_date,
        client_id,
        region_id,
        site_id,
        agency_id,
        department_id,
        shift_id
    } = updatedPayload;

    // Validate date range
    if (diffBetweenGivenTwoDatesInDays(start_date, end_date) !== 6) {
        return [400, ErrorResponse.WrongDateRange];
    }

    // Run client validation and site IDs fetch in parallel
    const [clientDetails, siteIds] = await Promise.all([
        getClientsById(client_id),
        getSiteIdsFromRegion(client_id, region_id, site_id)
    ]);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(start_date).getDay()];

    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    if (region_id && siteIds.length === 0) {
        return [200, { ok: true, result: [] }];
    }

    // Get booking graph data
    const result = await getBookingGraphData(client_id, {
        regionId: region_id,
        siteId: site_id,
        agencyId: agency_id,
        departmentId: department_id,
        shiftId: shift_id,
        startDate: start_date,
        endDate: end_date
    });

    return [200, {
        ok: true,
        result
    }];
};

export const siteStatsShiftFulfilment = async (requestArgs, loggedInUser) => {
    // Validate authorization
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    // Get client details once
    const clientDetails = await getClientsById(updatedPayload.client_id, false);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // Start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    // Calculate date ranges for comparison
    const diff = moment(endDate).diff(moment(startDate), "days") + 1;
    const lastWeekStartDate = moment(startDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
    const lastWeekEndDate = moment(endDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);

    // Prepare common query parameters
    const queryParams = {
        clientId: updatedPayload.client_id,
        regionId: updatedPayload.region_id || 0,
        agencyId: updatedPayload.agency_id ?? null,
        siteId: updatedPayload.site_id ?? null
    };

    // Execute both database queries in parallel
    const [currentShiftFulfilmentData, pastShiftFulfilmentData] = await Promise.all([
        clientWiseShiftFulFillment(
            queryParams.clientId,
            queryParams.regionId,
            startDate,
            endDate,
            queryParams.agencyId,
            queryParams.siteId
        ),
        clientWiseShiftFulFillment(
            queryParams.clientId,
            queryParams.regionId,
            lastWeekStartDate,
            lastWeekEndDate,
            queryParams.agencyId,
            queryParams.siteId,
            true
        )
    ]);

    // Combine results
    const combinedShiftFulfilmentData = [...currentShiftFulfilmentData, ...pastShiftFulfilmentData];

    return [
        200,
        {
            ok: true,
            result: combinedShiftFulfilmentData,
        },
    ];
};

export const siteStatsSpendHours = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    // Get client details once
    const clientDetails = await getClientsById(updatedPayload.client_id, false);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    // to compare data with past week
    const diff = moment(endDate).diff(moment(startDate), "days") + 1;
    const lastWeekStartDate = moment(startDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
    const lastWeekEndDate = moment(endDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);

    // Prepare common query parameters
    const queryParams = {
        clientId: updatedPayload.client_id,
        regionId: updatedPayload.region_id || 0,
        agencyId: updatedPayload.agency_id ?? null,
        siteId: updatedPayload.site_id ?? null
    };

    // Execute all database queries in parallel instead of sequentially
    const [spendAndHours, pastSpendAndHours, overSpendAndHours] = await Promise.all([
        siteWiseSpendNHours(
            queryParams.clientId,
            queryParams.regionId,
            startDate,
            endDate,
            queryParams.agencyId,
            queryParams.siteId
        ),
        siteWiseSpendNHours(
            queryParams.clientId,
            queryParams.regionId,
            lastWeekStartDate,
            lastWeekEndDate,
            queryParams.agencyId,
            queryParams.siteId,
            true
        ),
        siteWiseOverSpendNHours(
            queryParams.clientId,
            queryParams.regionId,
            startDate,
            endDate,
            queryParams.agencyId,
            queryParams.siteId
        )
    ]);

    // Create a Map for faster lookups instead of using reduce
    const overSpendMap = new Map();
    for (const item of overSpendAndHours) {
        overSpendMap.set(item.s_id, item);
    }

    // Calculate and merge results more efficiently
    const spendNHours = spendAndHours.map((item) => {
        const overSpendItem = overSpendMap.get(item.s_id);

        // Only calculate percentages if we have valid data
        let ot_spend_per = 0;
        let ot_hour_per = 0;

        if (overSpendItem && item.spend > 0) {
            ot_spend_per = +Math.max((+overSpendItem.over_spend / +item.spend) * 100, 0).toFixed(2) || 0;
        }

        if (overSpendItem && item.hours > 0) {
            ot_hour_per = +Math.max((+overSpendItem.over_hours / +item.hours) * 100, 0).toFixed(2) || 0;
        }

        return {
            ...item,
            ...(overSpendItem || {}),
            ot_spend_per,
            ot_hour_per,
        };
    });

    return [
        200,
        {
            ok: true,
            result: [...spendAndHours, ...pastSpendAndHours, ...spendNHours],
        },
    ];
};

export const siteStatsAveHours = async (requestArgs, loggedInUser) => {
    // Check authorization first
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    // Get client details once
    const clientDetails = await getClientsById(updatedPayload.client_id, false);

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // Start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    // Calculate date ranges for comparison
    const diff = moment(endDate).diff(moment(startDate), "days") + 1;
    const lastWeekStartDate = moment(startDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
    const lastWeekEndDate = moment(endDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);

    // Prepare common query parameters
    const queryParams = {
        clientId: updatedPayload.client_id,
        regionId: updatedPayload.region_id || 0,
        agencyId: updatedPayload.agency_id ?? null,
        siteId: updatedPayload.site_id ?? null
    };

    // Execute both database queries in parallel
    const [currentAveHours, pastAveHours] = await Promise.all([
        siteWiseAveHrs(
            queryParams.clientId,
            queryParams.regionId,
            startDate,
            endDate,
            queryParams.agencyId,
            queryParams.siteId
        ),
        siteWiseAveHrs(
            queryParams.clientId,
            queryParams.regionId,
            lastWeekStartDate,
            lastWeekEndDate,
            queryParams.agencyId,
            queryParams.siteId,
            true
        )
    ]);

    return [
        200,
        {
            ok: true,
            result: [...currentAveHours, ...pastAveHours],
        },
    ];
};

export const siteStatsPoolUtilisation = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    const clientDetails = await getClientsById(updatedPayload.client_id, false)

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const poolUtilisation = await siteWisePoolUtilisation(
        updatedPayload.client_id,
        updatedPayload.region_id || 0,
        startDate,
        endDate,
        updatedPayload.agency_id ?? null,
        updatedPayload.site_id ?? null
    );
    return [
        200,
        {
            ok: true,
            result: poolUtilisation,
        },
    ];

};

export const siteStatsLeavers = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    const clientDetails = await getClientsById(updatedPayload.client_id, false)

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const leaversCount = await siteWiseLeaverCount(
        updatedPayload.client_id,
        updatedPayload.region_id || 0,
        startDate,
        endDate,
        updatedPayload.agency_id ?? null,
        updatedPayload.site_id ?? null
    );
    return [
        200,
        {
            ok: true,
            result: leaversCount,
        },
    ];

};


export const siteStatsPerformance = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    const clientDetails = await getClientsById(updatedPayload.client_id, false)

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const performance = await siteWisePerformance(
        updatedPayload.client_id,
        updatedPayload.region_id || null,
        startDate,
        endDate,
        updatedPayload.agency_id ?? null,
        updatedPayload.site_id ?? null
    );
    return [
        200,
        {
            ok: true,
            result: performance,
        },
    ];

};

export const siteStatsShiftUtilisation = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date: startDate, end_date: endDate } = updatedPayload;

    const clientDetails = await getClientsById(updatedPayload.client_id, false)

    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(startDate).getDay()];

    // start day validation
    if (!clientDetails || clientDetails.weekday_start !== dayName) {
        return [400, ErrorResponse.InvalidStartDate];
    }

    const shiftUtilisation = await siteWiseShiftUtilisation(
        updatedPayload.client_id,
        updatedPayload.region_id || 0,
        startDate,
        endDate,
        updatedPayload.agency_id ?? null,
        updatedPayload.site_id ?? null
    );
    return [
        200,
        {
            ok: true,
            result: shiftUtilisation,
        },
    ];

};
