/**
 * All the service layer methods for the Agency.
 */
const _ = require('lodash');
import { config } from "./../configurations";
import { CreateAgencyDTO, ErrorResponse, ErrorCodes, labelWiseDefaultRatings, labelWiseAgencyDefaultRatings } from "./../common";
import {
    createAgency, updateAgency, getAgencyList, getAgencyById, getAssociatedAgencies,
    getDashboardAgencyRatingsHelper, getAgencyRatingsWithLabelHelper, getAverageAgencyRatings, getAgencyWiseReviewsCount,
    addAgencySiteUser, addResetPasswordToken, addAgencyRegionUser, updateAgencyUserHelper, getAgencyUsersByIDHelper, updateUserHelper, updateRegion, removeUserSiteAssociation, generateUserSiteAssociation, getAllUsers, getClientUsersHelper, assigneAutomatedMessagesToAgency, removeUserRegionAssociation, generateUserRegionAssociation, getUserByEmail,
    checkAuthorisedResourceAccess,
    getSites,
    getClientsById
} from "./../models";
import { uploadFileOnS3, sendTemplateEmail, notifyBugsnag, getUserByUserTypeId, dynamicErrorObject } from "../utils";
import { MessageActions, UserType, RedirectURLs } from "./../common";
import { sendDefaultMessageTemplate } from "./messages";
const jwt = require("jsonwebtoken");

/**
 * create agency.
 * @param  {CreateAgencyDTO} payload
 */
export const createAgencyService = async (payload: CreateAgencyDTO, loggedInUser) => {
    const agencyPayload = {
        name: payload.name,
        city: payload.city,
        country: payload.country,
        postCode: payload.postCode,
        address: {
            address_line_1: payload.address_line_1,
            address_line_2: payload.address_line_2 || '',
            address_line_3: payload.address_line_3 || '',
        },
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id,
    }
    let agency = await createAgency(agencyPayload);
    await assigneAutomatedMessagesToAgency(agency.id);
    return [201, {
        ok: true,
        message: MessageActions.CREATE_AGENCY,
        agency_id: agency.id,
    }];
};

/**
 * update agency.
 * @param id
 * @param  {UpdateAgencyDTO} payload
 * @param loggedInUser
 * @param image
 */
export const updateAgencyService = async (id, payload, loggedInUser, image) => {
    let agencytoUpdate = await getAgencyById(id);

    if (!agencytoUpdate) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    if (payload.profile && (payload.profile === "null")) {
        delete payload.profile
    } else if (image) {
        let resourceName = "AGENCY" + id + image.extension;
        payload["resource"] = config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + resourceName;
        await uploadFileOnS3(config.BUCKET_NAME, config.PROFILE_BUCKET_FOLDER, resourceName, image.mime, image.data);
    }
    payload.address = {
        address_line_1: payload.address_line_1,
        address_line_2: payload.address_line_2 || '',
        address_line_3: payload.address_line_3 || ''
    }
    payload.updatedBy = loggedInUser.user_id
    delete payload.address_line_1
    delete payload.address_line_2
    delete payload.address_line_3

    let agency = await updateAgency(id, payload);
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_AGENCY,
        agency_id: agency.id
    }];
};

/**
 * get agency list.
 * @param loggedInUser
 * @param data
 */
export const getAgencyListService = async (loggedInUser, data) => {

    let agencyList = await getAgencyList(loggedInUser, data.page || 1, data.limit || 10, data.sort_by || "agency_name", data.sort_type || "asc");
    let count = 0;
    if (agencyList.count) {
        count = parseInt(agencyList.count);
        agencyList = _.map(agencyList, (object) => {
            object.association_id = parseInt(object.association_id);
            object.agency_id = parseInt(object.agency_id);
            object.address = JSON.parse(object.address);
            return object;
        })
    }

    return [200, {
        "ok": true,
        "count": count,
        "agency_list": agencyList
    }];
};

/**
 * get agency by id.
 * @param id
 */
export const getAgencyByIdService = async (id: string) => {

    let agency = await getAgencyById(id);
    let url: any;
    if (agency.resource == null) {
        url = config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + config.DEFAULT_IMAGE;
    } else {
        url = agency.resource;
    }
    delete agency.resource;
    agency.address = JSON.parse(agency.address);
    agency.profile_url = url
    return [200, {
        "ok": true,
        "agency": agency
    }];
};


/**
 * Agency Ratings Service.
 * @param data
 * @param loggedInUser
 */
export const agencyRatingsService = async (data, loggedInUser) => {
    if (!_.size(data)) {
        return [400, ErrorResponse.BadRequestError];
    }

    const { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;

    const { client_id, agency_id, site_id, region_id, shift_id, department_id } = updatedPayload;

    let clientDetails = await getClientsById(client_id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    if (clientDetails.hide_ratings) {
        return [200, {
            ok: true,
            average_rating: 0,
            reviews_count: 0,
            label_wise_ratings: labelWiseAgencyDefaultRatings
        }];
    }

    const whereClause1 = `survey_question.belongs_to='AGENCY'`;

    let whereClause2 = 'survey_result.rating is not null';
    const whereClauseValue2 = {};

    if (client_id) {
        whereClause2 += ` AND survey_result.client_id = :client_id`;
        whereClauseValue2["client_id"] = client_id;
    }
    if (agency_id) {
        whereClause2 += ` AND survey_result.agency_id = :agency_id`;
        whereClauseValue2["agency_id"] = agency_id;
    }
    if (site_id) {
        whereClause2 += ` AND survey_result.site_id = :site_id`;
        whereClauseValue2["site_id"] = site_id;
    }
    if (region_id) {
        whereClause2 += ` AND site.region_id = :region_id`;
        whereClauseValue2["region_id"] = region_id;
    }

    let whereClause3 = '';
    const whereClauseValue3 = {};

    if (shift_id) {
        whereClause3 = `job.shift_id = :shift_id`;
        whereClauseValue3["shift_id"] = shift_id;
    }
    if (department_id) {
        whereClause3 += (whereClause3 ? ' AND ' : '') + `job_association.department_id = :department_id`;
        whereClauseValue3["department_id"] = department_id;
    }

    const response = await getDashboardAgencyRatingsHelper(whereClause1, {}, whereClause2, whereClauseValue2, whereClause3, whereClauseValue3);
    const reviews_count = response.reviews_count.map(object => parseInt(object.reviews_count, 10));
    const { average_rating, label_wise_ratings } = response;

    return [200, {
        "ok": true,
        average_rating: parseFloat(average_rating.ratings),
        reviews_count: _.sum(reviews_count),
        label_wise_ratings
    }];
};


/**
 * Agency Ratings Service.
 * @param data
 * @param loggedInUser
 */
export const detailedAgencyRatingsService = async (data, loggedInUser) => {
    if (!_.size(data)) {
        return [400, ErrorResponse.BadRequestError]
    }

    const { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;

    const { client_id, agency_id, site_id, region_id, ...others } = updatedPayload;

    let clientDetails = await getClientsById(client_id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    if (clientDetails.hide_ratings) {
        return [200, { ok: true, 'ratings': [] }]
    }

    let agencyWiseRatings: any;
    let agencies = agency_id ? [agency_id] : await getAssociatedAgencies(client_id);
    if (_.size(agencies)) {
        let agencyIdList = agencies.map(element => parseInt(element.agencyId));
        let whereClause = `survey_result.rating is not null AND survey_question.belongs_to='AGENCY' AND survey_result.agency_id IN (:agencyIdList) AND survey_result.client_id= :client_id`;
        let whereClauseValue = { "agencyIdList": agencyIdList, "client_id": client_id }

        if (site_id) {
            whereClause += ` AND survey_result.site_id = :site_id`;
            whereClauseValue["site_id"] = site_id;
        }
        if (region_id) {
            whereClause += ` AND site.region_id = :region_id`;
            whereClauseValue["region_id"] = region_id;
        }

        agencyWiseRatings = await getAgencyRatingsWithLabelHelper(whereClause, whereClauseValue);
        let agencyWiseAverageRatings = await getAverageAgencyRatings(whereClause, whereClauseValue);
        let agencyWiseReviewsCount = await getAgencyWiseReviewsCount(whereClause, whereClauseValue);

        let grouped = _.groupBy(agencyWiseRatings, object => object.agency_name);

        let response: any = []

        Object.keys(grouped).forEach(key => {
            let reviewsCount = [];
            reviewsCount = grouped[key].map(object => parseInt(object.reviews_count))
            let label_wise_ratings = grouped[key].map(object => { return { "label": object.label, "ratings": object.ratings } })
            let obj = agencyWiseReviewsCount.find(o => o.agency_id === grouped[key][0].agency_id);
            response.push({
                name: key,
                reviews_count: parseInt(obj.reviews_count),
                average_rating: parseFloat(agencyWiseAverageRatings.find(object => object.agency_name === key).ratings),
                label_wise_ratings
            })
        })

        return [200, { ok: true, 'ratings': response || [] }]
    }
    else {
        return [200, { ok: true, ratings: {} }]
    }
};

/**
 * Service for adding the agency admin users.
 */
export const addAgencyUsersService = async (payload, loggedInUser) => {
    try {
        let id: number;
        let company_name: any;

        // check user already exists.
        let userDetails = await getUserByEmail(payload.email);
        if (userDetails) {
            const errorObj = await dynamicErrorObject(ErrorResponse.UserAlreadyExistsCustom, getUserByUserTypeId(userDetails.userTypeId))
            return [409, errorObj]
        }

        if (payload.client_role === UserType.AGENCY_SITE) {
            //helper to add the agency user and update the admin id of the site.
            let agencySiteUser = await addAgencySiteUser(payload, loggedInUser);
            id = parseInt(agencySiteUser.id);
            company_name = agencySiteUser.company_name;

            await sendDefaultMessageTemplate(id);   // Send default template messages for agency site admin
        } else if (payload.client_role == UserType.AGENCY_REGIONAL) {
            //Helper to add the agency user and update tha admin id of the region.
            let agencyRegionUser = await addAgencyRegionUser(payload, loggedInUser);
            id = parseInt(agencyRegionUser.id);
            company_name = agencyRegionUser.company_name;

            await sendDefaultMessageTemplate(id);   // Send default template messages for agency regional admin
        } else if (!payload.id) {
            return [400, ErrorResponse.BadRequestError];
        } else {
            return [422, ErrorResponse.UnprocessableEntity]
        }

        //Email Authentication setup.
        const resetPasswordJwtToken = await jwt.sign(
            { user_id: id },
            config.JWT_TOKEN_KEY,
            {
                expiresIn: config.RESET_PASSWORD_LINK_EXPIRE_TIME,
            }
        );

        await addResetPasswordToken(resetPasswordJwtToken, id);

        let message = {
            toEmailId: payload.email,
            templateId: config.Sendgrid.INVITE_USER_EMAIL_TEMPLATE,
            dynamicTemplateData: {
                sender_name: loggedInUser.user_name,
                account_name: company_name,
                invitation_link:
                    config.PORTAL_HOST_URL +
                    RedirectURLs.RESET_PASSWORD +
                    "?type=set_password&code=" +
                    resetPasswordJwtToken,
            },
        };
        await sendTemplateEmail(message);
        return [201, { ok: true, user_id: id, message: MessageActions.CREATE_USER }]

    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to updating the agency users details.
 */
export const updateAgencyUserService = async (agency_user_id, payload, loggedInUser) => {
    try {
        let data: any = {};
        //Updating only user details
        if (!payload.user_type_id && !payload.id) {
            data = {
                name: payload.name,
                mobile: payload.phone,
                countryCode: payload.country_code
            }
            await updateAgencyUserHelper(agency_user_id, data);
        } else {
            //Assigning the admin to different site or different region.
            data = {
                name: payload.name,
                mobile: payload.phone,
                countryCode: payload.country_code
            }
            await updateAgencyUserHelper(agency_user_id, data);
            //TODO: Check for current user_type of the user admin and site assigned.
            let whereClause = `user.id = :agency_user_id`;
            let whereClauseValue = { "agency_user_id": agency_user_id }
            let userDetails = await getAgencyUsersByIDHelper(whereClause, whereClauseValue);

            if (!userDetails) {
                return [404, ErrorResponse.ResourceNotFound]
            }
            let { user_type_id } = userDetails;
            if (parseInt(user_type_id) == parseInt(payload.user_type_id)) {
                if (parseInt(user_type_id) == UserType.AGENCY_REGIONAL) {
                    //ToDo: Agency-Region-Admin reassignment to different region.
                    //Check if the association with the requested region exists.
                    if (parseInt(userDetails.region_id) == parseInt(payload.id)) {
                        return [400, ErrorResponse.AssociationAlreadyExists]
                    }

                    //Revoke the user credentials.
                    await updateUserHelper(agency_user_id, {
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    })

                    //Remove the existing User-Region Association.
                    await removeUserRegionAssociation(agency_user_id, userDetails.region_id);

                    //Generate a new Association.
                    await generateUserRegionAssociation(agency_user_id, parseInt(payload.id), loggedInUser.user_id)

                } else {
                    //ToDo: Agency-Site-Admin reassignment to different site.
                    //Check if the association with the requested site exists.
                    if (parseInt(userDetails.site_id) == parseInt(payload.id)) {
                        return [400, ErrorResponse.AssociationAlreadyExists]
                    }
                    //Revoke the user details
                    await updateUserHelper(agency_user_id, {
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    });

                    //Remove the existing User-Site Association.
                    await removeUserSiteAssociation(agency_user_id, userDetails.site_id);

                    //Generate a new Association.
                    await generateUserSiteAssociation(agency_user_id, parseInt(payload.id), loggedInUser.user_id)
                }
            } else {
                //Assigning region admin to site admin.
                if (parseInt(payload.user_type_id) === UserType.AGENCY_SITE) {
                    //Revoke and Update the user_type of the user to site admin from region admin.
                    await updateUserHelper(agency_user_id, {
                        userTypeId: UserType.AGENCY_SITE,
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    })

                    //Remove the existing User-Region Association.
                    await removeUserRegionAssociation(agency_user_id, userDetails.region_id);

                    //Generate the association.
                    await generateUserSiteAssociation(agency_user_id, parseInt(payload.id), loggedInUser.user_id);
                }
                //Assigning the site admin to region admin.
                else {
                    //Revoke and Update the user_type of the user to region admin from site admin.
                    await updateUserHelper(agency_user_id, {
                        userTypeId: UserType.AGENCY_REGIONAL,
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    });

                    // Remove the site association
                    await removeUserSiteAssociation(agency_user_id, userDetails.site_id);

                    //Generate a new Association.
                    await generateUserRegionAssociation(agency_user_id, parseInt(payload.id), loggedInUser.user_id)
                }
            }
            //Email invitation setup.

            const resetPasswordJwtToken = await jwt.sign(
                { user_id: agency_user_id },
                config.JWT_TOKEN_KEY,
                {
                    expiresIn: config.RESET_PASSWORD_LINK_EXPIRE_TIME,
                }
            );
            await addResetPasswordToken(resetPasswordJwtToken, agency_user_id);
            let message = {
                toEmailId: userDetails.email,
                templateId: config.Sendgrid.INVITE_USER_EMAIL_TEMPLATE,
                dynamicTemplateData: {
                    sender_name: loggedInUser.user_name,
                    account_name: userDetails.agency_name,
                    invitation_link:
                        config.PORTAL_HOST_URL +
                        RedirectURLs.RESET_PASSWORD +
                        "?type=set_password&code=" +
                        resetPasswordJwtToken,
                },
            };
            await sendTemplateEmail(message);
        }
        return [200, { ok: true, message: MessageActions.UPDATE_USER }]

    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound] // Return 404 if any foreign key
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service for GET list of agency users.
 */
export const getAgencyUsersService = async (data, loggedInUser) => {
    let userDetails = await getAllUsers(loggedInUser);
    const whereClause = `user.clientId = :client_id AND user.agencyId = :agency_id AND user.user_type_id IN (:user_type_id)`;
    const whereClauseValue = { "client_id": data.client_id, "agency_id": userDetails.agency_id, "user_type_id": [UserType.AGENCY_SITE, UserType.AGENCY_REGIONAL] }
    let agencyUsersDetails = await getClientUsersHelper(whereClause, whereClauseValue);

    agencyUsersDetails = _.map(agencyUsersDetails, (agency) => {
        agency.name = agency.user_type_id == UserType.AGENCY_REGIONAL ? agency.region_name : agency.user_type_id == UserType.AGENCY_SITE ? agency.site_name : "";
        agency.verbose_id = agency.user_type_id == UserType.AGENCY_REGIONAL ? agency.region_id : agency.user_type_id == UserType.AGENCY_SITE ? agency.site_id : "";
        delete agency.region_id;
        delete agency.region_name;
        delete agency.site_id;
        delete agency.site_name;
        return agency;
    })
    const response = _.size(agencyUsersDetails) ? agencyUsersDetails : [];
    return [
        200,
        {
            ok: true,
            users: response,
        },
    ];
}
