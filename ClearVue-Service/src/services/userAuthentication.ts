/**
 * All the service layer methods for the User and its authentication.
 */
const bcrypt = require('bcryptjs');
const jwt = require("jsonwebtoken");
const deepClone = require('lodash.clonedeep');
const axios = require('axios');
import { config } from "./../configurations";
import {
	LoginUserDTO, ErrorResponse, ForgotPasswordDTO, RedirectURLs,
	ResetPasswordDTO, bcryptSaltRound, UserType,
	AuthType
} from "./../common";
import {
	getUserByEmail, getPermissionsByUserType, updatePasswordAndVerificationStatus,
	addResetPasswordToken, removeResetPasswordToken, checkBlockedLoginAttempt, countLoginAttempts, insertLoginData, removeUserFromLoginAttempt,
	getUserById, updateUser, initializeUserActivityForFreshLogin
} from "./../models";
import { verifyJwtToken, verifyJwtTokenDetailed, sendTemplateEmail, notifyBugsnag } from "../utils";
import {
	isFirebaseEnabled, migrateUserToFirebase,
	verifyFirebaseUserExists, updateFirebaseUserPassword, authenticateWithFirebase,
	setFirebaseUserClaims
} from "./firebaseAuth";
import { isMfaEnabled, getUserMfaStatus, revokeAllUserSessions, deleteAllUserMfaEnrollments } from './firebaseMfa';

/**
 * Renew access token (supports both Firebase and Legacy tokens)
 * @param  {string} refresh_token
 */
export const renewAccessTokenService = async (refresh_token: string) => {
	try {
		// If Firebase is enabled, try Firebase token refresh first
		if (isFirebaseEnabled()) {
			try {
				// Use Firebase REST API to refresh token
				const response = await axios({
					method: 'POST',
					url: `${config.FIREBASE_REFRESH_TOKEN_URL}?key=${config.FIREBASE_WEB_API_KEY}`,
					headers: {
						'Content-Type': 'application/json',
					},
					data: {
						grant_type: 'refresh_token',
						refresh_token: refresh_token,
					},
				});

				if (response.status === 200) {
					const data = response.data;
					return [200, {
						ok: true,
						access_token: data.id_token,
						refresh_token: data.refresh_token,
						auth_type: AuthType.FIREBASE
					}];
				}
			} catch (firebaseError) {
				console.error('Firebase token refresh failed:', firebaseError);
			}

			// If Firebase refresh failed, check if it's a legacy token
			const legacyTokenResult = verifyJwtTokenDetailed(refresh_token);

			if (legacyTokenResult.valid || legacyTokenResult.expired) {
				// Valid/Expired legacy token found while Firebase is enabled - force re-login
				return [401, ErrorResponse.InvalidCredentialsWithForcedRelogin];
			}

			// Invalid token format (not a JWT token) - could be malformed Firebase token
			// Return standard error without force_relogin
			return [401, ErrorResponse.InvalidCredentials];
		} else {
			// Firebase disabled, use legacy JWT renewal
			let tokenData = verifyJwtToken(refresh_token);

			if (tokenData) {
				return [200, {
					ok: true,
					access_token: await jwt.sign(
						tokenData,
						config.JWT_TOKEN_KEY,
						{
							expiresIn: config.USER_ACCESS_TOKEN_EXPIRE_TIME,
						}
					),
					auth_type: AuthType.LEGACY
				}];
			}
			return [401, ErrorResponse.InvalidCredentials];
		}
	} catch (error) {
		notifyBugsnag(error);
		return [500, error.message];
	}
};


/**
 * Helper function to build user response data
 */
const buildUserResponse = (userDetails, permissions, additionalData = {}) => ({
	ok: true,
	user_id: userDetails.id,
	permissions: permissions,
	user_type: userDetails.clientId ? "client" : (userDetails.agencyId ? "agency" : ""),
	user_type_id: userDetails.userTypeId, // Include the actual user_type_id
	client_id: userDetails.clientId ? parseInt(userDetails.clientId) : null,
	agency_id: userDetails.agencyId ? parseInt(userDetails.agencyId) : null,
	...additionalData
});

/**
 * Helper function to handle MFA flow and return appropriate response
 */
const handleMfaResponse = async (userDetails, firebaseAuthResult, firebaseUserCheck, message = "MFA verification required. Please enter your TOTP code.") => {
	const permissions = await getPermissionsByUserType(userDetails.userTypeId);

	// Check if user has MFA enrolled
	const mfaEnabled = firebaseUserCheck?.uid ?
		(await getUserMfaStatus(firebaseUserCheck.uid)).mfaEnabled : false;

	if (!mfaEnabled) {
		await updateUser(userDetails.id, { forceMfaSetup: true });
		return [200, buildUserResponse(userDetails, permissions, {
			requiresMfaSetup: true,
			idToken: firebaseAuthResult.idToken,
			message: "MFA setup required. Please complete MFA enrollment to continue."
		})];
	}

	return [200, buildUserResponse(userDetails, permissions, {
		requiresMfaVerification: true,
		mfaPendingCredential: firebaseAuthResult.mfaPendingCredential || firebaseAuthResult.idToken,
		mfaInfo: firebaseAuthResult.mfaInfo || [],
		message
	})];
};

/**
 * Login user to the system.
 * @param  {LoginUserDTO} payload
 */
export const userLoginService = async (payload) => {
	try {
		const userDetails = await getUserByEmail(payload.email);

		// Early validation returns
		if (!userDetails) return [404, ErrorResponse.UserNotFound];
		if (parseInt(userDetails.userTypeId) === UserType.AGENCY_WORKER) return [403, ErrorResponse.PermissionDenied];
		if (userDetails.password === null && !userDetails.isVerified) return [401, ErrorResponse.InvalidCredentials];
		if (userDetails.password === null && userDetails.isVerified) return [403, ErrorResponse.UserAccessRevoked];
		if (!userDetails.isActive) return [404, ErrorResponse.UserIsDeactivated];
		if (await checkBlockedLoginAttempt(userDetails.id)) return [401, ErrorResponse.BlockedAccount];

		// Check login attempts limit
		const attemptAtPeriod = new Date();
		attemptAtPeriod.setMinutes(attemptAtPeriod.getMinutes() - Number(config.LAST_ATTEMPTS));
		const loginAttempts = await countLoginAttempts(userDetails.id, attemptAtPeriod);

		if (loginAttempts >= config.MAX_WRONG_ATTEMPTS) {
			const blockedUntil = new Date();
			blockedUntil.setMinutes(blockedUntil.getMinutes() + Number(config.BLOCKED_TIME));
			await insertLoginData({ userId: userDetails.id, blockedAt: blockedUntil });
			return [401, { ok: false, message: `Your account is blocked for ${config.BLOCKED_TIME} minutes, please try again later` }];
		}

		// Authentication variables
		let authSuccess = false;
		let firebaseIdToken = null;
		let firebaseRefreshToken = null;
		const isFirebaseAuth = isFirebaseEnabled();

		if (isFirebaseAuth) {
			const firebaseUserCheck = await verifyFirebaseUserExists(payload.email);

			if (firebaseUserCheck.exists) {
				// Existing Firebase user authentication
				const firebaseAuthResult = await authenticateWithFirebase(payload.email, payload.password);

				if (firebaseAuthResult.success && firebaseAuthResult.idToken) {
					authSuccess = true;
					if (isMfaEnabled()) return await handleMfaResponse(userDetails, firebaseAuthResult, firebaseUserCheck);
					firebaseIdToken = firebaseAuthResult.idToken;
					firebaseRefreshToken = firebaseAuthResult.refreshToken;
				} else if (firebaseAuthResult.error === 'SECOND_FACTOR_REQUIRED' && isMfaEnabled()) {
					return await handleMfaResponse(userDetails, firebaseAuthResult, firebaseUserCheck);
				} else if (firebaseAuthResult.error === 'SECOND_FACTOR_REQUIRED' && !isMfaEnabled()) {
					// MFA is not enabled, but Firebase is returning a SECOND_FACTOR_REQUIRED error
					// This could happen if the user was enrolled in MFA in the past, but was later disabled it from BE
					// In this case, we need to delete the user's MFA enrollments from Firebase
					await deleteAllUserMfaEnrollments(firebaseUserCheck.uid);

					const retryAuthResult = await authenticateWithFirebase(payload.email, payload.password);
					if (retryAuthResult.success) {
						authSuccess = true;
						firebaseIdToken = retryAuthResult.idToken;
						firebaseRefreshToken = retryAuthResult.refreshToken;
					}
				}
				else if (firebaseAuthResult.error === 'Invalid password' && await bcrypt.compareSync(payload.password, userDetails.password)) {
					// Try updating Firebase password with legacy password
					const updateResult = await updateFirebaseUserPassword(payload.email, payload.password);
					if (updateResult.success) {
						const retryAuthResult = await authenticateWithFirebase(payload.email, payload.password);
						if (retryAuthResult.success) {
							authSuccess = true;
							if (isMfaEnabled()) return await handleMfaResponse(userDetails, retryAuthResult, firebaseUserCheck);
							firebaseIdToken = retryAuthResult.idToken;
							firebaseRefreshToken = retryAuthResult.refreshToken;
						}
					}
				}

				// If Firebase user auth failed, return error (don't fall back to legacy)
				if (!authSuccess) {
					return [401, { ok: false, message: firebaseAuthResult.error || 'Authentication failed' }];
				}
			} else if (await bcrypt.compareSync(payload.password, userDetails.password)) {
				// Legacy user migration to Firebase
				authSuccess = true;
				const userClaims = {
					user_id: userDetails.id,
					user_type_id: userDetails.userTypeId,
					user_name: userDetails.name,
					client_id: userDetails.clientId,
					agency_id: userDetails.agencyId
				};

				const migrationResult = await migrateUserToFirebase(payload.email, payload.password, userClaims);
				if (migrationResult.success) {
					if (migrationResult.uid) await updateUser(userDetails.id, { firebaseUid: migrationResult.uid });

					const firebaseAuthResult = await authenticateWithFirebase(payload.email, payload.password);
					if (firebaseAuthResult.success && firebaseAuthResult.idToken) {
						firebaseIdToken = firebaseAuthResult.idToken;
						firebaseRefreshToken = firebaseAuthResult.refreshToken;

						if (isMfaEnabled()) {
							await updateUser(userDetails.id, { forceMfaSetup: true });
							const permissions = await getPermissionsByUserType(userDetails.userTypeId);
							return [200, buildUserResponse(userDetails, permissions, {
								requiresMfaSetup: true,
								idToken: firebaseIdToken,
								message: "Account migrated to Firebase. MFA setup is now required. Please complete MFA enrollment to continue."
							})];
						}
					}
				}
			}
		} else {
			// Legacy authentication (Firebase disabled)
			authSuccess = await bcrypt.compareSync(payload.password, userDetails.password);
		}

		if (!authSuccess) {
			await insertLoginData({ userId: userDetails.id, attemptAt: new Date() });
			return [401, ErrorResponse.InvalidCredentials];
		}

		// Success flow - update Firebase claims if needed
		let firebaseUserCheck = null;
		if (isFirebaseAuth && firebaseIdToken) {
			const userClaims = {
				user_id: userDetails.id,
				user_type_id: userDetails.userTypeId,
				user_name: userDetails.name,
				client_id: userDetails.clientId,
				agency_id: userDetails.agencyId
			};
			firebaseUserCheck = await verifyFirebaseUserExists(payload.email);
			if (firebaseUserCheck.exists && firebaseUserCheck.uid) {
				await setFirebaseUserClaims(firebaseUserCheck.uid, userClaims);
			}
		}

		// Clear login attempts and get permissions
		await removeUserFromLoginAttempt(userDetails.id);
		const permissions = await getPermissionsByUserType(userDetails.userTypeId);

		// Initialize user activity tracking for successful fresh login
		if (isFirebaseAuth && firebaseIdToken && firebaseUserCheck?.uid) {
			try {
				// Use the fresh login initializer to ensure clean session tracking
				await initializeUserActivityForFreshLogin(
					userDetails.id.toString(),
					firebaseUserCheck.uid,
					new Date() // Current time as auth_time for fresh login
				);
			} catch (activityError) {
				// Log error but don't fail login
				console.error('Error initializing user activity tracking:', activityError);
			}
		}

		// Return tokens based on auth type
		if (isFirebaseAuth && firebaseIdToken) {
			return [200, buildUserResponse(userDetails, permissions, {
				access_token: firebaseIdToken,
				refresh_token: firebaseRefreshToken,
				auth_type: AuthType.FIREBASE
			})];
		}

		// Legacy JWT tokens
		const jwtUserData = {
			user_id: userDetails.id,
			user_type_id: userDetails.userTypeId,
			user_name: userDetails.name,
			client_id: userDetails.clientId
		};

		return [200, buildUserResponse(userDetails, permissions, {
			access_token: await jwt.sign(jwtUserData, config.JWT_TOKEN_KEY, { expiresIn: config.USER_ACCESS_TOKEN_EXPIRE_TIME }),
			refresh_token: await jwt.sign(jwtUserData, config.JWT_TOKEN_KEY, { expiresIn: config.USER_REFRESH_TOKEN_EXPIRE_TIME }),
			auth_type: AuthType.LEGACY
		})];

	} catch (error) {
		notifyBugsnag(error);
		return [500, error.message];
	}
};


/**
 * Forgot password service layer.
 * API will validate the email and then send email to the user with reset password link.
 * @param  {ForgotPasswordDTO} payload
 */
export const forgotPasswordService = async (payload: ForgotPasswordDTO) => {
	try {
		let userDetails = await getUserByEmail(payload.email);

		// Return 
		if (!userDetails) {
			return [404, ErrorResponse.UserNotFound];
		}
		if (userDetails.password === null && userDetails.isVerified) {
			return [403, ErrorResponse.UserAccessRevoked];
		}

		const resetPasswordJwtToken = await jwt.sign(
			{ user_id: userDetails.id },
			config.JWT_TOKEN_KEY,
			{
				expiresIn: config.RESET_PASSWORD_LINK_EXPIRE_TIME,
			}
		)
		let resetPasswordUrl = config.PORTAL_HOST_URL + RedirectURLs.RESET_PASSWORD + "?type=forgot_password&code=" + resetPasswordJwtToken;

		if (userDetails.nationalInsuranceNumber != null) {
			resetPasswordUrl += "&is_worker=true"
		}

		await addResetPasswordToken(resetPasswordJwtToken, parseInt(userDetails.id));

		let message = {
			toEmailId: payload.email,
			templateId: config.Sendgrid.FORGOT_PASSWORD_EMAIL_TEMPLATE,
			dynamicTemplateData: {
				"reset_password_url": resetPasswordUrl,
				"user_name": userDetails.name
			},
		}

		await sendTemplateEmail(message);

		return [200, {
			ok: true
		}]
	} catch (err) {
		notifyBugsnag(err);
		if (err.error && err.error === "SENDGRID_BAD_REQUEST") {
			return [400, ErrorResponse.ForgotPasswordEmailNotSent]
		}
		return [500, err.message]
	}
};


/**
 * Reset password service layer. It will also work for setting new password after user signup.
 * API will set user provided password and verification status to true.
 * @param  {ResetPasswordDTO} payload
 */
export const resetPasswordService = async (payload: ResetPasswordDTO) => {
	try {
		let tokenData = verifyJwtToken(payload.code);

		if (!tokenData) {
			let errorResponse = await deepClone(ErrorResponse.InvalidCredentials);
			errorResponse.message = "Unauthorized code is provided.";
			return [401, errorResponse];
		}

		let queryResponse = await removeResetPasswordToken(payload.code);

		if (!queryResponse.affected) {
			return [404, ErrorResponse.InvalidResetPasswordCodeError]
		}

		// Get user details to get email for Firebase update
		let userDetails = await getUserById(tokenData.user_id);
		if (!userDetails) {
			return [404, ErrorResponse.UserNotFound];
		}

		let salt = bcrypt.genSaltSync(bcryptSaltRound);
		let encodedPassword = bcrypt.hashSync(payload.password, salt);

		// Update password in database
		let response = await updatePasswordAndVerificationStatus(tokenData.user_id, encodedPassword);

		if (!response) {
			return [404, ErrorResponse.UserNotFound];
		}

		// Update password in Firebase if enabled
		if (isFirebaseEnabled()) {
			const firebaseUpdateResult = await updateFirebaseUserPassword(userDetails.email, payload.password);

			if (!firebaseUpdateResult.success) {
				// If user doesn't exist in Firebase, try to migrate them
				if (firebaseUpdateResult.error === 'User not found in Firebase') {
					const migrationResult = await migrateUserToFirebase(userDetails.email, payload.password);

					if (!migrationResult.success) {
						console.warn(`Failed to migrate user ${userDetails.email} to Firebase during password reset:`, migrationResult.error);
						// Continue with success since database update succeeded
					}
				} else {
					console.warn(`Failed to update Firebase password for user ${userDetails.email}:`, firebaseUpdateResult.error);
					// Continue with success since database update succeeded
				}
			}
		}

		return [200, {
			ok: true
		}];
	} catch (error) {
		notifyBugsnag(error);
		return [500, error.message];
	}
};
