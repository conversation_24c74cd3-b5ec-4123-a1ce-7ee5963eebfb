import {
    AddClientDTO,
    dateTimeFormates,
    ErrorCodes,
    ErrorResponse,
    RedirectURLs,
    WeekDays,
    workerInviteEmailSubjectLine,
} from "../common";
import { UserType } from '../common'
import {
    addNewClient,
    updateExistingClient,
    getClientsById,
    getAllClients,
    getClientUsersHelper,
    addClientSiteUser,
    addClientRegionUser,
    getAllUsers,
    updateClientUserHelper,
    addResetPasswordToken,
    getRegionById,
    getClientUsersByIDHelper,
    updateRegion,
    updateUserHelper,
    removeUserSiteAssociation,
    generateUserSiteAssociation,
    getDefaultMessageTemplate,
    createMessageTemplate,
    addClientMessageUser,
    assigneAutomatedMessagesToClient,
    removeUserRegionAssociation,
    generateUserRegionAssociation,
    getUserByEmail,
    getUserById,
    getAgencyAssociationByAgencyIdAndClientId,
    updateworkerInviteEmail,
    getWorkersWithoutAppDownload,
    addNewTrainingRuleHelper,
    findRegionSiteShiftDepartmentComboExist,
    fetchTrainingRules,
    getTrainingRuleById,
    updateTrainingRuleById,
    removeTrainingRuleById,
    addFtpConfig,
    updateFtpConfig,
    getFtpConfigByClientId,
    removeFtpConfigByClientId,
    addNewStartDateYearlyRuleHelper,
    fetchStartDateYearlyRules,
    deleteStartDateYearlyRuleHelper,
    getSingleClientYearlyRule,
    updateStartDateYearlyRuleHelper,
    fetchFinancialRules,
    addNewFinancialRuleHelper,
    deleteFinancialRuleHelper,
    updateFinancialRuleHelper
} from "../models";
import { dynamicErrorObject, getDurationBetweenDates, sendTemplateEmail, sendTemplateEmailInBulk, uploadFileOnS3 } from "../utils";
import { sendDefaultMessageTemplate } from "./messages";
const _ = require("lodash");
const jwt = require("jsonwebtoken");
import { config } from "../configurations";
import { MessageActions } from "../common";
import { notifyBugsnag, getUserByUserTypeId } from "../utils";
import moment from "moment";

/**
 * Service to add client.
 */
export const addClient = async (payload: AddClientDTO) => {
    try {
        let clientDetails = await addNewClient(payload);

        if (!clientDetails) return [404, ErrorResponse.ResourceNotFound];

        await assigneAutomatedMessagesToClient(clientDetails.id);
        if (payload.tna_ftp || payload.worker_ftp) {
            await addFtpConfig(payload, clientDetails.id);
        }

        return [
            201,
            {
                ok: true,
                message: MessageActions.CREATE_CLIENT,
                client_id: parseInt(clientDetails.id),
            },
        ];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * Service to amend client details.
 */
export const updateClient = async (payload, image, loggedInUser) => {
    try {
        const isSuperAdmin = loggedInUser.user_type_id == UserType.CLEARVUE_ADMIN;

        if (!isSuperAdmin) {
            const clientMismatch = loggedInUser.client_id != payload.client_id;
            if (clientMismatch) return [403, ErrorResponse.PermissionDenied]
        }

        let clientDetialsToUpdate = {
            name: payload.name,
            city: payload.city,
            country: payload.country,
            sectorId: payload.sectorId,
            postCode: payload.postCode,
            address: {
                address_line_1: payload.address_line_1,
                address_line_2: payload.address_line_2 || '',
                address_line_3: payload.address_line_3 || ''
            },
            updatedBy: loggedInUser.user_id
        }
        if (payload.profile) {
            clientDetialsToUpdate["profile"] = payload.profile
        }

        if (payload.hasOwnProperty("workerInviteEmail")) {
            clientDetialsToUpdate["workerInviteEmail"] = payload.workerInviteEmail
        }
        if (payload.hasOwnProperty("workerPerformance")) {
            clientDetialsToUpdate["workerPerformance"] = payload.workerPerformance
        }
        if (payload.hasOwnProperty("workerTraining")) {
            clientDetialsToUpdate["workerTraining"] = payload.workerTraining
        }
        if (payload.hasOwnProperty("rateCardLookup")) {
            clientDetialsToUpdate["rateCardLookup"] = payload.rateCardLookup
        }


        let clientId = payload.client_id;
        if (payload.profile && (payload.profile === "null")) {
            delete clientDetialsToUpdate["profile"]
        } else if (image) {
            let resourceName = "COMPANY" + payload["client_id"] + image.extension;
            clientDetialsToUpdate["resource"] = config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + resourceName;
            await uploadFileOnS3(config.BUCKET_NAME, config.PROFILE_BUCKET_FOLDER, resourceName, image.mime, image.data);
        }
        let clientDetails = await updateExistingClient(clientDetialsToUpdate, clientId);
        if (!clientDetails || clientDetails.affected == 0) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        if (isSuperAdmin) {
            payload["user_id"] = loggedInUser.user_id;
            let ftpConfig = await getFtpConfigByClientId(clientId)
            // Check if FTP credentials exist
            if (ftpConfig?.credentials && (payload.tna_ftp || payload.worker_ftp)) {
                // Prepare the update payload
                let ftpUpdate = {
                    tna_ftp: payload.tna_ftp,
                    worker_ftp: payload.worker_ftp,
                    ftp_host: payload.ftp_host,
                    ftp_username: payload.ftp_username,
                    ftp_password: payload.ftp_password,
                    protocol: "FTP",
                    tna_cron_expression: payload.tna_cron_expression,
                    worker_cron_expression: payload.worker_cron_expression,
                    remote_directory: payload.remote_directory,
                    notification_email: payload.notification_email,
                    user_id: loggedInUser.user_id
                };
                await updateFtpConfig(ftpUpdate, clientId);
            } else if (!ftpConfig?.credentials && (payload.tna_ftp || payload.worker_ftp)) {
                // Add a new FTP configuration if no credentials exist
                await addFtpConfig(payload, clientId);
            } else {
                // Remove FTP configurations and credentials if neither TNA nor worker FTP is specified
                await removeFtpConfigByClientId(clientId);
            }
        }
        return [
            200,
            {
                ok: true,
                message: MessageActions.UPDATE_CLIENT
            },
        ];
    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * Service to GET all the clients.
 */
export const getAllClientDetails = async (loggedInUser, data) => {
    try {
        let clientDetails = await getAllClients(loggedInUser, data.page || 1, data.limit || 10, data.sort_by || "client_name", data.sort_type || "asc");
        if (!clientDetails) {
            return [404, ErrorResponse.ResourceNotFound];
        }
        let count = 0;
        if (clientDetails.count) {
            count = parseInt(clientDetails.count);
        }

        // Convert worker_invite_email to boolean
        const clientsWithBooleanValues = clientDetails.map(client => ({
            ...client,
            worker_invite_email: !!parseInt(client.worker_invite_email),
            worker_performance: !!parseInt(client.worker_performance),
            worker_training: !!parseInt(client.worker_training),
            rate_card_lookup: !!parseInt(client.rate_card_lookup),
            tna_ftp: client.tna_ftp,
            worker_ftp: client.worker_ftp,
        }));
        return [200, {
            ok: true,
            count: count,
            client_details: clientsWithBooleanValues
        }];
    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }

}

/**
 * Service to GET the client details by ID.
 */
export const getClientDetailsById = async (clientId, loggedInUser) => {
    try {
        if (!clientId) {
            return [400, ErrorResponse.BadRequestError];
        }
        let clientDetails = await getClientsById(clientId);
        if (!clientDetails) {
            return [404, ErrorResponse.ResourceNotFound];
        }
        let url: any;
        if (clientDetails.resource === null) {
            url = config.BUCKET_URL + "/" + config.PROFILE_BUCKET_FOLDER + "/" + config.DEFAULT_IMAGE;
        } else {
            url = clientDetails.resource;
        }

        const yearlyRules = await fetchStartDateYearlyRules(clientId);

        let obj = {
            "id": parseInt(clientDetails.id),
            "name": clientDetails.name,
            "sector_id": parseInt(clientDetails.sector_id),
            "sector_name": clientDetails.sector_name,
            "address": JSON.parse(clientDetails.address),
            "post_code": clientDetails.post_code,
            "city": clientDetails.city,
            "country": clientDetails.country,
            "created_at": clientDetails.created_at,
            "profile_url": url,
            "weekday_start": clientDetails.weekday_start,
            "booking_format": clientDetails.booking_format,
            "worker_performance": Boolean(clientDetails.worker_performance),
            "worker_training": Boolean(clientDetails.worker_training),
            "rate_card_lookup": Boolean(clientDetails.rate_card_lookup),
            "tna_ftp": Boolean(clientDetails.tna_ftp),
            "worker_ftp": Boolean(clientDetails.worker_ftp),
            "ftp_host": clientDetails.ftp_host,
            "ftp_username": clientDetails.ftp_username,
            "ftp_password": clientDetails.ftp_password,
            "tna_cron_expression": clientDetails.tna_cron_expression,
            "worker_cron_expression": clientDetails.worker_cron_expression,
            "remote_directory": clientDetails.remote_directory,
            "notification_email": clientDetails.notification_email,
            "worker_invite_email": Boolean(clientDetails.worker_invite_email),
            "yearly_rules": yearlyRules,
        }
        if ([UserType.AGENCY_SITE, UserType.AGENCY_REGIONAL].includes(parseInt(loggedInUser.user_type_id))) {
            let userDetails = await getUserById(loggedInUser.user_id);
            let association = await getAgencyAssociationByAgencyIdAndClientId(userDetails.agency_id, userDetails.client_id);

            if (association && association.total_assignment_pay !== undefined) {
                (obj as unknown as { total_assignment_pay: number }).total_assignment_pay = association.total_assignment_pay;
            }
        }

        return [200, {
            ok: true,
            client_details: obj
        }];
    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service for GET list of client users.
 */
export const getClientUsersService = async (loggedInUser) => {
    let userDetails = await getAllUsers(loggedInUser);
    const whereClause = `user.id <> :user_id AND user.clientId = :client_id AND user.user_type_id IN (:user_type_id)`;
    const whereClauseValue = { "user_id": loggedInUser.user_id, "client_id": userDetails.client_id, "user_type_id": [UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL, UserType.MESSAGE_ADMIN] }

    let clientUsersDetails = await getClientUsersHelper(whereClause, whereClauseValue);
    clientUsersDetails = _.map(clientUsersDetails, (client) => {
        client.name = client.user_type_id == UserType.CLIENT_REGIONAL ? client.region_name :
            client.user_type_id == UserType.CLIENT_SITE ? client.site_name :
                client.user_type_id == UserType.MESSAGE_ADMIN ? client.user_name : "";

        client.verbose_id = client.user_type_id == UserType.CLIENT_REGIONAL ? client.region_id :
            client.user_type_id == UserType.CLIENT_SITE ? client.site_id :
                client.user_type_id == UserType.MESSAGE_ADMIN ? client.message_admin_id : "";

        delete client.region_id;
        delete client.region_name;
        delete client.site_id;
        delete client.site_name;
        delete client.message_admin_id;
        return client;
    })
    const response = _.size(clientUsersDetails) ? clientUsersDetails : [];
    return [
        200,
        {
            ok: true,
            users: response,
        },
    ];
}

/**
 * Service for adding the client users for site and region roles.
 */
export const addClientUsersService = async (payload, loggedInUser) => {
    try {
        let id: number;
        let company_name: any;

        // check user already exists.
        let userDetails = await getUserByEmail(payload.email);
        if (userDetails) {
            const errorObj = await dynamicErrorObject(ErrorResponse.UserAlreadyExistsCustom, getUserByUserTypeId(userDetails.userTypeId))
            return [409, errorObj]
        }

        if (payload.client_role === UserType.CLIENT_SITE) {
            //helper to add the client user and update the admin id of the site.
            let clientSiteUser = await addClientSiteUser(payload, loggedInUser);
            id = parseInt(clientSiteUser.id);
            company_name = clientSiteUser.company_name;

            await sendDefaultMessageTemplate(id);   // Send default template messages for site admin
        } else if (payload.client_role == UserType.CLIENT_REGIONAL) {
            //Helper to add the client and update tha admin id of the region.
            let clientRegionUser = await addClientRegionUser(payload, loggedInUser);
            id = parseInt(clientRegionUser.id);
            company_name = clientRegionUser.company_name;
        } else if (payload.client_role === UserType.MESSAGE_ADMIN) {
            //helper to add the client user.
            let clientMessageUser = await addClientMessageUser(payload, loggedInUser);
            id = parseInt(clientMessageUser.id);
            company_name = clientMessageUser.company_name;

            await sendDefaultMessageTemplate(id);   // Send default template messages for message admin
        } else if (!payload.id) {
            return [400, ErrorResponse.BadRequestError];
        } else {
            return [422, ErrorResponse.UnprocessableEntity]
        }

        //Email Authentication setup.
        const resetPasswordJwtToken = await jwt.sign(
            { user_id: id },
            config.JWT_TOKEN_KEY,
            {
                expiresIn: config.RESET_PASSWORD_LINK_EXPIRE_TIME,
            }
        );

        await addResetPasswordToken(resetPasswordJwtToken, id);

        let message = {
            toEmailId: payload.email,
            templateId: config.Sendgrid.INVITE_USER_EMAIL_TEMPLATE,
            dynamicTemplateData: {
                sender_name: loggedInUser.user_name,
                account_name: company_name,
                invitation_link:
                    config.PORTAL_HOST_URL +
                    RedirectURLs.RESET_PASSWORD +
                    "?type=set_password&code=" +
                    resetPasswordJwtToken,
            },
        };

        await sendTemplateEmail(message);
        return [201, { ok: true, user_id: id, message: MessageActions.CREATE_CLIENT_USER }]

    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key constraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to updating the client users details.
 */
export const updateClientUserService = async (client_user_id, payload, loggedInUser) => {
    try {
        let data: any = {};
        // Updating only user details
        if (!payload.user_type_id && !payload.id) {
            data = {
                name: payload.name,
                mobile: payload.phone,
                countryCode: payload.country_code
            }
            await updateClientUserHelper(client_user_id, data);
        } else {
            // Assigning the admin to different site or different region.
            data = {
                name: payload.name,
                mobile: payload.phone,
                countryCode: payload.country_code
            }
            await updateClientUserHelper(client_user_id, data);
            // TODO: Check for current user_type of the user admin and site assigned.
            let whereClause = `user.id = :client_user_id`;
            let whereClauseValue = { "client_user_id": client_user_id }
            let userDetails = await getClientUsersByIDHelper(whereClause, whereClauseValue);
            if (!userDetails) {
                return [404, ErrorResponse.ResourceNotFound]
            }
            let { user_type_id } = userDetails;
            if (parseInt(user_type_id) == parseInt(payload.user_type_id)) {
                if (parseInt(user_type_id) == UserType.CLIENT_REGIONAL) {
                    // ToDo: Client-Region-Admin reassignment to different region.
                    // Check if the association with the requested region exists.
                    if (parseInt(userDetails.region_id) == parseInt(payload.id)) {
                        return [400, ErrorResponse.AssociationAlreadyExists]
                    }

                    // Revoke the user credentials.
                    await updateUserHelper(client_user_id, {
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    })

                    // Remove the existing User-Region Association.
                    await removeUserRegionAssociation(client_user_id, userDetails.region_id);

                    // Generate a new Association.
                    await generateUserRegionAssociation(client_user_id, parseInt(payload.id), loggedInUser.user_id)


                } else {
                    // ToDo: Client-Site-Admin reassignment to different site.
                    // Check if the association with the requested site exists.
                    if (parseInt(userDetails.site_id) == parseInt(payload.id)) {
                        return [400, ErrorResponse.AssociationAlreadyExists]
                    }
                    // Revoke the user details
                    await updateUserHelper(client_user_id, {
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    });

                    // Remove the existing User-Site Association.
                    await removeUserSiteAssociation(client_user_id, userDetails.site_id);

                    // Generate a new Association.
                    await generateUserSiteAssociation(client_user_id, parseInt(payload.id), loggedInUser.user_id)
                }
            } else {
                // Assigning region admin to site admin.
                if (parseInt(payload.user_type_id) === UserType.CLIENT_SITE) {
                    // Revoke and Update the user_type of the user to site admin from region admin.
                    await updateUserHelper(client_user_id, {
                        userTypeId: UserType.CLIENT_SITE,
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    })

                    // Remove the existing User-Region Association.
                    await removeUserRegionAssociation(client_user_id, userDetails.region_id);

                    // Generate the association.
                    await generateUserSiteAssociation(client_user_id, parseInt(payload.id), loggedInUser.user_id);
                }
                // Assigning the site admin to region admin.
                else {
                    // Revoke and Update the user_type of the user to region admin from site admin.
                    await updateUserHelper(client_user_id, {
                        userTypeId: UserType.CLIENT_REGIONAL,
                        password: null,
                        updatedBy: loggedInUser.user_id,
                        updatedAt: new Date()
                    });

                    // Remove the site association
                    await removeUserSiteAssociation(client_user_id, userDetails.site_id);

                    //Generate a new Association.
                    await generateUserRegionAssociation(client_user_id, parseInt(payload.id), loggedInUser.user_id)
                }
            }
            // Email invitation setup.

            const resetPasswordJwtToken = await jwt.sign(
                { user_id: client_user_id },
                config.JWT_TOKEN_KEY,
                {
                    expiresIn: config.RESET_PASSWORD_LINK_EXPIRE_TIME,
                }
            );
            await addResetPasswordToken(resetPasswordJwtToken, client_user_id);
            let message = {
                toEmailId: userDetails.email,
                templateId: config.Sendgrid.INVITE_USER_EMAIL_TEMPLATE,
                dynamicTemplateData: {
                    sender_name: loggedInUser.user_name,
                    account_name: userDetails.client_name,
                    invitation_link:
                        config.PORTAL_HOST_URL +
                        RedirectURLs.RESET_PASSWORD +
                        "?type=set_password&code=" +
                        resetPasswordJwtToken,
                },
            };
            await sendTemplateEmail(message);
        }
        return [200, { ok: true, message: MessageActions.UPDATE_CLIENT_USER }]

    } catch (err) {
        notifyBugsnag(err);
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound] // Return 404 if any foreign key
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * update client details.
 */
export const restrictWorkerInviteEmailService = async (id, payload, loggedInUser) => {
    let clientDetails = await getClientsById(id);
    if (!clientDetails) {
        return [404, ErrorResponse.ResourceNotFound];
    }

    if (clientDetails.worker_invite_email == payload.worker_invite_email) {
        const errorObj = await dynamicErrorObject(ErrorResponse.WorkerInviteEmailAlreadySet, payload.worker_invite_email ? `already ON` : `already OFF`)
        return [400, errorObj]
    }

    if (payload.worker_invite_email == 0 && clientDetails.worker_invite_email == 1) {
        const errorObj = await dynamicErrorObject(ErrorResponse.WorkerInviteEmailAlreadySet, "ON")
        return [400, errorObj]
    }

    if (payload.worker_invite_email == 1 && clientDetails.worker_invite_email == 0) {
        let clientData = await updateworkerInviteEmail(id, { workerInviteEmail: payload.worker_invite_email, updatedBy: loggedInUser.user_id });

        let workerDetails = await getWorkersWithoutAppDownload(id);

        // Send worker invite email
        let message = {
            templateId: config.Sendgrid.WORKER_INVITE_EMAIL_TEMPLATE,
            personalizations: []
        }

        workerDetails.forEach(async worker => {
            message.personalizations.push({
                to: { email: worker.email },
                dynamicTemplateData: {
                    subject_line: workerInviteEmailSubjectLine,
                    worker_name: worker.first_name
                },
            });
        });
        await sendTemplateEmailInBulk(message);
    }

    return [
        200,
        {
            ok: true,
            message: MessageActions.UPDATE_CLIENT
        },
    ];
};


/**
 * Service to add training rules.
 * 
 * */
export const addTrainingRuleService = async (payload) => {
    try {
        const isComboExist = await findRegionSiteShiftDepartmentComboExist(
            payload.client_id,
            payload.region_id,
            payload.site_id,
            null,
            null
        );
        if (!isComboExist.length) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const isTrainingRuleExist = await fetchTrainingRules(payload);
        if (isTrainingRuleExist.length) {
            return [409, ErrorResponse.TrainingRuleAlreadyExists];
        }

        if (payload.threshold_week > payload.evaluation_week || payload.limited_hours_threshold_week > payload.limited_hours_evaluation_week) {
            return [400, ErrorResponse.InvalidWeekNumbers];
        }

        const ruleDetails = await addNewTrainingRuleHelper(payload);

        return [
            201,
            {
                ok: true,
                message: MessageActions.CREATE_TRAINING_RULE,
                rule_id: parseInt(ruleDetails.id, 10),
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.duplicateKeyError:
                return [409, ErrorResponse.ClientAlreadyExists];
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

/**
 * Service to get training rules.
 * 
 * */
export const getTrainingRuleService = async (payload) => {
    try {

        const trainingRules = await fetchTrainingRules(payload);

        return [
            200,
            {
                ok: true,
                rules: trainingRules
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.duplicateKeyError:
                return [409, ErrorResponse.ClientAlreadyExists];
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

/**
 * Service to update training rules.
 * 
 * */
export const updateTrainingRuleService = async (payload, trainingRuleId) => {
    try {
        const isTrainingRuleExist = await fetchTrainingRules(payload);
        if (isTrainingRuleExist.length && isTrainingRuleExist[0].id != trainingRuleId) {
            return [409, ErrorResponse.TrainingRuleAlreadyExists];
        }

        if (payload.threshold_week > payload.evaluation_week || payload.limited_hours_threshold_week > payload.limited_hours_evaluation_week) {
            return [400, ErrorResponse.InvalidWeekNumbers];
        }

        const isComboExist = await findRegionSiteShiftDepartmentComboExist(
            payload.client_id,
            payload.region_id,
            payload.site_id,
            null,
            null
        );

        if (!isComboExist.length) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        await updateTrainingRuleById(trainingRuleId, payload);

        return [
            200,
            {
                ok: true,
                message: MessageActions.UPDATE_TRAINING_RULE
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.duplicateKeyError:
                return [409, ErrorResponse.ClientAlreadyExists];
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};


/**
 * Service to delete training rules.
 * 
 * */
export const deleteTrainingRuleService = async (params) => {
    try {

        const isruleExist = await getTrainingRuleById(params.trainingRuleId, params.clientId);
        if (!isruleExist.length) {
            return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
        }

        await removeTrainingRuleById(params.trainingRuleId, params.clientId);

        return [
            200,
            {
                ok: true,
                message: MessageActions.DELETE_TRAINING_RULE
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.duplicateKeyError:
                return [409, ErrorResponse.ClientAlreadyExists];
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const addStartDateYearlyRuleService = async (payload, loggedInUser) => {
    try {
        const { client_id, start_date, end_date, total_weeks } = payload;

        // Validate dates
        const [dateStatus, dateResult] = validateDateRestrictions(start_date, end_date);
        if (dateStatus !== 200) return [dateStatus, dateResult];

        const startYear = dateResult["startYear"];
        const endYear = dateResult["endYear"];

        // Validate client weekday
        const [weekdayStatus, weekdayError, clientDetails] = await validateClientWeekday(client_id, start_date);
        if (weekdayStatus !== 200) return [weekdayStatus, weekdayError];

        // Validate total weeks
        const [weeksStatus, weeksError] = validateTotalWeeks(start_date, end_date, total_weeks);
        if (weeksStatus !== 200) return [weeksStatus, weeksError];

        const yearlyRules = await fetchStartDateYearlyRules(client_id);
        if (yearlyRules[startYear]) {
            return [409, ErrorResponse.RuleOverlapsExistingRules];
        }

        // Check previous year's rule
        if (Object.keys(yearlyRules).length > 0) {
            const lastYear = Object.keys(yearlyRules).sort().reverse()[0];
            if (parseInt(lastYear) !== startYear - 1) {
                return [400, ErrorResponse.InvalidRuleOrder];
            }

            const previousYearRule = yearlyRules[lastYear];
            const [statusCode, error] = validateStartDateWithPreviousYear(start_date, previousYearRule);
            if (statusCode !== 200) return [statusCode, error];
        }

        // Create new rule
        const newRulePayload = {
            ...payload,
            finacial_year_start: startYear,
            finacial_year_end: endYear,
            user_id: loggedInUser.user_id
        };
        const ruleDetails = await addNewStartDateYearlyRuleHelper(newRulePayload);

        return [
            201,
            {
                ok: true,
                message: MessageActions.CREATE_YEARLY_RULE,
                rule_id: parseInt(ruleDetails.id, 10)
            }
        ];

    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

// Shared validation functions
const validateDateRestrictions = (startDate, endDate) => {
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // // Validate start date months
    // if (![3, 4].includes(startDateObj.getMonth() + 1)) {
    //     return [400, ErrorResponse.InvalidStartDateMonth];
    // }

    // // Validate end date months
    // if (![3, 4].includes(endDateObj.getMonth() + 1)) {
    //     return [400, ErrorResponse.InvalidEndDateMonth];
    // }

    // // startDate should be >= current date
    // if (startDateObj < new Date()) {
    //     return [400, ErrorResponse.InvalidStartDateShouldBeInFuture];
    // }

    // Validate year consistency
    const startYear = startDateObj.getFullYear();
    const endYear = endDateObj.getFullYear();
    if (endYear !== startYear + 1) {
        return [400, ErrorResponse.InvalidEndDateYear];
    }

    return [200, { startYear, endYear }];
};

const validateClientWeekday = async (clientId, startDate) => {
    const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][moment(startDate, dateTimeFormates.YYYYMMDD).day()];
    const clientDetails = await getClientsById(clientId);

    if (!clientDetails || clientDetails.weekday_start != dayName) {
        return [400, ErrorResponse.InvalidStartDate, null];
    }

    return [200, null, clientDetails];
};

const validateTotalWeeks = (startDate, endDate, totalWeeks, fixed_weeks = true) => {
    const calculatedTotalWeeks = calculateWeeksDifference(startDate, endDate);
    const totalWeeksPossibilities = [52, 53];

    // Check if calculated weeks is a whole number (no partial weeks)
    if (!Number.isInteger(calculatedTotalWeeks)) {
        return [400, ErrorResponse.InvalidEndDate];
    }

    if (calculatedTotalWeeks !== totalWeeks || (fixed_weeks && !totalWeeksPossibilities.includes(totalWeeks))) {
        return [400, ErrorResponse.InvalidTotalWeeks];
    }

    return [200, null];
};

const calculateWeeksDifference = (start_date, end_date) => {
    // Convert inputs to moment objects
    const startMoment = moment(start_date);
    const endMoment = moment(end_date);

    // Calculate total days between dates
    const totalDays = endMoment.diff(startMoment, 'days') + 1;
    // Calculate weeks
    const weeks = (totalDays / 7);

    return weeks;
}

const validateStartDateWithPreviousYear = (currentStartDate: string, previousYearRule: any): [number, any] => {
    // Convert string dates to Date objects for comparison
    const previousYearEndDate = new Date(previousYearRule.end_date);
    const expectedStartDate = new Date(previousYearEndDate);
    expectedStartDate.setDate(expectedStartDate.getDate() + 1);

    // Format dates to YYYY-MM-DD for comparison
    const formattedExpectedStart = expectedStartDate.toISOString().split('T')[0];
    const formattedCurrentStart = new Date(currentStartDate).toISOString().split('T')[0];

    if (formattedCurrentStart !== formattedExpectedStart) {
        return [400, {
            status: 400,
            ok: false,
            message: `Start date must be ${formattedExpectedStart} to maintain chronological order (previous rule ends on ${previousYearRule.end_date}).`,
            error: "INVALID_START_DATE",
            details: {
                expectedStartDate: formattedExpectedStart,
                providedStartDate: formattedCurrentStart,
                previousYearEndDate: previousYearRule.end_date
            }
        }];
    }

    return [200, null];
};


export const getStartDateYearlyRuleService = async (payload) => {
    try {
        const yearlyRules = await fetchStartDateYearlyRules(payload["client_id"], null, false);

        return [
            200,
            {
                ok: true,
                rules: yearlyRules
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const updateStartDateYearlyRuleService = async (payload, yearlyRuleId, loggedInUser) => {
    try {
        const { client_id, start_date, end_date, total_weeks, ni_percent, ni_threshold, pension_percent, pension_threshold, app_levy_percent } = payload;

        // Validate dates
        const [dateStatus, dateResult] = validateDateRestrictions(start_date, end_date);
        if (dateStatus !== 200) return [dateStatus, dateResult];

        const startYear = dateResult["startYear"];
        const endYear = dateResult["endYear"];

        // Validate client weekday
        const [weekdayStatus, weekdayError, clientDetails] = await validateClientWeekday(client_id, start_date);
        if (weekdayStatus !== 200) return [weekdayStatus, weekdayError];

        // Validate total weeks
        const [weeksStatus, weeksError] = validateTotalWeeks(start_date, end_date, total_weeks);
        if (weeksStatus !== 200) return [weeksStatus, weeksError];

        const yearlyRules = await fetchStartDateYearlyRules(client_id);
        const currentRule = Object.values(yearlyRules).find((rule: any) => rule.id.toString() === yearlyRuleId.toString());

        if (!currentRule) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        // Update operation
        const updatePayload = {
            ...payload,
            finacial_year_start: startYear,
            finacial_year_end: endYear
        };

        // Only perform delete operation if non-NI fields were changed
        let deleteResponse = await deleteStartDateYearlyRuleHelper(client_id, yearlyRuleId.toString(), false);

        await updateStartDateYearlyRuleHelper(yearlyRuleId, updatePayload, loggedInUser);

        return [
            200,
            {
                ok: true,
                message: MessageActions.UPDATE_YEARLY_RULE,
                rule_id: parseInt(yearlyRuleId, 10),
                deletedRulesCount: deleteResponse ? deleteResponse.deletedRulesCount : 0
            }
        ];

    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const deleteStartDateYearlyRuleService = async (payload) => {
    try {

        // Destructure payload
        const { clientId: client_id, startDateYearlyRuleId: id } = payload;

        const rule = await getSingleClientYearlyRule(client_id, id);
        if (!rule) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const deleteResult = await deleteStartDateYearlyRuleHelper(client_id, id, true);

        // Return success response
        return [
            200,
            {
                ok: true,
                message: MessageActions.DELETE_YEARLY_RULE,
                deletedRulesCount: deleteResult.affected
            }
        ];

    } catch (err) {

        // Handle specific error cases
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};


// Financial Year Rules Services

export const addFinancialRuleService = async (payload, loggedInUser) => {
    try {
        const { client_id, start_date, end_date, total_weeks } = payload;

        const startDateObj = new Date(start_date);
        const endDateObj = new Date(end_date);

        // startDate should be < end date
        if (startDateObj >= endDateObj) {
            return [400, ErrorResponse.InvalidDates];
        }

        // Validate client weekday
        const [weekdayStatus, weekdayError, clientDetails] = await validateClientWeekday(client_id, start_date);
        if (weekdayStatus !== 200) return [weekdayStatus, weekdayError];

        // Validate total weeks
        const [weeksStatus, weeksError] = validateTotalWeeks(start_date, end_date, total_weeks, false);
        if (weeksStatus !== 200) return [weeksStatus, weeksError];

        const financialRules = await fetchFinancialRules(client_id, "DESC");

        // Check previous year's rule
        if (financialRules.length > 0) {
            const latestYearRule = financialRules[0];
            const [statusCode, error] = validateStartDateWithPreviousYear(start_date, latestYearRule);
            if (statusCode !== 200) return [statusCode, error];
        }

        // Create new rule
        const newRulePayload = {
            ...payload,
            finacial_year_start: startDateObj.getFullYear(),
            finacial_year_end: endDateObj.getFullYear(),
            user_id: loggedInUser.user_id
        };
        const ruleDetails = await addNewFinancialRuleHelper(newRulePayload);

        return [
            201,
            {
                ok: true,
                message: MessageActions.CREATE_FINANCIAL_RULE,
                rule_id: parseInt(ruleDetails.id, 10)
            }
        ];

    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const getFinancialRuleService = async (payload) => {
    try {
        const yearlyRules = await fetchFinancialRules(payload["client_id"], "ASC");

        return [
            200,
            {
                ok: true,
                rules: yearlyRules
            },
        ];
    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const updateFinancialRuleService = async (payload, yearlyRuleId, loggedInUser) => {
    try {
        const { client_id, start_date, end_date, total_weeks, ni_percent, ni_threshold, pension_percent, pension_threshold, app_levy_percent } = payload;

        const startDateObj = new Date(start_date);
        const endDateObj = new Date(end_date);

        // startDate should be < end date
        if (startDateObj >= endDateObj) {
            return [400, ErrorResponse.InvalidDates];
        }

        const startYear = startDateObj.getFullYear();
        const endYear = endDateObj.getFullYear();

        // Validate client weekday
        const [weekdayStatus, weekdayError, clientDetails] = await validateClientWeekday(client_id, start_date);
        if (weekdayStatus !== 200) return [weekdayStatus, weekdayError];

        // Validate total weeks
        const [weeksStatus, weeksError] = validateTotalWeeks(start_date, end_date, total_weeks, false);
        if (weeksStatus !== 200) return [weeksStatus, weeksError];

        let currentRule = await fetchFinancialRules(payload["client_id"], "ASC", yearlyRuleId.toString());
        if (currentRule.length === 0) {
            return [404, ErrorResponse.ResourceNotFound];
        }
        currentRule = currentRule[0];
        // Check if only relevant fields are changed
        const areOnlyRelevantFieldsChanged =
            currentRule["start_date"] === start_date &&
            currentRule["end_date"] === end_date &&
            currentRule["total_weeks"] === total_weeks &&
            currentRule["finacial_year_start"] === startYear.toString() &&
            currentRule["finacial_year_end"] === endYear.toString() &&
            (
                currentRule["ni_percent"] !== ni_percent ||
                currentRule["ni_threshold"] !== ni_threshold ||
                currentRule["pension_percent"] !== pension_percent ||
                currentRule["pension_threshold"] !== pension_threshold ||
                currentRule["app_levy_percent"] !== app_levy_percent
            );


        // Update operation
        const updatePayload = {
            ...payload,
            finacial_year_start: startYear,
            finacial_year_end: endYear
        };

        // Only perform delete operation if non-NI fields were changed
        let deleteResponse = null;
        if (!areOnlyRelevantFieldsChanged) {
            deleteResponse = await deleteFinancialRuleHelper(client_id, yearlyRuleId, false);
        }

        await updateFinancialRuleHelper(yearlyRuleId, updatePayload, loggedInUser);

        return [
            200,
            {
                ok: true,
                message: MessageActions.UPDATE_FINANCIAL_RULE,
                rule_id: parseInt(yearlyRuleId, 10),
                deletedRulesCount: deleteResponse ? deleteResponse.deletedRulesCount : 0
            }
        ];

    } catch (err) {
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};

export const deleteFinancialRuleService = async (payload) => {
    try {

        // Destructure payload
        const { clientId: client_id, financialRuleId: id } = payload;

        let rule = await fetchFinancialRules(client_id, "ASC", id);
        if (rule.length === 0) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const deleteResult = await deleteFinancialRuleHelper(client_id, id, true);

        // Return success response
        return [
            200,
            {
                ok: true,
                message: MessageActions.DELETE_FINANCIAL_RULE,
                deletedRulesCount: deleteResult.affected
            }
        ];

    } catch (err) {

        // Handle specific error cases
        switch (err.code) {
            case ErrorCodes.dbReferenceError:
                return [404, ErrorResponse.ResourceNotFound];
            default:
                notifyBugsnag(err);
                return [500, err.message];
        }
    }
};