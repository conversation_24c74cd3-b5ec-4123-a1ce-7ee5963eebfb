/**
 * All the service layer methods for the Rate Card.
 */
const _ = require('lodash');
const { EasyPromiseAll } = require('easy-promise-all');
import { ErrorResponse, ErrorCodes } from "./../common";
import { createRateCard, getRateCardList, getRateCardById, getRateCardCount, getRateCardForDropDown, getSiteById, getAgencyAssociationByAgencyIdAndClientId, getRateCard, deleteRateCardById, addRateCardData, deleteRateCardDataById } from "./../models";
import { MessageActions, PayTypes } from "./../common";
import { deleteObject, getNewTransaction, getSignedUrlForGetObject, notifyBugsnag, splitArrayWithSize, uploadFileOnS3, convertInternalPayTypeToSupervisor } from "../utils";
import { config } from "../configurations";
const uuid = require('uuid');

/**
 * create rateCard.
 */
export const createRateCardService = async (fileContent, rateCardData, payload, loggedInUser) => {
    let rateCard;
    try {
        const clientId = loggedInUser.client_id;
        const { site_id: siteId, agency_id: agencyId, name } = payload;
        const siteToUpdate = await getSiteById(siteId);

        if (!siteToUpdate || siteToUpdate.client_id != clientId) {
            return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
        }

        const existingAssociation = await getAgencyAssociationByAgencyIdAndClientId(agencyId, clientId);
        if (!existingAssociation) {
            return [409, ErrorResponse.ResourceNotFoundWithoutAnyUserInput]
        }

        const exisitingRateCard = await getRateCard({ clientId, agencyId, siteId });
        if (exisitingRateCard.length) {
            return [409, ErrorResponse.RateCardAlreadyExists]
        }

        const filename = uuid.v4();
        const url = await uploadFileOnS3(config.BUCKET_NAME, config.RATE_CARD_FOLDER, filename, "csv", fileContent);

        const rateCardMetaData = {
            awsPath: url.location,
            filename,
            cardName: name,
            clientId,
            agencyId,
            siteId,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id
        };
        rateCard = await createRateCard(rateCardMetaData);
        rateCard = await getRateCardById(rateCard.id);

        rateCardData.forEach(async wkr => {
            Object.assign(wkr, {
                rateCardId: rateCard.id,
                payRate: wkr.pay,
                chargeRate: wkr.charge,
                performanceLow: wkr.performance_low || null,
                performanceHigh: wkr.performance_high || null,
                supervisorRate: wkr.supervisor_rate ? (wkr.supervisor_rate.toLowerCase() === "no" ? false : true) : false,
                payType: await convertInternalPayTypeToSupervisor(wkr.pay_type),
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
            });
        });

        const arrays = await splitArrayWithSize(rateCardData, 1000);
        const queryRunner = getNewTransaction();
        await queryRunner.startTransaction();

        try {
            for (const chunk of arrays) {
                await addRateCardData(chunk);
            }
            await queryRunner.commitTransaction();
        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            await queryRunner.release();
        }

        return [201, {
            ok: true,
            message: MessageActions.CREATE_RATE_CARD,
            rate_card_id: rateCard.id,
        }];
    } catch (err) {
        if (rateCard.id) {
            await deleteObject(config.BUCKET_NAME, config.RATE_CARD_FOLDER, rateCard.filename);
            await deleteRateCardDataById(rateCard.id);
            await deleteRateCardById(rateCard.id);
        }

        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * get rateCard list.
 */
export const getRateCardListService = async (clientId: number, siteId: number) => {
    const rateCardList = await getRateCardList(clientId, siteId);

    const files = await Promise.all(rateCardList.map(async item => {
        const link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.RATE_CARD_FOLDER, item.filename);
        return { ...item, resource_url: link.url };
    }));

    return [200, {
        "ok": true,
        "rate_card_list": files,
    }];
};

/**
 * Service to Delete rate-card
 */
export const rateCardDeleteService = async (rateCardId: number) => {
    try {
        const rateCard = await getRateCardById(rateCardId);

        if (!rateCard) return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];

        await deleteObject(config.BUCKET_NAME, config.RATE_CARD_FOLDER, rateCard.filename);
        await deleteRateCardDataById(rateCardId);
        await deleteRateCardById(rateCardId);

        return [200, {
            ok: true,
            message: MessageActions.DELETE_RATE_CARD,
        }];

    } catch (err) {
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};