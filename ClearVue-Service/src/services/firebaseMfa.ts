/**
 * Firebase MFA Service Layer
 * Handles Firebase native TOTP MFA operations using Firebase Admin SDK and REST API
 */
import * as admin from 'firebase-admin';
import axios from 'axios';
import { config } from '../configurations';
import { notifyBugsnag } from '../utils';
import { getFirebaseUserEmail } from './firebaseAuth';

/**
 * Check if Firebase MFA is enabled
 */
export const isMfaEnabled = (): boolean => {
    return config.FIREBASE_AUTH_ENABLED && config.FIREBASE_TOTP_MFA_ENABLED;
};

/**
 * Get Firebase Auth instance
 */
const getFirebaseAuth = (): admin.auth.Auth | null => {
    try {
        return admin.auth();
    } catch (error) {
        console.error('Firebase not initialized:', error);
        return null;
    }
};

const envMapping = {
    'staging': 'test',
    'development': 'local',
    'production': ''
};

/**
 * Start MFA enrollment for a user using Firebase REST API
 * This generates a TOTP secret and returns QR code data
 */
export const startMfaEnrollment = async (idToken: string): Promise<{
    success: boolean;
    sessionInfo?: string;
    totpUri?: string;
    secretKey?: string;
    qrCodeUrl?: string;
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: false, error: 'MFA is not enabled' };
        }

        if (!config.FIREBASE_WEB_API_KEY) {
            return { success: false, error: 'Firebase Web API key not configured' };
        }

        // Start MFA enrollment using Firebase REST API
        const enrollmentResponse = await axios.post(
            `https://identitytoolkit.googleapis.com/v2/accounts/mfaEnrollment:start?key=${config.FIREBASE_WEB_API_KEY}`,
            {
                idToken: idToken,
                totpEnrollmentInfo: {}
            }
        );

        if (enrollmentResponse.data && enrollmentResponse.data.totpSessionInfo) {
            const { totpSessionInfo } = enrollmentResponse.data;
            const { sessionInfo, sharedSecretKey } = totpSessionInfo;

            const environment = envMapping[config.ENVIRONMENT] || '';
            const userEmail = await getFirebaseUserEmail(idToken);
            // Generate QR code URL for TOTP setup
            const totpUri = `otpauth://totp/ClearVue:${environment}-${userEmail || 'user'}?secret=${sharedSecretKey}&issuer=ClearVue`;
            const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(totpUri)}`;

            return {
                success: true,
                sessionInfo,
                secretKey: sharedSecretKey,
                qrCodeUrl,
                totpUri
            };
        } else {
            return { success: false, error: 'Invalid response from Firebase MFA enrollment' };
        }
    } catch (error) {
        console.error('Error starting MFA enrollment:', error);
        notifyBugsnag(error);

        if (error.response?.data?.error?.message) {
            return { success: false, error: error.response.data.error.message };
        }

        return { success: false, error: 'Failed to start MFA enrollment' };
    }
};

/**
 * Complete MFA enrollment by verifying the TOTP code
 */
export const completeMfaEnrollment = async (
    idToken: string,
    sessionInfo: string,
    totpCode: string,
    email?: string,
    displayName: string = 'TOTP Device'
): Promise<{
    success: boolean;
    idToken?: string;
    refreshToken?: string;
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: false, error: 'MFA is not enabled' };
        }

        if (!config.FIREBASE_WEB_API_KEY) {
            return { success: false, error: 'Firebase Web API key not configured' };
        }

        // Complete MFA enrollment using Firebase REST API
        const requestBody = {
            idToken: idToken,
            displayName: displayName,
            totpVerificationInfo: {
                sessionInfo: sessionInfo,
                verificationCode: totpCode
            }
        };

        const completionResponse = await axios.post(
            `https://identitytoolkit.googleapis.com/v2/accounts/mfaEnrollment:finalize?key=${config.FIREBASE_WEB_API_KEY}`,
            requestBody
        );

        if (completionResponse.data && completionResponse.data.idToken) {
            return {
                success: true,
                idToken: completionResponse.data.idToken,
                refreshToken: completionResponse.data.refreshToken
            };
        } else {
            return { success: false, error: 'Invalid response from Firebase MFA completion' };
        }
    } catch (error) {
        console.error('Error completing MFA enrollment:', error);
        console.error('Request data sent:', {
            idToken: idToken ? 'present' : 'missing',
            sessionInfo: sessionInfo ? 'present' : 'missing',
            totpCode: totpCode ? 'present' : 'missing',
            displayName
        });

        if (error.response) {
            console.error('Firebase error response:', error.response.data);
            console.error('Firebase error status:', error.response.status);
        }

        notifyBugsnag(error);

        if (error.response?.data?.error?.message) {
            return { success: false, error: error.response.data.error.message };
        }

        return { success: false, error: 'Failed to complete MFA enrollment' };
    }
};

/**
 * Start MFA sign-in process (after email/password authentication)
 */
export const startMfaSignIn = async (mfaPendingCredential: string, mfaEnrollmentId?: string): Promise<{
    success: boolean;
    sessionInfo?: string;
    error?: string;
}> => {
    try {
        if (!mfaEnrollmentId?.trim()) {
            return { success: false, error: 'MFA enrollment ID is required and cannot be empty' };
        }

        // According to Firebase Identity Toolkit API v2 documentation, for TOTP MFA sign-in start:
        // Try with totpSignInInfo field for TOTP-specific MFA
        const requestBody = {
            mfaPendingCredential: mfaPendingCredential,
            mfaEnrollmentId: mfaEnrollmentId.trim(),
            totpSignInInfo: {}
        };

        const signInResponse = await axios.post(
            `https://identitytoolkit.googleapis.com/v2/accounts/mfaSignIn:start?key=${config.FIREBASE_WEB_API_KEY}`,
            requestBody
        );

        return {
            success: true,
            sessionInfo: signInResponse.data.sessionInfo
        };
    } catch (error) {
        console.error('Error starting MFA sign-in:', error);
        console.error('Error response:', error.response?.data);
        console.error('Request that failed:', {
            url: error.config?.url,
            method: error.config?.method,
            data: error.config?.data
        });

        if (error.response?.data?.error?.message) {
            return { success: false, error: error.response.data.error.message };
        }

        return { success: false, error: 'Failed to start MFA sign-in' };
    }
};

/**
 * Complete MFA sign-in by verifying the TOTP code
 */
export const completeMfaSignIn = async (
    mfaPendingCredential: string,
    mfaEnrollmentId: string,
    totpCode: string
): Promise<{
    success: boolean;
    idToken?: string;
    refreshToken?: string;
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: false, error: 'MFA is not enabled' };
        }

        if (!config.FIREBASE_WEB_API_KEY) {
            return { success: false, error: 'Firebase Web API key not configured' };
        }

        // Complete MFA sign-in using Firebase REST API for TOTP
        // According to Firebase documentation, for TOTP MFA finalize we need:
        const requestBody = {
            mfaPendingCredential: mfaPendingCredential,
            mfaEnrollmentId: mfaEnrollmentId,
            totpVerificationInfo: {
                verificationCode: totpCode
            }
        };

        const completionResponse = await axios.post(
            `https://identitytoolkit.googleapis.com/v2/accounts/mfaSignIn:finalize?key=${config.FIREBASE_WEB_API_KEY}`,
            requestBody
        );

        if (completionResponse.data && completionResponse.data.idToken) {
            return {
                success: true,
                idToken: completionResponse.data.idToken,
                refreshToken: completionResponse.data.refreshToken
            };
        } else {
            return { success: false, error: 'Invalid response from Firebase MFA sign-in completion' };
        }
    } catch (error) {
        console.error('Error completing MFA sign-in:', error);
        notifyBugsnag(error);

        if (error.response?.data?.error?.message) {
            return { success: false, error: error.response.data.error.message };
        }

        return { success: false, error: 'Failed to complete MFA sign-in' };
    }
};

/**
 * Get user's MFA enrollment status using Firebase Admin SDK
 */
export const getUserMfaStatus = async (uid: string): Promise<{
    success: boolean;
    mfaEnabled: boolean;
    enrolledFactors?: admin.auth.MultiFactorInfo[];
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: true, mfaEnabled: false };
        }

        if (!uid) {
            return { success: false, mfaEnabled: false, error: 'Firebase UID is required' };
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, mfaEnabled: false, error: 'Firebase not initialized' };
        }

        const userRecord = await auth.getUser(uid);
        const mfaInfo = userRecord.multiFactor;

        // Handle case where multiFactor might be undefined
        if (!mfaInfo || !mfaInfo.enrolledFactors) {
            return {
                success: true,
                mfaEnabled: false,
                enrolledFactors: []
            };
        }

        return {
            success: true,
            mfaEnabled: mfaInfo.enrolledFactors.length > 0,
            enrolledFactors: mfaInfo.enrolledFactors
        };
    } catch (error) {
        console.error('Error getting user MFA status:', error);
        notifyBugsnag(error);
        return { success: false, mfaEnabled: false, error: error.message };
    }
};

/**
 * Force logout all existing sessions when MFA is enabled
 * This is done by revoking all refresh tokens
 */
export const revokeAllUserSessions = async (uid: string): Promise<{
    success: boolean;
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: true };
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        await auth.revokeRefreshTokens(uid);
        return { success: true };
    } catch (error) {
        console.error('Error revoking user sessions:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};

/**
 * Delete all MFA enrollments for a user
 * This is used when MFA is disabled on backend but Firebase returns SECOND_FACTOR_REQUIRED
 * It removes all TOTP enrollments from the user's Firebase account
 */
export const deleteAllUserMfaEnrollments = async (uid: string): Promise<{
    success: boolean;
    error?: string;
}> => {
    try {
        if (!uid) {
            return { success: false, error: 'Firebase UID is required' };
        }

        const auth = getFirebaseAuth();
        if (!auth) {
            return { success: false, error: 'Firebase not initialized' };
        }

        // Get the user record to check current MFA enrollments
        const userRecord = await auth.getUser(uid);

        // Check if user has any MFA enrollments
        if (!userRecord.multiFactor || !userRecord.multiFactor.enrolledFactors || userRecord.multiFactor.enrolledFactors.length === 0) {
            return { success: true };
        }

        // Remove all MFA enrollments by setting enrolledFactors to null
        await auth.updateUser(uid, {
            multiFactor: {
                enrolledFactors: null
            }
        });

        return { success: true };
    } catch (error) {
        console.error('Error deleting user MFA enrollments:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};

/**
 * Delete MFA enrollment for a user by email
 * This is an admin function to reset user's MFA when they lose their device
 * It finds the user by email and removes all TOTP enrollments from their Firebase account
 */
export const deleteUserMfaEnrollmentByEmail = async (email: string): Promise<{
    success: boolean;
    message?: string;
    error?: string;
}> => {
    try {
        if (!email) {
            return { success: false, error: 'Email is required' };
        }

        // Import getUserByEmail here to avoid circular dependency
        const { getUserByEmail, updateUser } = await import('../models');

        // Get user from database by email
        const userDetails = await getUserByEmail(email);
        if (!userDetails) {
            return { success: false, error: 'User not found' };
        }

        // Check if user has Firebase UID
        if (!userDetails.firebaseUid) {
            return { success: false, error: 'User does not have Firebase UID. No MFA enrollment to delete.' };
        }

        // Delete all MFA enrollments using the existing function
        const deleteResult = await deleteAllUserMfaEnrollments(userDetails.firebaseUid);

        if (!deleteResult.success) {
            return { success: false, error: deleteResult.error };
        }

        // Update user's MFA status in database
        try {
            await updateUser(userDetails.id, {
                mfaEnabled: false,
                mfaEnrolledAt: null,
                forceMfaSetup: false
            });
        } catch (dbError) {
            console.error('Error updating user MFA status in database:', dbError);
            // Continue even if DB update fails, as Firebase cleanup was successful
        }

        return {
            success: true,
            message: `Successfully deleted MFA enrollment for user: ${email}`
        };
    } catch (error) {
        console.error('Error deleting user MFA enrollment by email:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};

/**
 * Force MFA setup for all existing users when MFA is first enabled
 * This should be called when MFA is enabled for the first time
 */
export const forceAllUsersMfaSetup = async (): Promise<{
    success: boolean;
    affectedUsers?: number;
    error?: string;
}> => {
    try {
        if (!isMfaEnabled()) {
            return { success: true, affectedUsers: 0 };
        }

        // This would need to be implemented with database access
        // For now, return success - the actual implementation would be in a separate service
        return { success: true, affectedUsers: 0 };
    } catch (error) {
        console.error('Error forcing MFA setup for all users:', error);
        notifyBugsnag(error);
        return { success: false, error: error.message };
    }
};
