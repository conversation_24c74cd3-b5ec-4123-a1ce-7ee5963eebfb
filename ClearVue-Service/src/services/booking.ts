import moment from "moment";
import { dateTimeFormates } from "../common/constants";
import { BookingStatus, ErrorCodes, ErrorResponse, MessageActions, UserType, WeekDays, } from "../common";
import { config } from "../configurations";
import {
    createBookingAssociationHelper, createBookingHelper, getAllUsers, getBookingAssociationDetails,
    getBookingByAgencyHelper, getBookingByClientHelper, getBookingById, getbookingDetailsForEmail,
    getBookingHelper, getSiteById, updateBooking, updateBookingAssociationDetails, updateBookingHelper, deleteBookingById, deleteBookingAssociationByBookingId, getAgencyList, getOpenBookingByAgencyHelper, getClientsById, getTimeAndAttendance, getTimeAndAttendanceMultiple,
    deleteBookingAssociationHistoryByBookingId
} from "../models";
import { updateBookingStatusHelper, getShiftIdsListByNames, getDepartmentsIdsListByNames, checkIncorrectCombinationsOfIdNames } from "../models";
import { sendTemplateEmail, notifyBugsnag, sendTemplateEmailInBulk, diffBetweenGivenTwoDatesInDays, formatNumber, dynamicErrorObject } from "../utils";
import { In } from 'typeorm';
import { BookingFormat } from "../common/enum";
import { getConnection } from 'typeorm';
const _ = require("lodash");

/**
 * Service to create a booking.
 */
export const createBookingService = async (payload, loggedInUser) => {
    try {

        let { client_id, name: client_user_name, email: client_user_email } = await getAllUsers(loggedInUser);

        // Check - Date range should be of 1 week
        const DayDifference = diffBetweenGivenTwoDatesInDays(payload.start_date, payload.end_date, dateTimeFormates.YYYYMMDD);
        if (DayDifference !== 6) return [400, ErrorResponse.WrongDateRange]; // 6 days difference for a week

        // Check - Weekday of the start_date should be matched with the client-specific start weekday.
        const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][moment(payload.start_date, dateTimeFormates.YYYYMMDD).day()];
        let clientDetails = await getClientsById(client_id);
        if (!clientDetails || clientDetails.weekday_start != dayName) return [400, ErrorResponse.InvalidStartDate];



        let agency_requested_heads = payload.agency_requested.map(object => object.total);

        if (agency_requested_heads.includes(0)) return [400, ErrorResponse.InvalidWorkersAssignment]

        let sum = 0;
        agency_requested_heads.forEach(x => sum += x);

        let invalid_agency_requested_totals = payload.agency_requested.filter(object => (object.requested_workers_total + object.requested_supervisors_total) !== object.total);
        if (invalid_agency_requested_totals.length) return [400, ErrorResponse.InvalidBookingTotalAgencyRequestedNotMacthing]

        if ((payload.required_workers_total + payload.required_supervisors_total) !== sum) return [400, ErrorResponse.InvalidBookingTotalRequestedNotMacthing]

        if (payload.requested_total !== sum) return [400, ErrorResponse.InvalidBookingWorkers]


        // Check - If booking format is HEADS then check for requested heads
        if (clientDetails.booking_format === BookingFormat.HEADS) {
            for (let i = 0; i < payload.agency_requested.length; i++) {
                const requestedWorkersHeads = payload.agency_requested[i].requested_workers_heads;
                const requestedSupervisorsHeads = payload.agency_requested[i].requested_supervisors_heads;
                for (let j = 1; j <= 7; j++) {
                    if (
                        !/^\d+$/.test(payload.required_workers_heads[j] || 0) ||
                        !/^\d+$/.test(payload.required_supervisors_heads[j] || 0) ||
                        !/^\d+$/.test(requestedWorkersHeads[j] || 0) ||
                        !/^\d+$/.test(requestedSupervisorsHeads[j] || 0)
                    ) {
                        return [400, ErrorResponse.InvalidRequestedHeads];
                    }
                }
            }
        }


        let dataArray = []
        let { region_id, name: site_name } = await getSiteById(payload.site_id);
        let data = {
            clientId: client_id,
            siteId: payload.site_id,
            regionId: region_id,
            shiftTypeId: payload.shift_type_id,
            departmentId: payload.department_id,
            startDate: payload.start_date,
            endDate: payload.end_date,
            requiredWorkersHeads: payload.required_workers_heads,
            requiredWorkersTotal: payload.required_workers_total,
            requiredSupervisorsHeads: payload.required_supervisors_heads,
            requiredSupervisorsTotal: payload.required_supervisors_total,
            total: payload.requested_total,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id
        }
        dataArray.push(data);
        let days = 7;

        if (payload.repeat) {
            if (payload.repeat > config.MAX_REPEAT_BOOKING_ALLOWED) {
                return [400, ErrorResponse.InvalidRepeatBookingCount]
            }
            for (let i = 1; i <= parseInt(payload.repeat); i++) {
                let dataObj = _.cloneDeep(data)
                dataObj.startDate = moment(dataObj.startDate).add(days * i, 'd').format(dateTimeFormates.YYYYMMDD);
                dataObj.endDate = moment(dataObj.endDate).add(days * i, 'd').format(dateTimeFormates.YYYYMMDD);
                dataArray.push(dataObj);
            }
        }
        let bookingDetails = await createBookingHelper(dataArray);

        let arr = [];
        let agencyID = [];
        let obj = {};

        for (let i = 0; i < payload.agency_requested.length; i++) {
            agencyID.push(payload.agency_requested[i].agency_id)
            obj = {
                agencyId: payload.agency_requested[i].agency_id,
                requestedWorkersHeads: payload.agency_requested[i].requested_workers_heads,
                requestedWorkersTotal: payload.agency_requested[i].requested_workers_total,
                requestedSupervisorsHeads: payload.agency_requested[i].requested_supervisors_heads,
                requestedSupervisorsTotal: payload.agency_requested[i].requested_supervisors_total,
                requestedTotal: payload.agency_requested[i].total,
                bookingId: bookingDetails[0],
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id
            }
            arr.push(obj);
        }

        let newArr = [];
        if (payload.repeat) {
            if (payload.repeat > config.MAX_REPEAT_BOOKING_ALLOWED) {
                return [400, ErrorResponse.InvalidRepeatBookingCount]
            }
            for (let k = 1; k <= parseInt(payload.repeat); k++) {
                for (let i = 0; i < arr.length; i++) {
                    obj = _.cloneDeep(arr[i]);
                    obj["bookingId"] = bookingDetails[k]
                    newArr.push(obj);
                }
            }
            arr = arr.concat(newArr);
        }

        let bookingAssociationDetails = await createBookingAssociationHelper(arr);
        if (!bookingAssociationDetails) {
            return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
        }

        await sendBookingEmailsToAgencisInBulk(bookingDetails, client_user_name, client_id);

        return [
            201,
            {
                ok: true,
                booking_id: _.map(bookingDetails, (id) => parseInt(id))
            },
        ];

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        }
        else if (err.error && err.error === "SENDGRID_BAD_REQUEST") {
            return [400, ErrorResponse.BookingEmailNotSent]
        }
        else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};


const sendBookingEmailsToAgencisInBulk = async (bookingDetails, client_user_name, client_id) => {

    //Fetching the data for mailing the booking details.
    let whereClause = `booking.id IN (:booking_details) and user.user_type_id IN (:user_type_id) and user.client_id = :client_id`;
    let whereClausevalue = { "booking_details": bookingDetails, "user_type_id": [UserType.AGENCY_SITE], "client_id": client_id }

    let emailSendingDetails = await getbookingDetailsForEmail(whereClause, whereClausevalue);
    let emailContent = {
        templateId: config.Sendgrid.BOOKING_NOTIFICATION_EMAIL_TEMPLATE,
        personalizations: []
    }
    for (let i = 0; i < emailSendingDetails.length; i++) {
        emailContent.personalizations.push({
            to: { email: emailSendingDetails[i].email },
            dynamicTemplateData: {
                subject_line: `ClearVue | New Shift Booking Added`,
                booking_message: `Dear ${emailSendingDetails[i].agency_name},
                ${client_user_name} has requested new booking for ${emailSendingDetails[i].requested_total} ${emailSendingDetails[i].booking_format} from (${moment(new Date(emailSendingDetails[i].start_date)).format('DD MMMM, YYYY')} to ${moment(new Date(emailSendingDetails[i].end_date)).format('DD MMMM, YYYY')}).
                This includes ${emailSendingDetails[i].requested_supervisors_total} supervisors and ${emailSendingDetails[i].requested_workers_total} workers ${emailSendingDetails[i].booking_format}.`,
                // booking_message: `${client_name[i]} has requested new booking (${startDates[i]} to ${endDates[i]}) from your agency ${agency_name[i]} on ClearVue.`,
                invitation_link: config.PORTAL_HOST_URL + "/shift-booking"
            },
        });
    };
    await sendTemplateEmailInBulk(emailContent);
}

/**
 * Service to create a booking.
 */
export const createBulkShiftBookingService = async (payload, loggedInUser) => {
    try {

        let bookingsArray = [];
        let { client_id, name: client_user_name, site_id } = await getAllUsers(loggedInUser);
        let { region_id } = await getSiteById(site_id);

        const shiftNameList = [];
        const departmentNameList = [];
        const ExcessiveRepeatValueList = [];
        const totalAgencyRequestedHeads = [];
        const datesList = [];
        const nonDecimalRequestedHeads = [];


        let clientDetails = await getClientsById(client_id);
        const isBookingFormatHeads = clientDetails.booking_format === BookingFormat.HEADS;

        payload.forEach(element => {
            shiftNameList.push(element.shift.toLowerCase().trim());
            departmentNameList.push(element.department.toLowerCase().trim());
            datesList.push({ start_date: element.start_date, end_date: element.end_date });
            totalAgencyRequestedHeads.push({ total: element.total_heads, sum: (element.sun || 0) + (element.mon || 0) + (element.tue || 0) + (element.wed || 0) + (element.thu || 0) + (element.fri || 0) + (element.sat || 0) });
            if (element.repeat > config.MAX_REPEAT_BOOKING_ALLOWED) ExcessiveRepeatValueList.push(element.repeat)
            if (isBookingFormatHeads) {
                ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'].forEach(day => {
                    if (!/^\d+$/.test(element[day] || 0)) nonDecimalRequestedHeads.push(element[day]);
                });
            }
        });


        if (nonDecimalRequestedHeads.length > 0) {
            let errorObj = { ...ErrorResponse.InvalidRequestedHeads };
            errorObj.message += `\n Invalid Requested Heads: \n ${JSON.stringify(nonDecimalRequestedHeads)}`;
            return [400, errorObj]
        }

        // Check - Weekday of the start_date should be matched with the client-specific start weekday.
        const invalidStartDates = datesList.filter(bookingRequest => {
            const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][moment(bookingRequest.start_date, dateTimeFormates.DDMMYYYY).day()];
            const isWeekDayStart = clientDetails.weekday_start == dayName;
            return !(isWeekDayStart);
        });

        if (invalidStartDates.length > 0) {
            let errorObj = { ...ErrorResponse.InvalidStartDate };
            errorObj.message += `\n Invalid date range(s): \n ${JSON.stringify(invalidStartDates)}`;
            return [400, errorObj]
        }

        // Check - Date range should be of 1 week
        const invalidDateRange = datesList.filter(bookingRequest => {
            const DayDifference = diffBetweenGivenTwoDatesInDays(bookingRequest.start_date, bookingRequest.end_date, dateTimeFormates.DDMMYYYY);
            return DayDifference !== 6;  // 6 days difference for a week
        });

        if (invalidDateRange.length > 0) {
            let errorObj = { ...ErrorResponse.WrongDateRange };
            errorObj.message += `\n Invalid date range(s): \n ${JSON.stringify(invalidDateRange)}`;
            return [400, errorObj]
        }

        // Check if total and sum match for all objects
        const totalSumMismatch = totalAgencyRequestedHeads.filter(item => item.total !== item.sum);
        if (totalSumMismatch.length > 0) {
            let errorObj = { ...ErrorResponse.InvalidBookingSumNotMacthing };
            errorObj.message += `\n ${JSON.stringify(totalSumMismatch)}`;
            return [400, errorObj]
        }

        const hasZeroTotal = totalAgencyRequestedHeads.some(item => item.total === 0);
        if (hasZeroTotal) {
            return [400, ErrorResponse.InvalidWorkersAssignmentBulk]
        }



        let { shiftNameIdMappings, notFoundShiftNames } = await getShiftIdsListByNames(shiftNameList, client_id);
        if (notFoundShiftNames.length > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.InvalidShifts, [...new Set(notFoundShiftNames)].join(', \n'))
            return [404, errorObj]
        }

        let { departmentNameIdMappings, notFoundDepartmentNames } = await getDepartmentsIdsListByNames(departmentNameList, client_id);
        if (notFoundDepartmentNames.length > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.InvalidDepartments, [...new Set(notFoundDepartmentNames)].join(', \n'))
            return [404, errorObj]
        }

        if (ExcessiveRepeatValueList.length > 0) {
            return [400, ErrorResponse.InvalidRepeatBookingCount]
        }

        let checkIncorrectAgencyCombinationsOfIdNames = await checkIncorrectCombinationsOfIdNames(loggedInUser, payload)
        if (checkIncorrectAgencyCombinationsOfIdNames.length > 0) {
            const errorObj = await dynamicErrorObject(ErrorResponse.InvalidAgencyIdNameCombo, checkIncorrectAgencyCombinationsOfIdNames.join('\n'))
            return [400, errorObj]
        }
        const defaultHeads = { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 };
        let agencyIDList = [];
        for (let bookingRequest of payload) {
            const dayWiseHeads = {
                "1": bookingRequest.sun || 0,
                "2": bookingRequest.tue || 0,
                "3": bookingRequest.mon || 0,
                "4": bookingRequest.wed || 0,
                "5": bookingRequest.thu || 0,
                "6": bookingRequest.fri || 0,
                "7": bookingRequest.sat || 0,
            };
            const isSupervisor = bookingRequest.supervisor_flag == "yes";

            let object = {
                clientId: client_id,
                siteId: site_id,
                regionId: region_id,
                shiftTypeId: shiftNameIdMappings[bookingRequest.shift.toLowerCase().trim()],
                departmentId: departmentNameIdMappings[bookingRequest.department.toLowerCase().trim()],
                startDate: moment(bookingRequest.start_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD),
                endDate: moment(bookingRequest.end_date, "DD-MM-YYYY").format(dateTimeFormates.YYYYMMDD),
                requiredWorkersHeads: isSupervisor ? { ...defaultHeads } : dayWiseHeads,
                requiredSupervisorsHeads: isSupervisor ? dayWiseHeads : { ...defaultHeads },
                requiredWorkersTotal: isSupervisor ? 0 : bookingRequest.total_heads,
                requiredSupervisorsTotal: isSupervisor ? bookingRequest.total_heads : 0,
                total: bookingRequest.total_heads,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id
            }
            bookingsArray.push(object)
            agencyIDList.push(bookingRequest.agency_id)

            const days = 7;
            if (bookingRequest.repeat) {
                for (let i = 1; i <= parseInt(bookingRequest.repeat); i++) {
                    let duplicateObj = _.cloneDeep(object)
                    duplicateObj.startDate = moment(duplicateObj.startDate).add(days * i, 'd').format(dateTimeFormates.YYYYMMDD);
                    duplicateObj.endDate = moment(duplicateObj.endDate).add(days * i, 'd').format(dateTimeFormates.YYYYMMDD);
                    bookingsArray.push(duplicateObj);
                    agencyIDList.push(bookingRequest.agency_id)
                }
            }
        }
        let bookingDetails = await createBookingHelper(bookingsArray);

        let bookingAssociationArray = [];
        for (let i = 0; i < bookingsArray.length; i++) {
            let obj = {
                agencyId: agencyIDList[i],
                requestedWorkersHeads: bookingsArray[i].requiredWorkersHeads,
                requestedSupervisorsHeads: bookingsArray[i].requiredSupervisorsHeads,
                requestedWorkersTotal: bookingsArray[i].requiredWorkersTotal,
                requestedSupervisorsTotal: bookingsArray[i].requiredSupervisorsTotal,
                requestedTotal: bookingsArray[i].total,
                bookingId: bookingDetails[i],
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id
            }
            bookingAssociationArray.push(obj);
        }

        let bookingAssociationDetails = await createBookingAssociationHelper(bookingAssociationArray);

        if (!bookingAssociationDetails) {
            return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
        }

        await sendBookingEmailsToAgencisInBulk(bookingDetails, client_user_name, client_id);


        return [
            201,
            {
                ok: true,
                message: MessageActions.BULK_BOOKING,
                booking_id: _.map(bookingDetails, (id) => parseInt(id))
            },
        ];

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        }
        else if (err.error && err.error === "SENDGRID_BAD_REQUEST") {
            return [400, ErrorResponse.BookingEmailNotSent]
        }
        else {
            notifyBugsnag(err);
            return [500, ErrorResponse.UnprocessableEntity]
        }
    }
};


/**
 * Service to get the booking list.
 */
export const getBookingService = async (data, loggedInUser) => {
    try {
        let whereClause: string;
        let whereClauseValue: any = {};
        let bookingDetails: any;
        let count: number = 0;

        if (loggedInUser.user_type_id == UserType.CLIENT_ADMIN) {
            whereClause = `bk.client_id = :client_id`;
            whereClauseValue["client_id"] = data.client_id;
            bookingDetails = await getBookingByClientHelper(whereClause, whereClauseValue, data.page || 1, data.limit || 10, data.sort_by || "bk.created_at", data.sort_type || "desc");
            count = bookingDetails["count"];
        }
        else if (loggedInUser.user_type_id == UserType.CLIENT_SITE) {
            whereClause = `bk.site_id = :site_id`;
            whereClauseValue["site_id"] = data.site_id;
            bookingDetails = await getBookingByClientHelper(whereClause, whereClauseValue, data.page || 1, data.limit || 10, data.sort_by || "bk.created_at", data.sort_type || "desc");
            count = bookingDetails["count"];
        }
        else {
            whereClause = `bk.client_id = :client_id AND ba.agency_id = :agency_id`;

            if (data.site_id) {
                whereClause = whereClause + ` AND bk.site_id = :site_id`
            } else if (data.region_id) {
                whereClause = whereClause + ` AND bk.region_id = :region_id`
            }

            whereClauseValue = { ...whereClauseValue, "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "region_id": data.region_id };
            bookingDetails = await getBookingByAgencyHelper(whereClause, whereClauseValue, data.page || 1, data.limit || 10, data.sort_by || "bk.created_at", data.sort_type || "desc");
            count = bookingDetails["count"];
            bookingDetails = _.map(bookingDetails, (booking) => {
                booking.fulfilled_total = booking.fulfilled_total > 0 ? booking.fulfilled_total : booking.fulfilled_total == null ? 0 : '';
                return booking;
            })
        }

        bookingDetails = _.map(bookingDetails, (booking) => {
            booking.verbose_status = BookingStatus[parseInt(booking.status)]
            return booking;
        })
        if (!bookingDetails) {
            return [200, {
                ok: true,
                "count": 0,
                "bookings": []
            }]
        }
        return [200, {
            ok: true,
            "count": count,
            "bookings": bookingDetails
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to get the booking details
 */
export const getBookingDetailsService = async (data, booking_id, loggedInUser) => {
    //TODO: CREATE a helper class and create the query as per the user type.
    let whereClause: string;
    let whereClauseValue = {};
    let tnaFilter = {};

    if (loggedInUser.user_type_id == UserType.CLIENT_ADMIN || loggedInUser.user_type_id == UserType.CLIENT_SITE) {
        whereClause = `booking.id = :booking_id`;
        whereClauseValue["booking_id"] = booking_id;
    }
    else {
        whereClause = `booking.clientId = :client_id AND booking_association.agencyId = :agency_id AND booking.id = :booking_id`;
        whereClauseValue = { ...whereClauseValue, "client_id": data.client_id, "agency_id": data.agency_id, "booking_id": booking_id };

        tnaFilter["agencyId"] = data.agency_id;
        tnaFilter["clientId"] = data.client_id;

        if (data.site_id) {
            whereClause = whereClause + ` AND booking.site_id = :site_id`
            whereClauseValue["site_id"] = data.site_id;
            tnaFilter["siteId"] = data.site_id;
        } else if (data.region_id) {
            whereClause = whereClause + ` AND booking.region_id = :region_id`
            whereClauseValue["region_id"] = data.region_id;
        }
    }

    let bookingDetails = await getBookingHelper(whereClause, whereClauseValue);
    if (!bookingDetails) {
        return [200, {
            ok: true,
            "booking_details": []
        }]
    }

    let response = {
        ok: true,
        "booking_details": bookingDetails
    }

    let exisitingTimeAndAttendance;
    if (tnaFilter) {
        tnaFilter["startDate"] = bookingDetails[0].start_date;
        tnaFilter["endDate"] = bookingDetails[0].end_date;

        exisitingTimeAndAttendance = await getTimeAndAttendance(tnaFilter);

        response["allow_fulfilment"] = !exisitingTimeAndAttendance ? true : false
    }

    return [200, response]
}

/**
 * Service to update the booking details.
 */
export const updateBookingDetailsService = async (id, data, loggedInUser) => {
    //TODO: CREATE a helper class and create the query as per the user type.
    try {
        //Fetching the agency id from the loggedIn user.
        let { agency_id } = await getAllUsers(loggedInUser);

        //Validation agency ID for the agency update is requested.
        let where = { agencyId: agency_id, bookingId: id }
        let associationDetails = await getBookingAssociationDetails(where);
        if (!associationDetails) {
            return [401, ErrorResponse.Unauthorized]
        }

        let booking = await getBookingById(id);
        let clientDetails = await getClientsById(booking.clientId);
        if (!clientDetails) {
            return [404, ErrorResponse.ResourceNotFound];
        }


        // Verification: Allow fulfillment only if TNA has not been uploaded for the current week; if uploaded, restrict fulfillment.
        const exisitingTimeAndAttendance = await getTimeAndAttendance({
            startDate: booking.startDate,
            endDate: booking.endDate,
            agencyId: associationDetails.agencyId,
            clientId: booking.clientId,
            siteId: booking.siteId
        });
        if (exisitingTimeAndAttendance) return [409, ErrorResponse.InvalidFulfillmentOperation];


        // Check - If booking format is HEADS then check for requested heads
        if (clientDetails.booking_format === BookingFormat.HEADS) {
            for (let j = 1; j <= 7; j++) {
                if (
                    !/^\d+$/.test(data.fulfilled_workers_heads[j] || 0) ||
                    !/^\d+$/.test(data.fulfilled_supervisors_heads[j] || 0)
                ) {
                    return [400, ErrorResponse.InvalidRequestedHeads];
                }
            }
        }

        if ((data.fulfilled_workers_total + data.fulfilled_supervisors_total) !== data.total) return [400, ErrorResponse.InvalidFulfilmentTotalAgencyRequestedNotMacthing]


        //Creating object to update.
        let obj = {
            fulfilledWorkersHeads: data.fulfilled_workers_heads,
            fulfilledSupervisorsHeads: data.fulfilled_supervisors_heads,
            fulfilledWorkersTotal: data.fulfilled_workers_total,
            fulfilledSupervisorsTotal: data.fulfilled_supervisors_total,
            fulfilledTotal: data.total,
            status: BookingStatus.FULFILLED,
            updatedBy: loggedInUser.user_id,
            updatedAt: new Date()
        }

        //Updating the status of the booking in the booking table.
        let booking_status = { status: BookingStatus.FULFILLED }
        await updateBookingStatusHelper(id, booking_status)

        //Updating the booking association data.
        await updateBookingHelper(id, agency_id, obj)
        return [200, { ok: true, message: MessageActions.UPDATE_BOOKING }]

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to cancel the booking.
 */
export const cancelBookingService = async (booking_id, data, loggedInUser) => {
    try {
        if (loggedInUser.user_type_id == UserType.AGENCY_ADMIN) {
            return [403, ErrorResponse.PermissionDenied]
        }
        let { id } = await getBookingById(booking_id)
        if (!id) {
            return [404, ErrorResponse.ResourceNotFound]
        }
        await updateBookingStatusHelper(booking_id, data);
        return [200, { ok: true, message: MessageActions.CANCEL_BOOKING }]

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to amend the booking.
 */
export const updateBookingService = async (loggedInUser, data) => {
    const queryRunner = getConnection().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
        // Fetching the association IDS from the Object
        let associationIds = _.map(data.booking_details, (object) => {
            return object.booking_association_id;
        });
        let { client_id } = await getAllUsers(loggedInUser);

        let clientDetails = await getClientsById(client_id);

        // Check - If booking format is HEADS then check for requested heads
        if (clientDetails.booking_format === BookingFormat.HEADS) {
            for (let i = 0; i < data.booking_details.length; i++) {
                const requestedWorkersHeads = data.booking_details[i].requested_workers_heads;
                const requestedSupervisorsHeads = data.booking_details[i].requested_supervisors_heads;
                for (let j = 1; j <= 7; j++) {
                    if (
                        !/^\d+$/.test(requestedWorkersHeads[j] || 0) ||
                        !/^\d+$/.test(requestedSupervisorsHeads[j] || 0)
                    ) return [400, ErrorResponse.InvalidRequestedHeads];
                }
            }
        }

        let agency_requested_totals = data.booking_details.map(object => object.requested_total);

        if (agency_requested_totals.includes(0)) return [400, ErrorResponse.InvalidWorkersAssignment];

        let sum = 0;
        agency_requested_totals.forEach(x => sum += x);

        let invalid_agency_requested_totals = data.booking_details.filter(object => (object.requested_workers_total + object.requested_supervisors_total) !== object.requested_total);
        if (invalid_agency_requested_totals.length) return [400, ErrorResponse.InvalidBookingTotalAgencyRequestedNotMacthing];

        if (data.booking_total !== sum) return [400, ErrorResponse.InvalidBookingWorkers];

        let whereClause = `booking_association.id IN (:association_ids) and user.user_type_id IN (:user_type_id) and user.client_id = :client_id`;
        let whereClauseValue = { "association_ids": associationIds, "user_type_id": [UserType.AGENCY_SITE], "client_id": client_id };
        let details = await getbookingDetailsForEmail(whereClause, whereClauseValue);

        let bookingId = details[0] ? details[0].booking_id.toString() : null;

        if (!bookingId) {
            let where = { id: In(associationIds) };
            const response = await getBookingAssociationDetails(where);
            bookingId = response.bookingId.toString();
        }

        // Data for booking association update.
        const dataUpdate = _.map(data.booking_details, (object) => {
            object["id"] = object.booking_association_id.toString();
            object["requestedWorkersHeads"] = object.requested_workers_heads;
            object["requestedSupervisorsHeads"] = object.requested_supervisors_heads;
            object["requestedWorkersTotal"] = object.requested_workers_total;
            object["requestedSupervisorsTotal"] = object.requested_supervisors_total;
            object["requestedTotal"] = object.requested_total;
            object["status"] = BookingStatus.AMENDED;
            object["updatedBy"] = loggedInUser.user_id;
            object["updatedAt"] = new Date();
            delete object.booking_association_id;
            delete object.requested_workers_heads;
            delete object.requested_supervisors_heads;
            delete object.requested_total;
            return object;
        });
        await updateBookingAssociationDetails(dataUpdate, loggedInUser, queryRunner.manager);

        let totalRequestedWorkers = 0;
        let totalRequestedSupervisors = 0;
        data.booking_details.forEach(detail => {
            totalRequestedWorkers += detail.requested_workers_total;
            totalRequestedSupervisors += detail.requested_supervisors_total;
        });

        // Fetching the booking id for the associated booking id for booking details update.
        const dataObj = {
            id: bookingId,
            requiredWorkersTotal: totalRequestedWorkers,
            requiredSupervisorsTotal: totalRequestedSupervisors,
            total: data.booking_total,
            status: BookingStatus.AMENDED,
            updatedBy: loggedInUser.user_id,
            updatedAt: new Date()
        };

        await updateBooking(dataObj, queryRunner.manager);

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Generating the data to email the details to agency.
        const emails = details.map(({ email }) => email);
        const startDates = details.map(({ start_date }) => {
            let date = moment(new Date(start_date)).format('DD MMMM, YYYY');
            return date;
        });
        const endDates = details.map(({ end_date }) => {
            let date = moment(new Date(end_date)).format('DD MMMM, YYYY');
            return date;
        });
        const agency_name = details.map(({ agency_name }) => agency_name);
        const client_name = details.map(({ client_name }) => client_name);

        for (let i = 0; i < emails.length; i++) {
            let message = {
                toEmailId: emails[i],
                templateId: config.Sendgrid.BOOKING_NOTIFICATION_EMAIL_TEMPLATE,
                dynamicTemplateData: {
                    subject_line: `ClearVue | Shift Booking Amended`,
                    booking_message: `${client_name[i]} has amended a booking (${startDates[i]} to ${endDates[i]}) for your agency ${agency_name[i]} on ClearVue.`,
                    invitation_link: config.PORTAL_HOST_URL + "/shift-booking"
                },
            };
            await sendTemplateEmail(message);
        }

        return [
            200,
            {
                ok: true,
                message: MessageActions.UPDATE_BOOKING
            },
        ];

    } catch (err) {
        await queryRunner.rollbackTransaction();
        if (err.error && err.error === "SENDGRID_BAD_REQUEST") {
            return [400, ErrorResponse.BookingAmendEmailNotSent];
        }
        notifyBugsnag(err);
        return [500, err.message];
    } finally {
        await queryRunner.release();
    }
}

/**
 * Service to get the open booking list.
 */
export const getOpenBookingService = async (data) => {
    try {
        let whereClause: string;
        let whereClauseValue: any = {};
        let bookingDetails: any;

        whereClause = `bk.client_id = :client_id AND ba.agency_id = :agency_id AND bk.start_date >= :start_date AND bk.end_date <= :end_date`;

        if (data.site_id) {
            whereClause = whereClause + ` AND bk.site_id = :site_id`
        } else if (data.region_id) {
            whereClause = whereClause + ` AND bk.region_id = :region_id`
        }

        whereClauseValue = { ...whereClauseValue, "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "region_id": data.region_id, "start_date": data.start_date, "end_date": data.end_date };
        bookingDetails = await getOpenBookingByAgencyHelper(whereClause, whereClauseValue, data.sort_by || "bk.created_at", data.sort_type || "desc");

        if (!bookingDetails.length) {
            return [404, ErrorResponse.BookingNotFound]
        }
        return [200, {
            ok: true,
            "bookings": bookingDetails
        }]
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

/**
 * Service to update bulk shift bookings list.
 */
export const updateBulkShiftBookings = async (data, loggedInUser, queryParams) => {
    try {
        let response = await getOpenBookingService(queryParams);
        if (response[0] == 404) {
            return [404, ErrorResponse.BookingNotFound]
        }

        let dataNotFound = [];
        let totalNotMatch = [];
        let weekNotMatch = [];
        let sumNotMatch = [];
        const nonDecimalRequestedHeads = [];

        let clientDetails = await getClientsById(queryParams.client_id);
        if (!clientDetails) {
            return [404, ErrorResponse.ResourceNotFound];
        }

        const isBookingFormatHeads = clientDetails.booking_format === BookingFormat.HEADS;
        let siteIds = {};

        let distributedData = {};

        data.forEach(dataObj => {
            let found = response[1].bookings.find(resObj => parseInt(resObj.id) === dataObj.id && resObj.supervisor_flag == dataObj.supervisor_flag.toLowerCase());

            if (!found) {
                dataNotFound.push({ "id": dataObj.id, "supervisor_flag": dataObj.supervisor_flag });
            } else {
                let actual_requested = parseFloat(found.requested);
                let requested_value_in_csv = parseFloat(dataObj.requested_value_in_csv);
                let total_fulfillment = parseFloat(dataObj.total_fulfillment);

                let days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'].map(day => parseFloat(dataObj[day]));
                let responseDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'].map(day => parseFloat(found[day]));

                if (requested_value_in_csv > actual_requested) {
                    totalNotMatch.push({ "id": dataObj.id, "requested": requested_value_in_csv });
                } else if (days.some((day, index) => day > responseDays[index])) {
                    weekNotMatch.push({ "id": dataObj.id, "requested": requested_value_in_csv, "weekDays": days, "total_fulfillment": total_fulfillment });
                }

                if (_.sum(days) != total_fulfillment) {
                    sumNotMatch.push({ "id": dataObj.id, "sum of weekday headcounts": _.sum(days), "total_fulfillment": total_fulfillment });
                }

                siteIds[found.site_id] = found.site;
            }

            if (isBookingFormatHeads) {
                ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'].forEach(day => {
                    if (!/^\d+$/.test(dataObj[day] || 0)) nonDecimalRequestedHeads.push(dataObj[day]);
                });
            }

            // Separate data into distributedData based on 'id' values
            /* E.X. distributedData = {
                        "1427": [{id: "1427", "supervisor_flag":"Yes"}, {id: "1427", "supervisor_flag":"No"}],
                        "1429": [{id: "1429", "supervisor_flag":"No"}],
                        "1430": [{id: "1430", "supervisor_flag":"Yes"}],
                    }
            */
            if (!distributedData[dataObj.id]) distributedData[dataObj.id] = [];
            distributedData[dataObj.id].push(dataObj);

        });

        if (_.size(nonDecimalRequestedHeads)) {
            let errorObj = { ...ErrorResponse.InvalidRequestedHeads };
            errorObj.message += `\n Invalid Heads: \n ${JSON.stringify(nonDecimalRequestedHeads)}`;
            return [400, errorObj]
        }

        if (_.size(dataNotFound)) {
            return [400, {
                ok: false,
                message: `Open Shift Booking doesn't found for : ${JSON.stringify(dataNotFound)} . Please check 'Id' or 'supervisor_flag' column or download open shift booking file.`
            }]
        }

        if (_.size(totalNotMatch)) {
            return [400, {
                ok: false,
                message: `Requested total is exceeded for : ${JSON.stringify(totalNotMatch)} . Please check requested column or download open shift booking file.`
            }]
        }

        if (_.size(weekNotMatch)) {
            return [400, {
                ok: false,
                message: `Exceeded headcount for one or more weekdays: ${JSON.stringify(weekNotMatch)}. Ensure accurate headcounts for fulfillment or download the open shift booking file.`
            }]
        }

        if (_.size(sumNotMatch)) {
            return [400, {
                ok: false,
                message: `Value of 'total_fulfillment' does not match the sum of weekday headcounts. \n Please review the 'total_fulfillment' or days values. \n\n ${JSON.stringify(sumNotMatch)} `
            }]
        }


        // Verification: Allow fulfillment only if TNA has not been uploaded for the current week; if uploaded, restrict fulfillment.
        const exisitingTimeAndAttendance = await getTimeAndAttendanceMultiple({
            startDate: queryParams.start_date,
            endDate: queryParams.end_date,
            agencyId: queryParams.agency_id,
            clientId: queryParams.client_id,
            siteId: In(Object.keys(siteIds))
        });

        if (_.size(exisitingTimeAndAttendance)) {
            const keysToFind = exisitingTimeAndAttendance.map(obj => obj.siteId)
            let errorObj = { ...ErrorResponse.InvalidFulfillmentOperation };
            errorObj.message += `\n For following sites:  \n ${JSON.stringify(keysToFind.map(key => siteIds[key]))}`;
            return [400, errorObj]
        }


        //Fetching the agency id from the loggedIn user.
        let { agency_id } = await getAllUsers(loggedInUser);

        const defaultHeads = { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 };

        // Iterate over distributedData
        for (const id in distributedData) {
            const items = distributedData[id];
            let fulfilledWorkersHeads = { ...defaultHeads };
            let fulfilledSupervisorsHeads = { ...defaultHeads };
            let fulfilledWorkersTotal = 0;
            let fulfilledSupervisorsTotal = 0;

            // Loop through each object in the array
            items.forEach(item => {
                const { sun, mon, tue, wed, thu, fri, sat, total_fulfillment, supervisor_flag } = item;
                const fulfilledHeads = {
                    1: sun,
                    2: mon,
                    3: tue,
                    4: wed,
                    5: thu,
                    6: fri,
                    7: sat
                };

                if (supervisor_flag === "yes") {
                    fulfilledSupervisorsHeads = fulfilledHeads;
                    fulfilledSupervisorsTotal += total_fulfillment;
                } else {
                    fulfilledWorkersHeads = fulfilledHeads;
                    fulfilledWorkersTotal += total_fulfillment;
                }
            });

            const bookingAssociationDataToUpdate = {
                status: BookingStatus.FULFILLED,
                updatedBy: loggedInUser.user_id,
                updatedAt: new Date(),
                fulfilledWorkersHeads,
                fulfilledWorkersTotal,
                fulfilledSupervisorsHeads,
                fulfilledSupervisorsTotal,
                fulfilledTotal: fulfilledWorkersTotal + fulfilledSupervisorsTotal
            };

            // Updating the status of the booking in the booking table
            const booking_status = { status: BookingStatus.FULFILLED };
            await updateBookingStatusHelper(id, booking_status);

            // Updating the booking association data
            await updateBookingHelper(id, agency_id, bookingAssociationDataToUpdate);
        }

        return [200, { ok: true, message: MessageActions.UPDATE_BOOKING }]

    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

export const deleteBookingAndAssociations = async (bookingId: number) => {

    let booking = await getBookingById(bookingId)

    if (!booking || !booking.id) return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
    if (parseInt(booking.status) === BookingStatus.FULFILLED) return [404, ErrorResponse.InvalidBookingDeletionRequest];

    await deleteBookingAssociationHistoryByBookingId(bookingId);
    await deleteBookingAssociationByBookingId(bookingId);

    let response = await deleteBookingById(bookingId);
    if (response.affected === 0) return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput]

    return [200, {
        ok: true,
        message: MessageActions.DELETE_SHIFT_BOOKING
    }];
};


/**
 * Service for downloading dynamic booking sample sheet.
 */
export const downloadBookingDynamicSampleSheetService = async (loggedInUser) => {

    let agencyList = await getAgencyList(loggedInUser, 1, 1000, "agency_name", "ASC");
    let csvData = [];
    if (agencyList.count) {
        agencyList.forEach(element => {
            let obj = {
                start_date: "",
                end_date: "",
                department: "",
                shift: "",
                agency_id: parseInt(element.agency_id),
                agency_name: element.agency_name,
                total_heads: 0,
                repeat: 0,
                supervisor_flag: "Yes/No",
                sun: 0,
                mon: 0,
                tue: 0,
                wed: 0,
                thu: 0,
                fri: 0,
                sat: 0,
            };
            csvData.push(obj);
        });
    };

    return [200, {
        ok: true,
        csv_data: csvData,
    }];
}

export const parseAndFormatHeads = async (agencyRequestedWorkers, bookingFormat) => {
    return Object.fromEntries(
        Object.entries(agencyRequestedWorkers).map(([key, value]) => [key, value === '' ? 0 : formatNumber(value, bookingFormat)])
    );
}

export const calculateHeadsToAllocate = async (requestedWorkersCounts, dayWiseAvailableWorkers) => {
    return Object.fromEntries(
        Object.entries(requestedWorkersCounts).map(([day, requested]) => [
            day, Math.min(Number(requested), Number(dayWiseAvailableWorkers[day]))
        ])
    );
}

export const calculateRemainingHeads = async (dayWiseAvailableWorkers, workersToGiveToAgency, bookingFormat) => {
    return Object.fromEntries(
        Object.entries(dayWiseAvailableWorkers).map(([day, available]) => [day, formatNumber(Math.max(0, Number(available) - workersToGiveToAgency[day]), bookingFormat)])
    );
}

// Function to update shift bookings with calculated fulfilment data
export const updateShiftBookingsFromTna = async (requestedBookingsForTnaFulfilment, requestedBookingsKeyMap, fulfilmentObjects, bookingFormat, agency_id, loggedInUser) => {
    // Helper function to sum values of an object
    const sumValues = (obj: object) => Object.values(obj).reduce((sum, value) => sum + Number(value), 0);
    let workersToGiveToAgency, supervisorsToGiveToAgency;
    const defaultHeads = { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 };


    for (const item of requestedBookingsForTnaFulfilment) {
        const key = `${item.shift_id}_${item.department_id}`;
        const supervisorYes = `${key}_YES`;
        const supervisorNo = `${key}_NO`;
        let workersToGiveToAgency;

        if (!fulfilmentObjects[key]) continue;

        if (requestedBookingsKeyMap[key] > 1) {
            const agencyRequestedWorkers = JSON.parse(item.requested_workers_heads);
            const dayWiseAvailableWorkers = fulfilmentObjects[key][supervisorNo];

            if (dayWiseAvailableWorkers) {
                const requestedWorkersCounts = await parseAndFormatHeads(agencyRequestedWorkers, bookingFormat);
                workersToGiveToAgency = await calculateHeadsToAllocate(requestedWorkersCounts, dayWiseAvailableWorkers);

                fulfilmentObjects[key][supervisorNo] = await calculateRemainingHeads(dayWiseAvailableWorkers, workersToGiveToAgency, bookingFormat);
            }

            const agencyRequestedSupervisors = JSON.parse(item.requested_supervisors_heads);
            const dayWiseAvailableSupervisors = fulfilmentObjects[key][supervisorYes];
            if (dayWiseAvailableSupervisors) {
                const requestedSupervisorsCounts = await parseAndFormatHeads(agencyRequestedSupervisors, bookingFormat);

                supervisorsToGiveToAgency = await calculateHeadsToAllocate(requestedSupervisorsCounts, dayWiseAvailableSupervisors);

                fulfilmentObjects[key][supervisorYes] = await calculateRemainingHeads(dayWiseAvailableSupervisors, supervisorsToGiveToAgency, bookingFormat);
            }

            requestedBookingsKeyMap[key] -= 1;
        } else {
            workersToGiveToAgency = fulfilmentObjects[key][supervisorNo];
            supervisorsToGiveToAgency = fulfilmentObjects[key][supervisorYes];
        }

        const fulfilledWorkersHeads = workersToGiveToAgency || { ...defaultHeads };
        const fulfilledWorkersTotal = formatNumber(sumValues(fulfilledWorkersHeads), bookingFormat);

        const fulfilledSupervisorsHeads = supervisorsToGiveToAgency || { ...defaultHeads }
        const fulfilledSupervisorsTotal = formatNumber(sumValues(fulfilledSupervisorsHeads), bookingFormat);

        let updateObj = {
            fulfilledWorkersHeads,
            fulfilledWorkersTotal,
            fulfilledSupervisorsHeads,
            fulfilledSupervisorsTotal,
            fulfilledTotal: formatNumber(fulfilledWorkersTotal + fulfilledSupervisorsTotal, bookingFormat),
            status: BookingStatus.FULFILLED,
            updatedBy: loggedInUser.user_id,
            updatedAt: new Date()
        };

        await updateBookingStatusHelper(item.booking_id, { status: BookingStatus.FULFILLED });
        await updateBookingHelper(item.booking_id, agency_id, updateObj);
    }
}