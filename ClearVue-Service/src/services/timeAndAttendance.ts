/**
 * All the service layer methods for the TIME AND ATTENDANCE.
 */
const { EasyPromiseAll } = require('easy-promise-all');
const _ = require('lodash')
import { BookingStatus, ErrorCodes, ErrorResponse, PayTypes, TimeAndAttendanceStatus, UserType, agency, dateTimeFormates, payroll } from "./../common";
import { getWorkersCreditDuesService } from '../services'
import {
    addTimeAndAttendanceData,
    createTimeAndAttendance,
    getTimeAndAttendanceList,
    getTimeAndAttendance,
    getTimeAndAttendanceDetail,
    getWorkerByEmployeeIdAndAgencyId,
    getTimeAndAttendanceById,
    deleteTimeAndAttendanceById,
    getTimeAndAttendanceCount,
    getTimeAndAttendanceDataCount,
    getSiteById, deleteTimeAndAttendanceDataById,
    getSitesByWorkerIds,
    getDepartmentsByNames,
    getShiftsByNames,
    getClientsById,
    getRuleBySiteId,
    getTotalAgencyPayDataHelper,
    getUserById,
    getTotalAgencyPay,
    createTotalAgencyPay,
    deleteTotalAgencyPayDataById,
    deleteTotalAgencyPayById,
    addtotalAgencyPayData,
    getTotalAgencyPayById,
    getAgencyAssociationByAgencyIdAndClientId,
    getAssociatedAgencies,
    deleteTAPDataHelper,
    updateOtherAssignment,
    getBookingsDataForTnaFulfilment,
    getTimeAndAttendanceDataForShiftFulfilment,
    collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus,
    fetchTrainingRules,
    getWorkersPerformancesData,
    getGetFirstQualifyingPerformanceNumber,
    getTrainingCostsAndHours,
    addCreditDuesData,
    deleteCreditDuesBasedOnTnaIdHelper,
    updateWorkersQualificationStatus,
    deletePayrollMetaById,
    deletePayrollByMetaId,
    deletePayrollSummaryByMetaId,
    deleteHolidayPayrollSummaryByMetaId,
    getRateCardData,
    reActivateWorkers,
    getWorkersTrainingData,
    getAllUsers,
    getFtpConfigByClientId,
    fetchFinancialRules,
    fetchStartDateYearlyRules,
    deletePayrollDetailedSummaryByMetaId,
    getAdjustmentWorkersList
} from "./../models";
let moment = require('moment');
import { MessageActions, WeekDays, RoleType } from "./../common";
import { uploadFileOnS3, deleteObject, notifyBugsnag, getNumberValue, formatNumber, getPreviousDateByWeeks, getWeekRangeForGivenDate, validateDateRangeWithYearlyRules } from './../utils';
import { config } from "../configurations";
import { addPayrollDataAsPerTimeAndAttendance, getAllSites, updateShiftBookingsFromTna } from '.';
import { logger, splitArrayWithSize, getNewTransaction, diffBetweenGivenTwoDatesInDays, getSignedUrlForGetObject } from '../utils';
import { execFile } from "child_process";
import axios from 'axios';
import { convertInternalPayTypeToSupervisor, findMatchingFinancialRule } from "../utils/helper";

const uuid = require('uuid');


/**
 * Service to add T&A.
 */
export const addTimeAndAttendance = async (fileContent, timeAndAttendanceData, payload, loggedInUser, getDateRange) => {
    let timeAndAttendance: any = {};
    try {
        let clientDetails = await getClientsById(payload.client_id);
        const dates = getDateRange ? await getWeekRangeForGivenDate(payload.date_of_upload_week.toString(), clientDetails.weekday_start) : { start_date: payload["start_date"], end_date: payload["end_date"] };
        if (getDateRange) {
            payload["start_date"] = dates.start_date;
            payload["end_date"] = dates.end_date;
        }

        const startYear = new Date(payload["start_date"]).getFullYear();
        const endYear = new Date(payload["end_date"]).getFullYear();

        // Yearly Week Number Rule Fetch
        const yearlyWeekNumberRules = await fetchStartDateYearlyRules(payload.client_id);
        const [status, error, result] = validateDateRangeWithYearlyRules(dates.start_date, dates.end_date, yearlyWeekNumberRules);
        if (status !== 200) return [status, error];
        payload["week"] = getDateRange ? result["weeks"] : payload["week"];

        // Financial Rule Fetch
        const financialRules = await fetchFinancialRules(payload.client_id, "ASC", null, startYear, endYear);
        const matchedFinancialRule = findMatchingFinancialRule(dates.start_date, dates.end_date, financialRules);
        payload["ni_percent"] = Number(matchedFinancialRule["ni_percent"] || config.NI_PERCENT);
        payload["ni_threshold"] = Number(matchedFinancialRule["ni_threshold"] || config.NI_THRESHOLD);
        payload["pension_percent"] = Number(matchedFinancialRule["pension_percent"] || config.PENSION_PERCENT);
        payload["pension_threshold"] = Number(matchedFinancialRule["pension_threshold"] || config.PENSION_THRESHOLD);
        payload["app_levy_percent"] = Number(matchedFinancialRule["app_levy_percent"] || config.APP_LEVY_PERCENT);

        // Check - Date range should be of 1 week
        let diff_in_days = diffBetweenGivenTwoDatesInDays(payload.start_date, payload.end_date)
        if (diff_in_days !== 6) {
            return [400, ErrorResponse.WrongDateRange];
        }

        // Check - Weekday of the start_date should be matched with the client-specific start weekday.
        const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(payload.start_date).getDay()];
        if (!clientDetails || clientDetails.weekday_start != dayName) {
            return [400, ErrorResponse.InvalidStartDate];
        }

        const exisitingTimeAndAttendance = await getTimeAndAttendance({
            startDate: payload.start_date,
            endDate: payload.end_date,
            agencyId: payload.agency_id,
            clientId: payload.client_id,
            siteId: payload.site_id
        });
        if (exisitingTimeAndAttendance) {
            return [409, ErrorResponse.FileAlreadyExists];
        }

        const { site, workers } = await EasyPromiseAll({
            site: getSiteById(payload.site_id),
            workers: getWorkerByEmployeeIdAndAgencyId(_.map(timeAndAttendanceData, 'employee_id').filter(x => Boolean(x)), _.map(timeAndAttendanceData, 'payroll_ref').filter(x => Boolean(x)), payload)
        });
        payload["site_cost_centre"] = site.cost_centre;
        payload["vat_code"] = config.VAT_CODE;
        payload["vat_rate"] = config.VAT_RATE;

        const workerNotFoundEmployeeId = [];
        const workerNotFoundPayrollRef = [];
        const isWorkerTrainingActive = Boolean(clientDetails.worker_training);
        const isRateCardLookupActive = Boolean(clientDetails.rate_card_lookup);
        const isWorkerPerformanceCheckActive = Boolean(clientDetails.worker_performance);
        if (isWorkerPerformanceCheckActive && !isRateCardLookupActive) {
            return [400, {
                ok: false,
                message: `Performance check can't be done if rate card lookup is not activated. Please ask the client to either activate the rate card lookup or deactivate the worker performance check to proceed with TNA.`
            }]
        }

        let rateCardForSite;
        if (isRateCardLookupActive || isWorkerPerformanceCheckActive) {
            rateCardForSite = await getRateCardData(payload.client_id, payload.site_id);
            if (!rateCardForSite.length) return [400, {
                ok: false,
                message: `Either the Rate Card Lookup or Worker Performance Check is active, but the Rate Card for the site was not found. \n Please ask the client to upload it first and try again.`
            }]
        }

        let payrollDataObject = {};
        let departmentList = [];
        let shiftList = [];
        let shiftNotFound = [];
        let departmentNotFound = [];
        let TAPValueNotFoundWithEmployee = [];
        let notAcceptHolidayEmployeeIDs = [];
        let workersWithSupervisorPaytypeAndNoSupervisorStatus = new Set();
        let workersToApplyRule = [];
        let limitedHoursWorkersToApplyRule = [];
        let nonLimitedHoursWorkersToApplyRule = [];
        let workersPerformanceNotFound = [];
        let payChargeNotMatchRateCard = [];
        let wrongRateAppliedForPerformance = []
        let workersToMakeActive = [];
        let invalidAssignmentdates = [];

        // Check for TAP value
        const association = await getAgencyAssociationByAgencyIdAndClientId(payload.agency_id, payload.client_id);
        if (association.total_assignment_pay) {
            const filteredData = workers.filter(
                (data) => data.other_assignment === 1 && data.tap_value === null
            );
            TAPValueNotFoundWithEmployee = filteredData.map((data) => data.employee_id);
        }

        if (association.holiday_activation == 0) {
            notAcceptHolidayEmployeeIDs = timeAndAttendanceData.filter(entry => entry.pay_type === PayTypes.HOLIDAY).map(entry => entry.employee_id);
        }

        // Reject CSV if found holiday pay in the sheet and client has set as not to accept holiday pay 
        if (_.size(notAcceptHolidayEmployeeIDs)) {
            return [400, {
                ok: false,
                message: `Client has set as not to accept holiday pay : \nPlease remove holiday pay.\n[ ${Array.from(_.uniq(notAcceptHolidayEmployeeIDs)).join('\n')} ] `
            }]
        }

        // Validate calculations before processing
        const validationResult = await validateTnaUserInputData(timeAndAttendanceData);
        if (validationResult) return validationResult;

        timeAndAttendanceData.forEach(element => {
            departmentList.push(element.department.trim());
            shiftList.push(element.shift.trim());
        });

        let departmentIds = await getDepartmentsByNames(departmentList, payload.client_id);
        let shiftIds = await getShiftsByNames(shiftList, payload.client_id);

        let rules = await getRuleBySiteId(payload.site_id);
        let ruleNotFoundForRoleAndPayType = [];
        let ruleNotFoundForStartDate = [];

        let trainingRuleForSite, workerPerformanceData;
        if (isWorkerTrainingActive) {
            const trainingRulesForSiteArray = await fetchTrainingRules({ client_id: payload.client_id, site_id: payload.site_id });
            trainingRuleForSite = trainingRulesForSiteArray[0] || null;
        }

        if (isWorkerPerformanceCheckActive || isWorkerTrainingActive) {
            const whereClause = `client_id = :client_id AND site_id = :site_id AND start_date = :start_date`;
            const whereClauseValue = { client_id: payload.client_id, site_id: payload.site_id, start_date: payload.start_date };
            const workerPerformanceDataArray = await getWorkersPerformancesData(whereClause, whereClauseValue);

            // Convert it to key-Value pair object
            workerPerformanceData = workerPerformanceDataArray.reduce((acc, item) => {
                acc[item.worker_id] = Number(item.performance_number);
                return acc;
            }, {});
        }

        let workersInSheet = {};
        for (let i = 0; i < timeAndAttendanceData.length; i++) {
            let worker = null;
            if (timeAndAttendanceData[i].employee_id) {
                worker = _.find(workers, { employee_id: timeAndAttendanceData[i].employee_id, agency_id: payload.agency_id });
            } else if (timeAndAttendanceData[i].payroll_ref && !worker) {
                worker = _.find(workers, { payroll_ref: timeAndAttendanceData[i].payroll_ref, agency_id: payload.agency_id });
            }

            if (worker) {
                timeAndAttendanceData[i].workerId = worker.id
                if (!workersInSheet[worker.id]) {
                    workersInSheet[worker.id] = worker;
                }
                if (worker.is_active == '0' && (worker.in_actived_at == null || worker.in_actived_at <= payload.end_date)) {
                    workersToMakeActive.push(worker.id);
                }
                if (worker.assignment_date > payload.end_date) {
                    invalidAssignmentdates.push(worker.employee_id)
                }

                // Check if performance exists for worker
                if ((isWorkerPerformanceCheckActive || isWorkerTrainingActive) && !((worker.id in workerPerformanceData) && workerPerformanceData[worker.id] >= 0)) {
                    workersPerformanceNotFound.push(worker.employee_id);
                }

            } else {
                timeAndAttendanceData[i].employee_id && workerNotFoundEmployeeId.push(timeAndAttendanceData[i].employee_id);
                timeAndAttendanceData[i].payroll_ref && workerNotFoundPayrollRef.push(timeAndAttendanceData[i].payroll_ref);
                !timeAndAttendanceData[i].payroll_ref && !timeAndAttendanceData[i].employee_id && workerNotFoundEmployeeId.push("''");
                !timeAndAttendanceData[i].payroll_ref && !timeAndAttendanceData[i].employee_id && workerNotFoundPayrollRef.push("''");
            }


        }

        if (_.size(workerNotFoundEmployeeId) || _.size(workerNotFoundPayrollRef)) {
            return [400, {
                ok: false,
                message: `worker id does not match for employee id(s): (${Array.from(_.uniq(workerNotFoundEmployeeId)).join(', ')}) or payroll ref(s): (${Array.from(_.uniq(workerNotFoundPayrollRef)).join(', ')})`
            }]
        }

        if (_.size(invalidAssignmentdates)) {
            return [400, {
                ok: false,
                message: `The following employee(s) in the TNA sheet have an assignment_date later than the week range you're uploading TNA data for: \n\n [ ${[...invalidAssignmentdates]} ] \n\n
                Please update the assignment_date for these workers to upload the TNA for them.`
            }]
        }

        // Worker performance for performance check
        if (_.size(workersPerformanceNotFound)) {
            return [400, {
                ok: false,
                message: `Worker performance data are missing for the following employee(s) of the selected week and site: \n[ ${Array.from(_.uniq(workersPerformanceNotFound)).join(', ')} ] `
            }]
        }

        for (let i = 0; i < timeAndAttendanceData.length; i++) {
            let worker = workersInSheet[timeAndAttendanceData[i].workerId];

            let shift = _.find(shiftIds, { 'name': timeAndAttendanceData[i].shift.toLowerCase() });
            if (shift) timeAndAttendanceData[i].shiftId = shift.id;
            else shiftNotFound.push(timeAndAttendanceData[i].shift);

            let department = _.find(departmentIds, { 'name': timeAndAttendanceData[i].department.toLowerCase() });
            if (department) timeAndAttendanceData[i].departmentId = department.id
            else departmentNotFound.push(timeAndAttendanceData[i].department);

            timeAndAttendanceData[i].pay_type = await convertInternalPayTypeToSupervisor(timeAndAttendanceData[i].pay_type);

            const isSupervisorPaytype = [PayTypes.SUPERVISOR_STANDARD, PayTypes.SUPERVISOR_OVERTIME, PayTypes.SUPERVISOR_PERMANENT].includes(timeAndAttendanceData[i].pay_type);
            const isSupervisor = Boolean(worker.supervisor_status);

            const isExpnesesPaytype = [PayTypes.EXPENSES].includes(timeAndAttendanceData[i].pay_type);
            const isPaytypeToExcludeHours = [PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES].includes(timeAndAttendanceData[i].pay_type);
            const isAdjustment = timeAndAttendanceData[i].adjustment.toLowerCase() === "yes";
            const isPayCorrection = timeAndAttendanceData[i].pay_correction.toLowerCase() === "yes";

            // Prepare object to process the payroll data
            if (payrollDataObject[worker.id]) {

                if (!isPaytypeToExcludeHours) payrollDataObject[worker.id].total_hour += getNumberValue(timeAndAttendanceData[i].week_hours);
                if (!isExpnesesPaytype) {
                    payrollDataObject[worker.id].pay_rate += getNumberValue(timeAndAttendanceData[i].pay_rate);
                    payrollDataObject[worker.id].charge_rate += getNumberValue(timeAndAttendanceData[i].charge_rate);
                    payrollDataObject[worker.id].total_pay += getNumberValue(timeAndAttendanceData[i].standard_pay) + getNumberValue(timeAndAttendanceData[i].overtime_pay);
                    payrollDataObject[worker.id].total_charge += getNumberValue(timeAndAttendanceData[i].total_charges);

                    if (isSupervisor) {
                        if (isSupervisorPaytype) {
                            payrollDataObject[worker.id].supervisor_hours += getNumberValue(timeAndAttendanceData[i].week_hours);
                            payrollDataObject[worker.id].supervisor_charges += getNumberValue(timeAndAttendanceData[i].total_charges);
                        } else {
                            payrollDataObject[worker.id].non_supervisor_hours += getNumberValue(timeAndAttendanceData[i].week_hours);
                            payrollDataObject[worker.id].non_supervisor_charges += getNumberValue(timeAndAttendanceData[i].total_charges);
                        }
                    }
                }

                if ([PayTypes.HOLIDAY].includes(timeAndAttendanceData[i].pay_type)) {
                    payrollDataObject[worker.id].worker_got_holiday_pay_type = 1;
                }

                payrollDataObject[worker.id].is_worker_with_adjustment = payrollDataObject[worker.id].is_worker_with_adjustment || isAdjustment;

                if (timeAndAttendanceData[i].pay_type === PayTypes.STANDARD) {
                    payrollDataObject[worker.id].standard_hours = payrollDataObject[worker.id].standard_hours ?
                        (payrollDataObject[worker.id].standard_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.BH) {
                    payrollDataObject[worker.id].bh_hours = payrollDataObject[worker.id].bh_hours ?
                        (payrollDataObject[worker.id].bh_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.SUSPENSION) {
                    payrollDataObject[worker.id].suspension_hours = payrollDataObject[worker.id].suspension_hours ?
                        (payrollDataObject[worker.id].suspension_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.SUPERVISOR_STANDARD) {
                    payrollDataObject[worker.id].supervisor_standard_hours = payrollDataObject[worker.id].supervisor_standard_hours ?
                        (payrollDataObject[worker.id].supervisor_standard_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.SUPERVISOR_OVERTIME) {
                    payrollDataObject[worker.id].supervisor_overtime_hours = payrollDataObject[worker.id].supervisor_overtime_hours ?
                        (payrollDataObject[worker.id].supervisor_overtime_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.SUPERVISOR_PERMANENT) {
                    payrollDataObject[worker.id].supervisor_permanent_hours = payrollDataObject[worker.id].supervisor_permanent_hours ?
                        (payrollDataObject[worker.id].supervisor_permanent_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.NSP || timeAndAttendanceData[i].pay_type === PayTypes.SP) {
                    payrollDataObject[worker.id].nsp_hours = payrollDataObject[worker.id].nsp_hours ?
                        (payrollDataObject[worker.id].nsp_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.INDUCTION_TRAINING) {
                    payrollDataObject[worker.id].induction_training_hours = payrollDataObject[worker.id].induction_training_hours ?
                        (payrollDataObject[worker.id].induction_training_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.TRAINING) {
                    payrollDataObject[worker.id].training_hours = payrollDataObject[worker.id].training_hours ?
                        (payrollDataObject[worker.id].training_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.STANDARD_BONUS) {
                    payrollDataObject[worker.id].standard_bonus_hours = payrollDataObject[worker.id].standard_bonus_hours ?
                        (payrollDataObject[worker.id].standard_bonus_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.SPECIAL_BONUS) {
                    payrollDataObject[worker.id].special_bonus_hours = payrollDataObject[worker.id].special_bonus_hours ?
                        (payrollDataObject[worker.id].special_bonus_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.OVERTIME) {
                    payrollDataObject[worker.id].overtime_hours = payrollDataObject[worker.id].overtime_hours ?
                        (payrollDataObject[worker.id].overtime_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.WEEKEND) {
                    payrollDataObject[worker.id].weekend_hours = payrollDataObject[worker.id].weekend_hours ?
                        (payrollDataObject[worker.id].weekend_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.EXPENSES) {
                    payrollDataObject[worker.id].expenses_hours = payrollDataObject[worker.id].expenses_hours ?
                        (payrollDataObject[worker.id].expenses_hours + getNumberValue(timeAndAttendanceData[i].week_hours)) :
                        getNumberValue(timeAndAttendanceData[i].week_hours);
                }

                payrollDataObject[worker.id].worker_paytypes_list.push({
                    worker_role_type: worker.type,
                    worker_accrual_value: worker.accrual_value,
                    worker_pay_type: timeAndAttendanceData[i].pay_type == PayTypes.NSP ? PayTypes.SP : timeAndAttendanceData[i].pay_type,
                    pay: getNumberValue(timeAndAttendanceData[i].standard_pay) + getNumberValue(timeAndAttendanceData[i].overtime_pay),
                    charge: getNumberValue(timeAndAttendanceData[i].total_charges),
                    shift_id: timeAndAttendanceData[i].shiftId,
                    department_id: timeAndAttendanceData[i].departmentId,
                    department_cost_centre: department ? department.cost_centre : null,
                    week_hours: timeAndAttendanceData[i].week_hours,
                    is_paytype_to_exclude_hours: isPaytypeToExcludeHours,
                    adjustment: isAdjustment,
                    pay_correction: isPayCorrection,
                });
            } else {

                payrollDataObject[worker.id] = {
                    worker_id: worker.id,
                    limited_hours: worker.limited_hours,
                    worker_employee_id: timeAndAttendanceData[i].employee_id && timeAndAttendanceData[i].employee_id,
                    worker_payroll_ref: timeAndAttendanceData[i].payroll_ref && timeAndAttendanceData[i].payroll_ref,
                    worker_dob: worker.dob,
                    worker_assignment_date: worker.assignment_date,
                    worker_start_date: worker.start_date,
                    pension_opt_out: worker.pension_opt_out,
                    worker_transport: worker.transport,
                    worker_other_assignment: worker.other_assignment,
                    worker_tap_value: worker.tap_value,
                    worker_role_type: worker.type,
                    is_supervisor: isSupervisor,
                    worker_got_holiday_pay_type: [PayTypes.HOLIDAY].includes(timeAndAttendanceData[i].pay_type) ? 1 : 0,
                    total_hour: (!isPaytypeToExcludeHours) ? getNumberValue(timeAndAttendanceData[i].week_hours) : 0,
                    supervisor_hours: (isSupervisor && isSupervisorPaytype) ? getNumberValue(timeAndAttendanceData[i].week_hours) : 0,
                    non_supervisor_hours: (isSupervisor && !isSupervisorPaytype && !isExpnesesPaytype) ? getNumberValue(timeAndAttendanceData[i].week_hours) : 0,
                    total_charge: (!isExpnesesPaytype) ? getNumberValue(timeAndAttendanceData[i].total_charges) : 0,
                    supervisor_charges: (isSupervisor && isSupervisorPaytype) ? getNumberValue(timeAndAttendanceData[i].total_charges) : 0,
                    non_supervisor_charges: (isSupervisor && !isSupervisorPaytype) ? getNumberValue(timeAndAttendanceData[i].total_charges) : 0,
                    pay_rate: (!isExpnesesPaytype) ? getNumberValue(timeAndAttendanceData[i].pay_rate) : 0,
                    charge_rate: (!isExpnesesPaytype) ? getNumberValue(timeAndAttendanceData[i].charge_rate) : 0,
                    total_pay: (!isExpnesesPaytype) ? (getNumberValue(timeAndAttendanceData[i].standard_pay) + getNumberValue(timeAndAttendanceData[i].overtime_pay)) : 0,
                    is_worker_with_adjustment: isAdjustment,
                    worker_paytypes_list: [{
                        worker_role_type: worker.type,
                        worker_accrual_value: worker.accrual_value,
                        worker_pay_type: timeAndAttendanceData[i].pay_type == PayTypes.NSP ? PayTypes.SP : timeAndAttendanceData[i].pay_type,
                        pay: getNumberValue(timeAndAttendanceData[i].standard_pay) + getNumberValue(timeAndAttendanceData[i].overtime_pay),
                        charge: getNumberValue(timeAndAttendanceData[i].total_charges),
                        shift_id: timeAndAttendanceData[i].shiftId,
                        department_id: timeAndAttendanceData[i].departmentId,
                        department_cost_centre: department ? department.cost_centre : null,
                        week_hours: timeAndAttendanceData[i].week_hours,
                        is_paytype_to_exclude_hours: isPaytypeToExcludeHours,
                        adjustment: isAdjustment,
                        pay_correction: isPayCorrection,
                    }]
                }

                if (timeAndAttendanceData[i].pay_type == PayTypes.STANDARD) {
                    payrollDataObject[worker.id].standard_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.BH) {
                    payrollDataObject[worker.id].bh_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.SUSPENSION) {
                    payrollDataObject[worker.id].suspension_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.SUPERVISOR_STANDARD) {
                    payrollDataObject[worker.id].supervisor_standard_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.SUPERVISOR_OVERTIME) {
                    payrollDataObject[worker.id].supervisor_overtime_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.SUPERVISOR_PERMANENT) {
                    payrollDataObject[worker.id].supervisor_permanent_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                    await collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus(isSupervisor, timeAndAttendanceData[i].employee_id, workersWithSupervisorPaytypeAndNoSupervisorStatus)
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.NSP || timeAndAttendanceData[i].pay_type === PayTypes.SP) {
                    payrollDataObject[worker.id].nsp_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.INDUCTION_TRAINING) {
                    payrollDataObject[worker.id].induction_training_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.TRAINING) {
                    payrollDataObject[worker.id].training_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type === PayTypes.STANDARD_BONUS) {
                    payrollDataObject[worker.id].standard_bonus_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.SPECIAL_BONUS) {
                    payrollDataObject[worker.id].special_bonus_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.OVERTIME) {
                    payrollDataObject[worker.id].overtime_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.WEEKEND) {
                    payrollDataObject[worker.id].weekend_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                } else if (timeAndAttendanceData[i].pay_type == PayTypes.EXPENSES) {
                    payrollDataObject[worker.id].expenses_hours = getNumberValue(timeAndAttendanceData[i].week_hours);
                }
            }

            let pay_type = timeAndAttendanceData[i].pay_type == PayTypes.NSP ? PayTypes.SP : timeAndAttendanceData[i].pay_type;
            if (timeAndAttendanceData[i].pay_type != PayTypes.HOLIDAY && !isExpnesesPaytype) {
                const foundRule = rules.find(rule => rule.pay_type == pay_type && rule.role_type == worker.type);
                if (foundRule) {
                    if (new Date(payload.start_date) < new Date(foundRule.start_tax_year)) {
                        ruleNotFoundForStartDate.push(`${RoleType[Number(worker.type)]} - ${timeAndAttendanceData[i].pay_type}`);
                    }
                } else {
                    ruleNotFoundForRoleAndPayType.push(`${RoleType[Number(worker.type)]} - ${timeAndAttendanceData[i].pay_type}`)
                }
            }

            // at right position
            if (isRateCardLookupActive && !isExpnesesPaytype) {
                const matchedRateCard = rateCardForSite.find(rateCard =>
                    Number(rateCard.pay_rate) === Number(timeAndAttendanceData[i].pay_rate) &&
                    Number(rateCard.charge_rate) === Number(timeAndAttendanceData[i].charge_rate) &&
                    rateCard.pay_type.toLowerCase() === timeAndAttendanceData[i].pay_type.toLowerCase()
                );

                if (!matchedRateCard) {
                    payChargeNotMatchRateCard.push(i + 2);
                } else if (isWorkerPerformanceCheckActive && matchedRateCard.performance_low != null && matchedRateCard.performance_high != null && matchedRateCard.performance_low >= 0 && matchedRateCard.performance_high >= 0) {
                    const { performance_low, performance_high } = matchedRateCard;
                    if (!(Number(performance_low) <= workerPerformanceData[worker.id] && workerPerformanceData[worker.id] <= Number(performance_high))) {
                        wrongRateAppliedForPerformance.push(i + 2);
                    }
                }
            };

            timeAndAttendanceData[i].payrollRef = timeAndAttendanceData[i].payroll_ref;
            timeAndAttendanceData[i].weeklyHours = timeAndAttendanceData[i].week_hours;
            timeAndAttendanceData[i].payType = timeAndAttendanceData[i].pay_type;
            timeAndAttendanceData[i].payRate = timeAndAttendanceData[i].pay_rate;
            timeAndAttendanceData[i].totalCharge = timeAndAttendanceData[i].total_charges;
            timeAndAttendanceData[i].standardCharge = timeAndAttendanceData[i].standard_charges;
            timeAndAttendanceData[i].overtimeCharge = timeAndAttendanceData[i].overtime_charges;
            timeAndAttendanceData[i].chargeRate = timeAndAttendanceData[i].charge_rate;
            timeAndAttendanceData[i].standardPay = timeAndAttendanceData[i].standard_pay;
            timeAndAttendanceData[i].overtimePay = timeAndAttendanceData[i].overtime_pay;
            timeAndAttendanceData[i].day_1 = timeAndAttendanceData[i].sun;
            timeAndAttendanceData[i].day_2 = timeAndAttendanceData[i].mon;
            timeAndAttendanceData[i].day_3 = timeAndAttendanceData[i].tue;
            timeAndAttendanceData[i].day_4 = timeAndAttendanceData[i].wed;
            timeAndAttendanceData[i].day_5 = timeAndAttendanceData[i].thu;
            timeAndAttendanceData[i].day_6 = timeAndAttendanceData[i].fri;
            timeAndAttendanceData[i].day_7 = timeAndAttendanceData[i].sat;
            timeAndAttendanceData[i].clientId = payload.client_id;
            timeAndAttendanceData[i].agencyId = payload.agency_id;
            timeAndAttendanceData[i].siteId = payload.site_id;
            timeAndAttendanceData[i].regionId = site.region_id;
            timeAndAttendanceData[i].createdBy = loggedInUser.user_id;
            timeAndAttendanceData[i].updatedBy = loggedInUser.user_id;
            timeAndAttendanceData[i].week = payload.week;
            timeAndAttendanceData[i].startDate = payload.start_date;
            timeAndAttendanceData[i].endDate = payload.end_date;
            timeAndAttendanceData[i].adjustment = isAdjustment;
            timeAndAttendanceData[i].payCorrection = isPayCorrection;
        }

        if (_.size(shiftNotFound)) {
            return [400, {
                ok: false,
                message: `Shift does not exist with name(s): ${shiftNotFound} `
            }]
        }


        if (_.size(departmentNotFound)) {
            return [400, {
                ok: false,
                message: `Department does not exist with name(s): ${departmentNotFound} `
            }]
        }

        // Reject CSV if rule not matched with RoleType & PayType
        if (_.size(ruleNotFoundForRoleAndPayType)) {
            return [400, {
                ok: false,
                message: `Rule not found for roles: \nPlease add rules from site set up.\n[ ${Array.from(_.uniq(ruleNotFoundForRoleAndPayType)).join('\n')} ] `
            }]
        }

        // Reject CSV if payroll start_date fall outside start tax year date
        if (_.size(ruleNotFoundForStartDate)) {
            return [400, {
                ok: false,
                message: `The WTR Rule needs to be amended to cover the week Start Date. \nPlease update the tax year date for following roles. \n[ ${Array.from(_.uniq(ruleNotFoundForStartDate)).join('\n')} ].  `
            }]
        }

        // check worker with zero hours 
        let zeroHourWorkersEmployeeId = [];
        let zeroHourWorkersPayrollRef = [];
        Object.entries(payrollDataObject).filter(e => {
            if (!e[1]["is_worker_with_adjustment"] && e[1]['total_hour'] == 0 && (e[1]['pay_rate'] != 0 || e[1]['charge_rate'] != 0) && e[1]['worker_paytypes_list']) {
                let payTypeExists = e[1]['worker_paytypes_list'].some(
                    payTypeObj => ![PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES].includes(payTypeObj.worker_pay_type)
                );

                if (payTypeExists) {
                    e[1]['worker_employee_id'] && zeroHourWorkersEmployeeId.push(e[1]['worker_employee_id']);
                    e[1]['worker_payroll_ref'] && zeroHourWorkersPayrollRef.push(e[1]['worker_payroll_ref']);
                }
            }
        })


        if (_.size(zeroHourWorkersEmployeeId) || _.size(zeroHourWorkersPayrollRef)) {
            logger.info(`Worker has zero weekly hours for employee id(s): (${zeroHourWorkersEmployeeId}) or payroll ref(s): (${zeroHourWorkersPayrollRef})`);

            return [400, {
                ok: false,
                message: `Worker has zero weekly hours for employee id(s): (${zeroHourWorkersEmployeeId}) or payroll ref(s): (${zeroHourWorkersPayrollRef})`
            }]
        }



        // Reject T&A sheet for holiday pay value exceeds the holiday accrual value
        const holidayExceedsEmp = [];
        if (association.holiday_activation == 1) {
            for (const key in payrollDataObject) {
                const worker = payrollDataObject[key];
                for (const payType of worker.worker_paytypes_list) {
                    if (payType.worker_pay_type === PayTypes.HOLIDAY) {
                        const accrualValue = parseFloat(payType.worker_accrual_value);
                        const pay = parseFloat(payType.pay);
                        if (pay > accrualValue) {
                            holidayExceedsEmp.push({
                                employee_id: worker.worker_employee_id,
                                worker_accrual_value: accrualValue,
                                holiday_pay: pay
                            });
                        }
                    }
                }
            }
        }

        if (_.size(holidayExceedsEmp)) {
            // Extracting information from objects and converting them to strings
            const employeesWithHolidayExceedance = holidayExceedsEmp.map(employee => {
                return `Employee ID: ${employee.employee_id}, Holiday Pay: ${employee.holiday_pay}, Accrual Value: ${employee.worker_accrual_value}`;
            });

            return [400, {
                ok: false,
                message: `The holiday pay value exceeds the holiday accrual value for the following employees:\n[ ${employeesWithHolidayExceedance.join('\n')} ]`
            }]
        }

        if (_.size(workersWithSupervisorPaytypeAndNoSupervisorStatus)) {
            return [400, {
                ok: false,
                message: `The following employee(s) have an 'Supervisor' pay type in the TNA sheet but they are not a supervisor.  \n\n [ ${[...workersWithSupervisorPaytypeAndNoSupervisorStatus]} ] \n\n
                Please update their supervisor status or update the sheet and try again.`
            }]
        }

        if (_.size(payChargeNotMatchRateCard)) {
            return [400, {
                ok: false,
                message: `The following rows in your uploaded T&A sheet do not match the pay_rate, charge_rate, pay_type, and supervisor_rate combination as per the Rate Card: \n\n [ ${[...payChargeNotMatchRateCard]} ] \n\n
                Please update the Time and Attendance sheet to ensure the combinations are correct.`
            }]
        }

        if (_.size(wrongRateAppliedForPerformance)) {
            return [400, {
                ok: false,
                message: `The employees in the following rows in your uploaded T&A sheet are on the wrong rate as their performance number doesn't fall within the range mentioned in the Rate Card sheet: \n\n [ ${[...wrongRateAppliedForPerformance]} ] \n\n
                Please update the Time and Attendance sheet and try again.`
            }]
        }


        // Upload to AWS S3
        const fileName = uuid.v4();
        const url = await uploadFileOnS3(config.BUCKET_NAME, config.TIME_AND_ATTENDANCE_FOLDER, fileName, "csv", fileContent);
        const timeAndAttendanceMetaData = {
            path: url.location,
            name: fileName,
            week: payload.week,
            status: TimeAndAttendanceStatus.PROCESSED,
            clientId: payload.client_id,
            agencyId: payload.agency_id,
            siteId: payload.site_id,
            startDate: payload.start_date,
            endDate: payload.end_date,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
        }
        timeAndAttendance = await createTimeAndAttendance(timeAndAttendanceMetaData);
        timeAndAttendance = await getTimeAndAttendanceById(timeAndAttendance.id);
        timeAndAttendanceData.forEach(element => { element.timeAndAttendanceId = timeAndAttendance.id; });


        // Add payroll report data
        await deleteTimeAndAttendanceDataById(timeAndAttendance.id);

        // add data in chunk of size 1000
        let arrays = await splitArrayWithSize(timeAndAttendanceData, 1000);

        // lets now open a new transaction
        let queryRunner = getNewTransaction();
        await queryRunner.startTransaction();

        try {
            for (const timeAndAttendanceData of arrays) {
                await addTimeAndAttendanceData(timeAndAttendanceData);
            }

            // commit transaction now
            await queryRunner.commitTransaction();
        } catch (error) {
            // since we have errors let's rollback changes we made
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            // release query runner which is manually created
            await queryRunner.release();
        }

        // Add payroll data based on time and attendance
        const payrollResponse = await addPayrollDataAsPerTimeAndAttendance(Object.values(payrollDataObject), { ...payload, time_and_attendance_id: timeAndAttendance.id }, loggedInUser);
        if (payrollResponse[0] !== 201) throw payrollResponse[1];

        // Generate message for employees with missing Total Agency Pay value
        const otherAssignmentUpdateMessage = _.size(TAPValueNotFoundWithEmployee) ? `\nNote: Total Agency Pay value not found for employee id(s): \n[ ${[...new Set(TAPValueNotFoundWithEmployee)].join(', ')} ].  ` : "";

        if (isWorkerTrainingActive && trainingRuleForSite) {
            payload.startDateOfNewStarterWeek = await getPreviousDateByWeeks(payload.start_date, trainingRuleForSite.evaluation_week - 1);
            payload.endDateOfNewStarterWeek = await getPreviousDateByWeeks(payload.end_date, trainingRuleForSite.evaluation_week - 1);

            payload.hasLimitedHoursRule = Boolean(trainingRuleForSite.limited_hours_evaluation_week && trainingRuleForSite.limited_hours_threshold_week);
            if (payload.hasLimitedHoursRule) {
                payload.startDateOfNewStarterWeekLimitedHours = await getPreviousDateByWeeks(payload.start_date, trainingRuleForSite.limited_hours_evaluation_week - 1);
                payload.endDateOfNewStarterWeekStudent = await getPreviousDateByWeeks(payload.end_date, trainingRuleForSite.limited_hours_evaluation_week - 1);

                // Get "Limited Hours Workers" who received training and are eligible for training check 
                limitedHoursWorkersToApplyRule = await getWorkersTrainingData(payload.client_id, payload.agency_id, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeekLimitedHours, payload.endDateOfNewStarterWeekStudent, true);

                // Get "Non Limited Hours Workers" who received training and are eligible for training check 
                nonLimitedHoursWorkersToApplyRule = await getWorkersTrainingData(payload.client_id, payload.agency_id, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeek, payload.endDateOfNewStarterWeek, false);

                workersToApplyRule = [...limitedHoursWorkersToApplyRule, ...nonLimitedHoursWorkersToApplyRule];
            } else {
                // Get "All Workers" who received training and are eligible for training check 
                workersToApplyRule = await getWorkersTrainingData(payload.client_id, payload.agency_id, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeek, payload.endDateOfNewStarterWeek);
            }


            // Calculate workers' credit dues if worker training is active
            if (workersToApplyRule.length) {
                const creditDuesResponse = await processWorkersCreditDues(payload, workersToApplyRule, trainingRuleForSite, timeAndAttendance.id, loggedInUser);
                if (creditDuesResponse[0] !== 201) {
                    const payrollId = payrollResponse[1].payrollId;
                    await Promise.all([
                        await deletePayrollByMetaId(payrollId),
                        await deletePayrollSummaryByMetaId(payrollId),
                        await deletePayrollDetailedSummaryByMetaId(payrollId),
                        await deleteHolidayPayrollSummaryByMetaId(payrollId),
                        await deletePayrollMetaById(payrollId)
                    ]);
                    throw creditDuesResponse[1];
                }
            }

        }
        // Shift Fulfilment Via TNA
        await processShiftFulfilment(payload, timeAndAttendance.id, loggedInUser)

        if (workersToMakeActive.length > 0) {
            await reActivateWorkers(workersToMakeActive, loggedInUser.user_id);
        }

        return [201, {
            'ok': true,
            message: MessageActions.CREATE_TIME_AND_ATTENDANCE + `${otherAssignmentUpdateMessage}`,
            payrollId: payrollResponse[1].payrollId
        }]
    } catch (err) {
        if (timeAndAttendance.id) {
            await deleteObject(config.BUCKET_NAME, config.TIME_AND_ATTENDANCE_FOLDER, timeAndAttendance.name);
            await deleteTimeAndAttendanceDataById(timeAndAttendance.id);
            await deleteTimeAndAttendanceById(timeAndAttendance.id);
        }

        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }

};

/**
 * Service to GET T&A.
 */
export const getListOfTimeAndAttendanceService = async (client_id, page, limit, sortBy, sortType) => {
    const { timeAndAttendanceList, count } = await EasyPromiseAll({
        timeAndAttendanceList: getTimeAndAttendanceList(`client_id = :client_id`, { "client_id": client_id }, page, limit, sortBy, sortType),
        count: getTimeAndAttendanceCount({ clientId: client_id })
    });
    return [200, {
        ok: true,
        time_and_attendance_list: timeAndAttendanceList,
        count
    }];
}

export const getAdjustmentRowsService = async (tnaId, loggedInUser) => {
    // Get user details to check permissions
    const userDetails = await getUserById(loggedInUser.user_id);
    const agency_id = userDetails.agency_id || null;
    const client_id = userDetails.client_id || null;

    const workers = await getAdjustmentWorkersList(tnaId, client_id, agency_id, "employee_id", "ASC");

    return [200, {
        ok: true,
        total_count: workers.length,
        adjustment_rows: workers,
    }];
}


/**
 * Service to GET T&A details.
 */
export const getDetailOfTimeAndAttendanceService = async (id, page, limit, sortBy, sortType) => {
    const { timeAndAttendanceDetail, count } = await EasyPromiseAll({
        timeAndAttendanceDetail: getTimeAndAttendanceDetail(id, page, limit, sortBy, sortType),
        count: getTimeAndAttendanceDataCount(id)
    });
    return [200, {
        ok: true,
        time_and_attendance_detail: timeAndAttendanceDetail,
        count
    }];
}

/**
 * Service to add Total Agency Pay
 */
export const addTotalAgencyPay = async (fileContent, totalAgencyPayData, payload, loggedInUser) => {
    let totalAgencyPay: any = {};
    try {

        // Check - Date range should be of 1 week
        let diff_in_days = diffBetweenGivenTwoDatesInDays(payload.start_date, payload.end_date)
        if (diff_in_days !== 6) {
            return [400, ErrorResponse.WrongDateRange];
        }

        // Check - Weekday of the start_date should be matched with the client-specific start weekday.
        const dayName = [WeekDays.SUNDAY, WeekDays.MONDAY, WeekDays.TUESDAY, WeekDays.WEDNESDAY, WeekDays.THURSDAY, WeekDays.FRIDAY, WeekDays.SATURDAY][new Date(payload.start_date).getDay()];
        let clientDetails = await getClientsById(payload.client_id);
        if (!clientDetails || clientDetails.weekday_start != dayName) {
            return [400, ErrorResponse.InvalidStartDate];
        }

        const filter = { startDate: payload.start_date, endDate: payload.end_date, agencyId: payload.agency_id, clientId: payload.client_id, siteId: payload.site_id };

        const timeAndAttendance = await getTimeAndAttendance(filter)
        if (timeAndAttendance) {
            return [404, ErrorResponse.InvalidTapUploadOperation]
        }

        const exisitingTotalAgencyPay = await getTotalAgencyPay(filter);
        if (exisitingTotalAgencyPay) {
            return [409, ErrorResponse.FileAlreadyExists];
        }
        const fileName = uuid.v4();
        // upload s3
        const url = await uploadFileOnS3(
            config.BUCKET_NAME,
            config.TOTAL_AGENCY_PAY_FOLDER,
            fileName,
            "csv",
            fileContent
        );
        const totalAgencyPayMetaData = {
            path: url.location,
            name: fileName,
            week: payload.week,
            status: TimeAndAttendanceStatus.PROCESSED,
            clientId: payload.client_id,
            agencyId: payload.agency_id,
            siteId: payload.site_id,
            startDate: payload.start_date,
            endDate: payload.end_date,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
        }
        totalAgencyPay = await createTotalAgencyPay(totalAgencyPayMetaData);
        totalAgencyPay = await getTotalAgencyPayById(totalAgencyPay.id);

        const { workers } = await EasyPromiseAll({
            workers: getWorkerByEmployeeIdAndAgencyId(_.map(totalAgencyPayData, 'employee_id').filter(x => Boolean(x)), _.map(totalAgencyPayData, 'payroll_ref').filter(x => Boolean(x)), payload)
        });

        const workerNotFoundEmployeeId = [];
        let TAPValueOffEmployee = [];

        for (let i = 0; i < totalAgencyPayData.length; i++) {
            let worker = null;
            worker = _.find(workers, { employee_id: totalAgencyPayData[i].employee_id, agency_id: payload.agency_id });

            if (worker) {
                totalAgencyPayData[i].workerId = worker.id;
            } else {
                totalAgencyPayData[i].employee_id && workerNotFoundEmployeeId.push(totalAgencyPayData[i].employee_id);
                !totalAgencyPayData[i].employee_id && workerNotFoundEmployeeId.push("''");
            }

            totalAgencyPayData[i].employeeId = totalAgencyPayData[i].employee_id;
            totalAgencyPayData[i].clientId = payload.client_id;
            totalAgencyPayData[i].agencyId = payload.agency_id;
            totalAgencyPayData[i].siteId = payload.site_id;
            totalAgencyPayData[i].createdBy = loggedInUser.user_id;
            totalAgencyPayData[i].updatedBy = loggedInUser.user_id;
            totalAgencyPayData[i].totalAgencyPayId = totalAgencyPay.id;
            totalAgencyPayData[i].tapValue = totalAgencyPayData[i].tap_value;
            totalAgencyPayData[i].week = payload.week;
            totalAgencyPayData[i].startDate = payload.start_date;
            totalAgencyPayData[i].endDate = payload.end_date;
        }

        if (_.size(workerNotFoundEmployeeId)) {
            if (totalAgencyPay.id) {
                await deleteObject(config.BUCKET_NAME, config.TOTAL_AGENCY_PAY_FOLDER, totalAgencyPay.name)
                await deleteTotalAgencyPayById(totalAgencyPay.id);
            }
            return [400, {
                ok: false,
                message: `worker id does not match for employee id(s): (${workerNotFoundEmployeeId})`
            }]
        }

        await deleteTotalAgencyPayDataById(totalAgencyPay.id);

        // Check for other assignment value 
        const filteredData = workers.filter((data) => data.other_assignment === 0);
        TAPValueOffEmployee = filteredData.map((data) => data.employee_id);

        if (_.size(TAPValueOffEmployee)) {
            await updateOtherAssignment(TAPValueOffEmployee);
        }

        // add data in chunk of size 1000
        let arrays = await splitArrayWithSize(totalAgencyPayData, 1000);

        // lets now open a new transaction
        let queryRunner = getNewTransaction();
        await queryRunner.startTransaction();

        try {
            for (const totalAgencyPayData of arrays) {
                await addtotalAgencyPayData(totalAgencyPayData);
            }

            // commit transaction now
            await queryRunner.commitTransaction();
        } catch (error) {
            // since we have errors let's rollback changes we made
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            // release query runner which is manually created
            await queryRunner.release();
        }

        const otherAssignmentUpdateMessage = _.size(TAPValueOffEmployee) ? `\nNote: Other assignment flag has been updated for employee id(s): \n[ ${Array.from(_.uniq(TAPValueOffEmployee)).join('\n')} ].  ` : "";

        return [201, {
            'ok': true,
            message: MessageActions.CREATE_TOTAL_AGENCY_PAY_FOLDER + `${otherAssignmentUpdateMessage}`
        }]
    } catch (err) {
        if (totalAgencyPay.id) {
            await deleteObject(config.BUCKET_NAME, config.TOTAL_AGENCY_PAY_FOLDER, totalAgencyPay.name);
            await deleteTotalAgencyPayDataById(totalAgencyPay.id);
            await deleteTotalAgencyPayById(totalAgencyPay.id);
        }

        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [422, ErrorResponse.UnprocessableEntityForFile]
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * Service to GET Total Agency Pay Sheet.
 */
export const downloadTotalAgencyPayFileService = async (loggedInUser, params) => {

    let userDetails = await getUserById(loggedInUser.user_id);
    if (userDetails.agency_id !== params.agency_id) {
        return [404, ErrorResponse.PermissionDenied]
    }

    let whereClause = `client_id = :client_id And agency_id = :agency_id and site_id = :site_id and start_date = :start_date and end_date = :end_date`;
    let whereClauseValue = { "client_id": params.client_id, "agency_id": params.agency_id, "site_id": params.site_id, "start_date": params.start_date, "end_date": params.end_date };
    let fileDetails = await getTotalAgencyPayDataHelper(whereClause, whereClauseValue);

    if (!fileDetails) return [404, ErrorResponse.TapFileNotFound];

    let link = await getSignedUrlForGetObject(config.BUCKET_NAME, config.TAP_SHEETS_BUCKET_FOLDER, fileDetails.name);
    return [200, {
        ok: true,
        "resource_url": link.url,
    }];;
};

/**
 * Service to delete TAP data.
 */
export const deleteTAPDataService = async (payload, loggedInUser) => {
    try {
        let agencies = await getAssociatedAgencies(payload.client_id);
        let agencyIdList = agencies.map(element => parseInt(element.agencyId));
        if (!agencyIdList.includes(payload.agency_id)) {
            return [403, ErrorResponse.PermissionDenied]
        }

        let siteDetails: any = await getAllSites(payload.client_id);
        let site = siteDetails[1].sites;
        let site_id_list = site.map(object => parseInt(object.id));
        if (!site_id_list.includes(payload.site_id)) {
            return [403, ErrorResponse.PermissionDenied]
        }

        const timeAndAttendance = await getTimeAndAttendance({ startDate: payload.start_date, agencyId: payload.agency_id, clientId: payload.client_id, siteId: payload.site_id })
        if (timeAndAttendance) {
            return [404, ErrorResponse.InvalidTapSHeetDeletionRequest]
        }

        const totalAgencyPay = await getTotalAgencyPay({
            startDate: payload.start_date,
            agencyId: payload.agency_id,
            clientId: payload.client_id,
            siteId: payload.site_id
        });

        if (!totalAgencyPay) {
            return [404, ErrorResponse.TapFileNotFound];
        }
        await deleteObject(config.BUCKET_NAME, config.TOTAL_AGENCY_PAY_FOLDER, totalAgencyPay.name);
        await deleteTAPDataHelper(payload);
        return [200, {
            ok: true,
            message: MessageActions.DELETE_TOTAL_AGENCY_PAY,
        }];

    } catch (err) {
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}


// Function to get client details and initiate the shift fulfilment process
export const processShiftFulfilment = async (payload, time_and_attendance_id, loggedInUser) => {
    const clientDetails = await getClientsById(payload.client_id);
    const bookingFormat = clientDetails.booking_format;

    const tnaDataForShiftFulfilment = await getTimeAndAttendanceDataForShiftFulfilment(time_and_attendance_id, bookingFormat);

    const shiftIdsForBookings = new Set(tnaDataForShiftFulfilment.map(obj => obj.shift_id));
    const deptIdsForBookings = new Set(tnaDataForShiftFulfilment.map(obj => obj.department_id));


    const whereClause = [
        'b.start_date >= :startDate',
        'b.end_date <= :endDate',
        'ba.agency_id = :agencyId',
        'b.client_id = :clientId',
        'b.site_id = :siteId',
        'b.shift_type_id IN (:...shiftTypes)',
        'b.department_id IN (:...departmentIds)',
    ].join(' AND ');

    let whereClauseValue = { "startDate": payload.start_date, "endDate": payload.end_date, "agencyId": payload.agency_id, "clientId": payload.client_id, "siteId": payload.site_id, "shiftTypes": [...shiftIdsForBookings], "departmentIds": [...deptIdsForBookings] };

    const requestedBookingsForTnaFulfilment = await getBookingsDataForTnaFulfilment(whereClause, whereClauseValue);

    // For all shift-department combinations, calculate the number of times bookings were requested for the same week.
    const requestedBookingsKeyMap = await groupBookingObjects(requestedBookingsForTnaFulfilment);

    // Seprate HEADS based on shift-department & supervisor status combinations for workers in TNA data
    const fulfilmentObjects = await calculateFulfilmentObjects(tnaDataForShiftFulfilment, bookingFormat);

    await updateShiftBookingsFromTna(requestedBookingsForTnaFulfilment, requestedBookingsKeyMap, fulfilmentObjects, bookingFormat, payload.agency_id, loggedInUser);
}


/*  
    Fulfilment days for all workers with same Shift & Department Combo Sums up.
    I.e O/P = { '35_34': { '1': 1, '2': 1, '3': 1, '4': 2, '5': 1, '6': 1, '7': 0 } } 
*/
export const calculateFulfilmentObjects = async (tnaDataForShiftFulfilment, bookingFormat) => {
    const defaultHeads = { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 };
    return tnaDataForShiftFulfilment.reduce((acc, item) => {
        const key = `${item.shift_id}_${item.department_id}`;
        const subKey = `${key}_${item.is_supervisor}`;

        const supervisorYes = `${key}_YES`;
        const supervisorNo = `${key}_NO`;

        if (!acc[key]) {
            acc[key] = {};
            acc[key][supervisorYes] = { ...defaultHeads }
            acc[key][supervisorNo] = { ...defaultHeads }
            for (let i = 1; i <= 7; i++) {
                acc[key][subKey][`${i}`] = formatNumber(item[`${i}`], bookingFormat);
            }
        } else {
            for (let i = 1; i <= 7; i++) {
                acc[key][subKey][`${i}`] += formatNumber(item[`${i}`], bookingFormat);
                acc[key][subKey][`${i}`] = formatNumber(acc[key][subKey][`${i}`], bookingFormat);
            }
        }
        return acc;
    }, {});
}

// Function to group booking objects by shift and department
export const groupBookingObjects = async (shiftBookingsDataForTnaFulfilment) => {
    return shiftBookingsDataForTnaFulfilment.reduce((acc, obj) => {
        const key = `${obj.shift_id}_${obj.department_id}`;
        acc[key] ? acc[key] += 1 : (acc[key] = 1);
        return acc;
    }, {});
}

/**
 * Process workers' credit dues based on their performance and training rules.
 * 
 * @param {Object} payload - The payload containing client, agency, site, and other details.
 * @param {Object} workersToApplyRule - workers To Apply Rule
 * @param {Object} trainingRuleForSite - Training rules for the site.
 * @param {number} time_and_attendance_id - The ID of the time and attendance record.
 * @param {Object} loggedInUser - The logged-in user details.
 * @returns {Array} - The status code and response object.
 */
export const processWorkersCreditDues = async (payload, workersToApplyRule, trainingRuleForSite, time_and_attendance_id, loggedInUser) => {
    try {
        const creditDueWorkersToInsert = [];
        const workersToUpdateTrainingQualificationStatus = { "qualified": [], "not_qualified": [] };

        let workersFirstQualifyingPerformanceNumber = {};
        let workersTrainingCostsAndHours = {};
        if (payload.hasLimitedHoursRule) {
            const workerIds = workersToApplyRule.filter(wkr => !wkr.limited_hours).map(wkr => wkr.worker_id);
            workersFirstQualifyingPerformanceNumber = await getGetFirstQualifyingPerformanceNumber(workerIds, trainingRuleForSite.performance_threshold, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeek);
            workersTrainingCostsAndHours = await getTrainingCostsAndHours(payload.client_id, payload.agency_id, payload.site_id, workerIds, payload.start_date, payload.startDateOfNewStarterWeek, Number(trainingRuleForSite.max_training_hours));

            const workerIdsLimitedHours = workersToApplyRule.filter(wkr => wkr.limited_hours).map(wkr => wkr.worker_id);
            const workersFirstQualifyingPerformanceNumberLimiedHours = await getGetFirstQualifyingPerformanceNumber(workerIdsLimitedHours, trainingRuleForSite.performance_threshold, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeekLimitedHours);
            const workersTrainingCostsAndHoursLimitedHours = await getTrainingCostsAndHours(payload.client_id, payload.agency_id, payload.site_id, workerIdsLimitedHours, payload.start_date, payload.startDateOfNewStarterWeekLimitedHours, Number(trainingRuleForSite.max_training_hours));

            workersFirstQualifyingPerformanceNumber = { ...workersFirstQualifyingPerformanceNumber, ...workersFirstQualifyingPerformanceNumberLimiedHours };
            workersTrainingCostsAndHours = { ...workersTrainingCostsAndHours, ...workersTrainingCostsAndHoursLimitedHours };

        } else {
            const workerIds = workersToApplyRule.map(wkr => wkr.worker_id);
            workersFirstQualifyingPerformanceNumber = await getGetFirstQualifyingPerformanceNumber(workerIds, trainingRuleForSite.performance_threshold, payload.site_id, payload.start_date, payload.startDateOfNewStarterWeek);
            workersTrainingCostsAndHours = await getTrainingCostsAndHours(payload.client_id, payload.agency_id, payload.site_id, workerIds, payload.start_date, payload.startDateOfNewStarterWeek, Number(trainingRuleForSite.max_training_hours));
        }

        workersToApplyRule.forEach(wkr => {
            const applyLimitedHoursRule = Boolean(payload.hasLimitedHoursRule && wkr.limited_hours);

            const performanceData = workersFirstQualifyingPerformanceNumber[wkr.worker_id];
            const trainingCostAndHours = workersTrainingCostsAndHours[wkr.worker_id] || { total_training_employment_cost: 0, total_training_hours: 0 };
            const thresholdWeek = applyLimitedHoursRule ? trainingRuleForSite.limited_hours_threshold_week : trainingRuleForSite.threshold_week;
            const evaluationWeek = applyLimitedHoursRule ? trainingRuleForSite.limited_hours_evaluation_week : trainingRuleForSite.evaluation_week;

            if (performanceData) {
                const assignmentWeekStartDateMoment = moment(applyLimitedHoursRule ? payload.startDateOfNewStarterWeekLimitedHours : payload.startDateOfNewStarterWeek, dateTimeFormates.YYYYMMDD);
                const endDateMoment = moment(performanceData.end_date, dateTimeFormates.YYYYMMDD).add(1, 'day');
                const weekDifference = endDateMoment.diff(assignmentWeekStartDateMoment, 'weeks');

                wkr["achievement_week"] = weekDifference;
                wkr["performance_number"] = performanceData.performance_number;
                if (weekDifference <= thresholdWeek) {
                    wkr["applied_credit_rate"] = 0;
                    wkr["total_credit_due"] = 0;
                    wkr["is_qualified"] = true;
                } else if (weekDifference <= evaluationWeek) {
                    wkr["applied_credit_rate"] = Number(trainingRuleForSite.creditRate);
                    wkr["total_credit_due"] = _.round((trainingCostAndHours.total_training_employment_cost * Number(trainingRuleForSite.creditRate)) / 100, 2);
                    wkr["is_qualified"] = true;
                } else {
                    wkr["applied_credit_rate"] = 100;
                    wkr["total_credit_due"] = trainingCostAndHours.total_training_employment_cost;
                    wkr["is_qualified"] = false;
                }

            } else {
                wkr["applied_credit_rate"] = 100;
                wkr["total_credit_due"] = trainingCostAndHours.total_training_employment_cost;
                wkr["is_qualified"] = false;
                wkr["achievement_week"] = null;
                wkr["performance_number"] = null;
            }

            creditDueWorkersToInsert.push({
                clientId: payload.client_id,
                agencyId: payload.agency_id,
                siteId: payload.site_id,
                workerId: wkr.worker_id,
                limitedHours: applyLimitedHoursRule,
                timeAndAttendanceId: time_and_attendance_id,
                performanceNumber: wkr.performance_number,
                completedTrainingHours: trainingCostAndHours.total_training_hours,
                maxTrainingHours: trainingRuleForSite.max_training_hours,
                performanceThreshold: trainingRuleForSite.performance_threshold,
                thresholdWeek: thresholdWeek,
                evaluationWeek: evaluationWeek,
                achievementWeek: wkr.achievement_week,
                trainingEmploymentCost: trainingCostAndHours.total_training_employment_cost,
                creditRate: trainingRuleForSite.creditRate,
                appliedCreditRate: wkr.applied_credit_rate,
                totalCreditDue: wkr.total_credit_due,
                startDate: payload.start_date,
                endDate: payload.end_date,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
            });

            if (wkr.is_qualified) {
                workersToUpdateTrainingQualificationStatus["qualified"].push(wkr.worker_id);
            } else {
                workersToUpdateTrainingQualificationStatus["not_qualified"].push(wkr.worker_id);
            }
        });

        await addCreditDuesData(creditDueWorkersToInsert);
        await updateWorkersQualificationStatus(workersToUpdateTrainingQualificationStatus['qualified'], "QUALIFIED", loggedInUser.user_id);
        await updateWorkersQualificationStatus(workersToUpdateTrainingQualificationStatus['not_qualified'], "NOT_QUALIFIED", loggedInUser.user_id);
        return [201, { ok: true }];
    } catch (err) {
        await deleteCreditDuesBasedOnTnaIdHelper([time_and_attendance_id], loggedInUser);
        return [500, err];
    }
}


export const triggerFtpLookupPythonService = async (
    clientId: number,
    loggedInUser: any,
    ftpScriptType: string
) => {
    const permissionDenied = () => [403, ErrorResponse.PermissionDenied]

    try {
        // Permission checks for the logged-in user
        let { credentials: ftpCreds, configurations: ftpConfig } = await getFtpConfigByClientId(clientId, ftpScriptType);

        if (!ftpCreds || !ftpConfig.length) return permissionDenied();

        let userDetails = await getUserById(loggedInUser.user_id);
        const userTypeId = parseInt(loggedInUser.user_type_id);
        const clientMismatch = loggedInUser.client_id != clientId;

        if ([UserType.CLIENT_ADMIN].includes(userTypeId) && clientMismatch) return permissionDenied();
        if ([UserType.AGENCY_ADMIN].includes(userTypeId)) {
            const existingAssociation = await getAgencyAssociationByAgencyIdAndClientId(
                userDetails.agency_id,
                clientId
            );
            if (!existingAssociation) return permissionDenied();
        }

        // Flask service configuration
        const FLASK_SERVICE_URL = config.FLASK_SERVICE_URL;

        // Get endpoint based on ftpScriptType
        const endpoint = getEndpointByScriptType(ftpScriptType, clientId);
        if (!endpoint) {
            return [400, {
                status: 400,
                ok: false,
                message: `Invalid FTP script type: ${ftpScriptType}`,
            }];
        }

        // Make request to Flask service
        const response = await axios.get(`${FLASK_SERVICE_URL}${endpoint}`, {
            timeout: 120000, // 120 seconds timeout
            headers: {
                'Content-Type': 'application/json',
            }
        });

        logger.info('FTP Service Triggered Successfully', {
            clientId,
            ftpScriptType,
            response: response.data
        });

        // Return the Flask service response directly
        return [response.status, response.data];

    } catch (error) {
        logger.error('FTP Service Error', {
            clientId,
            ftpScriptType,
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        });

        if (axios.isAxiosError(error)) {
            // Handle specific HTTP errors from Flask service
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                return [error.response.status, error.response.data || {
                    status: error.response.status,
                    ok: false,
                    message: 'FTP service error',
                    error: error.message
                }];
            } else if (error.request) {
                // The request was made but no response was received
                return [503, {
                    status: 503,
                    ok: false,
                    message: 'FTP service is currently unavailable',
                    error: 'Service unavailable'
                }];
            }
        }

        // Handle unexpected errors
        return [500, {
            status: 500,
            ok: false,
            message: 'Oops! We encountered an unexpected error. Please contact support for assistance.',
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        }];
    }
};

// Helper function to get endpoint based on script type
function getEndpointByScriptType(ftpScriptType: string, clientId: number): string | null {
    switch (ftpScriptType.toUpperCase()) {
        case 'TNA':
            return `/ftp-tna-lookup-trigger/${clientId}`;
        case 'WORKERS_UPLOAD':
            return `/ftp-workers-upload-lookup-trigger/${clientId}`;
        default:
            return null;
    }
}

export const validateTnaUserInputData = async (timeAndAttendanceData) => {
    const invalidExpensesRate = [];
    const invalidExpensesCalculations = [];
    const invalidPayCorrectionAdjustment = [];

    for (let i = 0; i < timeAndAttendanceData.length; i++) {
        const record = timeAndAttendanceData[i];

        // Check for pay_correction and adjustment validation
        if (record.pay_correction.toLowerCase() === "yes" && record.adjustment.toLowerCase() !== "yes") {
            invalidPayCorrectionAdjustment.push(`Row ${i + 2}: Employee ${record.employee_id}`);
        }

        // Check for EXPENSES pay type validations
        if (record.pay_type === PayTypes.EXPENSES) {
            // Check 1: pay_rate and charge_rate should be exactly equal
            if (Math.abs(getNumberValue(record.pay_rate) - getNumberValue(record.charge_rate)) > 0.01) {
                invalidExpensesRate.push(`Row ${i + 2}: Employee ${record.employee_id}`);
            }

            // Check 2: Sum of standard_pay + overtime_pay should match standard_charges + overtime_charges and total_charges
            const totalPay = getNumberValue(record.standard_pay) + getNumberValue(record.overtime_pay);
            const totalCharges = getNumberValue(record.standard_charges) + getNumberValue(record.overtime_charges);
            const recordTotalCharges = getNumberValue(record.total_charges);

            // Allow small decimal differences (0.1 tolerance for rounding)
            const payChargesDiff = Math.abs(totalPay - totalCharges);
            const chargesTotalDiff = Math.abs(totalCharges - recordTotalCharges);
            if (payChargesDiff > 0.1 || chargesTotalDiff > 0.1) {
                invalidExpensesCalculations.push(`Row ${i + 2}: Employee ${record.employee_id}`);
            }
        }
    }

    if (invalidPayCorrectionAdjustment.length > 0) {
        return [400, {
            ok: false,
            message: `Adjustment must be 'Yes' if Pay Correction is 'Yes'.\n${invalidPayCorrectionAdjustment.join('\n')}. `
        }];
    }

    if (invalidExpensesRate.length > 0) {
        return [400, {
            ok: false,
            message: `${PayTypes.EXPENSES} pay type require the pay_rate to be equal to the charge_rate.\n${invalidExpensesRate.join('\n')}. `
        }];
    }

    if (invalidExpensesCalculations.length > 0) {
        return [400, {
            ok: false,
            message: `${PayTypes.EXPENSES} calculation mismatch. Ensure pay totals = charge totals. \n${invalidExpensesCalculations.join('\n')}. `
        }];
    }

    return null;
};