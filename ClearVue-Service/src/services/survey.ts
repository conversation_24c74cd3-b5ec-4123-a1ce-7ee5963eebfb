const _ = require('lodash');
const moment = require('moment');
import { MessageActions, ErrorResponse, WorkerTypes, agency, Languages } from "./../common";
import {
    getSurveyCategories, getWorkerStartDateById, getSurveyQuestions, addNewSurvey, addNewAnswer, getSurveyQuestionsForDownload,
    getSurveyAnalysis, downloadSurveyAnalysis, createSurveyQuestions, updateSurveyQuestionHelper, getSurveySubmittedDate,
    getWorkerIdfromUserIdWithLimit, getClientUserIdById, getSubmittedSurveyCount, getWorkerIdfromUserId, countSurveyQuestions
} from "./../models";
import { translateText } from '../utils';
import { config } from '../configurations';
import { UserType } from '../common';
import { number } from "joi";

/**
 * Service to GET Survey Category.
 */
export const getSurveyCategoryService = async (userId = null, languageCode: string) => {
    const selectKeys = userId ? ['id', 'name', 'triggered_week', 'submission_limit'] : ['id', 'name']
    const whereClause = userId ? 'is_visible = 1' : ''
    let surveyCategories = await getSurveyCategories(selectKeys, whereClause, {});
    if (userId) {
        let workerDetail = await getWorkerIdfromUserIdWithLimit(userId);

        // Disable all surveys for unknown worker.
        if (!workerDetail.employee_id && !workerDetail.client_id && !workerDetail.agency_id) {
            surveyCategories = _.map(surveyCategories, (category) => {
                category.isVisible = false;
                category.name = JSON.parse(category.name)[languageCode]
                delete category.triggered_week;
                delete category.submission_limit;
                return category;
            });
            return [200, {
                ok: true,
                surveys: surveyCategories,
            }];
        }

        let surveySubmittedDate = await getSurveySubmittedDate(workerDetail.id, null);
        let categoryObj: Object = {};
        let submittedDateObj: Object = {};

        surveyCategories.forEach((element) => {
            categoryObj[element.id] = element.submission_limit;
        });
        surveySubmittedDate.forEach((element) => {
            submittedDateObj[element.survey_id] = element.created_at;
        });

        const worker = await getWorkerStartDateById(workerDetail.id);
        const currentWeek = moment().diff(worker.start_date, 'weeks');

        const startDate = moment(worker.start_date).startOf('day');
        const dayDifference = moment().startOf('day').diff(startDate, 'days');

        surveyCategories = _.map(surveyCategories, (category) => {
            category.isVisible = true;
            if (dayDifference < 0) {
                category.isVisible = false;
            } else if (category.triggered_week === 1) {
                category.isVisible = currentWeek > 1 ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else if (category.triggered_week === 2) {
                category.isVisible = (currentWeek < 1 || currentWeek > 2) ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else if (category.triggered_week === 4) {
                category.isVisible = (currentWeek < 2 || currentWeek > 4) ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else if (category.triggered_week === 8) {
                category.isVisible = (currentWeek < 4 || currentWeek > 8) ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else if (category.triggered_week === 12) {
                category.isVisible = (currentWeek < 8 || currentWeek > 12) ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else if (JSON.parse(category.name)["en"] === "Exit Survey") {
                category.isVisible = worker.is_active === 1 ? false : isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            } else {
                category.isVisible = isAbleToSubmitSurvey(categoryObj[category.id], submittedDateObj[category.id]) && isSurveyAllowedToWorker(category.id, workerDetail.type);
            }
            delete category.triggered_week;
            delete category.submission_limit;
            return category;
        })
    }

    // Extract language specific survey name
    surveyCategories.forEach((survey) => {
        survey.name = JSON.parse(survey.name)[languageCode]
    })

    return [200, {
        ok: true,
        surveys: surveyCategories,
    }];
}

export const isAbleToSubmitSurvey = (surveyLimit, lastSubmitDate = null) => {
    let isVisible = true;
    if (surveyLimit == 'ONCE_A_MONTH') {
        isVisible = !moment(lastSubmitDate).isSame(new Date(), 'month');
    } else if (surveyLimit == 'ONCE_A_WEEK') {
        isVisible = !moment(lastSubmitDate).isSame(new Date(), 'week');
    } else if (surveyLimit == 'ONLY_ONCE') {
        isVisible = lastSubmitDate ? false : true;
    }
    return isVisible
}

export const isSurveyAllowedToWorker = (surveyId, workerType) => {
    return workerType == WorkerTypes.PERMANENT && !JSON.parse(config.PERMANENT_WORKER_ALLOWED_SURVEYS).includes(parseInt(surveyId)) ? false : true;
}

/**
 * Service to GET Survey Analysis.
 */
export const getSurveyAnalysisService = async (surveyId, query) => {
    let whereClause = `survey_result.surveyId = :survey_id`;
    if (query.client_id) {
        whereClause = `${whereClause} and survey_result.clientId = :client_id`
    }
    if (query.agency_id) {
        whereClause = `${whereClause} and survey_result.agencyId = :agency_id`
    }
    if (query.site_id) {
        whereClause = `${whereClause} and survey_result.siteId = :site_id`
    }
    if (query.region_id) {
        whereClause = `${whereClause} and site.regionId = :region_id`
    }
    if (query.start_date && query.end_date) {
        whereClause = `${whereClause} and survey_result.created_at >= :date_1 and survey_result.created_at <= :date_2`
    }

    whereClause += query.type ? ` and worker.type = :type` : '';

    let whereClauseValue = { "survey_id": surveyId, "client_id": query.client_id, "agency_id": query.agency_id, "site_id": query.site_id, "region_id": query.region_id, "date_1": moment(query.start_date).format('YYYY-MM-DD 00:00:01'), "date_2": moment(query.end_date).format('YYYY-MM-DD 23:59:59'), "type": query.type };

    let surveyAnalysis = await getSurveyAnalysis(whereClause, whereClauseValue);
    let response = []
    _.map(surveyAnalysis, (survey) => {
        let formattedSurvey = _.find(response, { id: survey.question_id });
        if (survey.value != 0) {
            if (formattedSurvey) {
                formattedSurvey.options.push({
                    name: survey.option_type == 'Rating' ? `${survey.rating_name} Stars` : survey.rating_name,
                    count: survey.value
                })
            } else {
                let analysis = {
                    id: survey.question_id,
                    question_text: survey.question_text,
                    label: survey.label,
                    options: [{
                        name: survey.option_type == 'Rating' ? `${survey.rating_name} Stars` : survey.rating_name,
                        count: survey.value
                    }]
                };
                response.push(analysis)
            }
        }
    })
    return [200, {
        ok: true,
        questions: response,
    }];
}

/**
 * Service to downbload Survey Analysis.
 */
export const downloadSurveyAnalysisService = async (surveyId, query, loggedInUser) => {
    let whereClause = `survey_result.surveyId = :survey_id`;
    if (query.client_id) {
        whereClause = `${whereClause} and survey_result.clientId = :client_id`
    }
    if (query.agency_id) {
        whereClause = `${whereClause} and survey_result.agencyId =:agency_id`
    }
    if (query.site_id) {
        whereClause = `${whereClause} and survey_result.siteId = :site_id`
    }
    if (query.start_date && query.end_date) {
        whereClause = `${whereClause} and survey_result.created_at >= :date_1 and survey_result.created_at <= :date_2`
    }

    whereClause += query.type ? ` and worker.type = :type` : '';
    let userId = loggedInUser.user_id;
    if (loggedInUser.user_type_id != UserType.CLIENT_ADMIN) {
        userId = (await getClientUserIdById(query.client_id)).user_id;
    }

    let questions = await getSurveyQuestionsForDownload(surveyId, ['id', `JSON_UNQUOTE(JSON_EXTRACT(survey_questions.question_json, '$.en')) AS question_text`, 'label'], query.client_id, query.agency_id);
    questions = _.map(questions, (question) => {
        question.question_text = question.question_text.replace(/,/g, "")
        return question
    });

    let whereClauseValue = { "survey_id": surveyId, "client_id": query.client_id, "agency_id": query.agency_id, "site_id": query.site_id, "region_id": query.region_id, "date_1": moment(query.start_date).format('YYYY-MM-DD 00:00:01'), "date_2": moment(query.end_date).format('YYYY-MM-DD 23:59:59'), "type": query.type };

    let surveyAnalysis = await downloadSurveyAnalysis(whereClause, whereClauseValue);
    let response = []
    _.map(surveyAnalysis, (survey) => {
        const question_id = survey.question_id;
        let formattedSurvey = _.find(response, { created_at: survey.created_at });
        if (formattedSurvey) {
            // let formattedQuestions = _.find(formattedSurvey.questions, {question_id: survey.question_id});
            let formattedQuestions = formattedSurvey.questions[question_id];
            if (formattedQuestions && survey.answer) {
                formattedQuestions.answer.push(survey.answer)
            } else {
                formattedSurvey.questions[question_id] = {
                    "rating": survey.rating,
                    "answer": survey.answer ? [survey.answer] : [],
                }
            }
        } else {
            let analysis = {
                id: survey.id,
                worker_id: survey.worker_id,
                worker_first_name: survey.worker_first_name,
                worker_last_name: survey.worker_last_name,
                shift_name: survey.shift_name,
                site_name: survey.site_name,
                department_name: survey.department_name,
                worker_employee_id: survey.worker_employee_id,
                worker_role: survey.worker_role,
                questions: {},
                worker_type: survey.worker_type,
                created_at: survey.created_at
            };
            analysis.questions[question_id] = {
                "rating": survey.rating,
                "answer": survey.answer ? [survey.answer] : [],
            }
            response.push(analysis)
        }
    })
    return [200, {
        ok: true,
        questions: questions,
        surveys: response,
    }];
}


/**
 * Service to GET Survey Questions.
 */
export const getSurveyQuestionsService = async (surveyId, userId, userType, languageCode: string) => {
    let surveyQuestions = await getSurveyQuestions(surveyId, ['id', 'question_json', 'label', 'sequence', 'option_type', 'options_json'], 0, userType, userId);
    surveyQuestions = _.map(surveyQuestions, (question) => {

        question.question_text = JSON.parse(question.question_json)[languageCode]
        delete question.question_json

        question.options = question.options_json ? JSON.parse(question.options_json)[languageCode] : question.options_json;
        question.options = question.options ? Object.entries(question.options).map(([id, answer]) => ({ id, answer })) : question.options;
        delete question.options_json

        return question;
    })
    return [200, {
        ok: true,
        questions: surveyQuestions,
    }];
}

/**
 * Service to add Survey.
 */
export const addSurveyService = async (result, loggedInUserId) => {

    let workerIds = [];
    result.forEach((question) => {
        !question.agencyId && delete question.agencyId;
        workerIds.push(question.workerId);
    })

    if (_.size([...new Set(workerIds)]) > 1) {
        return [403, ErrorResponse.PermissionDenied]
    }

    // To check loggedIn worker has access to add survey response
    const latestWorkerId = Math.max(...(await getWorkerIdfromUserId({ userId: loggedInUserId })).map(({ id }) => id));

    if (!latestWorkerId) {
        return [404, ErrorResponse.WorkerNotFound]
    }
    if (latestWorkerId != result[0].workerId) {
        return [403, ErrorResponse.PermissionDenied]
    }

    let selectKeys = ['id', 'name', 'submission_limit'];
    let whereClause = `is_visible = 1 AND id = :survey_id`;
    let whereClauseValue = { "survey_id": result[0].surveyId };

    let surveyCategory = await getSurveyCategories(selectKeys, whereClause, whereClauseValue);
    let surveySubmittedDate = await getSurveySubmittedDate(result[0].workerId, result[0].surveyId);
    let submissionFlag = isAbleToSubmitSurvey(
        surveyCategory[0].submission_limit,
        surveySubmittedDate.length ? surveySubmittedDate[0].created_at : null
    );

    if (!submissionFlag) {
        return [409, ErrorResponse.SurveyAlreadyFilled]
    }

    const responseWithNoMcq = [];
    const answerData = [];
    for (let response of result) {
        response.createdBy = loggedInUserId;
        if (response.hasOwnProperty('rating') && response.rating != null) {
            responseWithNoMcq.push(response);
        } else {
            const { "answers": answers, ...resultObj } = response;
            const surveyResult = await addNewSurvey(resultObj);
            for (let ans of answers) {
                answerData.push({
                    answerId: Number(ans.id),
                    createdBy: loggedInUserId,
                    resultId: surveyResult[0]
                })
            }
        }
    }

    if (_.size(responseWithNoMcq)) {
        await addNewSurvey(responseWithNoMcq);
    }
    if (_.size(answerData)) {
        await addNewAnswer(answerData);
    }
    return [200, {
        ok: true,
        message: MessageActions.SURVEY_RESPONSE
    }];
}

/**
 * Service to add default general survey questions for client admin
 */
export const addDefaultGeneralQuestions = async (id: number, userType: number) => {
    let questions = await getSurveyQuestions(config.GENERAL_SURVEY_ID, ['id', 'survey_id', 'question_json', 'label', 'belongs_to', 'sequence', 'option_type'], 1, userType);

    let questionsToInsert = []
    questionsToInsert = questions.map(payload => {
        return {
            questionJson: JSON.parse(payload.question_json),
            surveyId: payload.survey_id,
            label: payload.label,
            belongsTo: payload.belongs_to,
            sequence: payload.sequence,
            optionType: payload.option_type,
            createdBy: id.toString(),
            updatedBy: id.toString()
        }
    })
    await createSurveyQuestions(questionsToInsert);
}

/**
 * update general survey questions
 * @param  {number} surveyId
 * @param  {any} payload
 * @param  {number} loggedInUser
 */
export const updateSurveyQuestionService = async (surveyId: number, payload: any, loggedInUser: number) => {
    const questionIds = await Promise.all(_.map(payload.questions, async (object) => { return object.id }));
    let questionsCount = await countSurveyQuestions(surveyId, loggedInUser, questionIds);

    if (questionIds.length != questionsCount) return [403, ErrorResponse.UnauthorizedUser];

    const dataUpdate = await Promise.all(_.map(payload.questions, async (object) => {
        object["id"] = object.id.toString();
        object["questionJson"] = await translateTextToMultipleLanguage(object.question_text);
        object["updatedAt"] = new Date();
        object["updatedBy"] = loggedInUser;
        delete object.question_text;
        return object;
    }));

    await updateSurveyQuestionHelper(dataUpdate);
    return [200, {
        ok: true,
        message: MessageActions.UPDATE_QUESTION,
    }];
};


/**
 * Translate to multiple language and create an json paylaod for that
 * @param  {any} text
 */
export const translateTextToMultipleLanguage = async (text: any) => {
    let translatedMessage = {};
    for (let lang in Languages) {
        if (lang === Languages.TIGRINYA) {
            continue;
        }
        translatedMessage[Languages[lang]] = await translateText(text, Languages.ENGLISH, Languages[lang]);
    };
    return translatedMessage;
}

