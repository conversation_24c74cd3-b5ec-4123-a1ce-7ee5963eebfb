import {
    getWorkerDemographicsDetails, getStartAndInactivatedDateForTheWorkers,
    getAgencyWiseWorkerDemographicsDetails, getStartAndInactivatedDateForTheAgencyWiseWorkers, getAssociatedAgenciesList,
    getShiftFulfillmentFromBookingAssociation, getShiftUtilisationDetailsModel, getWorkersWorkingHours,
    getWorkersDayWiseShiftUtilisationDetails, getWorkersLeaversDetails, getTotalWorkers, getFulfilmentAndLossCount, getActivityTotalSpendByAgencyHelper,
    getWorkersLeaversCountByDateRange,
    getStandardAndOvertimeHourAndPay, getWorkForcePoolUtilizationTotalWorkers, getWorkForcePoolUtilizationActiveWorkers,
    getInactivatedWorkersPerAgencyByStartDate, getTrendAgencyRating,
    getHeaderCumulativeClearVueSavings, getTrendCompanyRating,
    getPreviousWeekClearVueSavings, getTrendSiteRating, getSites,
    getWorkersTotalWorkingHours, getPoolUtilizationInactiveWorkers,
    getWorkersCountForAverageWorkingHours, getTADataAvailableWorkers, getLeaverAnalysis, getTotalSpendTrendsAnalytics,
    getTotalHoursTrendsAnalytics, getTotalHeadsTrendsAnalytics, getTotalLeaversTrendsAnalytics,
    getNewStarterRetentionData,
    getAgencyWiseNewStarterRetentionData, getWorkersWithSixtyPlusHours, getWorkersBasedOnCardCondition, getWorkersWithSamePostCodeAndHouseNumbers, getWorkersWithConsecutiveDaysCard, getAllUsers,
    getClientsById,
    checkAuthorisedResourceAccess,
    getWorkersWithSameSortCodeAndAccountNumbers,
    fetchStartDateYearlyRules, getWorkerByWorkerId, getLatestComplianceApproval, insertComplianceApproval, getUserById
} from "../models";
import { ComplianceApproval } from '../models/sql/entities/ComplianceApproval';
import {
    getWeeksOfTwoDates, dayRangeAsPerDayCount, removeKeyFromObject, objectToMySQLConditionString, getWeekWiseWorkingDays, getWeeklabels,
    arrayToObject, objectToMySQLConditionStrings, objectToMySQLConditionStringForTAData, objectToMySQLWhereClause, objectToMySQLConditionStringForTADataWhereClause,
    generateMaskedAccountIdentifier
} from '../utils';
import { lengthOfServiceResponse, databaseSeparator, PayType, ComplinaceCardsType, RoleType, PayTypes, dateTimeFormates, UserType, ErrorResponse, WeekDays } from '../common';
import { getAllSites } from '../services'
import moment from "moment";
const deepClone = require('lodash.clonedeep');
const _ = require("lodash");
import { config } from "../configurations";
import { worker } from "cluster";
import { calculateWeeksForAllDatePairs, findMatchingYearlyRule, getDurationBetweenDates, notifyBugsnag } from "../utils/helper";
const { EasyPromiseAll } = require('easy-promise-all');

/*
    WorkForce Top-Deck
*/

/**
 * Get worker nationality demographics data for the various filters like site, agency, client, shift & department
 * @param  {} requestArgs
 */
export const getWorkerDemographicsDataService = async (requestArgs) => {
    let { "is_active": isActive, "type": workerType, ...otherRequestArgs } = requestArgs;
    isActive = isActive === 'true' || isActive === undefined ? 1 : 0;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherRequestArgs, isActive);

    whereClause += (workerType ? ` AND workers.type = :worker_type` : "")
    whereClauseValue["worker_type"] = workerType;

    let response = await getWorkerDemographicsDetails(
        requestArgs,
        whereClause,
        whereClauseValue,
        `COALESCE(NULLIF(workers.nationality, ''), 'Undeclared')`
    );

    return {
        "ok": true,
        "result": {
            "rows": parseDemographicsValueToInteger(response)
        },
        "is_data_available": response.length ? true : false
    }
};


/**
 * Get length of service data response
 * @param  {} weekDifference
 * @param  {} locResponse
 */
const getLengthOfServiceWeeks = (weekDifference, locResponse) => {
    switch (true) {
        case (1 <= weekDifference && weekDifference <= 2): {
            locResponse[0].data[0] += 1;
            break;
        }
        case (3 <= weekDifference && weekDifference <= 4): {
            locResponse[1].data[0] += 1;
            break;
        }
        case (5 <= weekDifference && weekDifference <= 8): {
            locResponse[2].data[0] += 1;
            break;
        }
        case (9 <= weekDifference && weekDifference <= 12): {
            locResponse[3].data[0] += 1;
            break;
        }
        case (13 <= weekDifference && weekDifference <= 16): {
            locResponse[4].data[0] += 1;
            break;
        }
        case (17 <= weekDifference && weekDifference <= 26): {
            locResponse[5].data[0] += 1;
            break;
        }
        case (27 <= weekDifference && weekDifference <= 52): {
            locResponse[6].data[0] += 1;
            break;
        }
        case (52 <= weekDifference): {
            locResponse[7].data[0] += 1;
            break;
        }
    }
    return locResponse;
}


/**
 * Get day wise completed shift data for the workers from the T&A sheet (For active workers)
 * @param  {} requestArgs
 */
export const getWorkForceShiftUtilizationService = async (requestArgs, user) => {
    return getShiftUtilisationDetails(requestArgs, 1, user);
}


/**
 * Get length of service data.
 * - Consider start date of the workers and calculate length of service as per the week duration.
 * @param  {} requestArgs
 */
export const getWorkForceLOSService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 2, 'workers', startDate, endDate);
    let response = await getStartAndInactivatedDateForTheWorkers(deepClone(otherRequestArgs), whereClause, whereClauseValue);
    let weekDifference: number;
    let locResponse = deepClone(lengthOfServiceResponse);

    for (let element of response) {
        let workerEndDate = element.in_actived_at && element.in_actived_at > endDate ? element.in_actived_at : endDate;
        weekDifference = getWeeksOfTwoDates(element.start_date, workerEndDate);
        locResponse = getLengthOfServiceWeeks(weekDifference, locResponse);
    }

    return {
        "ok": true,
        "result": {
            "rows": locResponse
        },
        "is_data_available": locResponse.length ? true : false
    }
}


/**
 * Get pool utilization data as per calculating data of number of workers from the T&A sheet (For currently active workers)
 * @param  {} requestArgs
 */
export const getWorkForcePoolUtilizationService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { start_date, end_date, ...otherRequestArgs } = updatedPayload;
    let activeWorkersObject = _.cloneDeep(otherRequestArgs);
    const diff = moment(end_date).diff(moment(start_date), 'days') + 1;
    let isPastRangeValueRequired = getWeeksOfTwoDates(start_date, end_date) === 1 ? true : false;
    const lastStartDate = moment(start_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
    const lastEndDate = moment(end_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);

    let totalWorkerWhere = getWorkerWhereClauseStringForTotalWorkers(otherRequestArgs, "workers", 2, start_date, end_date);

    let { total_count } = await getWorkForcePoolUtilizationTotalWorkers(
        updatedPayload,
        totalWorkerWhere.whereClause,
        totalWorkerWhere.whereClauseValue,
        1,
        start_date,
        end_date
    );

    let { "total_count": past_total_count } = await getWorkForcePoolUtilizationTotalWorkers(
        updatedPayload,
        totalWorkerWhere.whereClause,
        totalWorkerWhere.whereClauseValue,
        1,
        lastStartDate,
        lastEndDate
    );

    if (parseInt(total_count) === 0) {
        return {
            "ok": true,
            "result": {
                current_range: {
                    "total_count": 0, "active_count": 0
                },
                last_range: {
                    "total_count": 0, "active_count": 0
                },
                "is_past_range_value_required": isPastRangeValueRequired
            },
            "is_data_available": true
        }
    }
    let activeWorkersWhere = getWorkerWhereClauseStringForActiveWorkers(activeWorkersObject);

    const { active_workers_current, active_workers_last } = await EasyPromiseAll({
        active_workers_current: getWorkForcePoolUtilizationActiveWorkers(activeWorkersWhere.whereClause, activeWorkersWhere.whereClauseValue, start_date, end_date),
        active_workers_last: isPastRangeValueRequired ? getWorkForcePoolUtilizationActiveWorkers(activeWorkersWhere.whereClause, activeWorkersWhere.whereClauseValue, lastStartDate, lastEndDate) : {}
    });
    return {
        "ok": true,
        "result": {
            current_range: {
                "total_count": parseInt(total_count), "active_count": parseInt(active_workers_current.active_workers) || 0
            },
            last_range: {
                "total_count": parseInt(past_total_count), "active_count": parseInt(active_workers_last.active_workers) || 0
            },
            "is_past_range_value_required": isPastRangeValueRequired
        },
        "is_data_available": true
    }
}


/**
 * Get pool utilization data as per calculating data of number of workers from the T&A sheet (For currently inactive workers)
 * @param  {} requestArgs
 */
export const getLeaverPoolUtilizationService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { start_date, end_date, ...otherRequestArgs } = updatedPayload;
    let inactiveWorkersObject = _.cloneDeep(otherRequestArgs);
    let totalWorkerWhere = getWorkerWhereClauseStringForTotalWorkers(otherRequestArgs);
    let { total_count } = await getWorkForcePoolUtilizationTotalWorkers(updatedPayload, totalWorkerWhere.whereClause, totalWorkerWhere.whereClauseValue, 0);
    if (parseInt(total_count) === 0) {
        return {
            "ok": true,
            "result": {
                current_range: {
                    "total_count": 0, "inactive_count": 0
                }
            },
            "is_data_available": true
        }
    }

    let activeWorkersWhere = getWorkerWhereClauseStringForActiveWorkers(inactiveWorkersObject);
    const inactive_workers_current = await getPoolUtilizationInactiveWorkers(activeWorkersWhere.whereClause, activeWorkersWhere.whereClauseValue, start_date, end_date);
    return {
        "ok": true,
        "result": {
            current_range: {
                "total_count": parseInt(total_count), "inactive_count": parseInt(inactive_workers_current.inactive_workers) || 0
            }
        },
        "is_data_available": true
    }
}


/*
    Leavers: Top-Deck
*/


/**
 * Get length of service data.
 * - Consider start date and inactivate date of the workers and calculate length of service as per the week duration
 * @param  {} requestArgs
 */
export const getLeaversLengthOfServService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 0, 'workers', startDate, endDate, true);
    let response = await getStartAndInactivatedDateForTheWorkers(otherRequestArgs, whereClause, whereClauseValue);
    let weekDifference: number;
    let locResponse = deepClone(lengthOfServiceResponse);

    for (let element of response) {
        weekDifference = getWeeksOfTwoDates(element.start_date, element.in_actived_at);
        locResponse = getLengthOfServiceWeeks(weekDifference, locResponse);
    }

    return {
        "ok": true,
        "result": {
            "rows": locResponse
        },
        "is_data_available": locResponse.length ? true : false
    }
}


/**
 * Get worker counts who have marked inactivated in provided date range
 * @param  {} requestArgs
 */
export const getLeaversCountAndStarterRetentionService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let where = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, "workers");
    let [inactiveWorkersCount, activatedWorkersCount] = await getNewStarterRetentionData(
        otherRequestArgs, where.whereClause, where.whereClauseValue, startDate, endDate);

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 0, 'workers', startDate, endDate, false);
    let isPastRangeValueRequired = getWeeksOfTwoDates(startDate, endDate) === 1 ? true : false;

    return {
        "ok": true,
        "result": {
            "is_past_range_value_required": isPastRangeValueRequired,
            "leavers_count": {
                "current_value": parseInt(await getWorkersLeaversCountByDateRange(
                    deepClone(otherRequestArgs),
                    whereClause,
                    whereClauseValue,
                    startDate,
                    endDate
                )),
                "past_value": isPastRangeValueRequired ? parseInt(await getWorkersLeaversCountByDateRange(
                    deepClone(otherRequestArgs),
                    whereClause,
                    whereClauseValue,
                    moment(startDate, dateTimeFormates.YYYYMMDD).subtract(7, 'days').format(dateTimeFormates.YYYYMMDD),
                    moment(endDate, dateTimeFormates.YYYYMMDD).subtract(7, 'days').format(dateTimeFormates.YYYYMMDD)
                )) : 0
            },
            "new_starter_retention": {
                "active": parseInt(activatedWorkersCount || 0) - parseInt(inactiveWorkersCount || 0),
                "inactive": parseInt(inactiveWorkersCount || 0)
            }
        },
        "is_data_available": true
    }
}


/**
 * Get day wise completed shift data for the workers from the T&A sheet (For active workers)
 * @param  {} requestArgs
 */
export const getLeaversShiftUtilizationService = async (requestArgs, user) => {
    return getShiftUtilisationDetails(requestArgs, 0, user);
}


/*
    Leavers: Bottom-Deck
*/


/**
 * Get agency wise length of service of the workers.
 * - Consider start date of the workers and calculate length of service as per the week duration
 * - Parse that data agency wise
 * @param  {} requestArgs
 */
export const getAgencyWiseLeaversLOSService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;
    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 0, 'workers', startDate, endDate, true);

    let dbResponse = await getStartAndInactivatedDateForTheAgencyWiseWorkers(
        deepClone(otherRequestArgs),
        whereClause,
        whereClauseValue
    );

    let response = await processAgencyWiseLengthOfService(dbResponse, otherRequestArgs, true);

    return {
        "ok": true,
        "result": response,
        "is_data_available": response.agencies.length ? true : false
    }
}

/**
 * Get agency wise inactivated workers count as per provided date range
 * @param  {} requestArgs
 */
export const getAgencyWiseLeaversCountAndStarterRetentionService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let { whereClause, whereClauseValue } = getWhereClauseString(deepClone(otherRequestArgs), 'agency_client_association');
    let agencyList = await getAssociatedAgenciesList(
        otherRequestArgs,
        whereClause,
        whereClauseValue
    );

    let where = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, "workers");
    let [inactiveWorkersCount, activatedWorkersCount] = await getAgencyWiseNewStarterRetentionData(
        otherRequestArgs, where.whereClause, where.whereClauseValue, startDate, endDate);


    let agencyWiseNewStarterRetention = {};
    let response = [];

    if (agencyList.length) {
        let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, "workers");
        let inactivatedWorkersPerAgencyByStartDate = await getInactivatedWorkersPerAgencyByStartDate(otherRequestArgs, whereClause, whereClauseValue, startDate, endDate);

        inactivatedWorkersPerAgencyByStartDate.forEach((element) => {
            agencyWiseNewStarterRetention[element.agency_id] = parseInt(element.inactive_workers_count)
        })

        agencyList.forEach((element) => {
            let active = activatedWorkersCount.find(x => x.agency_id == element.agency_detail_id);
            let inactive = inactiveWorkersCount.find(x => x.agency_id == element.agency_detail_id);
            response.push({
                label: element.agency_name,
                active: (parseInt(active?.active_workers_count) || 0) - (parseInt(inactive?.inactive_workers_count) || 0),
                in_active: parseInt(inactive?.inactive_workers_count) || 0,
                count: agencyWiseNewStarterRetention[element.agency_detail_id] || 0
            });
        });
    }

    return {
        "ok": true,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}


/**
 * Get calculate shift utilization for the workers from the T&A sheet (For inactive workers) and parse them agency wise
 *  @param  {} requestArgs
 */
export const getAgencyWiseLeaversShiftUtilizationService = async (requestArgs, loggedInUser) => {

    let response = await getDayWiseShiftUtilisationDetails(requestArgs, loggedInUser, 0)

    return {
        "ok": true,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}


/**
 * Get agency wise inactivated workers count for the provided date range
 * @param  {} requestArgs
 */
export const getLeaversService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;
    let response = [];
    let total = 0;

    let where = getWhereClauseString(deepClone(otherRequestArgs), 'agency_client_association');
    let agencyList = await getAssociatedAgenciesList(otherRequestArgs, where.whereClause, where.whereClauseValue);

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 0, 'workers', startDate, endDate, false);
    if (agencyList.length) {
        let leaversDetails = await getWorkersLeaversDetails(
            updatedPayload,
            whereClause,
            whereClauseValue,
            startDate, endDate);
        let leaversDetailsObject = Object.assign({}, ...(leaversDetails.map(item => ({ [item.agency_id]: item.workers_count }))));

        agencyList.forEach((element) => {
            total += parseInt(leaversDetailsObject[element.agency_detail_id] || 0)
            response.push({
                "agency_name": element.agency_name,
                "inactive": parseInt(leaversDetailsObject[element.agency_detail_id] || 0)
            })
        });
    }


    return {
        "ok": true,
        total,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}


const processAgencyWiseLengthOfService = async (dbResponse: any, otherRequestArgs: any, requestForInactivatedWorkers = false, endDate: string = null) => {
    let { whereClause, whereClauseValue } = getWhereClauseString(deepClone(otherRequestArgs), 'agency_client_association');
    let agencyList = await getAssociatedAgenciesList(otherRequestArgs, whereClause, whereClauseValue);

    let response = {
        "agencies": [],
        "rows": []
    };

    let lengthOfServiceData = {
        '1-2': {},
        '3-4': {},
        '5-8': {},
        '9-12': {},
        '13-16': {},
        '17-26': {},
        '27-52': {},
        '52+': {}
    }

    for (let element of dbResponse) {
        let workerEndDate = endDate ? (element.in_actived_at && element.in_actived_at > endDate ? element.in_actived_at : endDate) : element.in_actived_at;
        let weekDifference = requestForInactivatedWorkers ? getWeeksOfTwoDates(element.start_date, element.in_actived_at) : getWeeksOfTwoDates(element.start_date, workerEndDate);

        if (!(weekDifference >= 0)) continue;  // Skip element if start-date of worker is after inactivated date

        let losKey = dayRangeAsPerDayCount(weekDifference);

        if (lengthOfServiceData[losKey].hasOwnProperty(element.worker_agency_id)) {
            lengthOfServiceData[losKey][element.worker_agency_id] += 1;
        } else {
            lengthOfServiceData[losKey][element.worker_agency_id] = 1;
        }
    }

    for (let key in lengthOfServiceData) {

        let data = [];
        for (let agency of agencyList) {

            data.push(
                lengthOfServiceData[key] && lengthOfServiceData[key][agency.agency_detail_id] ? lengthOfServiceData[key][agency.agency_detail_id] : 0
            )
        }
        response.rows.push({
            'name': key,
            'data': data
        });
    }

    agencyList.forEach((agency) => {
        response.agencies.push(agency.agency_name);
    })

    return response;
}

/*
    WorkForce: Bottom-Deck
*/

/**
 * Get agency wise length of service of the workers.
 * - Consider start date of the workers and calculate length of service as per the week duration
 * - Parse that data agency wise
 * @param  {} requestArgs
 */
export const getAgencyWiseWorkForceLengthOfServService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), 2, 'workers', startDate, endDate);
    let dbResponse = await getStartAndInactivatedDateForTheAgencyWiseWorkers(deepClone(otherRequestArgs), whereClause, whereClauseValue);

    let response = await processAgencyWiseLengthOfService(dbResponse, otherRequestArgs, false, endDate);

    return {
        "ok": true,
        "result": response,
        "is_data_available": response.agencies.length ? true : false
    }
}


/**
 * Get agency wise nationality wise demographics data
 * @param  {} requestArgs
 */
export const getAgencyWiseWorkForceDemoGraphicsService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(updatedPayload))
    let dbResponse = await getAgencyWiseWorkerDemographicsDetails(updatedPayload, whereClause, whereClauseValue);

    let data = {};
    let uniqueAgencies = [];
    let nationalities = [];

    dbResponse.forEach(element => {
        if (!data.hasOwnProperty(element.nationality)) {
            data[element.nationality] = {
                [element.agency_detail]: element.value
            }
        } else {
            data[element.nationality][element.agency_detail] = element.value;
        }

        if (!uniqueAgencies.includes(element.agency_detail)) {
            uniqueAgencies.push(element.agency_detail);
        }

        if (!nationalities.includes(element.nationality)) {
            nationalities.push(element.nationality);
        }
    });

    let responseData = [];
    uniqueAgencies = uniqueAgencies.sort();

    nationalities.forEach((nationality) => {
        let nationalityWiseData = [];
        uniqueAgencies.forEach((agency) => {
            nationalityWiseData.push({
                "name": agency.split(databaseSeparator)[1],
                "value": data[nationality][agency] ? parseInt(data[nationality][agency]) : 0
            })
        })

        responseData.push({
            "label": nationality,
            "value": nationalityWiseData
        })
    });

    return {
        "ok": true,
        "result": {
            "rows": responseData
        },
        "is_data_available": responseData.length ? true : false
    }
}

/**
 * Get agency wise shift utilization of the workers. Return number of workers who worked for 1-3 and 4+ days in a week.
 * @param  {} requestArgs
 */
export const getAgencyWiseWorkShiftUtilizationService = async (requestArgs, loggedInUser) => {

    let response = await getDayWiseShiftUtilisationDetails(requestArgs, loggedInUser, 1)

    return {
        "ok": true,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}


/*
    Activity: Top-Deck
*/

/**
 * Get activity stats related data
 * - Shift fulfillment
 * - Shift lost
 * - Total spend
 * - Total hours
 * @param  {} requestArgs
 */
export const getActivityAllStatsService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, "shift_id": shift_id, ...otherRequestArgs } = updatedPayload;
    const diff = moment(endDate).diff(moment(startDate), 'days') + 1;
    const lastStartDate = moment(startDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
    const lastEndDate = moment(endDate).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);

    const { whereClause, whereClauseValue } = objectToMySQLWhereClause(otherRequestArgs);
    let whereClauseCurrentBK = `${whereClause} AND bk.startDate >= :start_date AND bk.endDate <= :end_date `;
    let whereClauseValueCurrentBK = { ...whereClauseValue, "start_date": startDate, "end_date": endDate }
    let whereClauselastBK = `${whereClause}  AND bk.startDate >= :last_start_date AND bk.endDate <= :last_end_date `;
    let whereClauseValuelastBK = { ...whereClauseValue, "last_start_date": lastStartDate, "last_end_date": lastEndDate }

    const { whereClauseForTA, whereClauseValueForTA } = objectToMySQLConditionStringForTADataWhereClause(otherRequestArgs);
    let whereClauseCurrentTAData = `${whereClauseForTA} AND tadata.startDate >= :start_date AND tadata.endDate <= :end_date `;
    let whereClauseValueCurrentTAData = { ...whereClauseValueForTA, "start_date": startDate, "end_date": endDate }
    let whereClauseLastTAData = `${whereClauseForTA} AND tadata.startDate >= :last_start_date AND tadata.endDate <= :last_end_date `;
    let whereClauseValueLastTAData = { ...whereClauseValueForTA, "last_start_date": lastStartDate, "last_end_date": lastEndDate }

    if (shift_id) {
        whereClauseCurrentBK += `AND shift_type_id = :shift_id`;
        whereClauselastBK += `AND shift_type_id = :shift_id`;
        whereClauseCurrentTAData += `AND shift_id = :shift_id`;
        whereClauseLastTAData += `AND shift_id = :shift_id`;

        whereClauseValueCurrentBK["shift_id"] = shift_id;
        whereClauseValuelastBK["shift_id"] = shift_id;
        whereClauseValueCurrentTAData["shift_id"] = shift_id;
        whereClauseValueLastTAData["shift_id"] = shift_id;

    }
    let isPastRangeValueRequired = getWeeksOfTwoDates(startDate, endDate) === 1 ? true : false;
    const { shiftFulfilledCurrent, shiftFulfilledLast, standardOvertimePayAndHourCurrent, standardOvertimePayAndHourLast } = await EasyPromiseAll({
        shiftFulfilledCurrent: getFulfilmentAndLossCount(whereClauseCurrentBK, whereClauseValueCurrentBK),
        shiftFulfilledLast: isPastRangeValueRequired ? getFulfilmentAndLossCount(whereClauselastBK, whereClauseValuelastBK) : {},
        standardOvertimePayAndHourCurrent: getStandardAndOvertimeHourAndPay(whereClauseCurrentTAData, whereClauseValueCurrentTAData),
        standardOvertimePayAndHourLast: isPastRangeValueRequired ? getStandardAndOvertimeHourAndPay(whereClauseLastTAData, whereClauseValueLastTAData) : []
    });

    // Initialize to zero.
    let [standardTotal, standardHour, bhTotal, bhHour, suspensionTotal, suspensionHour, supervisorStandardTotal, supervisorStandardHour, supervisorOvertimeTotal, supervisorOvertimeHour, supervisorPermanentTotal, supervisorPermanentHour, nspTotal, nspHour, inductionTrainingTotal, inductionTrainingHour, trainingTotal, trainingHour, overtimeTotal, overtimeHour, standardBonusTotal, standardBonusHour, specialBonusTotal, specialBonusHour, weekendTotal, weekendHour, expensesTotal, expensesHour, holidayTotal, wtrCosts] = Array(30).fill(0);
    let [standardTotalLast, standardHourLast, bhTotalLast, bhHourLast, suspensionTotalLast, suspensionHourLast, supervisorStandardTotalLast, supervisorStandardHourLast, supervisorOvertimeTotalLast, supervisorOvertimeHourLast, supervisorPermanentTotalLast, supervisorPermanentHourLast, nspTotalLast, nspHourLast, inductionTrainingTotalLast, inductionTrainingHourLast, trainingTotalLast, trainingHourLast, overtimeTotalLast, overtimeHourLast, standardBonusTotalLast, standardBonusHourLast, specialBonusTotalLast, specialBonusHourLast, weekendTotalLast, weekendHourLast, expensesTotalLast, expensesHourLast, holidayTotalLast, wtrCostsLast] = Array(30).fill(0);
    let [totalSupervisorsHours, totalNonSupervisorsHours, totalSupervisorsHoursLast, totalNonSupervisorsHoursLast] = Array(4).fill(0);
    _.map(standardOvertimePayAndHourCurrent, (s) => {
        if (s.pay_type === PayTypes.STANDARD) {
            standardTotal = s.total;
            standardHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.BH || s.pay_type === PayTypes.COVID) {
            bhTotal = s.total;
            bhHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUSPENSION) {
            suspensionTotal = s.total;
            suspensionHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_STANDARD) {
            supervisorStandardTotal = s.total;
            supervisorStandardHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_OVERTIME) {
            supervisorOvertimeTotal = s.total;
            supervisorOvertimeHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_PERMANENT) {
            supervisorPermanentTotal = s.total;
            supervisorPermanentHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.NSP || s.pay_type === PayTypes.SP) {
            nspTotal = s.total;
            nspHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.INDUCTION_TRAINING) {
            inductionTrainingTotal = s.total;
            inductionTrainingHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.TRAINING) {
            trainingTotal = s.total;
            trainingHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.STANDARD_BONUS) {
            standardBonusTotal = s.total;
            standardBonusHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SPECIAL_BONUS) {
            specialBonusTotal = s.total;
            specialBonusHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.OVERTIME) {
            overtimeTotal = s.total;
            overtimeHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.WEEKEND) {
            weekendTotal = s.total;
            weekendHour = s.weekly_hours;
        } else if (s.pay_type === PayTypes.HOLIDAY) {
            holidayTotal = s.total;
            wtrCosts = s.wtr_costs
        } else if (s.pay_type === PayTypes.EXPENSES) {
            expensesTotal = s.total;
            expensesHour = s.weekly_hours;
        }

        const isSupervisorPaytype = [PayTypes.SUPERVISOR_STANDARD, PayTypes.SUPERVISOR_OVERTIME, PayTypes.SUPERVISOR_PERMANENT].includes(s.pay_type);
        const isPaytypesToExclude = [PayTypes.SP, PayTypes.NSP, PayTypes.SPECIAL_BONUS, PayTypes.STANDARD_BONUS, PayTypes.HOLIDAY, PayTypes.EXPENSES].includes(s.pay_type);

        if (isSupervisorPaytype) { totalSupervisorsHours += Number(s.weekly_hours) }
        else if (!isPaytypesToExclude) { totalNonSupervisorsHours += Number(s.weekly_hours) }
    });

    _.map(standardOvertimePayAndHourLast, (s) => {
        if (s.pay_type === PayTypes.STANDARD) {
            standardTotalLast = s.total;
            standardHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.BH || s.pay_type === PayTypes.COVID) {
            bhTotalLast = s.total;
            bhHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUSPENSION) {
            suspensionTotalLast = s.total;
            suspensionHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_STANDARD) {
            supervisorStandardTotalLast = s.total;
            supervisorStandardHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_OVERTIME) {
            supervisorOvertimeTotalLast = s.total;
            supervisorOvertimeHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SUPERVISOR_PERMANENT) {
            supervisorPermanentTotalLast = s.total;
            supervisorPermanentHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.NSP || s.pay_type === PayTypes.SP) {
            nspTotalLast = s.total;
            nspHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.INDUCTION_TRAINING) {
            inductionTrainingTotalLast = s.total;
            inductionTrainingHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.TRAINING) {
            trainingTotalLast = s.total;
            trainingHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.STANDARD_BONUS) {
            standardBonusTotalLast = s.total;
            standardBonusHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.SPECIAL_BONUS) {
            specialBonusTotalLast = s.total;
            specialBonusHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.OVERTIME) {
            overtimeTotalLast = s.total;
            overtimeHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.WEEKEND) {
            weekendTotalLast = s.total;
            weekendHourLast = s.weekly_hours;
        } else if (s.pay_type === PayTypes.HOLIDAY) {
            holidayTotalLast = s.total;
            wtrCostsLast = s.wtr_costs
        } else if (s.pay_type === PayTypes.EXPENSES) {
            expensesTotalLast = s.total;
            expensesHourLast = s.weekly_hours;
        }

        const isSupervisorPaytype = [PayTypes.SUPERVISOR_STANDARD, PayTypes.SUPERVISOR_OVERTIME, PayTypes.SUPERVISOR_PERMANENT].includes(s.pay_type);
        const isPaytypesToExclude = [PayTypes.SP, PayTypes.NSP, PayTypes.SPECIAL_BONUS, PayTypes.STANDARD_BONUS, PayTypes.HOLIDAY, PayTypes.EXPENSES].includes(s.pay_type);

        if (isSupervisorPaytype) { totalSupervisorsHoursLast += Number(s.weekly_hours) }
        else if (!isPaytypesToExclude) { totalNonSupervisorsHoursLast += Number(s.weekly_hours) }
    });

    const requestedCurrent = parseInt(shiftFulfilledCurrent.requested_total || 0);
    const fulfilledCurrent = parseInt(shiftFulfilledCurrent.fulfilled_total || 0);
    const requestedSupervisorsCurrent = parseInt(shiftFulfilledCurrent.requested_supervisors_total || 0);
    const fulfilledSupervisorsCurrent = parseInt(shiftFulfilledCurrent.fulfilled_supervisors_total || 0);

    const requestedLast = parseInt(shiftFulfilledLast.requested_total || 0);
    const fulfilledLast = parseInt(shiftFulfilledLast.fulfilled_total || 0);
    const requestedSupervisorsLast = parseInt(shiftFulfilledCurrent.requested_supervisors_total || 0);
    const fulfilledSupervisorsLast = parseInt(shiftFulfilledCurrent.fulfilled_supervisors_total || 0);

    return {
        "ok": true,
        "result": {
            "is_past_range_value_required": isPastRangeValueRequired,
            "shift_fullfilment": {
                "current_overall_range": {
                    "fulfilled": fulfilledCurrent,
                    "requested": requestedCurrent,
                    "lost": Math.max(requestedCurrent - fulfilledCurrent, 0),
                    "percentage": requestedCurrent !== 0 ? Math.min(((fulfilledCurrent / requestedCurrent) * 100), 100).toFixed(2) : 0,
                },
                "past_overall_range": {
                    "fulfilled": fulfilledLast,
                    "requested": requestedLast,
                    "lost": Math.max(requestedLast - fulfilledLast, 0),
                    "percentage": requestedLast !== 0 ? Math.min(((fulfilledLast / requestedLast) * 100), 100).toFixed(2) : 0,
                },
                "current_supervisors_range": {
                    "fulfilled": fulfilledSupervisorsCurrent,
                    "requested": requestedSupervisorsCurrent,
                    "lost": Math.max(requestedSupervisorsCurrent - fulfilledSupervisorsCurrent, 0),
                    "percentage": requestedSupervisorsCurrent !== 0 ? Math.min(((fulfilledSupervisorsCurrent / requestedSupervisorsCurrent) * 100), 100).toFixed(2) : 0,
                },
                "past_supervisors_range": {
                    "fulfilled": fulfilledSupervisorsLast,
                    "requested": requestedSupervisorsLast,
                    "lost": Math.max(requestedSupervisorsLast - fulfilledSupervisorsLast, 0),
                    "percentage": requestedSupervisorsLast !== 0 ? Math.min(((fulfilledSupervisorsLast / requestedSupervisorsLast) * 100), 100).toFixed(2) : 0,
                },
                "current_hours": {
                    "totalNonSupervisorsHours": totalNonSupervisorsHours.toFixed(2),
                    "totalSupervisorsHours": totalSupervisorsHours.toFixed(2),
                    "output": totalSupervisorsHours != 0 ? (totalNonSupervisorsHours / totalSupervisorsHours).toFixed(2) : 0
                },
                "past_hours": {
                    "totalNonSupervisorsHoursLast": totalNonSupervisorsHoursLast.toFixed(2),
                    "totalSupervisorsHoursLast": totalSupervisorsHoursLast.toFixed(2),
                    "output": totalSupervisorsHoursLast != 0 ? (totalNonSupervisorsHoursLast / totalSupervisorsHoursLast).toFixed(2) : 0
                }
            },
            "shift_lost": {
                "current_range": {
                    "count": Math.max(requestedCurrent - fulfilledCurrent, 0)
                },
                "past_range": {
                    "count": Math.max(requestedLast - fulfilledLast, 0)
                }
            },
            "total_spent": {
                "current_range": {
                    "standardTotal": Number(standardTotal),
                    "bhTotal": Number(bhTotal),
                    "suspensionTotal": Number(suspensionTotal),
                    "supervisorStandardTotal": Number(supervisorStandardTotal),
                    "supervisorOvertimeTotal": Number(supervisorOvertimeTotal),
                    "supervisorPermanentTotal": Number(supervisorPermanentTotal),
                    "nspTotal": Number(nspTotal),
                    "inductionTrainingTotal": Number(inductionTrainingTotal),
                    "trainingTotal": Number(trainingTotal),
                    "overtimeTotal": Number(overtimeTotal),
                    "standardBonusTotal": Number(standardBonusTotal),
                    "specialBonusTotal": Number(specialBonusTotal),
                    "weekendTotal": Number(weekendTotal),
                    "holidayTotal": Number(holidayTotal),
                    "expensesTotal": Number(expensesTotal),
                    "wtrCosts": Number(wtrCosts),
                },
                "past_range": {
                    "standardTotal": Number(standardTotalLast),
                    "bhTotal": Number(bhTotalLast),
                    "suspensionTotal": Number(suspensionTotalLast),
                    "supervisorStandardTotal": Number(supervisorStandardTotalLast),
                    "supervisorOvertimeTotal": Number(supervisorOvertimeTotalLast),
                    "supervisorPermanentTotal": Number(supervisorPermanentTotalLast),
                    "nspTotal": Number(nspTotalLast),
                    "inductionTrainingTotal": Number(inductionTrainingTotalLast),
                    "trainingTotal": Number(trainingTotalLast),
                    "overtimeTotal": Number(overtimeTotalLast),
                    "standardBonusTotal": Number(standardBonusTotalLast),
                    "specialBonusTotal": Number(specialBonusTotalLast),
                    "weekendTotal": Number(weekendTotalLast),
                    "holidayTotal": Number(holidayTotalLast),
                    "expensesTotal": Number(expensesTotalLast),
                    "wtrCosts": Number(wtrCostsLast),
                }
            },
            "total_hours": {
                "current_range": {
                    "standardHour": Number(standardHour),
                    "bhHour": Number(bhHour),
                    "supervisorStandardHour": Number(supervisorStandardHour),
                    "supervisorOvertimeHour": Number(supervisorOvertimeHour),
                    "supervisorPermanentHour": Number(supervisorPermanentHour),
                    "nspHour": Number(nspHour),
                    "inductionTrainingHour": Number(inductionTrainingHour),
                    "trainingHour": Number(trainingHour),
                    "overtimeHour": Number(overtimeHour),
                    "suspensionHour": Number(suspensionHour),
                    "standardBonusHour": Number(standardBonusHour),
                    "specialBonusHour": Number(specialBonusHour),
                    "weekendHour": Number(weekendHour),
                },
                "past_range": {
                    "standardHour": Number(standardHourLast),
                    "bhHour": Number(bhHourLast),
                    "supervisorStandardHour": Number(supervisorStandardHourLast),
                    "supervisorOvertimeHour": Number(supervisorOvertimeHourLast),
                    "supervisorPermanentHour": Number(supervisorPermanentHourLast),
                    "nspHour": Number(nspHourLast),
                    "inductionTrainingHour": Number(inductionTrainingHourLast),
                    "trainingHour": Number(trainingHourLast),
                    "overtimeHour": Number(overtimeHourLast),
                    "suspensionHour": Number(suspensionHourLast),
                    "standardBonusHour": Number(standardBonusHourLast),
                    "specialBonusHour": Number(specialBonusHourLast),
                    "weekendHour": Number(weekendHourLast),
                }
            }
        },
        "is_data_available": true
    }
}

/**
 * Get agency wise shift completion details from the booking association table
 * @param  {} requestArgs
 */
export const getActivityShiftDetailsService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let where = getWhereClauseString(deepClone(otherRequestArgs), 'agency_client_association');
    let agencyList = await getAssociatedAgenciesList(otherRequestArgs, where.whereClause, where.whereClauseValue);

    if (otherRequestArgs.shift_id) {
        otherRequestArgs = removeKeyFromObject("shift_id", "shift_type_id", otherRequestArgs);
    }

    let { whereClause, whereClauseValue } = objectToMySQLWhereClause(otherRequestArgs);
    let shiftFulfillmentDetails = await getShiftFulfillmentFromBookingAssociation(whereClause, whereClauseValue, startDate, endDate);

    let agencyDetails = {};
    let agencyIds = [];
    let agencyWiseFulfilledResponse = {};
    let agencyWiseTotalShifts = {};
    let response = [];

    agencyList.forEach((element) => {
        agencyDetails[element.agency_detail_id] = {
            name: element.agency_name
        }

        agencyIds.push(element.agency_detail_id);
    });

    shiftFulfillmentDetails.forEach(element => {
        agencyWiseFulfilledResponse[element.agency_id] = (agencyWiseFulfilledResponse[element.agency_id] || 0) + parseInt(element.fulfilled_total || 0);
        agencyWiseTotalShifts[element.agency_id] = (agencyWiseTotalShifts[element.agency_id] || 0) + parseInt(element.requested_total || 0);
    });

    agencyIds.forEach((agencyId) => {
        response.push({
            label: agencyDetails[agencyId].name,
            lost_count: (agencyWiseTotalShifts[agencyId] - agencyWiseFulfilledResponse[agencyId]) ? agencyWiseTotalShifts[agencyId] - agencyWiseFulfilledResponse[agencyId] : 0,
            fulfilled_count: agencyWiseFulfilledResponse[agencyId] ? agencyWiseFulfilledResponse[agencyId] : 0
        })
    })

    return {
        "ok": true,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}

/**
 * Get agency wise worked workers count for the selected date range
 * @param  {} requestArgs
 */
export const getActivityHeadCountService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date, end_date, ...otherArgs } = updatedPayload;

    let where = getWhereClauseString(deepClone(otherArgs), 'agency_client_association');

    let agencyList = await getAssociatedAgenciesList(otherArgs, where.whereClause, where.whereClauseValue);
    let response = [];

    if (agencyList) {

        let where = getWorkerWhereClauseString(deepClone(otherArgs), 2, 'workers', start_date, end_date);

        let totalWorkerDetails = await getTotalWorkers(
            deepClone(updatedPayload),
            where.whereClause,
            where.whereClauseValue
        );

        let totalWorkerDetailsObject = Object.assign({}, ...(totalWorkerDetails.map(item => ({ [item.agency_id]: parseInt(item.workers_count) }))));

        let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherArgs), null, "time_and_attendance_data");
        let agencyWiseActiveWorkers = await getTADataAvailableWorkers(
            whereClause,
            whereClauseValue,
            start_date,
            end_date
        );
        let agencyWiseActiveWorkersObject = Object.assign({}, ...(agencyWiseActiveWorkers.map(item => ({ [item.agency_id]: parseInt(item.active_workers) }))));

        agencyList.forEach((item) => {
            response.push({
                "label": item.agency_name,
                "active": agencyWiseActiveWorkersObject[item.agency_detail_id] || 0,
                "inactive": ((totalWorkerDetailsObject[item.agency_detail_id] || 0) - (agencyWiseActiveWorkersObject[item.agency_detail_id] || 0)) <= 0 ? 0 : (totalWorkerDetailsObject[item.agency_detail_id] || 0) - (agencyWiseActiveWorkersObject[item.agency_detail_id] || 0),
                "total": totalWorkerDetailsObject[item.agency_detail_id] || 0
            })
        })
    }

    return {
        "ok": true,
        "result": {
            "rows": response
        },
        "is_data_available": response.length ? true : false
    }
}


/**
 * Get agency wise total spend for the selected date range from T&A data
 * @param  {} requestArgs
 */
export const getActivitySpendService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { start_date, end_date, ...otherArgs } = updatedPayload;

    let where = getWhereClauseString(deepClone(otherArgs), 'agency_client_association')
    let agencyList = await getAssociatedAgenciesList(otherArgs, where.whereClause, where.whereClauseValue);
    let response = [];
    let isPastRangeValueRequired = getWeeksOfTwoDates(start_date, end_date) === 1 ? true : false;
    let currentTotal = 0, lastTotal = 0;

    if (agencyList.length > 0) {
        const diff = moment(end_date).diff(moment(start_date), 'days') + 1;
        const lastStartDate = moment(start_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
        const lastEndDate = moment(end_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
        let { whereClause, whereClauseValue } = getWhereClauseString(deepClone(otherArgs), "time_and_attendance_data", false);
        let { activityTotalSpendDetails, activityTotalSpendDetailsLast } = await EasyPromiseAll({
            activityTotalSpendDetails: getActivityTotalSpendByAgencyHelper(start_date, end_date, whereClause, whereClauseValue),
            activityTotalSpendDetailsLast: getActivityTotalSpendByAgencyHelper(lastStartDate, lastEndDate, whereClause, whereClauseValue),
        });

        agencyList.forEach((element) => {
            let obj = {};
            obj["label"] = element.agency_name;
            let objForCount = activityTotalSpendDetails.find(o => o.label === element.agency_detail_id);
            if (objForCount) {
                obj["count"] = objForCount.count;
            }
            else {
                obj["count"] = 0;
            }
            response.push(obj);
        });
        currentTotal = _.sumBy(activityTotalSpendDetails, 'count');
        lastTotal = _.sumBy(activityTotalSpendDetailsLast, 'count');
    }
    else {
        response = [];
    }

    return {
        "ok": true,
        "result": {
            "is_past_range_value_required": isPastRangeValueRequired,
            current_value: currentTotal,
            past_value: lastTotal,
            rows: response
        },
        "is_data_available": response.length ? true : false
    }
}


/**
 * Get agency wise average hours for the selected date range from T&A data
 * @param  {} requestArgs
 */
export const getActivityAverageHoursService = async (requestArgs, loggedInUser) => {

    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;
    const { start_date, end_date, ...otherArgs } = updatedPayload;

    const where = getWhereClauseString(deepClone(otherArgs), 'agency_client_association');
    const agencyList = await getAssociatedAgenciesList(otherArgs, where.whereClause, where.whereClauseValue);

    const response = [];
    const isPastRangeValueRequired = getWeeksOfTwoDates(start_date, end_date) === 1;
    let currentTotal = 0, lastTotal = 0;

    if (agencyList.length > 0) {
        const diff = moment(end_date).diff(moment(start_date), 'days') + 1;
        const lastStartDate = moment(start_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
        const lastEndDate = moment(end_date).subtract(diff, "days").format(dateTimeFormates.YYYYMMDD);
        const { whereClause, whereClauseValue } = getWhereClauseString(deepClone(otherArgs), "time_and_attendance_data", false);

        const [activityTotalHoursDetails, activityTotalHoursDetailsLast] = await Promise.all([
            getWorkersWorkingHours(start_date, end_date, whereClause, whereClauseValue),
            getWorkersWorkingHours(lastStartDate, lastEndDate, whereClause, whereClauseValue)
        ]);

        agencyList.forEach((element) => {
            const obj = {
                label: element.agency_name,
                count: 0
            };
            const objForCount = activityTotalHoursDetails.find(o => o.agency_id === element.agency_detail_id);
            if (objForCount) {
                obj.count = objForCount.weekly_hours;
            }
            response.push(obj);
        });

        currentTotal = _.sumBy(activityTotalHoursDetails, 'weekly_hours');
        lastTotal = _.sumBy(activityTotalHoursDetailsLast, 'weekly_hours');
    }

    return {
        ok: true,
        result: {
            is_past_range_value_required: isPastRangeValueRequired,
            current_value: currentTotal,
            past_value: lastTotal,
            rows: response
        },
        is_data_available: response.length > 0
    };
}


/*
    Headers
*/

/**
 * Get data to display that in dashboard header
 * - Cumulative savings
 * - Current saving week
 * - Last uploaded week average working hours
 * - Last 8 week average working hours
 * @param  {} requestArgs
 */
export const getHeaderStatsService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let { start_date, end_date, ...otherArgs } = updatedPayload;

    const yearlyWeekNumberRules = await fetchStartDateYearlyRules(otherArgs.client_id);
    const currentDate = moment().format(dateTimeFormates.YYYYMMDD);
    const matchedRule = findMatchingYearlyRule(currentDate, currentDate, yearlyWeekNumberRules);

    // 8 week ago date range
    let eightWeekAgoStartDate = moment(start_date).subtract(8, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD); // moment().subtract(8, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD);
    let eightWeekAgoEndDate = moment(start_date).subtract(1, 'weeks').endOf('week').format(dateTimeFormates.YYYYMMDD);

    // previous week date range
    let pastEightWeekStartDate = moment().subtract(1, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD);
    let pastEightWeekEndDate = moment().subtract(1, 'weeks').endOf('week').format(dateTimeFormates.YYYYMMDD);

    // Selected date range last week average working hours
    let lastWeekCurrentStartDate = ((moment().isBetween(moment(start_date), moment(end_date)) || moment().isSame(moment(start_date))) && getWeeksOfTwoDates(start_date, end_date) <= 2
    ) ? (
        moment().subtract(1, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD)
    ) : moment(start_date).startOf('week').format(dateTimeFormates.YYYYMMDD);

    let lastWeekCurrentEndDate = ((moment().isBetween(moment(start_date), moment(end_date)) || moment().isSame(moment(start_date))) && getWeeksOfTwoDates(start_date, end_date) < 2
    ) ? (
        moment().subtract(1, 'weeks').endOf('week').format(dateTimeFormates.YYYYMMDD)
    ) : moment(end_date).endOf('week').format(dateTimeFormates.YYYYMMDD);

    let lastWeekPastStartDate = (moment().isBetween(moment(start_date), moment(end_date)) && getWeeksOfTwoDates(start_date, end_date) <= 2) || (
        moment(start_date).isSame(moment().subtract(1, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD)) && getWeeksOfTwoDates(start_date, end_date)
        < 2) ? (
        moment().subtract(2, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD)
    ) : pastEightWeekStartDate;

    let lastWeekPastEndDate = (moment().isBetween(moment(start_date), moment(end_date)) && getWeeksOfTwoDates(start_date, end_date) <= 2) || (
        moment(start_date).isSame(moment().subtract(1, 'weeks').startOf('week').format(dateTimeFormates.YYYYMMDD)) && getWeeksOfTwoDates(start_date, end_date)
        < 2) ? (
        moment().subtract(2, 'weeks').endOf('week').format(dateTimeFormates.YYYYMMDD)
    ) : pastEightWeekEndDate;

    let { whereClause, whereClauseValue } = getWhereClauseString(deepClone(otherArgs), "time_and_attendance_data", false);
    let { payrollWhereClause, payrollWhereClauseValue } = getPayrollWhereClauseString(deepClone(otherArgs));

    let { eight_week_current_total_hours, eight_week_current_worker_count, eight_week_past_total_hours, eight_week_past_worker_count,
        last_week_current_total_hours, last_week_current_worker_count, last_week_past_total_hours, last_week_past_worker_count,
        cumulative_clearvue_savings, last_week_clearvue_savings } = await EasyPromiseAll({

            eight_week_current_total_hours: await getWorkersTotalWorkingHours(whereClause, whereClauseValue, eightWeekAgoStartDate, eightWeekAgoEndDate),
            eight_week_current_worker_count: await getWorkersCountForAverageWorkingHours(whereClause, whereClauseValue, eightWeekAgoStartDate, eightWeekAgoEndDate),

            eight_week_past_total_hours: await getWorkersTotalWorkingHours(whereClause, whereClauseValue, pastEightWeekStartDate, pastEightWeekEndDate),
            eight_week_past_worker_count: await getWorkersCountForAverageWorkingHours(whereClause, whereClauseValue, pastEightWeekStartDate, pastEightWeekEndDate),

            last_week_current_total_hours: await getWorkersTotalWorkingHours(whereClause, whereClauseValue, lastWeekCurrentStartDate, lastWeekCurrentEndDate),
            last_week_current_worker_count: await getWorkersCountForAverageWorkingHours(whereClause, whereClauseValue, lastWeekCurrentStartDate, lastWeekCurrentEndDate),

            last_week_past_total_hours: await getWorkersTotalWorkingHours(whereClause, whereClauseValue, lastWeekPastStartDate, lastWeekPastEndDate),
            last_week_past_worker_count: await getWorkersCountForAverageWorkingHours(whereClause, whereClauseValue, lastWeekPastStartDate, lastWeekPastEndDate),

            cumulative_clearvue_savings: await getHeaderCumulativeClearVueSavings(requestArgs, payrollWhereClause, payrollWhereClauseValue, matchedRule),
            last_week_clearvue_savings: await getPreviousWeekClearVueSavings(requestArgs, payrollWhereClause, payrollWhereClauseValue)
        });

    return {
        "ok": true,
        "result": {
            "clearvue_savings": {
                "last_week": "",
                "value": parseFloat(last_week_clearvue_savings.dbResponse.clearvue_savings) || 0
            },
            "cumulative_clearvue_savings": parseFloat(cumulative_clearvue_savings.cumulative_savings) || 0,
            "last_week_average_hours": {
                "average_hours_per_worker": Math.round((parseFloat(last_week_current_total_hours.working_hours) / parseFloat(last_week_current_worker_count)) * 100) / 100 || 0,
                "past_average_hours_per_worker": Math.round((parseFloat(last_week_past_total_hours.working_hours) / parseFloat(last_week_past_worker_count)) * 100) / 100 || 0,
                "last_week": ""
            },
            "eight_week_average_worker_working_hours": Math.round((parseFloat(eight_week_current_total_hours.working_hours) / eight_week_current_worker_count) * 100) / 100 || 0,
            "past_eight_week_average_worker_working_hours": Math.round((parseFloat(eight_week_past_total_hours.working_hours) / eight_week_past_worker_count) * 100) / 100 || 0

        },
        "is_data_available": parseFloat(cumulative_clearvue_savings.cumulative_savings) > 0 ? true : false
    }
}

export const getLeaversAnalysisService = async (requestArgs, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    let whereClause = `survey_result.surveyId = :survey_id and survey_result.questionId = :question_id`;
    let whereClauseValue = {};
    if (updatedPayload.client_id) {
        whereClause = `${whereClause} and survey_result.clientId = :client_id`
    }
    if (updatedPayload.agency_id) {
        whereClause = `${whereClause} and survey_result.agencyId = :agency_id`
    }
    if (updatedPayload.site_id) {
        whereClause = `${whereClause} and survey_result.siteId = :site_id`
    }
    if (updatedPayload.region_id) {
        const sites = await getSites(`site.region_id= :region_id`, { "region_id": updatedPayload.region_id });
        if (!_.size(sites)) {
            return {
                "ok": true,
                "result": {
                    "rows": []
                },
                "is_data_available": false
            }
        }
        whereClause = `${whereClause} and survey_result.siteId IN (:site_id_list)`;
        whereClauseValue = { ...whereClauseValue, "site_id_list": _.map(sites, 'id') }
    }
    if (updatedPayload.shift_id) {
        whereClause = `${whereClause} and job.shift_id = :shift_id`
    }
    if (updatedPayload.department_id) {
        whereClause = `${whereClause} and job_association.department_id = :department_id`
    }

    whereClauseValue = { ...whereClauseValue, "survey_id": config.EXIT_SURVEY_ID, "question_id": config.EXIT_SURVEY_QUESTION_ID, "client_id": updatedPayload.client_id, "agency_id": updatedPayload.agency_id, "site_id": updatedPayload.site_id, "shift_id": updatedPayload.shift_id, "department_id": updatedPayload.department_id }
    let leaverAnalysis = await getLeaverAnalysis(whereClause, whereClauseValue);

    return {
        "ok": true,
        "result": {
            "rows": leaverAnalysis
        },
        "is_data_available": leaverAnalysis.length ? true : false
    }
}


/*
    Other
*/
export const getWorkerWhereClauseString = (requestArgs, isActiveWorkers = 1, mainEntity = "workers", start_date = "", end_date = "", shouldIncludeInactivateFilter = false) => {
    let whereClauseString = "";

    if (isActiveWorkers === 2) { // Consider active workers for provided date range
        whereClauseString = `(workers.in_actived_at is null OR workers.in_actived_at >= :start_date) AND workers.start_date <= :end_date AND `
    }

    else if (isActiveWorkers === 0) {
        whereClauseString = `workers.is_active = :is_active_workers AND `
        if (shouldIncludeInactivateFilter) {
            whereClauseString += `workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date AND `;
        }
    }

    else if (isActiveWorkers !== null) {
        whereClauseString = `workers.is_active = :is_active_workers AND `;
    }

    if (requestArgs.client_id) {
        requestArgs[`${mainEntity}.client_id`] = requestArgs.client_id;
        delete requestArgs.client_id;
    }

    if (requestArgs.agency_id) {
        requestArgs[`${mainEntity}.agency_id`] = requestArgs.agency_id;
        delete requestArgs.agency_id;
    }

    let whereClauseValue = { "start_date": start_date, "end_date": end_date, "is_active_workers": isActiveWorkers }
    return objectToMySQLWhereClause(requestArgs, whereClauseString, whereClauseValue);
}

const getPayrollWhereClauseString = (requestArgs, isActiveWorkers = 1, mainEntity = "payroll") => {
    let whereClauseString = ``;

    if (requestArgs.client_id) {
        requestArgs[`${mainEntity}.client_id`] = requestArgs.client_id;
        delete requestArgs.client_id;
    }

    if (requestArgs.agency_id) {
        requestArgs[`${mainEntity}.agency_id`] = requestArgs.agency_id;
        delete requestArgs.agency_id;
    }

    if (requestArgs.site_id) {
        requestArgs[`${mainEntity}.site_id`] = requestArgs.site_id;
        delete requestArgs.site_id;
    }

    if (requestArgs.region_id) {
        requestArgs[`site.region_id`] = requestArgs.region_id;
        delete requestArgs.region_id;
    }

    if (requestArgs.department_id) {
        requestArgs[`job_association.department_id`] = requestArgs.department_id;
        delete requestArgs.department_id;
    }

    if (requestArgs.shift_id) {
        requestArgs[`job.shift_id`] = requestArgs.shift_id;
        delete requestArgs.shift_id;
    }

    let where = objectToMySQLWhereClause(requestArgs, whereClauseString);
    return { "payrollWhereClause": where.whereClause, "payrollWhereClauseValue": where.whereClauseValue };
}

const getWhereClauseString = (requestArgs: any, associatedTableName: string, renameOriginTables = true) => {

    let whereClauseString = "";

    if (requestArgs.client_id) {
        requestArgs = removeKeyFromObject("client_id", `${associatedTableName}.client_id`, requestArgs);
    }

    if (requestArgs.agency_id) {
        requestArgs = removeKeyFromObject("agency_id", `${associatedTableName}.agency_id`, requestArgs);
    }

    if (renameOriginTables) {
        if (requestArgs.site_id) {
            requestArgs = removeKeyFromObject("site_id", "site.id", requestArgs);
        }
        if (requestArgs.region_id) {
            requestArgs = removeKeyFromObject("region_id", "region.id", requestArgs);
        }
        if (requestArgs.shift_id) {
            requestArgs = removeKeyFromObject("shift_id", "shift.id", requestArgs);
        }
        if (requestArgs.department_id) {
            requestArgs = removeKeyFromObject("department_id", "departments.id", requestArgs);
        }
    }

    return objectToMySQLWhereClause(requestArgs, whereClauseString);
};


/**
 * Get day wise completed shift details for a given date range for active and inactive workers as per passed argument
 * @param  {} requestArgs
 * @param  {} forActiveWorkers=1
 */
const getShiftUtilisationDetails = async (requestArgs, forActiveWorkers = 1, loggedInUser) => {

    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;
    let result = [0, 0, 0, 0, 0, 0, 0];
    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherRequestArgs, forActiveWorkers == 1 ? 2 : forActiveWorkers, "time_and_attendance_data", startDate, endDate)

    let response = await getShiftUtilisationDetailsModel(
        startDate,
        endDate,
        whereClause,
        whereClauseValue
    );

    if (response.length) {
        let dayWiseData = arrayToObject(response, "avg_days");
        result = [
            parseInt(dayWiseData['1'] && (dayWiseData['1'].worker_counts) || 0),
            parseInt(dayWiseData['2'] && (dayWiseData['2'].worker_counts) || 0),
            parseInt(dayWiseData['3'] && (dayWiseData['3'].worker_counts) || 0),
            parseInt(dayWiseData['4'] && (dayWiseData['4'].worker_counts) || 0),
            parseInt(dayWiseData['5'] && (dayWiseData['5'].worker_counts) || 0),
            parseInt(dayWiseData['6'] && (dayWiseData['6'].worker_counts) || 0),
            parseInt(dayWiseData['7'] && (dayWiseData['7'].worker_counts) || 0)
        ]
    }

    return {
        "ok": true,
        "result": result,
        "is_data_available": true
    }
};


const getDayWiseShiftUtilisationDetails = async (requestArgs, loggedInUser, isForActiveWorkers = 1) => {
    let weekAndWorkerWiseDetails = {};
    let agencyWiseHours = {};
    let agencyDetails = {};
    let agencyIdsList = [];
    let apiResponse = [];

    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { "start_date": startDate, "end_date": endDate, ...otherRequestArgs } = updatedPayload;

    let where = getWhereClauseString(deepClone(otherRequestArgs), 'agency_client_association');
    let agencyList = await getAssociatedAgenciesList(otherRequestArgs, where.whereClause, where.whereClauseValue);

    if (agencyList) {

        let where = getWhereClauseString(deepClone(otherRequestArgs), "time_and_attendance_data", false);

        where.whereClause += isForActiveWorkers ? ` AND workers.start_date <= :start_date AND (workers.in_actived_at >= :end_date OR workers.in_actived_at is null)` : ""
        where.whereClauseValue = { ...where.whereClauseValue, "start_date": startDate, "end_date": endDate }
        let response = await getWorkersDayWiseShiftUtilisationDetails(
            startDate,
            endDate,
            where.whereClause,
            where.whereClauseValue
        );

        let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), null)

        whereClause += ` AND workers.start_date <= :end_date AND (workers.in_actived_at >= :start_date OR workers.in_actived_at is null)`
        whereClauseValue = { ...whereClauseValue, "end_date": endDate, "start_date": startDate }

        let totalWorkerDetails = await getTotalWorkers(
            deepClone(updatedPayload),
            whereClause,
            whereClauseValue
        );

        let totalWorkerDetailsObject = Object.assign({}, ...(totalWorkerDetails.map(item => ({ [item.agency_id]: parseInt(item.workers_count) }))));

        agencyList.forEach((element) => {
            agencyDetails[element.agency_detail_id] = {
                name: element.agency_name
            }

            if (!agencyIdsList.includes(element.agency_detail_id)) {
                agencyIdsList.push(element.agency_detail_id);
            }
        })

        let agencyWiseResponse = {};

        response.forEach((element) => {
            let key = element.avg_days < 4 ? '1-3' : '4+';

            if (agencyWiseResponse[element.agency_id]) {
                agencyWiseResponse[element.agency_id][key] = agencyWiseResponse[element.agency_id][key] + parseInt(element.worker_counts);
            } else {
                agencyWiseResponse[element.agency_id] = {
                    "1-3": key === "1-3" ? parseInt(element.worker_counts) : 0,
                    "4+": key === "4+" ? parseInt(element.worker_counts) : 0,
                    "total": parseInt(totalWorkerDetailsObject[element.agency_id] || 0)
                }
            }
        });

        agencyIdsList.forEach((agencyId) => {

            if (agencyWiseResponse[agencyId]) {
                apiResponse.push({
                    "label": agencyDetails[agencyId]['name'],
                    "1-3": agencyWiseResponse[agencyId]['1-3'],
                    "4+": agencyWiseResponse[agencyId]['4+'],
                    "total": agencyWiseResponse[agencyId]['total']
                })
            } else {
                apiResponse.push({
                    "label": agencyDetails[agencyId]['name'],
                    "1-3": 0,
                    "4+": 0,
                    "total": parseInt(totalWorkerDetailsObject[agencyId] || 0)
                })
            }
        })
    }

    return apiResponse;
}

const getWorkerWhereClauseStringForTotalWorkers = (requestArgs, mainEntity = "workers", isActiveWorkers = 1, start_date = "", end_date = "", shouldIncludeInactivateFilter = false) => {

    let whereClauseString = "";

    if (isActiveWorkers === 2) { // Consider active workers for provided date range
        whereClauseString = `(workers.in_actived_at is null OR workers.in_actived_at >= :start_date) AND workers.start_date <= :end_date AND `
    }

    else if (isActiveWorkers === 0) {
        whereClauseString = `workers.is_active = :is_active AND `
        if (shouldIncludeInactivateFilter) {
            whereClauseString += `workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date AND `;
        }
    }

    if (requestArgs.client_id) {
        requestArgs[`${mainEntity}.client_id`] = requestArgs.client_id;
        delete requestArgs.client_id;
    }

    if (requestArgs.agency_id) {
        requestArgs[`${mainEntity}.agency_id`] = requestArgs.agency_id;
        delete requestArgs.agency_id;
    }

    let whereClauseValue = { "start_date": start_date, "end_date": end_date, "is_active": isActiveWorkers }
    return objectToMySQLWhereClause(requestArgs, whereClauseString, whereClauseValue);
}

const getWorkerWhereClauseStringForActiveWorkers = (requestArgs, mainEntity = "time_and_attendance_data") => {
    let whereClauseString = "";
    if (requestArgs.client_id) {
        requestArgs[`${mainEntity}.client_id`] = requestArgs.client_id;
        delete requestArgs.client_id;
    }

    if (requestArgs.agency_id) {
        requestArgs[`${mainEntity}.agency_id`] = requestArgs.agency_id;
        delete requestArgs.agency_id;
    }
    return objectToMySQLWhereClause(requestArgs, whereClauseString);
}



/**
 * Demographis API service layer
 */


/**
 * Get gender details of the workers with support of various filters like agency, client & site
 * @param  {} requestArgs
 */
export const getGenderAnalyticsService = async (requestArgs) => {
    let { "is_active": isActive, "type": workerType, ...otherRequestArgs } = requestArgs;
    isActive = isActive === 'true' || isActive === undefined ? 1 : 0;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherRequestArgs, isActive);
    whereClause += (workerType ? ` AND workers.type = :worker_type` : "")
    whereClauseValue["worker_type"] = workerType;


    let response = await getWorkerDemographicsDetails(
        requestArgs,
        whereClause,
        whereClauseValue,
        `COALESCE(NULLIF(workers.orientation, ''), 'Undeclared')`
    );
    return {
        "ok": true,
        "result": {
            "rows": parseDemographicsValueToInteger(response)
        },
        "is_data_available": response.length ? true : false
    };
}


/**
 * Get proximity details (post-code) of the workers with support of various filters like agency, client & site
 * @param  {} requestArgs
 */
export const getProximityAnalyticsService = async (requestArgs) => {

    let { "is_active": isActive, "type": workerType, ...otherRequestArgs } = requestArgs;
    isActive = isActive === 'true' || isActive === undefined ? 1 : 0;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherRequestArgs, isActive);
    whereClause += (workerType ? ` AND workers.type = :worker_type` : "")
    whereClauseValue["worker_type"] = workerType;

    // Exclude NULL and "" post_code
    whereClause += ` AND (workers.post_code IS NOT NULL AND workers.post_code <> '')`;

    let response = await getWorkerDemographicsDetails(
        requestArgs,
        whereClause,
        whereClauseValue,
        'workers.post_code'
    );
    return {
        "ok": true,
        "result": {
            "rows": parseDemographicsValueToInteger(response)
        },
        "is_data_available": response.length ? true : false
    };
}


/**
 * Get age group details of the workers with support of various filters like agency, client & site
 * @param  {} requestArgs
 */
export const getAgeAnalyticsService = async (requestArgs) => {

    let { "is_active": isActive, "type": workerType, ...otherRequestArgs } = requestArgs;
    isActive = isActive === 'true' || isActive === undefined ? 1 : 0;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherRequestArgs, isActive);
    whereClause += (workerType ? ` AND workers.type = :worker_type` : "")
    whereClauseValue["worker_type"] = workerType;

    let response = await getWorkerDemographicsDetails(
        requestArgs,
        whereClause,
        whereClauseValue,
        `CASE
            WHEN IFNULL(TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()), 0) < 24 THEN "< 23"
            WHEN IFNULL(TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()), 0) < 41 THEN "24-40"
            WHEN IFNULL(TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()), 0) < 51 THEN "41-50"
            WHEN IFNULL(TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()), 0) < 61 THEN "51-60"
            else "60+"
        END`
    );
    return {
        "ok": true,
        "result": {
            "rows": parseDemographicsValueToInteger(response)
        },
        "is_data_available": response.length ? true : false
    };
}

const parseDemographicsValueToInteger = (data) => {
    return data.map(({ value, label }) => ({ label: label, value: parseInt(value) }));
};

/**
 * Get standard and overtime group by start date
 * @param  {} requestArgs
 */
export const getSpendTrendsAnalysticsService = async (requestArgs) => {

    const { start_date, end_date, type, ...otherRequestArgs } = requestArgs;

    let clientDetails = await getClientsById(otherRequestArgs.client_id);

    const { startDate, endDate, standard, workerData, numberOfWeeks } = getStartDateEndDateWeekNumber(start_date, end_date, clientDetails.weekday_start);

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, 'time_and_attendance_data');

    whereClause += (type ? ` AND worker.type = :type ` : '');
    whereClauseValue["type"] = type;

    let totalSpendTrends = await getTotalSpendTrendsAnalytics(startDate, endDate, whereClause, whereClauseValue, otherRequestArgs.region_id, otherRequestArgs.client_id);
    totalSpendTrends.forEach(key => {
        let weekNumber = getWeeksOfTwoDates(startDate, key.end_date);
        if (key.pay_type == PayTypes.STANDARD) {
            standard[weekNumber - 1] = key.charges;
        }
        if (key.pay_type == PayTypes.OVERTIME) {
            workerData[weekNumber - 1] = key.charges;
        }
    });

    return {
        "ok": true,
        "result": {
            labels: getWeeklabels(numberOfWeeks),
            standard,
            overtime: workerData
        }
    };
}

/**
 * Get standard and overtime group by pay type 
 * @param  {} requestArgs
 */
export const getHoursTrendsAnalysticsService = async (requestArgs) => {
    const { start_date, end_date, type, ...otherRequestArgs } = requestArgs;

    let clientDetails = await getClientsById(otherRequestArgs.client_id);
    const { startDate, endDate, standard, workerData, numberOfWeeks } = getStartDateEndDateWeekNumber(start_date, end_date, clientDetails.weekday_start);

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, 'time_and_attendance_data');

    whereClause += (type ? ` AND worker.type = :type ` : '');
    whereClauseValue["type"] = type;

    let totalHoursTrends = await getTotalHoursTrendsAnalytics(startDate, endDate, whereClause, whereClauseValue, otherRequestArgs.region_id, otherRequestArgs.client_id);

    totalHoursTrends.forEach(key => {
        let weekNumber = getWeeksOfTwoDates(startDate, key.end_date);
        if (key.pay_type == PayTypes.STANDARD) {
            standard[weekNumber - 1] = key.hours;
        }
        if (key.pay_type == PayTypes.OVERTIME) {
            workerData[weekNumber - 1] = key.hours;
        }
    });

    return {
        "ok": true,
        "result": {
            labels: getWeeklabels(numberOfWeeks),
            standard,
            overtime: workerData
        }
    };
}

/**
 * Get total workers with group by start date  
 * @param  {} requestArgs
 */

export const getTotalHeadsTrendsAnalysticsService = async (requestArgs) => {
    const { start_date, end_date, type, ...otherRequestArgs } = requestArgs;

    let clientDetails = await getClientsById(otherRequestArgs.client_id);
    const { startDate, endDate, workerData, numberOfWeeks } = getStartDateEndDateWeekNumber(start_date, end_date, clientDetails.weekday_start);

    if (type == config.PERMANENT_WORKER) {
        return {
            "ok": true,
            "result": {
                labels: getWeeklabels(numberOfWeeks),
                total_workers: workerData
            }
        }
    }

    let where = objectToMySQLWhereClause(otherRequestArgs);
    let totalHeadsTrends = await getTotalHeadsTrendsAnalytics(startDate, endDate, where.whereClause, where.whereClauseValue, otherRequestArgs.region_id, otherRequestArgs.client_id);

    totalHeadsTrends.forEach(key => {
        let weekNumber = getWeeksOfTwoDates(startDate, key.end_date);
        if (key.heads) {
            workerData[weekNumber - 1] = parseInt(key.heads);
        }
    });
    return {
        "ok": true,
        "result": {
            labels: getWeeklabels(numberOfWeeks),
            total_workers: workerData
        }
    }
}

/**
 * Get total leaver workers with group by in actived date  
 * @param  {} requestArgs
 */
export const getLeaversTrendsAnalysticsService = async (requestArgs) => {

    const { start_date, end_date, type, ...otherRequestArgs } = requestArgs;

    let clientDetails = await getClientsById(otherRequestArgs.client_id);
    const { startDate, endDate, workerData, numberOfWeeks } = getStartDateEndDateWeekNumber(start_date, end_date, clientDetails.weekday_start);

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherRequestArgs), null, 'workers');
    whereClause += (type ? ` AND workers.type = :type ` : '');
    whereClauseValue["type"] = type;

    let totalLeaversTrends = await getTotalLeaversTrendsAnalytics(startDate, endDate, whereClause, whereClauseValue, otherRequestArgs.region_id, otherRequestArgs.client_id);

    totalLeaversTrends.forEach(key => {
        let weekNumber = getWeeksOfTwoDates(startDate, key.in_actived_at);
        workerData[weekNumber - 1] += parseInt(key.total);
    });
    return {
        "ok": true,
        "result": {
            labels: getWeeklabels(numberOfWeeks),
            total_hours: workerData
        }
    }
}

export const getSiteRatingsTrendsAnalysticService = async (requestArgs) => {
    let { client_id, site_id, agency_id, region_id, start_date, end_date, type } = requestArgs;
    let whereClause: string = `survey_result.rating is not null AND survey_question.belongs_to='SITE' AND survey_result.created_at >= :start_date AND survey_result.created_at<= :end_date `;
    let whereClauseValue = {};

    whereClause += agency_id ? `AND survey_result.agency_id = :agency_id ` : "";
    whereClause += type ? `AND worker.type = :type ` : '';

    if (client_id && !site_id) {

        let siteDetails: any = await getAllSites(client_id, region_id);
        let site = siteDetails[1].sites;
        let site_id_list = site.map(object => parseInt(object.id));
        whereClauseValue = { ...whereClauseValue, "site_id_list": site_id_list }
        if (_.size(site_id_list)) {
            whereClause += `AND survey_result.site_id IN (:site_id_list) AND survey_result.client_id = :client_id `;
        } else {
            whereClause = ``;
        }
    }
    else {
        whereClause += `AND survey_result.site_id = :site_id AND survey_result.client_id = :client_id `;
    }

    whereClauseValue = { ...whereClauseValue, "start_date": start_date, "end_date": end_date, "agency_id": agency_id, "type": type, "site_id": site_id, "client_id": client_id }
    return {
        "ok": true,
        "result": formatTrendsResponse(whereClause ? await getTrendSiteRating(whereClause, whereClauseValue) : [])
    }
}

export const getAgencyRatingsTrendsAnalysticService = async (requestArgs) => {
    let { start_date, end_date, type, ...otherArgs } = requestArgs;
    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(deepClone(otherArgs), null, 'survey_result');
    whereClause += (type ? ` AND worker.type = :type ` : '');
    whereClauseValue["type"] = type;

    return {
        "ok": true,
        "result": formatTrendsResponse(await getTrendAgencyRating(whereClause, whereClauseValue, start_date, end_date, otherArgs.region_id, otherArgs.client_id))
    }
}

export const getCompanyRatingsTrendsAnalysticService = async (requestArgs) => {
    let { start_date, end_date, type, ...otherArgs } = requestArgs;
    let { client_id } = requestArgs;

    let { whereClause, whereClauseValue } = getWorkerWhereClauseString(otherArgs, null, 'survey_result');
    whereClause += (type ? ` AND worker.type = :type ` : '');
    whereClauseValue["type"] = type;

    return {
        "ok": true,
        "result": formatTrendsResponse(await getTrendCompanyRating(whereClause, whereClauseValue, start_date, end_date, requestArgs.region_id, client_id))
    }
}


export const getCompliancesCountService = async (clientId, UnFilteredParams, loggedInUser) => {
    // Validate access and get payload
    let { isError, error, payload: params } = await checkAuthorisedResourceAccess({ ...UnFilteredParams, client_id: clientId }, loggedInUser);
    if (isError) return error;

    let { count: workersWithSixtyPlusHoursCount } = await getWorkersWithSixtyPlusHours(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, params.sort_by, params.sort_type, true)

    let { count: workersWithSamePostCodeAndHouseNumbersCount } = await getWorkersWithSamePostCodeAndHouseNumbers(clientId, params.agency_id, params.region_id, params.site_id, params.page, params.limit, params.sort_by, params.sort_type, false, true)

    const studentVisaWhereClauseString = `workers.student_visa = 1 AND payroll.total_hours > 20`;
    let { count: workersWithStudentVisaCount } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, params.sort_by, params.sort_type, studentVisaWhereClauseString, true)

    let { count: WorkersWithConsecutiveDaysCount } = await getWorkersWithConsecutiveDaysCard(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, params.sort_by, params.sort_type, true)

    const underEighteenWhereClauseString = `EXISTS (SELECT 1 FROM payroll WHERE payroll.worker_id = workers.id AND payroll.start_date >= :start_date AND payroll.end_date <= :end_date AND TIMESTAMPDIFF(YEAR, workers.date_of_birth, payroll.start_date) < 18)`;
    let { count: workersUnderEighteenCount } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, params.sort_by, params.sort_type, underEighteenWhereClauseString, true)

    let { count: workersWithSameSortCodeAndAccountNumbersCount } = await getWorkersWithSameSortCodeAndAccountNumbers(clientId, params.agency_id, params.region_id, params.site_id, params.page, params.limit, params.sort_by, params.sort_type, false, true)

    const limitedHoursWhereClauseString = `payroll.total_hours > 20 AND EXISTS (SELECT 1 FROM payroll WHERE payroll.worker_id = workers.id AND payroll.start_date >= :start_date AND payroll.end_date <= :end_date AND payroll.limited_hours = 1)`;
    let { count: workersWithLimitedHoursCount } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, params.sort_by, params.sort_type, limitedHoursWhereClauseString, true)
    return {
        "ok": true,
        "count": [
            {
                id: ComplinaceCardsType.WORKER_SIXTY_PLUS_HOURS,
                count: workersWithSixtyPlusHoursCount
            },
            {
                id: ComplinaceCardsType.MULTIPLE_OCCUPANCY,
                count: workersWithSamePostCodeAndHouseNumbersCount
            },
            {
                id: ComplinaceCardsType.STUDENT_VISA,
                count: workersWithStudentVisaCount
            },
            {
                id: ComplinaceCardsType.CONSECUTIVE_DAYS,
                count: WorkersWithConsecutiveDaysCount
            },
            {
                id: ComplinaceCardsType.UNDER_18,
                count: workersUnderEighteenCount
            },
            {
                id: ComplinaceCardsType.BANKING,
                count: workersWithSameSortCodeAndAccountNumbersCount
            },
            {
                id: ComplinaceCardsType.LIMITED_HOURS,
                count: workersWithLimitedHoursCount
            }
        ]
    };
};

/**
 * Checks if the compliance card type uses length of service calculations
 */
const isLengthOfServiceCard = (complianceCardId: ComplinaceCardsType): boolean => {
    return [
        ComplinaceCardsType.STUDENT_VISA,
        ComplinaceCardsType.UNDER_18,
        ComplinaceCardsType.LIMITED_HOURS
    ].includes(Number(complianceCardId));
}

const isConsecutiveDaysCard = (complianceCardId: ComplinaceCardsType): boolean => {
    return [
        ComplinaceCardsType.CONSECUTIVE_DAYS
    ].includes(Number(complianceCardId));
}

const isBankingCard = (complianceCardId: ComplinaceCardsType): boolean => {
    return [
        ComplinaceCardsType.BANKING
    ].includes(Number(complianceCardId));
}

const isMultipleOccupancyCard = (complianceCardId: ComplinaceCardsType): boolean => {
    return [
        ComplinaceCardsType.MULTIPLE_OCCUPANCY
    ].includes(Number(complianceCardId));
}
/**
 * Determines the appropriate sort field based on compliance card type and requested sort
 */
const determineSortField = (complianceCardId: ComplinaceCardsType, requestedSort: string): string => {
    const sortByLower = requestedSort.toLowerCase();

    switch (sortByLower) {
        case 'role':
            return 'first_name';

        case 'masked_account_number':
            return 'sort_code';

        case 'los':
            return isLengthOfServiceCard(complianceCardId)
                ? 'days_between_assignment_and_payroll_end'
                : 'employee_id';

        case 'total_hours':
            return !isLengthOfServiceCard(complianceCardId) ? 'employee_id' : requestedSort;

        case 'week':
            return 'start_date';

        case 'consecutive_days':
            return isConsecutiveDaysCard(complianceCardId)
                ? requestedSort
                : 'employee_id';

        case 'client_approval_status':
            return isMultipleOccupancyCard(complianceCardId) || isBankingCard(complianceCardId)
                ? requestedSort
                : 'employee_id';

        case 'agency_approval_status':
            return isMultipleOccupancyCard(complianceCardId) || isBankingCard(complianceCardId)
                ? requestedSort
                : 'employee_id';
        default:
            return requestedSort;
    }
}

export const getCompliancesCardByIdService = async (complianceCardId: ComplinaceCardsType, clientId, UnFilteredParams, loggedInUser) => {
    // Determine sort field based on compliance card type and requested sort
    const sortBy = determineSortField(complianceCardId, UnFilteredParams.sort_by);

    let finalWorkers = [];
    let finalCount = 0;

    // Validate access and get payload
    let { isError, error, payload: params } = await checkAuthorisedResourceAccess({ ...UnFilteredParams, client_id: clientId }, loggedInUser);
    if (isError) return error;

    const userTypeId = parseInt(loggedInUser.user_type_id);
    const isAgencyUser = [UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE].includes(userTypeId);
    const isClientUser = [UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE].includes(userTypeId);

    const yearlyRules = await fetchStartDateYearlyRules(clientId);

    switch (Number(complianceCardId)) {
        case ComplinaceCardsType.WORKER_SIXTY_PLUS_HOURS: {
            let { workers, count } = await getWorkersWithSixtyPlusHours(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, sortBy, params.sort_type)
            const weekMap = await calculateWeeksForAllDatePairs(workers, yearlyRules);
            finalWorkers = workers.map(worker => {
                worker.week = weekMap.get(`${worker.start_date}|${worker.end_date}`);
                return worker
            })
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.MULTIPLE_OCCUPANCY: {
            let { workers, count } = await getWorkersWithSamePostCodeAndHouseNumbers(clientId, params.agency_id, params.region_id, params.site_id, params.page, params.limit, sortBy, params.sort_type, params.all_approved);
            finalWorkers = workers;
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.STUDENT_VISA: {
            let studentVisaWhereClauseString = `payroll.total_hours > 20 AND workers.student_visa = 1`;

            let { workers, count } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, sortBy, params.sort_type, studentVisaWhereClauseString)
            const weekMap = await calculateWeeksForAllDatePairs(workers, yearlyRules);
            finalWorkers = workers.map(worker => {
                // Worker length of service from worker ID.
                worker.los = getDurationBetweenDates(worker.assignment_date, worker.payroll_end_date);
                worker.los = `${worker.los.years}Y ${worker.los.months}M ${worker.los.weeks}W`;
                worker.week = weekMap.get(`${worker.start_date}|${worker.end_date}`);
                return worker
            })
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.CONSECUTIVE_DAYS: {
            let { workers, count, consecutiveDays } = await getWorkersWithConsecutiveDaysCard(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, sortBy, params.sort_type)
            finalWorkers = workers.map(worker => {
                worker.consecutive_days = consecutiveDays[worker.worker_id].consecutiveCount;
                worker.consecutive_days_start_date = consecutiveDays[worker.worker_id].startDate;
                worker.consecutive_days_end_date = consecutiveDays[worker.worker_id].endDate;
                return worker
            })
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.UNDER_18: {
            const underEighteenWhereClauseString = `EXISTS (SELECT 1 FROM payroll WHERE payroll.worker_id = workers.id AND payroll.start_date >= :start_date AND payroll.end_date <= :end_date AND TIMESTAMPDIFF(YEAR, workers.date_of_birth, payroll.start_date) < 18)`;
            let { workers, count } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, sortBy, params.sort_type, underEighteenWhereClauseString)
            const weekMap = await calculateWeeksForAllDatePairs(workers, yearlyRules);
            finalWorkers = workers.map(worker => {
                worker.week = weekMap.get(`${worker.start_date}|${worker.end_date}`);
                return worker
            })
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.BANKING: {

            let { workers, count } = await getWorkersWithSameSortCodeAndAccountNumbers(clientId, params.agency_id, params.region_id, params.site_id, params.page, params.limit, sortBy, params.sort_type, params.all_approved);
            finalWorkers = workers.map(worker => {
                worker.masked_account_number = generateMaskedAccountIdentifier(worker.sort_code, worker.account_number, isAgencyUser);
                worker.date_of_birth = !isClientUser ? worker.date_of_birth : '';
                delete worker.sort_code;
                delete worker.account_number;
                return worker
            })
            finalCount = count;
            break;
        }
        case ComplinaceCardsType.LIMITED_HOURS: {
            let limitedHoursWhereClauseString = `payroll.total_hours > 20 AND EXISTS (SELECT 1 FROM payroll WHERE payroll.worker_id = workers.id AND payroll.start_date >= :start_date AND payroll.end_date <= :end_date AND payroll.limited_hours = 1)`;

            let { workers, count } = await getWorkersBasedOnCardCondition(clientId, params.agency_id, params.region_id, params.site_id, params.start_date, params.end_date, params.page, params.limit, sortBy, params.sort_type, limitedHoursWhereClauseString)
            const weekMap = await calculateWeeksForAllDatePairs(workers, yearlyRules);

            finalWorkers = workers.map(worker => {
                // Worker length of service from worker ID.
                worker.los = getDurationBetweenDates(worker.assignment_date, worker.payroll_end_date);
                worker.los = `${worker.los.years}Y ${worker.los.months}M ${worker.los.weeks}W`;
                worker.week = weekMap.get(`${worker.start_date}|${worker.end_date}`);

                return worker
            })
            finalCount = count;
            break;
        }

    }

    // sorting of results by "role" filed.
    if (params.sort_by.toLowerCase() == "role") {
        const sortOrder = params.sort_type.toLowerCase() == 'asc' ? 1 : -1;
        finalWorkers.sort((a, b) => a.role.localeCompare(b.role) * sortOrder);
    }

    return { "ok": true, count: finalCount, workers: finalWorkers }

}


const getStartDateEndDateWeekNumber = (start_date, end_date, weekdayStart) => {

    // Map weekday name to its moment.js equivalent number (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    const weekdayMap = {
        [WeekDays.SUNDAY]: 0,
        [WeekDays.MONDAY]: 1,
        [WeekDays.TUESDAY]: 2,
        [WeekDays.WEDNESDAY]: 3,
        [WeekDays.THURSDAY]: 4,
        [WeekDays.FRIDAY]: 5,
        [WeekDays.SATURDAY]: 6
    };

    // Get the start and end of the financial year period (April 1st to March 31st)
    let startOfFinancialYear = moment(start_date);
    let endOfFinancialYear = moment(end_date);

    // Find the first weekdayStart from April 1st onwards
    let startDate = startOfFinancialYear.clone().day(weekdayMap[weekdayStart]);
    if (startDate.isBefore(startOfFinancialYear)) {
        startDate = startDate.add(1, 'week');
    }

    // Find the last occurrence of the previous weekday before the next year's April 1st
    let endDate = endOfFinancialYear.clone().day(weekdayMap[weekdayStart] - 1);
    if (endDate.isBefore(endOfFinancialYear)) {
        endDate = endDate.add(1, 'week');
    }

    // Format the dates to YYYY-MM-DD
    let startDateFormatted = startDate.format('YYYY-MM-DD');
    let endDateFormatted = endDate.format('YYYY-MM-DD');

    // Calculate number of weeks between the start and end dates
    let numberOfWeeks = moment(endDate).diff(startDate, 'weeks') + 1;

    // Initialize standard and workerData arrays with zero values
    let standard = new Array(numberOfWeeks).fill(0);
    let workerData = new Array(numberOfWeeks).fill(0);

    return {
        startDate: startDateFormatted,
        endDate: endDateFormatted,
        standard,
        numberOfWeeks,
        workerData
    };
};


const formatTrendsResponse = (ratingsArray) => {
    let avg_score = [];

    let labels = Array.from({ length: 52 }, (_, i) => {
        i += 1
        let obj = ratingsArray.find(x => x.week_number === i)
        avg_score.push(obj ? parseInt(obj.ratings) : 0)
        return "Week " + i;
    });

    return { labels, avg_score }

}

// Configuration constants for easy maintenance
const APPROVAL_CONFIG = {
    USER_TYPES: {
        CLIENT: [UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE],
        AGENCY: [UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE]
    },

    APPROVAL_FIELDS: {
        client: { status: 'clientApprovalStatus', by: 'clientApprovalBy', at: 'clientApprovalAt' },
        agency: { status: 'agencyApprovalStatus', by: 'agencyApprovalBy', at: 'agencyApprovalAt' }
    }
};

// Utility function for permission validation and user type determination
const validateUserAndGetType = async (worker, clientId, loggedInUser) => {
    const userTypeId = parseInt(loggedInUser.user_type_id);
    const invalidResponse = ErrorResponse.InvalidParams;

    if (APPROVAL_CONFIG.USER_TYPES.CLIENT.includes(userTypeId)) {
        return (worker.clientId == loggedInUser.client_id && clientId == loggedInUser.client_id)
            ? { ok: true, userType: 'client' }
            : invalidResponse;
    }

    if (APPROVAL_CONFIG.USER_TYPES.AGENCY.includes(userTypeId)) {
        const userDetails = await getUserById(loggedInUser.user_id);
        let agencyId = userDetails?.agency_id;

        return (worker.agencyId == agencyId && worker.clientId == clientId)
            ? { ok: true, userType: 'agency' }
            : invalidResponse;
    }

    return invalidResponse;
};

// Build approval payload with all logic consolidated
const buildApprovalPayload = (params) => {
    const { complianceCardId, worker, userType, approvalStatus, loggedInUser, latestApproval } = params;
    const now = new Date();
    const payload = { ...(latestApproval || {}) };

    // Update approval fields for current user
    const approvalFields = APPROVAL_CONFIG.APPROVAL_FIELDS[userType];
    payload[approvalFields.status] = approvalStatus;
    payload[approvalFields.by] = loggedInUser.user_id;
    payload[approvalFields.at] = now;

    return {
        complianceCardId: String(complianceCardId),
        workerId: String(worker.id),
        createdAt: now,
        updatedAt: now,
        clientApprovalStatus: payload.clientApprovalStatus,
        clientApprovalBy: payload.clientApprovalBy || null,
        clientApprovalAt: payload.clientApprovalAt || null,
        agencyApprovalStatus: payload.agencyApprovalStatus,
        agencyApprovalBy: payload.agencyApprovalBy || null,
        agencyApprovalAt: payload.agencyApprovalAt || null,
        houseNumber: worker.houseNumber || null,
        postCode: worker.postCode || null,
        accountNumber: worker.accountNumber || null,
        sortCode: worker.sortCode || null
    }
};

// Main service function
export const updateCompliancesApprovalStatusService = async (pathParams, inputParams, loggedInUser) => {
    try {
        const { complianceCardId, clientId } = pathParams;
        const { worker_id: workerId, is_approved: approvalStatus } = inputParams;

        // Fetch worker
        const worker = await getWorkerByWorkerId(workerId);
        if (!worker) return [400, ErrorResponse.InvalidParams];

        // Validate user permissions and get user type
        const validation = await validateUserAndGetType(worker, clientId, loggedInUser);
        if (!validation.ok) return [400, validation];

        // Fetch latest approval record
        const latestApproval = await getLatestComplianceApproval(workerId, complianceCardId);

        if (latestApproval && latestApproval[`${validation['userType']}ApprovalStatus`] == approvalStatus) {
            return [200, ErrorResponse.ApprovalStatusAlredySet];
        }

        // Build and insert new approval record
        const payload = buildApprovalPayload({
            complianceCardId,
            worker,
            userType: validation['userType'],
            approvalStatus,
            loggedInUser,
            latestApproval
        });

        await insertComplianceApproval(payload);
        return [200, { ok: true }];

    } catch (error) {
        notifyBugsnag(error);
        return [500, error.message]
    }
};

exports.getPayrollWhereClauseString = getPayrollWhereClauseString;
