import {
    getTimelineQualifiedWorkerDetails, getWorkAnniversaryQualifiedWorkerDetails,
    getTimelineRelatedMessagesDetails, addRecordInMessageReceiverGroup, getBirthdayWorkerDetails,
    getMessageDetailsByLabel, getWorkerDetailsWhoRemainInactive, inactivateWorkers,
    getWorkersWhoseStartDateIsCurrentDate, getWorkersByIds, createSystemTypeMessage, getSystemDefaultMessageByLabelAndId, getClientsByWeekdayStart, getDistinctWorkersWithTNAForSpecifiedDateRange,
    getActiveTemporaryWorkers, getMaxInactivatedAt, bulkinactivateWorkers, getWorkersData, getWorkerDetailsbyIds
} from "../models";
import { AutomatedMessagesLabels, Languages, MessageType, DeviceTokensByLanguage } from "../common";
import { sendMessageNotification } from ".";
import { config } from "../configurations";
import { arrayToObject, diffBetweenGivenDateAndTodayInYear, deviceTokenGroupByLanguages, getDateRanges } from "../utils";

/**
 * Send timeline completion messages to the workers
 */
export const sendTimelineCompletionMessagesService = async () => {
    await sendAutomatedTimelineEventMessages();
    await sendAnniversaryMessage();
    return [
        200,
        {
            ok: true,
        },
    ];
};

/**
 * Send automated messages on completion of certain timeline
 */
const sendAutomatedTimelineEventMessages = async () => {
    let daysDurationsWorkerDetails = {
        "7": [],
        "14": [],
        "28": [],
        "56": [],
        "84": []
    };
    let timelineQualifiedWorkerDetails =
        await getTimelineQualifiedWorkerDetails();

    for (let element of timelineQualifiedWorkerDetails) {
        daysDurationsWorkerDetails[element.duration].push(element);
    }

    for (let days in daysDurationsWorkerDetails) {
        await sendDayDurationMessage(
            days,
            daysDurationsWorkerDetails[days]
        );
    }
};

/**
 * Send day wise messages and notifications to the workers
 * @param  {string} numberOfDays
 * @param  {any} workerDetails
 */
const sendDayDurationMessage = async (
    numberOfDays: string,
    workerDetails: any
) => {
    const messageNameMapping = {
        "7": AutomatedMessagesLabels.NEW_STARTER_WEEK_1,
        "14": AutomatedMessagesLabels.NEW_STARTER_WEEK_2,
        "28": AutomatedMessagesLabels.NEW_STARTER_WEEK_4,
        "56": AutomatedMessagesLabels.NEW_STARTER_WEEK_8,
        "84": AutomatedMessagesLabels.NEW_STARTER_WEEK_12
    };

    await prepareAndSendAutomatedEventMessages(workerDetails, messageNameMapping[numberOfDays]);
};


/**
 * Send anniversary completion messages
 */
const sendAnniversaryMessage = async () => {
    let workAnniversaryQualifiedWorkerDetails =
        await getWorkAnniversaryQualifiedWorkerDetails();

    await prepareAndSendAutomatedEventMessages(workAnniversaryQualifiedWorkerDetails, AutomatedMessagesLabels.ANNUAL_WORK_ANNIVERSARY);
};

/**
 * Send Automated messages and notifications to the workers
 * @param  {Array<string>} workerIds
 * @param  {any} messageDetails
 * @param  {DeviceTokensByLanguage} deviceTokens
 */
export const sendAutomatedEventMessages = async (
    workerIds: Array<number>,
    messageDetails: any,
    deviceTokensByLanguage: DeviceTokensByLanguage
) => {
    if (workerIds.length && messageDetails) {

        // Add record in message table
        let insertedMessage = await createSystemTypeMessage(messageDetails.id, workerIds);
        let insertId = JSON.parse(JSON.stringify(insertedMessage)).insertId;
        if (insertedMessage && insertId) {
            // Add record in message_receiver_workers table
            await addRecordInMessageReceiverGroup(
                insertId,
                workerIds,
                config.DEFAULT_SYSTEM_USER_ID
            );

            if (Object.keys(deviceTokensByLanguage).length > 0) {
                for (let lang in deviceTokensByLanguage) {
                    const languageTokens = deviceTokensByLanguage[lang];

                    // Send notification to the workers asynchronously
                    sendMessageNotification(
                        null,
                        {
                            type: MessageType.GENERAL,
                            body: messageDetails.body,
                            title: messageDetails.title,
                            titleTranslations: messageDetails.titleTranslations,
                        },
                        insertId,
                        messageDetails,
                        [...new Set(languageTokens)], // Pass the language-specific tokens
                        lang
                    );
                }
            }
        }
    }
};


/**
 * Send Automated messages to workers separetely as per clients and agencies.
 * @param  {any} messageGroups
 * @param  {string} messageLable
 */
export const sendAutomatedEventMessagesAsPerClientAgency = async (messageGroups: any, messageLable: string) => {
    if (messageGroups) {
        for (const [acc, accValue] of Object.entries(messageGroups)) {
            for (const [id, workers] of Object.entries(accValue)) {

                let message_details = await getSystemDefaultMessageByLabelAndId(messageLable, id, acc == "clients" ? "clientId" : "agencyId");
                let workerIds = workers.map(worker => worker.id);
                // let deviceTokens = workers.map(worker => worker.device_token).filter(Boolean);

                const deviceTokensByLanguage: DeviceTokensByLanguage = await deviceTokenGroupByLanguages(workers);
                await sendAutomatedEventMessages(
                    workerIds,
                    message_details,
                    deviceTokensByLanguage
                );

            };
        };
    };
};


/**
 * Do workers separation as per clients and agencies, then proceed to send automated messages.
 * @param  {any} messageGroups
 * @param  {string} messageLable
 */
export const prepareAndSendAutomatedEventMessages = async (workerDetails: any, messageLable: string) => {

    if (workerDetails.length) {

        /** Separate workers according to client & agency wise
         ## i.e messageGroups = {
            clients: {"Cid_1": [{wkr1}, {wkr2}, {wkr3}, ...], "Cid_2": [{wkr5}, {}, ...], Cid_3 : [], ...}
            agencies: {"Aid_1": [{wkr1}, {wkr5}, {}, ...], "Aid_2": [{}, {}, ...]}, Aid_3 : [], ...}
        */
        let messageGroups = workerDetails.reduce((acc, workerDetail) => {
            acc.clients[workerDetail.client_id] = acc.clients[workerDetail.client_id] || [];
            acc.clients[workerDetail.client_id].push(workerDetail);
            acc.agencies[workerDetail.agency_id] = acc.agencies[workerDetail.agency_id] || [];
            acc.agencies[workerDetail.agency_id].push(workerDetail);
            return acc;
        }, { clients: {}, agencies: {} });

        await sendAutomatedEventMessagesAsPerClientAgency(messageGroups, messageLable)
    }
};


/**
 * Send birthday messages to the workers
 */
export const sendBirthdayMessagesService = async () => {

    let workerDetails = await getBirthdayWorkerDetails();
    await prepareAndSendAutomatedEventMessages(workerDetails, AutomatedMessagesLabels.BIRTHDAY_MESSAGES)

    return [
        200,
        {
            ok: true,
        },
    ];
};


/**
 *  Send automated messages to inactive workers based on the specified week
 */
export const workerInactiveMessagesService = async () => {
    let workersWhoAlreadyNotified = new Set();

    workersWhoAlreadyNotified = await sendWorkerInactiveMessages(3, workersWhoAlreadyNotified);
    workersWhoAlreadyNotified = await sendWorkerInactiveMessages(2, workersWhoAlreadyNotified);
    workersWhoAlreadyNotified = await sendWorkerInactiveMessages(1, workersWhoAlreadyNotified);

    return [
        200,
        {
            ok: true,
        },
    ];
};


/**
 * Send automated messages to inactive workers based on the specified week
 * @param {number} week - The week number
 * @param {Set<any>} workersWhoAlreadyNotified - Set of workers who have already been notified
 * @returns {Set<any>} - Updated set of workers who have been notified
 */
const sendWorkerInactiveMessages = async (week: number, workersWhoAlreadyNotified: Set<any>) => {
    if (week < 1 || week > 3) return;

    const lastFourWeekRange = await getDateRanges(week + 1);

    // Get the list of client IDs for the specified weekday start
    let clientIdList = await getClientsByWeekdayStart(lastFourWeekRange.weekday_start);
    clientIdList = clientIdList.map(obj => obj.id);

    // Return if no client IDs are found
    if (!clientIdList.length) return;

    // Get workers with Time and Attendance data for the specified date range and week
    const workersWithTNA = await getDistinctWorkersWithTNAForSpecifiedDateRange(lastFourWeekRange, clientIdList, week);

    // Get active temporary workers for the specified client IDs and with the given assignment_date in the past
    const activeWorkers = await getActiveTemporaryWorkers(clientIdList, lastFourWeekRange[`w${week}`].start_date);

    const inactiveWorkerDetails = activeWorkers.filter(item => !workersWithTNA.includes(item));

    // Proceed to make workers inactive who not received payroll in pastv 3 weeks
    if (inactiveWorkerDetails.length && week === 3) {
        const { workersWithInactivationDate: workersInactivationDate, missingWorkerIds } = await getMaxInactivatedAt(inactiveWorkerDetails);
        const workersData = await getWorkersData(missingWorkerIds);

        workersData.forEach((workerDetail) => {
            const inactivatedAt = workerDetail.assignmentDate || null;
            workersInactivationDate.push({ employee_id: workerDetail.employeeId, worker_id: workerDetail.id, inactivated_at: inactivatedAt, assignment_date: inactivatedAt });
        });

        // Bulk inactivate workers
        await bulkinactivateWorkers(workersInactivationDate);
    }

    // Send mobile inactivity notifications for inactive workers
    if (inactiveWorkerDetails.length && config.SEND_WORKERS_MOBILE_INACTIVITY_NOTIFICATION === 1 && week <= 3) {
        const differenceList = [...inactiveWorkerDetails].filter(element => !workersWhoAlreadyNotified.has(element));

        if (differenceList.length) {
            const workersDetailsForMessageSending = await getWorkerDetailsbyIds(differenceList);
            const workersToSendMessage = workersDetailsForMessageSending.filter((worker) => worker.is_worker_registered === '1');

            // Prepare and send automated event messages based on the week
            await prepareAndSendAutomatedEventMessages(
                workersToSendMessage,
                week === 3 ? AutomatedMessagesLabels.UNASSINGED_WORKER : AutomatedMessagesLabels.ZERO_HOURS_MESSAGE
            );
        }

        // Update the set of workers who have been notified
        workersWhoAlreadyNotified = new Set([...inactiveWorkerDetails, ...workersWhoAlreadyNotified]);
    }

    return workersWhoAlreadyNotified;
};


/**
 * Send first day welcome message to the workers
 */
export const sendFirstDayWelcomeMessageService = async () => {

    let workerDetails = await getWorkersWhoseStartDateIsCurrentDate();
    await prepareAndSendAutomatedEventMessages(workerDetails, AutomatedMessagesLabels.FIRST_DAY_WELCOME_MESSAGE)

    return [
        200,
        {
            ok: true,
        },
    ];
};


/**
 * Send unassigned messages to the workers
 * @param  {Array<string>} workerIds
 */
export const sendUnassignedWorkerMessages = async (workerIds: Array<number>) => {
    let workerDetails = await getWorkersByIds(workerIds);
    await prepareAndSendAutomatedEventMessages(workerDetails, AutomatedMessagesLabels.UNASSINGED_WORKER);
};


/** 
 *  This function will Send `n` Annual Work Anniversary messages to worker if he has completed `n` working years.
 *  @param  {} workerData
 */
export const sendWorkAnniversaryGreetingsIfEligible = async (workerData) => {
    let numberOfWorkYears = await diffBetweenGivenDateAndTodayInYear(workerData.assignment_date);
    for (var i = 0; i < numberOfWorkYears; i++) {
        await prepareAndSendAutomatedEventMessages([workerData], AutomatedMessagesLabels.ANNUAL_WORK_ANNIVERSARY);
    }
}