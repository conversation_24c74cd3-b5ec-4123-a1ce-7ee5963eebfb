import { addShiftHelper, getShiftHelper, updateShift, getShiftByWhereClause, getAllUsers, createShiftSiteAssociation, getShiftById, getShiftSitSiteAssociationsByShiftSitId, removeShiftSitSiteAssociations, checkAuthorisedResourceAccess } from "../models";
import {
    ErrorCodes,
    ErrorResponse,
    MessageActions,
    UserType,
} from "./../common";
import { notifyBugsnag, stripEmojis } from "../utils";
import { getSitesDropDownService } from ".";

/**
 * Service to add shift.
 */
export const addShiftService = async (payload, loggedInUser) => {
    try {
        let exisitingShift = await getShiftByWhereClause({ name: payload.name.trim(), clientId: loggedInUser.client_id });
        if (exisitingShift) {
            return [400, ErrorResponse.ShiftAlreadyExists];
        }
        const data = {
            name: payload.name.trim(),
            clientId: loggedInUser.client_id,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id
        }

        let addShiftDetails;
        if (payload.associations.length > 0) {
            let siteDetails: any = await getSitesDropDownService({ client_id: loggedInUser.client_id });
            let siteIdList = siteDetails[1].sites.map(object => parseInt(object.id));

            const areSiteIdsValid = payload.associations.every(assoc => siteIdList.includes(parseInt(assoc.site_id)));

            if (!areSiteIdsValid) {
                return [403, ErrorResponse.InvalidSiteIds];
            }

            addShiftDetails = await addShiftHelper(data);
            if (!addShiftDetails) {
                return [404, ErrorResponse.ResourceNotFound];
            }

            const siteIdToAssociate = new Set(payload.associations.map((associationItem) => associationItem.site_id));

            const bulkInsertPayload = [...siteIdToAssociate].map(siteId => ({
                shiftId: addShiftDetails.id,
                siteId: siteId,
                createdBy: loggedInUser.user_id,
                createdAt: new Date(),
                updatedBy: loggedInUser.user_id,
                updatedAt: new Date(),
            }));

            await createShiftSiteAssociation(bulkInsertPayload);

        } else {
            addShiftDetails = await addShiftHelper(data);
            if (!addShiftDetails) {
                return [404, ErrorResponse.ResourceNotFound];
            }
        }


        return [
            201,
            {
                ok: true,
                message: MessageActions.CREATE_SHIFT,
                shift_id: parseInt(addShiftDetails.id),
            },
        ];
    } catch (err) {
        if (err.code === ErrorCodes.duplicateKeyError) {
            return [409, ErrorResponse.ClientAlreadyExists]    // Return 409 if worker already exists
        } else if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [401, ErrorResponse.InvalidShiftName]
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
};

/**
 * Service to edit shift.
 */
export const editShiftService = async (id, body, loggedInUser) => {
    try {
        let exisitingShift = await getShiftByWhereClause({ name: body.name.trim(), clientId: loggedInUser.client_id });
        if (exisitingShift && exisitingShift.id != id) {
            return [400, ErrorResponse.ShiftAlreadyExists];
        }

        let shiftToUpdate = await getShiftById(id);
        if (!shiftToUpdate || shiftToUpdate.client_id != loggedInUser.client_id) {
            return [404, ErrorResponse.ResourceNotFound];
        }
        const shiftPayload = {
            name: body.name.trim(),
            updatedBy: loggedInUser.user_id,
            updatedAt: new Date()
        }

        let updateResponse;

        // Fetch existing department site associations
        let existingAssociations = await getShiftSitSiteAssociationsByShiftSitId(id);

        let existingSiteIds = existingAssociations.map(assoc => assoc.siteId);
        // Extract site IDs from the payload associations
        let associationSiteIds = body.associations.map(assoc => assoc.site_id);

        // Find site IDs to be removed (existing in table but not in associations)
        let siteIdsToRemove = existingSiteIds.filter(siteId => !associationSiteIds.includes(siteId));
        // Find associations to be inserted (in associations but not in table)
        let associationsToInsert = body.associations.filter(assoc => !existingSiteIds.includes(assoc.site_id));

        // Remove associations from the table
        if (siteIdsToRemove.length > 0) await removeShiftSitSiteAssociations(id, siteIdsToRemove);

        // Insert new associations
        if (associationsToInsert.length > 0) {
            let siteDetails: any = await getSitesDropDownService({ client_id: loggedInUser.client_id });
            let siteIdList = siteDetails[1].sites.map(object => parseInt(object.id));
            const areSiteIdsValid = associationsToInsert.every(assoc => siteIdList.includes(parseInt(assoc.site_id)));

            if (!areSiteIdsValid) {
                return [403, ErrorResponse.InvalidSiteIds];
            }

            updateResponse = await updateShift(id, shiftPayload);

            let bulkInsertPayload = associationsToInsert.map(associationItem => ({
                shiftId: id,
                siteId: associationItem.site_id,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
                createdAt: new Date(),
                updatedAt: new Date(),
            }));
            await createShiftSiteAssociation(bulkInsertPayload);
        } else {
            updateResponse = await updateShift(id, shiftPayload);
        }

        return [201, {
            ok: true,
            message: MessageActions.UPDATE_SHIFT,
            shift_id: updateResponse.id
        }];

    } catch (err) {
        if (ErrorCodes.wrongValueForField.includes(err.code)) {
            return [401, ErrorResponse.InvalidShiftName]
        }
        notifyBugsnag(err);
        return [500, err.message]
    }
};

/**
 * Service to GET shift.
 */
export const getShiftService = async (requestArgs, loggedInUser) => {
    try {
        const { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
        if (isError) return error;

        const { client_id, site_id, region_id, ...others } = updatedPayload;


        let whereClause = `shift.client_id = :client_id`;
        whereClause += site_id ? ` AND shiftSiteAssociations.site_id = :site_id` : "";
        whereClause += region_id ? ` AND site.region_id = :region_id` : "";

        let whereClauseValue = { "client_id": client_id, "site_id": site_id, "region_id": region_id }
        let getShiftDetails = await getShiftHelper(whereClause, whereClauseValue);
        return [200, {
            "ok": true,
            "count": getShiftDetails.length,
            "shifts": getShiftDetails,
        }]
    } catch (err) {
        notifyBugsnag(err);
        return [500, err.message]
    }
};
