/**
 * All the service layer methods for the Department.
 */
const _ = require('lodash');
import { CreateAndUpdateDepartmentDTO, ErrorResponse, UserType } from "./../common";
import { createDepartment, updateDepartment, getDepartmentById, getDepartmentListWithPagination, getDepartmentByWhereClause, getUserById, getAllUsers, createDepartmentSiteAssociation, getDepartmentSiteAssociationsByDepartmentId, removeDepartmentSiteAssociations, checkAuthorisedResourceAccess } from "./../models";
import { MessageActions } from "./../common";
import { getAllSites, getSitesDropDownService } from ".";


/**
 * create department.
 * @param  {CreateAndUpdateDepartmentDTO} payload
 */
export const createDepartmentService = async (payload: CreateAndUpdateDepartmentDTO, loggedInUser) => {
    let exisitingDepartment = await getDepartmentByWhereClause({ name: payload.name.trim(), clientId: payload.client_id });
    if (exisitingDepartment) {
        return [400, ErrorResponse.DepartmentAlreadyExists];
    }
    const departmentPayload = {
        name: payload.name.trim(),
        clientId: payload.client_id,
        costCentre: payload.cost_centre || null,
        createdBy: loggedInUser.user_id,
        updatedBy: loggedInUser.user_id,
    }

    let department;
    if (payload.associations.length > 0) {
        let siteDetails: any = await getSitesDropDownService({ client_id: loggedInUser.client_id });
        let siteIdList = siteDetails[1].sites.map(object => parseInt(object.id));

        const areSiteIdsValid = payload.associations.every(assoc => siteIdList.includes(parseInt(assoc.site_id)));

        if (!areSiteIdsValid) {
            return [403, ErrorResponse.InvalidSiteIds];
        }

        department = await createDepartment(departmentPayload);
        const siteIdToAssociate = new Set(payload.associations.map((associationItem) => associationItem.site_id));

        const bulkInsertPayload = [...siteIdToAssociate].map(siteId => ({
            departmentId: department.id,
            siteId: siteId,
            createdBy: loggedInUser.user_id,
            createdAt: new Date(),
            updatedBy: loggedInUser.user_id,
            updatedAt: new Date(),
        }));

        await createDepartmentSiteAssociation(bulkInsertPayload);
    } else {
        department = await createDepartment(departmentPayload);
    }

    return [201, {
        ok: true,
        message: MessageActions.CREATE_DEPARTMENT,
        department_id: department.id
    }];

};

/**
 * update department.
 * @param  {id}
 * @param  {CreateAndUpdateDepartmentDTO} payload
 */
export const updateDepartmentService = async (id: string, payload: CreateAndUpdateDepartmentDTO, loggedInUser) => {
    let exisitingDepartment = await getDepartmentByWhereClause({ name: payload.name.trim(), clientId: payload.client_id });

    if (exisitingDepartment && exisitingDepartment.id != id) {
        return [400, ErrorResponse.DepartmentAlreadyExists];
    }

    let departmentToUpdate = await getDepartmentById(id);
    if (!departmentToUpdate || departmentToUpdate.client_id != payload.client_id || payload.client_id != loggedInUser.client_id) {
        return [404, ErrorResponse.ResourceNotFound];
    }
    const departmentPayload = {
        name: payload.name.trim(),
        costCentre: payload.cost_centre || null,
        updatedBy: loggedInUser.user_id,
    }
    let updateResponse;

    // Fetch existing department site associations
    let existingAssociations = await getDepartmentSiteAssociationsByDepartmentId(id);

    let existingSiteIds = existingAssociations.map(assoc => assoc.siteId);
    // Extract site IDs from the payload associations
    let associationSiteIds = payload.associations.map(assoc => assoc.site_id);

    // Find site IDs to be removed (existing in table but not in associations)
    let siteIdsToRemove = existingSiteIds.filter(siteId => !associationSiteIds.includes(siteId));
    // Find associations to be inserted (in associations but not in table)
    let associationsToInsert = payload.associations.filter(assoc => !existingSiteIds.includes(assoc.site_id));

    // Remove associations from the table
    if (siteIdsToRemove.length > 0) await removeDepartmentSiteAssociations(id, siteIdsToRemove);

    // Insert new associations
    if (associationsToInsert.length > 0) {
        let siteDetails: any = await getSitesDropDownService({ client_id: payload.client_id });
        let siteIdList = siteDetails[1].sites.map(object => parseInt(object.id));

        const areSiteIdsValid = associationsToInsert.every(assoc => siteIdList.includes(parseInt(assoc.site_id)));

        if (!areSiteIdsValid) {
            return [403, ErrorResponse.InvalidSiteIds];
        }

        updateResponse = await updateDepartment(id, departmentPayload);

        let bulkInsertPayload = associationsToInsert.map(associationItem => ({
            departmentId: id,
            siteId: associationItem.site_id,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await createDepartmentSiteAssociation(bulkInsertPayload);
    } else {
        updateResponse = await updateDepartment(id, departmentPayload);
    }


    return [200, {
        ok: true,
        message: MessageActions.UPDATE_DEPARTMENT,
        department_id: updateResponse.id
    }];
};

/**
 * get department list.
 */
export const getDepartmentListService = async (requestArgs, loggedInUser) => {
    const { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(requestArgs, loggedInUser);
    if (isError) return error;

    const { client_id, site_id, region_id, ...others } = updatedPayload;

    let whereClause = `departments.client_id = :client_id `;
    whereClause += site_id ? ` AND departmentSiteAssociations.site_id = :site_id` : "";
    whereClause += region_id ? ` AND site.region_id = :region_id` : "";

    let whereClauseValue = { "client_id": requestArgs.client_id, "site_id": site_id, "region_id": region_id };
    const totalDepartment = await getDepartmentListWithPagination(whereClause, whereClauseValue);
    return [200, {
        ok: true,
        count: totalDepartment.length,
        department_list: totalDepartment
    }];
};