const _ = require('lodash');
const moment = require('moment');
const uuid = require('uuid');
const { EasyPromiseAll } = require('easy-promise-all');
import { BookingStatus, MessageActions, PayTypes, shift } from "./../common";
import { notifyBugsnag, splitArrayWithSize, getNewTransaction, getSignedUrlForGetObject, getNumberValue, findCalculatedPathway, getApplicableMargin, getDurationBetweenDates, calculateWeeksForAllDatePairs } from './../utils';
import { config } from "../configurations";
import { getAssociatedAgencies, getRuleBySiteAndRoleType, getBookingsDataForTnaFulfilment, updateBookingStatusHelper, updateBookingHelper, getCreditDuesData, getSites, deletePayrollSummaryByMetaId, getWorkerPerformance, checkAuthorisedResourceAccess, getClientsById, fetchStartDateYearlyRules } from '../models';
import { getAllSites } from "../services";

import {
    deletePayrollMetaById,
    addPayrollData, createPayrollMeta, getAgencyAssociationWithDetailedMarginsByAgencyIdAndClientId,
    addPayrollSummaryData, getTimeAndAttendanceListWithPayrollSummary, getPayrollMeta, getTimeAndAttendanceCountWithTotalPayrollSaving, getPayrollsByPayrollMetaId, deletePayrollByMetaId, deletePayrollData, getStatusCountForWorkers, addHolidayPayrollSummaryData,
    getSupervisorsWeeklyDataHelper, getSupervisorsWeeklyDataCountHelper, getSupervisorDetailsByPayrollMetaId, getSupervisorsWorkerDetailsCountHelper, deleteHolidayPayrollSummaryByMetaId,
    insertPayrollDetailedSummary, getPayrollDetialedSummaryByPayrollMetaId, getPayrollInvoiceHelper, deletePayrollDetailedSummaryByMetaId
} from "./../models";
import { ErrorCodes, ErrorResponse, TimeAndAttendanceStatus } from "./../common";
import { CalculationPathWays, RoleType } from "../common/enum";

/**
 * Service to GET payroll summary. 
 */
export const getPayrollSummaryService = async (data, loggedInUser, requestSupervisorsData = false) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;
    let whereClause = `time_and_attendance.clientId = :client_id`;
    if (updatedPayload.agency_id) {
        whereClause = `${whereClause} AND time_and_attendance.agencyId = :agency_id`;
    }
    if (updatedPayload.site_id) {
        whereClause = `${whereClause} AND time_and_attendance.siteId = :site_id`;
    }
    if (updatedPayload.region_id) {
        whereClause = `${whereClause} AND site.regionId = :region_id`;
    }
    if (updatedPayload.start_date && updatedPayload.end_date) {
        whereClause = `${whereClause} AND time_and_attendance.startDate >= :start_date AND time_and_attendance.endDate <= :end_date`;
    }

    // data will also have list of payroll_summary.payroll_meta_id, so required to return data based on it.
    if (data.payroll_meta_ids && data.payroll_meta_ids.length > 0) {
        whereClause = `${whereClause} AND payroll_summary.payroll_meta_id IN (:payroll_meta_ids)`;
    }

    let whereClauseValue = { "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "region_id": data.region_id, "start_date": data.start_date, "end_date": data.end_date, "payroll_meta_ids": data.payroll_meta_ids }

    let sortBy = data.sort_by.toLowerCase();
    sortBy = sortBy === "week" ? "start_date" : sortBy;

    const yearlyRules = await fetchStartDateYearlyRules(data.client_id);

    // Get Payroll Data
    if (!requestSupervisorsData) {
        const isCustomSortBy = ["worked_hours_charge", "other_assignment_pay_value", "credit_value", "total_cost"].includes(sortBy);
        if (isCustomSortBy) sortBy = "start_date"

        let { payrolls, sumAndCount } = await EasyPromiseAll({
            payrolls: getTimeAndAttendanceListWithPayrollSummary(whereClause, whereClauseValue, data.page || 1, data.limit || 10, sortBy || "id", data.sort_type || "asc"),
            sumAndCount: getTimeAndAttendanceCountWithTotalPayrollSaving(whereClause, whereClauseValue)
        })
        const weekMap = await calculateWeeksForAllDatePairs(payrolls, yearlyRules);
        payrolls = await Promise.all(_.map(payrolls, async (p) => {
            const exisitingWorkerPerformance = await getWorkerPerformance({ startDate: p.start_date, endDate: p.end_date, clientId: data.client_id, siteId: data.site_id });
            // Lookup the pre-calculated week value
            const key = `${p.start_date}|${p.end_date}`;
            p.week = weekMap.get(key);
            p.worker_clock_report = p.time_and_attendance_status ? true : false;
            p.detail_payroll_report = p.payroll_meta_status ? true : false;
            p.total_agency_pay_status = p.total_agency_pay_status ? true : false;
            p.uploaded_tna_sheet = (await getSignedUrlForGetObject(config.BUCKET_NAME, config.TNA_SHEETS_BUCKET_FOLDER, p.time_and_attendance_name)).url;
            p.uploaded_tap_sheet = p.total_agency_pay_status ? `Tap_week_${p.week}.csv` : null;
            p.uploaded_performance_sheet = exisitingWorkerPerformance.length > 0 ? `Performance_${exisitingWorkerPerformance[0].uploadedBy.toLowerCase()}_week_${p.week}.csv` : null;
            p.worked_hours_charge = p.total_worked_charge || p.total_charge;
            // p.other_assignment_pay_value = p.total_agency_pay_id ? _.round((await sumTotalAgencyPayValueForAllWorkers(p.total_agency_pay_id) - p.total_pay), 2) : 0;
            p.credit_value = _.round(getNumberValue(p.clearvue_savings) - getNumberValue(p.total_wtr_costs), 2);
            p.total_cost = _.round(getNumberValue(p.worked_hours_charge) + getNumberValue(p.total_wtr_costs) - getNumberValue(p.clearvue_savings) - getNumberValue(p.training_credits), 2);
            p.accrual_value = p.calculated_pathway === CalculationPathWays.YESYES ? p.accrual_value : null;
            p.highlighted_payroll = p.calculated_pathway === CalculationPathWays.YESNO ? 1 : 0;
            return p;
        }));

        // sorting of results by "sent_to" filed.
        if (isCustomSortBy) {
            sortBy = data.sort_by.toLowerCase();
            const sortOrder = data.sort_type.toLowerCase() == 'asc' ? 1 : -1;
            payrolls.sort((a, b) => {
                const aValue = getNumberValue(a[sortBy]);
                const bValue = getNumberValue(b[sortBy]);

                return (aValue - bValue) * sortOrder;
            });

        }

        return [200, {
            ok: true,
            payroll_list: payrolls,
            count: sumAndCount.count,
            total: sumAndCount.total
        }];
    }
    // Get Supervisor Weekly Data
    else {
        let { supervisors, sumAndCount } = await EasyPromiseAll({
            supervisors: getSupervisorsWeeklyDataHelper(whereClause, whereClauseValue, data.page || 1, data.limit || 10, sortBy || "id", data.sort_type || "asc"),
            sumAndCount: getSupervisorsWeeklyDataCountHelper(whereClause, whereClauseValue)
        })
        const weekMap = await calculateWeeksForAllDatePairs(supervisors, yearlyRules);

        supervisors = await Promise.all(_.map(supervisors, async (p) => {
            // Lookup the pre-calculated week value
            const key = `${p.start_date}|${p.end_date}`;
            p.week = weekMap.get(key);
            return p
        }));

        return [200, {
            ok: true,
            supervisors_data: supervisors,
            count: sumAndCount.count
        }];
    };
};

/**
 * Service for downloading the payroll summary.
 */
export const downloadPayrollSummaryService = async (payrollMetaId) => {
    const payrolls = await getPayrollsByPayrollMetaId(payrollMetaId)

    let workerIdList = payrolls.map(wkr => wkr.worker_id);
    const pensionStatus = await getStatusCountForWorkers(workerIdList);

    const pensionStatusMap = new Map<string, string>();
    pensionStatus.forEach(status => {
        pensionStatusMap.set(status.worker_id, status.status_count);
    });

    payrolls.forEach(element => {
        element.total_margin = element.total_margin ? element.total_margin : element.total_charge - element.actual_employment_costs;
        element.under_twentyone = Boolean(element.under_twentyone);
        element.under_twentytwo = Boolean(element.under_twentytwo);
        element.within_twelveweeks = Boolean(element.within_twelveweeks);
        element.pension_opt_out = element.pension_opt_out_prev == null ? Boolean(element.pension_opt_out) : Boolean(element.pension_opt_out_prev);
        // element.other_assignment_pay_value = element.total_agency_pay ? _.round((element.total_agency_pay - (element.total_pay || 0)), 2) : '0';
        element.credit_value = _.round((element.total_savings || 0) - (element.wtr_cost || 0), 2);
        element.worked_hours_charge = element.total_worked_charge || element.total_charge;
        element.total_cost = _.round(getNumberValue(element.worked_hours_charge) + getNumberValue(element.wtr_cost) - getNumberValue(element.total_savings), 2);

        element.accrual_value = element.calculated_pathway === CalculationPathWays.YESYES ? element.client_wtr_accrual : null;
        delete element.pension_opt_out_prev;

        const statusCount = pensionStatusMap.get(element.worker_id) || '0';
        element.pension_status_count = parseInt(statusCount);
    });
    return [200, {
        ok: true,
        payroll_list: payrolls,
    }];
}

/**
 * Service to GET payroll summary. 
 */
export const getPayrollInvoiceService = async (data, loggedInUser) => {

    let whereClause = `payroll_detailed_summary.clientId = :client_id`;
    if (data.agency_id) {
        whereClause = `${whereClause} AND payroll_detailed_summary.agencyId = :agency_id`;
    }
    if (data.site_id) {
        whereClause = `${whereClause} AND payroll_detailed_summary.siteId = :site_id`;
    }
    if (data.region_id) {
        whereClause = `${whereClause} AND site.regionId = :region_id`;
    }
    if (data.start_date && data.end_date) {
        whereClause = `${whereClause} AND payroll_detailed_summary.startDate >= :start_date AND payroll_detailed_summary.endDate <= :end_date`;
    }

    // data will also have list of payroll_summary.payroll_meta_id, so required to return data based on it.
    if (data.payroll_meta_ids && data.payroll_meta_ids.length > 0) {
        whereClause = `${whereClause} AND payroll_detailed_summary.payroll_meta_id IN (:payroll_meta_ids)`;
    }

    let whereClauseValue = { "client_id": data.client_id, "agency_id": data.agency_id, "site_id": data.site_id, "region_id": data.region_id, "start_date": data.start_date, "end_date": data.end_date, "payroll_meta_ids": data.payroll_meta_ids }

    const yearlyRules = await fetchStartDateYearlyRules(data.client_id);

    let invoiceData = await getPayrollInvoiceHelper(whereClause, whereClauseValue, "payroll_meta_id", "ASC");
    const weekMap = await calculateWeeksForAllDatePairs(invoiceData, yearlyRules);

    invoiceData = await Promise.all(_.map(invoiceData, async (p) => {
        // Lookup the pre-calculated week value
        const key = `${p.start_date}|${p.end_date}`;
        p.week = weekMap.get(key);
        return p;
    }));

    return [200, {
        ok: true,
        payroll_list: invoiceData
    }];
};

/**
 * Service for downloading the payroll detailed summary.
 */
export const downloadPayrollDetialedSummaryService = async (payrollMetaId) => {

    const detailedSummary = await getPayrollDetialedSummaryByPayrollMetaId(payrollMetaId);
    if (!detailedSummary || detailedSummary.length === 0) {
        return [404, ErrorResponse.DetailedSummaryNotExist];
    }

    // Transform boolean fields
    detailedSummary.forEach(record => {
        record.under_twentyone = Boolean(record.under_twentyone);
        record.under_twentytwo = Boolean(record.under_twentytwo);
        record.within_twelveweeks = Boolean(record.within_twelveweeks);
        record.pension_opt_out = Boolean(record.pension_opt_out);
        record.adjustment = Boolean(record.adjustment) ? "Yes" : "No";
        record.pay_correction = Boolean(record.pay_correction) ? "Yes" : "No";
        record.pension_status_count = parseInt(record.pension_status_count || '0');
        record.pension_opt_out = record.pension_opt_out_prev == null ? Boolean(record.pension_opt_out) : Boolean(record.pension_opt_out_prev);
    });

    return [200, {
        ok: true,
        detailed_summary: detailedSummary
    }];

};

/**
 * Service for getSupervisorsWorkersData
 */
export const getSupervisorsWorkersDataService = async (payrollMetaId, params, loggedInUser) => {
    // let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(params, loggedInUser);
    // if (isError) return error;
    let sortBy = params.sort_by.toLowerCase();
    let supervisors = await getSupervisorDetailsByPayrollMetaId(payrollMetaId, params.page || 1, params.limit || 10, sortBy || "employee_id", params.sort_type || "asc")
    let totalCount = await getSupervisorsWorkerDetailsCountHelper(payrollMetaId);

    supervisors = await Promise.all(_.map(supervisors, async (worker) => {
        worker.role = `${worker.job_name} - ${worker.shift_name} - ${RoleType[worker.job_type]} - ${worker.department_name}`;
        return worker;
    }));

    return [200, {
        ok: true,
        total_count: totalCount.count,
        supervisors_list: supervisors,
    }];
}


export const calculatePaytypeWiseRatecardMargin = async (payType, hours, marginsToApply, worker_transport = null) => {
    // Ensure hours is a number and default to 0 if not provided
    hours = Number(hours) || 0;

    switch (payType) {
        case PayTypes.STANDARD:
            const standardMargin = _.round(_.multiply(hours, marginsToApply.margin), 2);
            const standardSSPMargin = _.round(_.multiply(hours, marginsToApply.ssp), 2);
            const standardTransportMargin = worker_transport ? _.round(_.multiply(hours, marginsToApply.transportFee), 2) : 0;
            return _.round(_.subtract(_.sum([standardMargin, standardSSPMargin]), standardTransportMargin), 2);

        case PayTypes.OVERTIME:
            const overtimeMargin = _.round(_.multiply(hours, marginsToApply.overtimeMargin), 2);
            const overtimeSSPMargin = _.round(_.multiply(hours, marginsToApply.ssp), 2);
            const overtimeTransportMargin = worker_transport ? _.round(_.multiply(hours, marginsToApply.transportFee), 2) : 0;
            return _.round(_.subtract(_.sum([overtimeMargin, overtimeSSPMargin]), overtimeTransportMargin), 2);

        case PayTypes.WEEKEND:
            return _.round(_.multiply(hours, marginsToApply.margin), 2);

        case PayTypes.BH:
            return _.round(_.multiply(hours, marginsToApply.bhMargin), 2);

        case PayTypes.SUSPENSION:
            return _.round(_.multiply(hours, marginsToApply.suspensionMargin), 2);

        case PayTypes.SUPERVISOR_STANDARD:
            return _.round(_.multiply(hours, marginsToApply.supervisorStandardMargin), 2);

        case PayTypes.SP:
        case PayTypes.NSP:
            return _.round(_.multiply(hours, marginsToApply.nspMargin), 2);

        case PayTypes.INDUCTION_TRAINING:
            return _.round(_.multiply(hours, marginsToApply.inductionTrainingMargin), 2);

        case PayTypes.TRAINING:
            return _.round(_.multiply(hours, marginsToApply.trainingMargin), 2);

        case PayTypes.SUPERVISOR_OVERTIME:
            return _.round(_.multiply(hours, marginsToApply.supervisorOvertimeMargin), 2);

        default:
            return 0;
    }
}

/**
 * Service to adding the payroll.
 */
export const addPayrollDataAsPerTimeAndAttendance = async (payrollData, payload, loggedInUser) => {
    let payroll;
    try {
        const exisitingPayrollMeta = await getPayrollMeta({ startDate: payload.start_date, endDate: payload.end_date, agencyId: payload.agency_id, clientId: payload.client_id, siteId: payload.site_id });
        if (exisitingPayrollMeta) {
            return [409, ErrorResponse.FileAlreadyExists];
        }

        const payrollMetaData = {
            path: null,
            name: uuid.v4(),
            week: payload.week,
            status: TimeAndAttendanceStatus.PROCESSED,
            timeAndAttendanceId: payload.time_and_attendance_id,
            startDate: payload.start_date,
            endDate: payload.end_date,
            clientId: payload.client_id,
            agencyId: payload.agency_id,
            siteId: payload.site_id,
            siteCostCentre: payload.site_cost_centre,
            vatCode: payload.vat_code,
            vatRate: payload.vat_rate,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id,
        }
        payroll = await createPayrollMeta(payrollMetaData);

        const payrollDataToInsert = [];
        const payrollDetialedSummaryDataToInsert = [];
        const holidayPayrollSummary = [];
        const association = await getAgencyAssociationWithDetailedMarginsByAgencyIdAndClientId(payload.agency_id, payload.client_id, payload.site_id);

        const calculatedPathway = await findCalculatedPathway(association.holidayActivation, association.holidayCostRemoved);

        let supervisorCount = 0;
        let adjustmentCount = 0;
        let payCorrectionCount = 0;

        await Promise.all(_.map(payrollData, async (payrollRecord) => {
            // Calculate total pay
            const totalPay = _.round(payrollRecord.total_pay, 2);

            // Worker length of service from worker ID.
            const lengthOfService = await getDurationBetweenDates(payrollRecord.worker_assignment_date, payload.end_date);
            const within12Weeks = lengthOfService.asWeeks < 12;

            const ageDuration = await getDurationBetweenDates(payrollRecord.worker_dob, payload.end_date);
            const under21 = ageDuration.asYears < 21;
            const under22 = ageDuration.asYears < 22;

            const marginsToApply = await getApplicableMargin(association.margins, lengthOfService);

            let totalNI = 0;
            let totalPension = 0;
            let totalHoliday = 0;
            let totalAppLevy = 0;
            let totalAppLevyWithWTR = association.holidayCostRemoved ? 0 : null;
            let appLevyDifference = null;
            let totalWorkerHolidayAccrual = null;
            let holidayEmploymentCost = null;
            let trainingEmploymentCost = 0;
            let trainingHours = 0;
            let otherAssignmentPay = payrollRecord.worker_tap_value ? _.round(Number(payrollRecord.worker_tap_value) - totalPay, 2) : 0;
            let holidayPayTypeValue = 0;
            let holidayPayTypeHours = 0;

            for (let i = 0; i < payrollRecord.worker_paytypes_list.length; i++) {
                // get los rule by pay type
                let holidayPayPercent = null;
                const isHolidayPaytype = payrollRecord.worker_paytypes_list[i].worker_pay_type == PayTypes.HOLIDAY;
                const isInductionTrainingPaytype = payrollRecord.worker_paytypes_list[i].worker_pay_type == PayTypes.INDUCTION_TRAINING;

                const isPaytypeToExcludeHours = payrollRecord.worker_paytypes_list[i].is_paytype_to_exclude_hours;
                const isExpnesesPaytype = payrollRecord.worker_paytypes_list[i].worker_pay_type == PayTypes.EXPENSES;

                const payPerPaytype = payrollRecord.worker_paytypes_list[i].pay;
                const chargePerPaytype = payrollRecord.worker_paytypes_list[i].charge;
                const hoursPerPaytype = payrollRecord.worker_paytypes_list[i].week_hours;

                adjustmentCount += payrollRecord.worker_paytypes_list[i].adjustment ? 1 : 0;
                payCorrectionCount += payrollRecord.worker_paytypes_list[i].pay_correction ? 1 : 0;

                if (!isExpnesesPaytype) {
                    if (isHolidayPaytype) {
                        holidayPayPercent = 0;
                        holidayPayTypeValue += payrollRecord.worker_paytypes_list[i].pay;
                        holidayPayTypeHours += payrollRecord.worker_paytypes_list[i].week_hours;
                    } else {
                        let rules = await getRuleBySiteAndRoleType(payload.site_id, payrollRecord.worker_role_type, payrollRecord.worker_paytypes_list[i].worker_pay_type);
                        let los = JSON.parse(rules.los);

                        if (los) {
                            let yearData = Object.keys(los).sort((a, b) => parseFloat(b) - parseFloat(a));
                            if (los[String(lengthOfService.years)]) {
                                holidayPayPercent = los[String(lengthOfService.years)].value;
                            } else if (lengthOfService.years == 0 && lengthOfService.months >= 6) {
                                holidayPayPercent = los['0.5'] ? los['0.5'].value : null;
                            } else if (yearData.length > 0 && (lengthOfService.years >= 1 || lengthOfService.months >= 6)) {
                                for (let index = 0; index < yearData.length; index++) {
                                    const element = yearData[index];
                                    if (Number(element) < lengthOfService.years) {
                                        holidayPayPercent = los[String(element)].value;
                                        break;
                                    }
                                }
                            }
                        }

                        if (holidayPayPercent == null && !within12Weeks) {
                            holidayPayPercent = rules.post_twelve_week;
                        } else if (holidayPayPercent == null) {
                            holidayPayPercent = rules.pre_twelve_week;
                        }
                    }

                    // Calculate NI
                    let NICostPerPayType = 0;
                    NICostPerPayType = await calculateNICost(association, payrollRecord, totalPay, payload, payrollRecord.worker_paytypes_list[i]);
                    totalNI = _.round(_.sum([totalNI, NICostPerPayType]), 2);


                    // add NI paytype holiday to holiday employment cost
                    if (isHolidayPaytype) {
                        holidayEmploymentCost = holidayEmploymentCost || 0;
                        holidayEmploymentCost += NICostPerPayType;
                    }


                    let pensionCostPerPayType = 0;
                    let holidayPerPayType = 0;
                    let appLevyPerPayType = 0;
                    let appLevyWithWTRPerPayType = null;
                    let workerHolidayAccrualPerPayType = 0;

                    // Calculate pension
                    pensionCostPerPayType = await calculatePensionCost(association, payrollRecord, totalPay, payload, payrollRecord.worker_paytypes_list[i]);
                    totalPension = _.round(_.sum([totalPension, pensionCostPerPayType]), 2);

                    // add holiday paytype pension to holiday employment cost
                    if (isHolidayPaytype) {
                        holidayEmploymentCost += pensionCostPerPayType;
                    }

                    // Calculate holiday
                    if (association.holidayActivation) {
                        holidayPerPayType = await calculateHolidayCost(association, NICostPerPayType, pensionCostPerPayType, holidayPayPercent, payrollRecord.worker_paytypes_list[i]);
                        workerHolidayAccrualPerPayType = await calculateWorkerHolidayAccrual(holidayPayPercent, payrollRecord.worker_paytypes_list[i])
                        totalWorkerHolidayAccrual = _.round(_.sum([totalWorkerHolidayAccrual, workerHolidayAccrualPerPayType]), 2);
                    } else {
                        holidayPerPayType = await calculateHolidayCostForNonHolidayEmp(association, NICostPerPayType, pensionCostPerPayType, holidayPayPercent, payrollRecord.worker_paytypes_list[i])
                    }
                    totalHoliday = _.round(_.sum([totalHoliday, holidayPerPayType]), 2);

                    // Calculate app levy 
                    if (association.holidayActivation) {
                        ({ appLevyPerPayType, appLevyWithWTRPerPayType } = await calculateAppLevyCost(calculatedPathway, holidayPerPayType, payrollRecord.worker_paytypes_list[i], payload));
                        if (calculatedPathway === CalculationPathWays.YESYES) {
                            totalAppLevyWithWTR = _.round(_.sum([totalAppLevyWithWTR, appLevyWithWTRPerPayType]), 2);
                        }
                    } else {
                        appLevyPerPayType = await calculateAppLevyCostForNonHolidayEmp(association, holidayPerPayType, payrollRecord.worker_paytypes_list[i], payload);
                    }
                    totalAppLevy = _.round(_.sum([totalAppLevy, appLevyPerPayType]), 2);

                    // add holiday paytype app levy to holiday employment cost
                    if (isHolidayPaytype) {
                        holidayEmploymentCost += appLevyPerPayType;
                    }

                    if (isInductionTrainingPaytype) {
                        trainingEmploymentCost += (payrollRecord.worker_paytypes_list[i].pay + NICostPerPayType + pensionCostPerPayType + holidayPerPayType + appLevyPerPayType);
                        trainingHours += payrollRecord.worker_paytypes_list[i].week_hours;
                    }

                    const actualCostToEmployPerPaytype = _.round(
                        payPerPaytype + NICostPerPayType + pensionCostPerPayType + holidayPerPayType + appLevyPerPayType
                        + (isHolidayPaytype ? payPerPaytype * (calculatedPathway === CalculationPathWays.YESYES ? 0 : 1) : 0),
                        2
                    );


                    const totalMarginPerPaytype = _.round(_.subtract(chargePerPaytype, actualCostToEmployPerPaytype), 2);
                    const actalMarginPerHourPerPaytype = (!isPaytypeToExcludeHours && hoursPerPaytype) ? _.round(_.divide(totalMarginPerPaytype, hoursPerPaytype), 2) : 0;
                    const ratecardMarginPerPaytype = await calculatePaytypeWiseRatecardMargin(payrollRecord.worker_paytypes_list[i].worker_pay_type, hoursPerPaytype, marginsToApply, payrollRecord.worker_transport);

                    const clearvueSavingsPerPayType = _.round(_.subtract(totalMarginPerPaytype, ratecardMarginPerPaytype), 2);
                    const creditPerHourPerPaytype = clearvueSavingsPerPayType && hoursPerPaytype ? _.round(_.divide(clearvueSavingsPerPayType, hoursPerPaytype), 2) : 0;
                    const holidayEmploymentCostPerPayType = isHolidayPaytype ? (NICostPerPayType + pensionCostPerPayType + appLevyPerPayType) : null;
                    const wtrCostPerPayType = calculatedPathway === CalculationPathWays.YESYES ? _.round(((isHolidayPaytype ? payPerPaytype : 0) + holidayEmploymentCostPerPayType || 0), 2) : (holidayEmploymentCostPerPayType || 0);
                    const creditValuePerPayType = _.round(_.subtract(clearvueSavingsPerPayType, wtrCostPerPayType), 2);

                    const appLevyDifferencePerPayType = calculatedPathway === CalculationPathWays.YESYES ? _.round(_.subtract(appLevyWithWTRPerPayType, appLevyPerPayType), 2) : 0;
                    const accrualValuePerPayType = calculatedPathway === CalculationPathWays.YESYES ? _.round(_.sum([holidayPerPayType, appLevyDifferencePerPayType]), 2) : holidayPerPayType;

                    const totalWorkedChargePerPaytype = calculatedPathway === CalculationPathWays.YESYES ? _.round(_.subtract(chargePerPaytype, (isHolidayPaytype ? payPerPaytype : 0)), 2) : null;
                    const workedHoursChargePerPaytype = totalWorkedChargePerPaytype || chargePerPaytype;
                    const totalCostPerPayType = _.round(getNumberValue(workedHoursChargePerPaytype) + getNumberValue(wtrCostPerPayType) - getNumberValue(clearvueSavingsPerPayType), 2);

                    payrollDetialedSummaryDataToInsert.push({
                        payrollMetaId: payroll.id,
                        workerId: payrollRecord.worker_id,
                        clientId: payload.client_id,
                        agencyId: payload.agency_id,
                        shiftId: payrollRecord.worker_paytypes_list[i].shift_id,
                        departmentId: payrollRecord.worker_paytypes_list[i].department_id,
                        siteId: payload.site_id,
                        week: payload.week,
                        startDate: payload.start_date,
                        endDate: payload.end_date,
                        payType: payrollRecord.worker_paytypes_list[i].worker_pay_type,
                        adjustment: payrollRecord.worker_paytypes_list[i].adjustment,
                        payCorrection: payrollRecord.worker_paytypes_list[i].pay_correction,
                        totalHours: hoursPerPaytype,
                        totalCharge: chargePerPaytype,
                        actualCostToEmploy: actualCostToEmployPerPaytype,
                        totalMargin: totalMarginPerPaytype,
                        actualMargin: actalMarginPerHourPerPaytype,
                        rateCardMargin: ratecardMarginPerPaytype,
                        creditPerHour: creditPerHourPerPaytype,
                        clearvueSavings: clearvueSavingsPerPayType,
                        totalPay: payPerPaytype,
                        nationalInsurance: NICostPerPayType,
                        pension: pensionCostPerPayType,
                        apprenticeshipLevy: appLevyPerPayType,
                        holiday: holidayPerPayType,
                        holidayPayTypeValue: isHolidayPaytype ? payPerPaytype : 0,
                        holidayEmploymentCost: holidayEmploymentCostPerPayType,
                        underTwentyone: under21,
                        underTwentytwo: under22,
                        withinTwelveweeks: within12Weeks,
                        otherAssignmentPayValue: otherAssignmentPay,
                        creditValue: creditValuePerPayType,
                        accrualValue: accrualValuePerPayType,
                        wtrCost: wtrCostPerPayType,
                        appLevyDifference: appLevyDifferencePerPayType,
                        appLevyWithWTR: appLevyWithWTRPerPayType,
                        totalWorkedCharge: totalWorkedChargePerPaytype,
                        workedHoursCharge: workedHoursChargePerPaytype,
                        totalCost: totalCostPerPayType,
                        departmentCostCentre: payrollRecord.worker_paytypes_list[i].department_cost_centre,
                        createdBy: loggedInUser.user_id,
                        updatedBy: loggedInUser.user_id,
                    })
                } else {
                    payrollDetialedSummaryDataToInsert.push({
                        payrollMetaId: payroll.id,
                        workerId: payrollRecord.worker_id,
                        clientId: payload.client_id,
                        agencyId: payload.agency_id,
                        shiftId: payrollRecord.worker_paytypes_list[i].shift_id,
                        departmentId: payrollRecord.worker_paytypes_list[i].department_id,
                        siteId: payload.site_id,
                        week: payload.week,
                        startDate: payload.start_date,
                        endDate: payload.end_date,
                        payType: payrollRecord.worker_paytypes_list[i].worker_pay_type,
                        adjustment: payrollRecord.worker_paytypes_list[i].adjustment,
                        payCorrection: payrollRecord.worker_paytypes_list[i].pay_correction,
                        totalHours: hoursPerPaytype,
                        totalCharge: chargePerPaytype,
                        actualCostToEmploy: 0,
                        totalMargin: 0,
                        actualMargin: 0,
                        rateCardMargin: 0,
                        creditPerHour: 0,
                        clearvueSavings: 0,
                        totalPay: payPerPaytype,
                        nationalInsurance: 0,
                        pension: 0,
                        apprenticeshipLevy: 0,
                        holiday: 0,
                        holidayPayTypeValue: 0,
                        holidayEmploymentCost: 0,
                        underTwentyone: under21,
                        underTwentytwo: under22,
                        withinTwelveweeks: within12Weeks,
                        otherAssignmentPayValue: otherAssignmentPay,
                        creditValue: 0,
                        accrualValue: 0,
                        wtrCost: 0,
                        appLevyDifference: 0,
                        appLevyWithWTR: 0,
                        totalWorkedCharge: 0,
                        workedHoursCharge: 0,
                        totalCost: 0,
                        departmentCostCentre: payrollRecord.worker_paytypes_list[i].department_cost_centre,
                        createdBy: loggedInUser.user_id,
                        updatedBy: loggedInUser.user_id,
                    })
                }
            }

            if (calculatedPathway === CalculationPathWays.YESYES) {
                appLevyDifference = 0;
                appLevyDifference = _.round(_.subtract(totalAppLevyWithWTR, totalAppLevy), 2);
            }

            // Calculate balance
            let balance = holidayPayTypeValue ? _.round(_.subtract(holidayPayTypeValue, totalWorkerHolidayAccrual), 2) : 0;

            totalNI = (
                moment(payload.start_date).diff(moment(payrollRecord.worker_dob), 'years') >= 21 &&
                (
                    (association.totalAssignmentPay && payrollRecord.worker_tap_value != null && Number(payrollRecord.worker_tap_value) > Number(payload.ni_threshold)) ||
                    (!association.totalAssignmentPay || !(payrollRecord.worker_tap_value != null)) && totalPay > Number(payload.ni_threshold)
                )
            ) ? totalNI : 0;

            const totalHour = _.round(payrollRecord.total_hour, 2);

            const discount = 0; // Currently not used
            const totalCharge = _.round(payrollRecord.total_charge, 2);

            // Calculate Actual Cost
            let actualCostToEmploy = totalPay + totalNI + totalPension + totalHoliday + totalAppLevy;
            if (calculatedPathway !== CalculationPathWays.YESYES) actualCostToEmploy -= holidayPayTypeValue;

            actualCostToEmploy = _.round(actualCostToEmploy, 2);


            // Calculate accrual value
            let accrualValue = calculatedPathway === CalculationPathWays.YESYES ? _.round((totalHoliday + appLevyDifference), 2) : totalHoliday;

            // Calculate worked hours charge
            let totalWorkedCharge = calculatedPathway === CalculationPathWays.YESYES ? _.round(_.subtract(totalCharge, holidayPayTypeValue), 2) : null;

            // Calculate total Standard Margin
            let totalStandardMargin = _.round(_.multiply(payrollRecord.standard_hours || 0, marginsToApply.margin), 2);

            // Calculate total Overtime Margin
            let totalOvertimeMargin = _.round(_.multiply(payrollRecord.overtime_hours || 0, marginsToApply.overtimeMargin), 2);

            // Calculate total Bh Margin
            let totalBhMargin = _.round(_.multiply(payrollRecord.bh_hours || 0, marginsToApply.bhMargin), 2);

            // Calculate total Nsp Margin
            let totalNspMargin = _.round(_.multiply(payrollRecord.nsp_hours || 0, marginsToApply.nspMargin), 2);

            // Calculate total Supervisor standard Margin
            let totalSupervisorStandardMargin = _.round(_.multiply(payrollRecord.supervisor_standard_hours || 0, marginsToApply.supervisorStandardMargin), 2);

            // Calculate total Supervisor overtime Margin
            let totalSupervisorOvertimeMargin = _.round(_.multiply(payrollRecord.supervisor_overtime_hours || 0, marginsToApply.supervisorOvertimeMargin), 2);

            // Calculate total Suspension Margin
            let totalSuspensionMargin = _.round(_.multiply(payrollRecord.suspension_hours || 0, marginsToApply.suspensionMargin), 2);

            // Calculate total Training Margin
            let totalInductionTrainingMargin = _.round(_.multiply(payrollRecord.induction_training_hours || 0, marginsToApply.inductionTrainingMargin), 2);

            // Calculate total Training Margin
            let totalTrainingMargin = _.round(_.multiply(payrollRecord.training_hours || 0, marginsToApply.trainingMargin), 2);

            // Calculate total Transport Margin
            let totalTransportMargin = payrollRecord.worker_transport ? _.round(_.multiply(_.round(_.sum([payrollRecord.standard_hours || 0, payrollRecord.overtime_hours || 0]), 2), marginsToApply.transportFee), 2) : 0;

            // Calculate total SSP Margin
            let totalSSPMargin = _.round(_.multiply(_.round(_.sum([payrollRecord.standard_hours || 0, payrollRecord.overtime_hours || 0]), 2), marginsToApply.ssp), 2);

            // Calculate total Standard Margin
            let totalWeekendMargin = _.round(_.multiply(payrollRecord.weekend_hours || 0, marginsToApply.margin), 2);

            // Total Margin
            const totalMargin = _.round(_.subtract(totalCharge, actualCostToEmploy), 2);

            // Actual margin per hour
            const actalMarginPerHour = totalHour ? _.round(_.divide(totalMargin, totalHour), 2) : 0;

            let rateCardMargin = _.sum([totalStandardMargin, totalOvertimeMargin, totalSSPMargin, totalInductionTrainingMargin, totalTrainingMargin, totalBhMargin, totalSupervisorStandardMargin, totalSupervisorOvertimeMargin, totalNspMargin, totalSuspensionMargin, totalWeekendMargin]);
            rateCardMargin = _.round((_.subtract(rateCardMargin, totalTransportMargin)), 2);

            const clearvueSavings = _.round(_.subtract(totalMargin, rateCardMargin), 2);

            // Credit Per Hour
            const creditPerHour = clearvueSavings && totalHour ? _.round(_.divide(clearvueSavings, totalHour), 2) : 0;

            // Worked hours saving for detailed summary report
            const workedHoursSaving = association.holidayActivation ? _.round(_.subtract(totalHour, holidayPayTypeHours), 2) : null;

            supervisorCount += payrollRecord.is_supervisor ? 1 : 0;
            payrollDataToInsert.push({
                payrollMetaId: payroll.id,
                totalHours: totalHour,
                supervisorHours: _.round(payrollRecord.supervisor_hours, 2),
                nonSupervisorHours: _.round(payrollRecord.non_supervisor_hours, 2),
                holidayPayTypeValue: holidayPayTypeValue,
                totalCharge: totalCharge,
                supervisorCharges: _.round(payrollRecord.supervisor_charges, 2),
                nonSupervisorCharges: _.round(payrollRecord.non_supervisor_charges, 2),
                totalPay: totalPay,
                otherAssignmentPay: otherAssignmentPay,
                flaggedSupervisor: payrollRecord.is_supervisor,
                nationalInsurance: totalNI,
                holiday: totalHoliday,
                apprenticeshipLevy: totalAppLevy,
                discount: discount,
                pension: totalPension,
                clientId: payload.client_id,
                agencyId: payload.agency_id,
                siteId: payload.site_id,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
                week: payload.week,
                startDate: payload.start_date,
                endDate: payload.end_date,
                workerId: payrollRecord.worker_id,
                withinTwelveweeks: within12Weeks,
                underTwentyone: under21,
                underTwentytwo: under22,
                limitedHours: payrollRecord.limited_hours,
                actualCostToEmploy: actualCostToEmploy,
                trainingHours: trainingHours,
                trainingEmploymentCost: trainingEmploymentCost,
                totalAgencyMargin: totalMargin,
                actualMargin: actalMarginPerHour,
                totalMargin: totalMargin,
                rateCardMargin: rateCardMargin,
                creditPerHour: creditPerHour,
                clearvueSavings: clearvueSavings,
                pensionOptOutPrev: payrollRecord.pension_opt_out,
            })

            holidayPayrollSummary.push({
                payrollMetaId: payroll.id,
                workerId: payrollRecord.worker_id,
                clientId: payload.client_id,
                agencyId: payload.agency_id,
                siteId: payload.site_id,
                week: payload.week,
                startDate: payload.start_date,
                endDate: payload.end_date,
                totalAppLevyWithWtr: totalAppLevyWithWTR,
                appLevyDifference: appLevyDifference,
                balance: balance,
                accrualValue: accrualValue,
                workerHolidayAccrualValue: totalWorkerHolidayAccrual,
                totalWorkedCharge: totalWorkedCharge,
                holidayEmploymentCost: holidayEmploymentCost,
                calculatedPathway: calculatedPathway,
                wtrCost: calculatedPathway == CalculationPathWays.YESYES ? ((holidayPayTypeValue || 0) + (holidayEmploymentCost || 0)) : holidayEmploymentCost,
                workedHoursSaving: workedHoursSaving,
                createdBy: loggedInUser.user_id,
                updatedBy: loggedInUser.user_id,
            })
        }));

        // calculate holiday employment cost, total worked charge and total accrual value
        let total_holiday_employment_cost = null;
        let total_worked_charge = null;
        let total_accrual_value = null;
        let total_wtr_Cost = null;
        holidayPayrollSummary.forEach(summary => {
            if (summary.holidayEmploymentCost !== null && summary.holidayEmploymentCost !== 0) {
                if (total_holiday_employment_cost === null) {
                    total_holiday_employment_cost = 0;
                }
                total_holiday_employment_cost += summary.holidayEmploymentCost;
            }

            if (summary.wtrCost !== null && summary.wtrCost !== 0) {
                if (total_wtr_Cost === null) {
                    total_wtr_Cost = 0;
                }
                total_wtr_Cost += summary.wtrCost;
            }

            if (summary.totalWorkedCharge !== null && summary.totalWorkedCharge !== 0) {
                if (total_worked_charge === null) {
                    total_worked_charge = 0;
                }
                total_worked_charge += summary.totalWorkedCharge;
            }

            if (summary.accrualValue !== null && summary.accrualValue !== 0) {
                if (total_accrual_value === null) {
                    total_accrual_value = 0;
                }
                total_accrual_value += summary.accrualValue;
            }
        });

        // calculate self bill value
        let self_bill_value = null;
        if (association.holidayActivation) {
            if (association.holidayCostRemoved) {
                self_bill_value = _.round(_.round(_.sumBy(payrollDataToInsert, 'totalCharge'), 2) - _.round(_.sumBy(payrollDataToInsert, 'clearvueSavings'), 2), 2);
            } else {
                self_bill_value = total_holiday_employment_cost != null ? _.round(_.round(_.sumBy(payrollDataToInsert, 'totalCharge'), 2) + _.round(total_holiday_employment_cost, 2) - _.round(_.sumBy(payrollDataToInsert, 'clearvueSavings'), 2), 2) : null;
            }
        }

        const payrollSummary = {
            payrollMetaId: payroll.id,
            clientId: payload.client_id,
            siteId: payload.site_id,
            agencyId: payload.agency_id,
            totalHours: _.round(_.sumBy(payrollDataToInsert, 'totalHours'), 2),
            supervisorHours: _.round(_.sumBy(payrollDataToInsert, 'supervisorHours'), 2),
            nonSupervisorHours: _.round(_.sumBy(payrollDataToInsert, 'nonSupervisorHours'), 2),
            totalHolidayPay: _.round(_.sumBy(payrollDataToInsert, 'holidayPayTypeValue'), 2),
            totalCharge: _.round(_.sumBy(payrollDataToInsert, 'totalCharge'), 2),
            supervisorCharges: _.round(_.sumBy(payrollDataToInsert, 'supervisorCharges'), 2),
            nonSupervisorCharges: _.round(_.sumBy(payrollDataToInsert, 'nonSupervisorCharges'), 2),
            identifiedSupervisors: supervisorCount,
            totalPay: _.round(_.sumBy(payrollDataToInsert, 'totalPay'), 2),
            otherAssignmentPay: _.round(_.sumBy(payrollDataToInsert, 'otherAssignmentPay'), 2),
            totalAgencyMargin: _.round(_.sumBy(payrollDataToInsert, 'totalAgencyMargin'), 2),
            totalMargin: _.round(_.sumBy(payrollDataToInsert, 'totalMargin'), 2),
            actualMargin: _.sumBy(payrollDataToInsert, 'totalHours') ? _.round(_.round(_.sumBy(payrollDataToInsert, 'totalAgencyMargin'), 2) / _.round(_.sumBy(payrollDataToInsert, 'totalHours'), 2), 2) : 0,
            rateCardMargin: _.round(_.sumBy(payrollDataToInsert, 'rateCardMargin'), 2),
            creditPerHour: _.round(_.sumBy(payrollDataToInsert, 'creditPerHour'), 2),
            clearvueSavings: _.round(_.sumBy(payrollDataToInsert, 'clearvueSavings'), 2),
            startDate: payload.start_date,
            endDate: payload.end_date,
            week: payload.week,
            totalWorkedCharge: total_worked_charge != null ? _.round(total_worked_charge, 2) : total_worked_charge,
            holidayEmploymentCost: total_holiday_employment_cost != null ? _.round(total_holiday_employment_cost, 2) : total_holiday_employment_cost,
            totalWtrCosts: total_wtr_Cost != null ? _.round(total_wtr_Cost, 2) : total_wtr_Cost,
            selfBillValue: self_bill_value,
            holidayCharge: total_worked_charge != null ? _.round(_.round(_.sumBy(payrollDataToInsert, 'totalCharge'), 2) - _.round(total_worked_charge, 2), 2) : null,
            accrualValue: total_accrual_value != null ? _.round(total_accrual_value, 2) : total_accrual_value,
            calculatedPathway: calculatedPathway,
            adjustmentCount: adjustmentCount,
            payCorrectionCount: payCorrectionCount,
            createdBy: loggedInUser.user_id,
            updatedBy: loggedInUser.user_id
        }

        // add data in chunk of size 1000
        let arrays = await splitArrayWithSize(payrollDataToInsert, 1000);
        let detailedSummaryArrays = await splitArrayWithSize(payrollDetialedSummaryDataToInsert, 1000);

        // lets now open a new transaction
        let queryRunner = getNewTransaction();
        await queryRunner.startTransaction();

        try {
            // Insert payroll data in chunks
            for (const payrollDataToInsert of arrays) {
                await addPayrollData(payrollDataToInsert, queryRunner);
            }

            // Insert detailed summary data in chunks
            for (const detailedSummaryChunk of detailedSummaryArrays) {
                await insertPayrollDetailedSummary(detailedSummaryChunk, queryRunner);
            }

            // commit transaction now
            await queryRunner.commitTransaction();
        } catch (error) {
            // since we have errors let's rollback changes we made
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            // release query runner which is manually created
            await queryRunner.release();
        }

        await addHolidayPayrollSummaryData(holidayPayrollSummary);
        await addPayrollSummaryData(payrollSummary);
        return [201, {
            'ok': true,
            payrollId: payroll.id,
            message: MessageActions.CREATE_PAYROLL,
        }]
    } catch (err) {
        await deletePayrollByMetaId(payroll.id);
        await deletePayrollSummaryByMetaId(payroll.id)
        await deletePayrollDetailedSummaryByMetaId(payroll.id);
        await deleteHolidayPayrollSummaryByMetaId(payroll.id)
        await deletePayrollMetaById(payroll.id);
        return [500, err]
    }
}

/**
 * Service to delete payroll data.
 */
export const deletePayrollDataService = async (payload, loggedInUser) => {
    try {
        if (loggedInUser.client_id != payload.client_id) {
            return [403, ErrorResponse.PermissionDenied]
        }

        let agencies = await getAssociatedAgencies(payload.client_id);
        let agencyIdList = agencies.map(element => parseInt(element.agencyId));
        if (!agencyIdList.includes(payload.agency_id)) {
            return [403, ErrorResponse.PermissionDenied]
        }

        let siteDetails: any = await getAllSites(payload.client_id);
        let site = siteDetails[1].sites;
        let site_id_list = site.map(object => parseInt(object.id));
        if (!site_id_list.includes(payload.site_id)) {
            return [403, ErrorResponse.PermissionDenied]
        }

        await deletePayrollData(payload, loggedInUser);

        // Resetting Shift Fulfillment to 0 for all days as we are deleting the TNA.
        const whereClause = [
            'b.start_date = :startDate',
            'ba.agency_id = :agencyId',
            'b.client_id = :clientId',
            'b.site_id = :siteId',
        ].join(' AND ');
        let whereClauseValue = { "startDate": payload.start_date, "agencyId": payload.agency_id, "clientId": payload.client_id, "siteId": payload.site_id };

        const shiftBookingsDataForTnaFulfilment = await getBookingsDataForTnaFulfilment(whereClause, whereClauseValue);
        for (const item of shiftBookingsDataForTnaFulfilment) {
            let updateObj = {
                fulfilledWorkersHeads: { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 },
                fulfilledSupervisorsHeads: { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0 },
                fulfilledWorkersTotal: 0,
                fulfilledSupervisorsTotal: 0,
                fulfilledTotal: 0,
                status: BookingStatus.FULFILLED,
                updatedBy: loggedInUser.user_id,
                updatedAt: new Date()
            };

            await updateBookingStatusHelper(item.booking_id, { status: BookingStatus.FULFILLED });
            await updateBookingHelper(item.booking_id, payload.agency_id, updateObj);
        }

        return [200, {
            ok: true,
            message: MessageActions.DELETE_PAYROLL,
        }];

    } catch (err) {
        if (err.code === ErrorCodes.dbReferenceError) {
            return [404, ErrorResponse.ResourceNotFound]    // Return 404 if any foreign key contraint is not available in DB
        } else {
            notifyBugsnag(err);
            return [500, err.message]
        }
    }
}

export const calculateNICost = async (association, payrollRecord, totalPay, payload, worker_paytypes_list) => {
    // Calculation of NI is unaffected for both path- Holiday Cost Removed From RateCard
    let payProportionForNI;
    let NICostPerPayType = 0;
    if (association.totalAssignmentPay && payrollRecord.worker_tap_value != null) {
        let new_threshold = (totalPay / Number(payrollRecord.worker_tap_value)) * Number(payload.ni_threshold);
        payProportionForNI = totalPay == 0 ? 0 : _.round(((_.round(((worker_paytypes_list.pay / totalPay) * 100), 2) * (_.round(totalPay - new_threshold, 2))) / 100), 2);
        NICostPerPayType = moment(payload.start_date).diff(moment(payrollRecord.worker_dob), 'years') >= 21 ? (Number(payrollRecord.worker_tap_value) > Number(payload.ni_threshold)) ? _.round(((payProportionForNI * Number(payload.ni_percent)) / 100), 2) : 0 : 0;

    } else {
        payProportionForNI = totalPay == 0 ? 0 : _.round(((_.round(((worker_paytypes_list.pay / totalPay) * 100), 2) * (_.round(totalPay - Number(payload.ni_threshold), 2))) / 100), 2);
        NICostPerPayType = moment(payload.start_date).diff(moment(payrollRecord.worker_dob), 'years') >= 21 ? (totalPay > Number(payload.ni_threshold)) ? _.round(((payProportionForNI * Number(payload.ni_percent)) / 100), 2) : 0 : 0;

    }
    NICostPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : NICostPerPayType;

    return NICostPerPayType;
}

export const calculatePensionCost = async (association, payrollRecord, totalPay, payload, worker_paytypes_list) => {

    // Calculation of Pension is unaffected for both path- Holiday Cost Removed From RateCard
    let pensionCostPerPayType = 0;
    if (payrollRecord.pension_opt_out || totalPay < payload.pension_threshold) {
        pensionCostPerPayType = 0;
    } else if (payrollRecord.pension_opt_out == 0 && moment(payload.start_date).diff(moment(payrollRecord.worker_start_date), 'weeks') < 12) {
        pensionCostPerPayType = 0;
    } else {
        // Calculate pension
        let payProportionForPension;
        if (association.totalAssignmentPay && payrollRecord.worker_tap_value != null) {
            let new_threshold = (totalPay / Number(payrollRecord.worker_tap_value)) * payload.pension_threshold;
            payProportionForPension = totalPay == 0 ? 0 : _.round(((_.round(((worker_paytypes_list.pay / totalPay) * 100), 2) * (_.round(totalPay - new_threshold, 2))) / 100), 2);
        } else {
            payProportionForPension = totalPay == 0 ? 0 : _.round(((_.round(((worker_paytypes_list.pay / totalPay) * 100), 2) * (_.round(totalPay - payload.pension_threshold, 2))) / 100), 2);
        }

        pensionCostPerPayType = _.round(((payProportionForPension * 3) / 100), 2);
    }
    pensionCostPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : pensionCostPerPayType;

    return pensionCostPerPayType;
}

export const calculateHolidayCost = async (association, NICostPerPayType, pensionCostPerPayType, holidayPayPercent, worker_paytypes_list) => {
    let holidayPerPayType = 0;

    if (association.holidayCostRemoved) {
        holidayPerPayType = _.round((((worker_paytypes_list.pay + NICostPerPayType + pensionCostPerPayType) * holidayPayPercent) / 100), 2);
    } else {
        holidayPerPayType = _.round((((worker_paytypes_list.pay) * holidayPayPercent) / 100), 2);
    }
    holidayPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : holidayPerPayType;

    return holidayPerPayType;
}

export const calculateAppLevyCost = async (calculatedPathway, holidayPerPayType, worker_paytypes_list, payload) => {
    let appLevyPerPayType = 0;
    let appLevyWithWTRPerPayType = 0;
    if (calculatedPathway === CalculationPathWays.YESYES) {
        appLevyWithWTRPerPayType = _.round((((worker_paytypes_list.pay + holidayPerPayType) * payload.app_levy_percent) / 100), 2);
        appLevyPerPayType = _.round((((worker_paytypes_list.pay) * payload.app_levy_percent) / 100), 2);

    } else {
        appLevyPerPayType = _.round((((worker_paytypes_list.pay) * payload.app_levy_percent) / 100), 2);
    }
    appLevyPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : appLevyPerPayType;

    return { appLevyPerPayType, appLevyWithWTRPerPayType };
}

export const calculateHolidayCostForNonHolidayEmp = async (association, NICostPerPayType, pensionCostPerPayType, holidayPayPercent, worker_paytypes_list) => {
    let holidayPerPayType = 0;
    holidayPerPayType = _.round((((worker_paytypes_list.pay + NICostPerPayType + pensionCostPerPayType) * holidayPayPercent) / 100), 2);
    holidayPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : holidayPerPayType;

    return holidayPerPayType;
}

export const calculateAppLevyCostForNonHolidayEmp = async (association, holidayPerPayType, worker_paytypes_list, payload) => {
    let appLevyPerPayType = 0;
    appLevyPerPayType = _.round((((worker_paytypes_list.pay + holidayPerPayType) * payload.app_levy_percent) / 100), 2);
    appLevyPerPayType = [PayTypes.SUPERVISOR_PERMANENT].includes(worker_paytypes_list.worker_pay_type) ? 0 : appLevyPerPayType;

    return appLevyPerPayType;
}

export const calculateWorkerHolidayAccrual = async (holidayPayPercent, worker_paytypes_list) => {
    let workerHolidayAccrualPerPayType = 0;
    workerHolidayAccrualPerPayType = _.round((((worker_paytypes_list.pay) * holidayPayPercent) / 100), 2);
    return workerHolidayAccrualPerPayType;
}



/**
 * Service to GET Credit dues. 
 */
export const getWorkersCreditDuesService = async (data, loggedInUser) => {
    let { isError, error, payload: updatedPayload } = await checkAuthorisedResourceAccess(data, loggedInUser);
    if (isError) return error;
    let site_ids = [];
    if (updatedPayload.region_id) {
        const sites = await getSites(`site.region_id= :region_id`, { "region_id": updatedPayload.region_id });
        const site_ids = _.map(sites, 'id')
        if (updatedPayload.site_id && !site_ids.include(updatedPayload.site_id)) {
            return [404, ErrorResponse.ResourceNotFoundWithoutAnyUserInput];
        }
    } else if (updatedPayload.site_id) {
        site_ids = [updatedPayload.site_id]
    }

    let whereClause = `credit_dues.client_id = :client_id `;
    whereClause += updatedPayload.agency_id ? ` AND credit_dues.agency_id = :agency_id` : "";
    whereClause += updatedPayload.start_date ? ` AND credit_dues.start_date >= :start_date` : "";
    whereClause += updatedPayload.end_date ? ` AND credit_dues.end_date <= :end_date` : "";
    whereClause += updatedPayload.worker_id ? ` AND credit_dues.worker_id = :worker_id` : "";
    whereClause += updatedPayload.where_rate_is ? ` AND credit_dues.applied_credit_rate = :applied_credit_rate` : "";

    whereClause += site_ids.length ? ` AND credit_dues.site_id IN (:...site_ids)` : "";

    const whereClauseValue = { "client_id": updatedPayload.client_id, "agency_id": updatedPayload.agency_id, "site_ids": site_ids, "start_date": updatedPayload.start_date, "end_date": updatedPayload.end_date, "worker_id": updatedPayload.worker_id, "applied_credit_rate": updatedPayload.where_rate_is };
    const credutDues = await getCreditDuesData(whereClause, whereClauseValue, updatedPayload.sort_by || "employee_id", updatedPayload.sort_type || "asc", updatedPayload.page || 1, updatedPayload.limit || 10)

    return [200, {
        ok: true,
        total: credutDues.total,
        count: credutDues.count,
        credit_dues: credutDues.data,
    }];
};