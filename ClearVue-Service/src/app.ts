import 'reflect-metadata';

import { default as express } from "express";
import { createConnection } from 'typeorm';
import { default as cors } from 'cors';

import { config, ormConfig } from './configurations';
import { requestLogger, handleErrors, bugsnagMiddleware, endpointNotFound, sqlEscape } from './middlewares';
import { logger, expressWinstonRequestErrorLogger } from './utils';
import { initializeFirebase } from './services';
import {
    userAuthenticationRouter, serviceStatusRoutes, swaggerRoutes, clientDetails, agencyRoute,
    regionRoute, departmentRoute, siteRoute, workerRoutes, jobRoutes, rateCardRoute, sectorRoutes,
    agencyClientAssociationRoute, userRoutes, timeAndAttendanceRoutes, shiftRoutes, bookingRoutes,
    payrollRouters, masterAdminDadhboardRouter, dashboardRouter, messageRouter, surveyRouter,
    automatedMessagesRouter, faqRouter, mobileVersionRouter, agencyManagementRouter, marginsRouter, reportingRouter,
    mfaRouter
} from './routes';

class App {
    private port: number;
    private app: express.Application;

    constructor(port: number) {
        this.port = port;

        this.app = express();

        // Initialize all the middleware of the service.
        this.initializeMiddlewares();

        // Logs request start and end details
        this.app.use(requestLogger);

        /**
         * Create Database connection.
         * So database operations can be done from anywhere in the service.
        */
        this.createDBConnections();

        // Initialize Firebase if enabled
        this.initializeFirebase();

        // Initialize all the routes.
        this.initializeRoutes();

        // Initialize error handler to provide endpoint not found error response.
        this.initializeNotFoundErrorHandling();

        // Initialize request error logger to log all the generated error logs.
        this.initializeRequestErrorLogger();

        // Initialize error handler to provide custom and system generated error response.
        this.initializeErrorHandling();

    }

    private createDBConnections() {
        // Create MySQL Connection
        createConnection(ormConfig);
    }

    private initializeFirebase() {
        // Initialize Firebase Admin SDK if enabled
        if (config.FIREBASE_AUTH_ENABLED) {
            initializeFirebase();
        }
    }

    private initializeMiddlewares() {
        this.app.use(cors({
            origin: config.API_HOSTS,
            methods: "GET,POST,PUT,DELETE,PATCH"
        }));

        this.app.use(express.urlencoded({
            extended: true,
            limit: '100KB'
        }))
        this.app.use(express.json({
            limit: '200KB',
            strict: false
        }));

        if (config.bugsnag.ENABLE_BUGSNAG_ERROR_LOGGING) {
            this.app.use(bugsnagMiddleware.requestHandler);
        }

        // SqlInection Prevention
        this.app.use(sqlEscape);
    }

    private initializeRequestErrorLogger() {
        this.app.use(expressWinstonRequestErrorLogger);
    }

    private initializeNotFoundErrorHandling() {
        this.app.use(endpointNotFound);
        if (config.bugsnag.ENABLE_BUGSNAG_ERROR_LOGGING) {
            this.app.use(bugsnagMiddleware.errorHandler);
        }
    }

    private initializeRoutes() {
        // All the service provided routes.
        this.app.use('/api', userAuthenticationRouter);
        this.app.use('/api', clientDetails);
        this.app.use('/api', regionRoute);
        this.app.use('/api', agencyRoute);
        this.app.use('/api', departmentRoute);
        this.app.use('/api', siteRoute)
        this.app.use('/api', workerRoutes);
        this.app.use('/api', timeAndAttendanceRoutes);
        this.app.use('/api', rateCardRoute);
        this.app.use('/api', jobRoutes);
        this.app.use('/api', sectorRoutes);
        this.app.use('/api', agencyClientAssociationRoute);
        this.app.use('/api', userRoutes);
        this.app.use('/api', swaggerRoutes);
        this.app.use('/api', shiftRoutes);
        this.app.use('/k8', serviceStatusRoutes);
        this.app.use('/api', bookingRoutes)
        this.app.use('/api', payrollRouters);
        this.app.use('/api', masterAdminDadhboardRouter);
        this.app.use('/api', dashboardRouter);
        this.app.use('/api', messageRouter);
        this.app.use('/api', surveyRouter);
        this.app.use('/api', automatedMessagesRouter);
        this.app.use('/api', faqRouter);
        this.app.use('/api', mobileVersionRouter);
        this.app.use('/api', agencyManagementRouter);
        this.app.use('/api', marginsRouter);
        this.app.use('/api', reportingRouter);
        this.app.use('/api', mfaRouter);
    }

    private initializeErrorHandling() {
        this.app.use(handleErrors);
        if (config.bugsnag.ENABLE_BUGSNAG_ERROR_LOGGING) {
            this.app.use(bugsnagMiddleware.errorHandler);
        }
    }

    public Start() {
        this.app.listen(this.port, () => {
            logger.info(`${config.APP_NAME} Server: Express server is listening on port ${this.port}!`);
        });
    }
}

new App(parseInt(config.SERVICE_PORT)).Start();
