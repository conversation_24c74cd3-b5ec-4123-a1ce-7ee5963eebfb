import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Site } from "./Site";

@Index("unique_site_role_pay", ["siteId", "roleType", "payType"], {
  unique: true,
})
@Index("FK_los_rule_client_details", ["clientId"], {})
@Index("fk_site_id_idx", ["siteId"], {})
@Index("fk_los_rule_created_by_idx", ["createdBy"], {})
@Index("fk_los_rule_updated_by_idx", ["updatedBy"], {})
@Entity("los_rule")
export class LosRule {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "client_id", nullable: true, unsigned: true })
  clientId: string | null;

  @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
  siteId: string | null;

  @Column("varchar", { name: "name", length: 250 })
  name: string;

  @Column("enum", {
    name: "role_type",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6"],
  })
  roleType: "1" | "2" | "3" | "4" | "5" | "6" | null;

  @Column("date", { name: "start_tax_year", nullable: true })
  startTaxYear: string | null;

  @Column("varchar", { name: "pay_type", nullable: true, length: 45 })
  payType: string | null;

  @Column("float", { name: "pre_twelve_week", precision: 12 })
  preTwelveWeek: number;

  @Column("float", { name: "post_twelve_week", precision: 12 })
  postTwelveWeek: number;

  @Column("json", { name: "los", nullable: true })
  los: object | null;

  @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
  createdBy: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(() => ClientDetails, (clientDetails) => clientDetails.losRules, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.losRules, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Site, (site) => site.losRules, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.losRules2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
