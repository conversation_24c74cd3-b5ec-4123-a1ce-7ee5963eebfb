import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Message } from "./Message";
import { Workers } from "./Workers";

@Index("idx_message_comments_message_id", ["messageId"], {})
@Index("idx_message_comments_worker_id", ["workerId"], {})
@Entity("message_comment")
export class MessageComment {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "message_id", unsigned: true })
  messageId: string;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("text", { name: "comment", nullable: true })
  comment: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @ManyToOne(() => Message, (message) => message.messageComments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "message_id", referencedColumnName: "id" }])
  message: Message;

  @ManyToOne(() => Workers, (workers) => workers.messageComments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
