import {
    Column,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Site } from "./Site";
import { TimeAndAttendance } from "./TimeAndAttendance";
import { Workers } from "./Workers";

@Index("fk_credit_dues_agency_id", ["agencyId"], {})
@Index("fk_credit_dues_client_id", ["clientId"], {})
@Index("fk_credit_dues_created_by_idx", ["createdBy"], {})
@Index("fk_credit_dues_site_id", ["siteId"], {})
@Index("fk_credit_dues_time_and_attendance_id", ["timeAndAttendanceId"], {})
@Index("fk_credit_dues_updated_by_idx", ["updatedBy"], {})
@Index("fk_credit_dues_worker_id", ["workerId"], {})
@Entity("credit_dues")
export class CreditDues {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "client_id", unsigned: true })
    clientId: string;

    @Column("bigint", { name: "agency_id", unsigned: true })
    agencyId: string;

    @Column("bigint", { name: "site_id", unsigned: true })
    siteId: string;

    @Column("bigint", { name: "worker_id", unsigned: true })
    workerId: string;

    @Column("tinyint", { name: "limited_hours", width: 1, default: () => "'0'" })
    limitedHours: boolean;

    @Column("date", { name: "start_date", nullable: true })
    startDate: string | null;

    @Column("date", { name: "end_date", nullable: true })
    endDate: string | null;

    @Column("bigint", { name: "time_and_attendance_id", unsigned: true })
    timeAndAttendanceId: string;

    @Column("decimal", { name: "performance_number", nullable: true, precision: 10, scale: 2 })
    performanceNumber: string;

    @Column("decimal", { name: "performance_threshold", precision: 10, scale: 2 })
    performanceThreshold: string;

    @Column("int", { name: "achievement_week", nullable: true })
    achievementWeek: number | null;

    @Column("int", { name: "threshold_week" })
    thresholdWeek: number;

    @Column("int", { name: "evaluation_week" })
    evaluationWeek: number;

    @Column("decimal", {
        name: "completed_training_hours",
        precision: 10,
        scale: 2,
    })
    completedTrainingHours: string;

    @Column("decimal", { name: "max_training_hours", precision: 10, scale: 2 })
    maxTrainingHours: string;

    @Column("decimal", {
        name: "training_employment_cost",
        precision: 10,
        scale: 2,
    })
    trainingEmploymentCost: string;

    @Column("decimal", { name: "credit_rate", precision: 5, scale: 2 })
    creditRate: string;

    @Column("decimal", { name: "applied_credit_rate", precision: 5, scale: 2 })
    appliedCreditRate: string;

    @Column("decimal", { name: "total_credit_due", precision: 10, scale: 2 })
    totalCreditDue: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @ManyToOne(() => AgencyDetails, (agencyDetails) => agencyDetails.creditDues, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
    agency: AgencyDetails;

    @ManyToOne(() => ClientDetails, (clientDetails) => clientDetails.creditDues, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.creditDues, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Site, (site) => site.creditDues, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(
        () => TimeAndAttendance,
        (timeAndAttendance) => timeAndAttendance.creditDues,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "time_and_attendance_id", referencedColumnName: "id" }])
    timeAndAttendance: TimeAndAttendance;

    @ManyToOne(() => User, (user) => user.creditDues2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;

    @ManyToOne(() => Workers, (workers) => workers.creditDues, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
    worker: Workers;
}
