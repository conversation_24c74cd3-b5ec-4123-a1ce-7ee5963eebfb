import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencySiteRestrictions } from "./AgencySiteRestrictions";
import { Booking } from "./Booking";
import { CreditDues } from "./CreditDues";
import { DepartmentSiteAssociation } from "./DepartmentSiteAssociation";
import { HolidayPayrollSummary } from "./HolidayPayrollSummary";
import { JobAssociation } from "./JobAssociation";
import { LosRule } from "./LosRule";
import { Margins } from "./Margins";
import { Message } from "./Message";
import { Payroll } from "./Payroll";
import { PayrollDetailedSummary } from "./PayrollDetailedSummary";
import { PayrollMeta } from "./PayrollMeta";
import { PayrollSummary } from "./PayrollSummary";
import { RateCard } from "./RateCard";
import { ShiftSiteAssociation } from "./ShiftSiteAssociation";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Region } from "./Region";
import { SurveyResult } from "./SurveyResult";
import { TimeAndAttendance } from "./TimeAndAttendance";
import { TimeAndAttendanceData } from "./TimeAndAttendanceData";
import { TotalAgencyPay } from "./TotalAgencyPay";
import { TotalAgencyPayData } from "./TotalAgencyPayData";
import { TrainingRules } from "./TrainingRules";
import { UserSiteAssociation } from "./UserSiteAssociation";
import { WorkerPerformance } from "./WorkerPerformance";
import { WorkerPerformanceData } from "./WorkerPerformanceData";

@Index("fk_region_id_idx", ["regionId"], {})
@Index("FK_site_client_details", ["clientId"], {})
@Index("fk_site_created_by_idx", ["createdBy"], {})
@Index("fk_site_updated_by_idx", ["updatedBy"], {})
@Entity("site")
export class Site {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 250 })
  name: string;

  @Column("bigint", { name: "region_id", nullable: true, unsigned: true })
  regionId: string | null;

  @Column("bigint", { name: "client_id", nullable: true, unsigned: true })
  clientId: string | null;

  @Column("json", { name: "address", nullable: true })
  address: object | null;

  @Column("varchar", { name: "post_code", nullable: true, length: 45 })
  postCode: string | null;

  @Column("varchar", { name: "cost_centre", nullable: true, length: 100 })
  costCentre: string | null;

  @Column("varchar", { name: "city", nullable: true, length: 250 })
  city: string | null;

  @Column("varchar", { name: "country", nullable: true, length: 250 })
  country: string | null;

  @Column("json", { name: "wtr", nullable: true })
  wtr: object | null;

  @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
  createdBy: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @OneToMany(
    () => AgencySiteRestrictions,
    (agencySiteRestrictions) => agencySiteRestrictions.site
  )
  agencySiteRestrictions: AgencySiteRestrictions[];

  @OneToMany(() => Booking, (booking) => booking.site)
  bookings: Booking[];

  @OneToMany(() => CreditDues, (creditDues) => creditDues.site)
  creditDues: CreditDues[];

  @OneToMany(
    () => DepartmentSiteAssociation,
    (departmentSiteAssociation) => departmentSiteAssociation.site
  )
  departmentSiteAssociations: DepartmentSiteAssociation[];

  @OneToMany(
    () => HolidayPayrollSummary,
    (holidayPayrollSummary) => holidayPayrollSummary.site
  )
  holidayPayrollSummaries: HolidayPayrollSummary[];

  @OneToMany(() => JobAssociation, (jobAssociation) => jobAssociation.site)
  jobAssociations: JobAssociation[];

  @OneToMany(() => LosRule, (losRule) => losRule.site)
  losRules: LosRule[];

  @OneToMany(() => Margins, (margins) => margins.site)
  margins: Margins[];

  @OneToMany(() => Message, (message) => message.site)
  messages: Message[];

  @OneToMany(() => Payroll, (payroll) => payroll.site)
  payrolls: Payroll[];

  @OneToMany(
    () => PayrollDetailedSummary,
    (payrollDetailedSummary) => payrollDetailedSummary.site
  )
  payrollDetailedSummaries: PayrollDetailedSummary[];

  @OneToMany(() => PayrollMeta, (payrollMeta) => payrollMeta.site)
  payrollMetas: PayrollMeta[];

  @OneToMany(() => PayrollSummary, (payrollSummary) => payrollSummary.site)
  payrollSummaries: PayrollSummary[];

  @OneToMany(() => RateCard, (rateCard) => rateCard.site)
  rateCards: RateCard[];

  @OneToMany(
    () => ShiftSiteAssociation,
    (shiftSiteAssociation) => shiftSiteAssociation.site
  )
  shiftSiteAssociations: ShiftSiteAssociation[];

  @ManyToOne(() => ClientDetails, (clientDetails) => clientDetails.sites, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.sites, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Region, (region) => region.sites, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "region_id", referencedColumnName: "id" }])
  region: Region;

  @ManyToOne(() => User, (user) => user.sites2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @OneToMany(() => SurveyResult, (surveyResult) => surveyResult.site)
  surveyResults: SurveyResult[];

  @OneToMany(
    () => TimeAndAttendance,
    (timeAndAttendance) => timeAndAttendance.site
  )
  timeAndAttendances: TimeAndAttendance[];

  @OneToMany(
    () => TimeAndAttendanceData,
    (timeAndAttendanceData) => timeAndAttendanceData.site
  )
  timeAndAttendanceData: TimeAndAttendanceData[];

  @OneToMany(() => TotalAgencyPay, (totalAgencyPay) => totalAgencyPay.site)
  totalAgencyPays: TotalAgencyPay[];

  @OneToMany(
    () => TotalAgencyPayData,
    (totalAgencyPayData) => totalAgencyPayData.site
  )
  totalAgencyPayData: TotalAgencyPayData[];

  @OneToMany(() => TrainingRules, (trainingRules) => trainingRules.site)
  trainingRules: TrainingRules[];

  @OneToMany(
    () => UserSiteAssociation,
    (userSiteAssociation) => userSiteAssociation.site
  )
  userSiteAssociations: UserSiteAssociation[];

  @OneToMany(
    () => WorkerPerformance,
    (workerPerformance) => workerPerformance.site
  )
  workerPerformances: WorkerPerformance[];

  @OneToMany(
    () => WorkerPerformanceData,
    (workerPerformanceData) => workerPerformanceData.site
  )
  workerPerformanceData: WorkerPerformanceData[];
}
