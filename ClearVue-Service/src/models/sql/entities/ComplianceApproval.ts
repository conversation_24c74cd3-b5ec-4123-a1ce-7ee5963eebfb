import {
    Column,
    <PERSON><PERSON><PERSON>,
    Index,
    Join<PERSON><PERSON><PERSON>n,
    ManyToOne,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn
} from "typeorm";
import { Workers } from "./Workers";
import { User } from "./User";

@Index("idx_worker_card", ["workerId", "complianceCardId"], {})
@Entity("compliance_approval")
export class ComplianceApproval {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("int", { name: "compliance_card_id" })
    complianceCardId: number;

    @Column("bigint", { name: "worker_id", unsigned: true })
    workerId: string;

    @Column("varchar", { name: "house_number", nullable: true, length: 250 })
    houseNumber: string | null;

    @Column("varchar", { name: "post_code", nullable: true, length: 45 })
    postCode: string | null;

    @Column("varchar", { name: "account_number", nullable: true, length: 15 })
    accountNumber: string | null;

    @Column("varchar", { name: "sort_code", nullable: true, length: 6 })
    sortCode: string | null;

    @Column("tinyint", { name: "client_approval_status", nullable: true })
    clientApprovalStatus: number | null;

    @Column("bigint", { name: "client_approval_by", nullable: true, unsigned: true })
    clientApprovalBy: string | null;

    @Column("datetime", { name: "client_approval_at", nullable: true })
    clientApprovalAt: Date | null;

    @Column("tinyint", { name: "agency_approval_status", nullable: true })
    agencyApprovalStatus: number | null;

    @Column("bigint", { name: "agency_approval_by", nullable: true, unsigned: true })
    agencyApprovalBy: string | null;

    @Column("datetime", { name: "agency_approval_at", nullable: true })
    agencyApprovalAt: Date | null;

    @Column("tinyint", { name: "is_reset", default: () => "0", nullable: false })
    isReset: boolean;

    @CreateDateColumn({ name: "created_at" })
    createdAt: Date;

    @UpdateDateColumn({ name: "updated_at" })
    updatedAt: Date;

    @ManyToOne(() => Workers, (worker) => worker.id)
    @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
    worker: Workers;

    @ManyToOne(() => User, (user) => user.id)
    @JoinColumn([{ name: "client_approval_by", referencedColumnName: "id" }])
    clientApprovalUser: User;

    @ManyToOne(() => User, (user) => user.id)
    @JoinColumn([{ name: "agency_approval_by", referencedColumnName: "id" }])
    agencyApprovalUser: User;
} 