import {
    Column,
    <PERSON><PERSON>ty,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Site } from "./Site";
import { Workers } from "./Workers";
import { WorkerPerformance } from "./WorkerPerformance";

@Index("fk_worker_performance_data_agency_id_idx", ["agencyId"], {})
@Index("fk_worker_performance_data_client_id_idx", ["clientId"], {})
@Index("fk_worker_performance_data_created_by_idx", ["createdBy"], {})
@Index("fk_worker_performance_data_site_id", ["siteId"], {})
@Index("fk_worker_performance_data_updated_by_idx", ["updatedBy"], {})
@Index("fk_worker_performance_data_worker_id", ["workerId"], {})
@Index(
    "fk_worker_performance_data_worker_performance_id_idx",
    ["workerPerformanceId"],
    {}
)
@Entity("worker_performance_data")
export class WorkerPerformanceData {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "worker_performance_id", unsigned: true })
    workerPerformanceId: string;

    @Column("bigint", { name: "agency_id", nullable: true, unsigned: true })
    agencyId: string | null;

    @Column("bigint", { name: "client_id", nullable: true, unsigned: true })
    clientId: string | null;

    @Column("bigint", { name: "worker_id", unsigned: true })
    workerId: string;

    @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
    siteId: string | null;

    @Column("decimal", { name: "performance_number", precision: 10, scale: 2 })
    performanceNumber: string;

    @Column("date", { name: "start_date", nullable: true })
    startDate: string | null;

    @Column("int", { name: "week", nullable: true })
    week: number | null;

    @Column("date", { name: "end_date", nullable: true })
    endDate: string | null;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @ManyToOne(
        () => AgencyDetails,
        (agencyDetails) => agencyDetails.workerPerformanceData,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
    agency: AgencyDetails;

    @ManyToOne(
        () => ClientDetails,
        (clientDetails) => clientDetails.workerPerformanceData,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.workerPerformanceData, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Site, (site) => site.workerPerformanceData, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.workerPerformanceData2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;

    @ManyToOne(() => Workers, (workers) => workers.workerPerformanceData, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
    worker: Workers;

    @ManyToOne(
        () => WorkerPerformance,
        (workerPerformance) => workerPerformance.workerPerformanceData,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "worker_performance_id", referencedColumnName: "id" }])
    workerPerformance: WorkerPerformance;
}
