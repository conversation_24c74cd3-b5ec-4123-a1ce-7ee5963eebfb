import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { SurveyAnswer } from "./SurveyAnswer";

@Entity("survey_mcq_options")
export class SurveyMcqOptions {
  @PrimaryGeneratedColumn({ type: "int", name: "id" })
  id: number;

  @Column("varchar", { name: "options", nullable: true, length: 200 })
  options: string | null;

  @OneToMany(() => SurveyAnswer, (surveyAnswer) => surveyAnswer.answer)
  surveyAnswers: SurveyAnswer[];
}
