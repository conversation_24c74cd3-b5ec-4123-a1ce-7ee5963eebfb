import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";

@Index("fk_message_default_created_by_idx", ["createdBy"], {})
@Index("fk_message_default_modified_by_idx", ["updatedBy"], {})
@Index("fk_message_default_name_idx", ["name"], {})
@Entity("message_system_default")
export class MessageSystemDefault {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 750 })
  name: string;

  @Column("varchar", { name: "title", length: 750 })
  title: string;

  @Column("json", { name: "title_translations", nullable: true })
  titleTranslations: object | null;

  @Column("varchar", { name: "from", length: 250 })
  from: string;

  @Column("varchar", { name: "label", nullable: true, length: 250 })
  label: string | null;

  @Column("json", { name: "body" })
  body: object;

  @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
  createdBy: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @Column("tinyint", {
    name: "assigned_to_client",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  assignedToClient: boolean | null;

  @Column("tinyint", {
    name: "assigned_to_agency",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  assignedToAgency: boolean | null;

  @ManyToOne(() => User, (user) => user.messageSystemDefaults, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => User, (user) => user.messageSystemDefaults2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
