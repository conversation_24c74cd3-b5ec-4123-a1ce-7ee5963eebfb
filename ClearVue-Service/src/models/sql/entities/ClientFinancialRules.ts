import {
    Column,
    <PERSON><PERSON><PERSON>,
    Index,
    <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";

@Index("fk_client_financial_rules_client_id", ["clientId"], {})
@Index("fk_client_financial_rules_created_by", ["createdBy"], {})
@Index("fk_client_financial_rules_updated_by", ["updatedBy"], {})
@Entity("client_financial_rules")
export class ClientFinancialRules {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "client_id", unsigned: true })
    clientId: string;

    @Column("varchar", { name: "financial_year_start", length: 4 })
    financialYearStart: string;

    @Column("varchar", { name: "financial_year_end", length: 4 })
    financialYearEnd: string;

    @Column("date", { name: "start_date" })
    startDate: string;

    @Column("date", { name: "end_date" })
    endDate: string;

    @Column("int", { name: "total_weeks" })
    totalWeeks: number;

    @Column("decimal", {
        name: "ni_percent",
        comment: "Percentage value between 0 and 100",
        precision: 5,
        scale: 2,
    })
    niPercent: string;

    @Column("decimal", {
        name: "ni_threshold",
        comment: "Threshold value between 0 and 999",
        precision: 6,
        scale: 2,
    })
    niThreshold: string;

    @Column("decimal", {
        name: "pension_percent",
        comment: "Percentage value between 0 and 100",
        precision: 5,
        scale: 2,
    })
    pensionPercent: string;

    @Column("decimal", {
        name: "pension_threshold",
        comment: "Threshold value between 0 and 999",
        precision: 6,
        scale: 2,
    })
    pensionThreshold: string;

    @Column("decimal", {
        name: "app_levy_percent",
        comment: "Percentage value between 0 and 100",
        precision: 5,
        scale: 2,
    })
    appLevyPercent: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
    createdBy: string | null;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
    updatedBy: string | null;

    @ManyToOne(
        () => ClientDetails,
        (clientDetails) => clientDetails.clientFinancialRules,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.clientFinancialRules, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => User, (user) => user.clientFinancialRules2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;
}
