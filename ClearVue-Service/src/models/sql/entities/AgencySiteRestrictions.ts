import {
    Column,
    <PERSON>ti<PERSON>,
    Index,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyClientAssociation } from "./AgencyClientAssociation";
import { User } from "./User";
import { Site } from "./Site";

@Index("fk_agency_site_restrictions_created_by", ["createdBy"], {})
@Index("fk_agency_site_restrictions_site_id", ["siteId"], {})
@Index("fk_agency_site_restrictions_updated_by", ["updatedBy"], {})
@Index("unique_association_site", ["agencyClientAssociationId", "siteId"], {
    unique: true,
})
@Entity("agency_site_restrictions")
export class AgencySiteRestrictions {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "agency_client_association_id", unsigned: true })
    agencyClientAssociationId: string;

    @Column("bigint", { name: "site_id", unsigned: true })
    siteId: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @ManyToOne(
        () => AgencyClientAssociation,
        (agencyClientAssociation) => agencyClientAssociation.agencySiteRestrictions,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([
        { name: "agency_client_association_id", referencedColumnName: "id" },
    ])
    agencyClientAssociation: AgencyClientAssociation;

    @ManyToOne(() => User, (user) => user.agencySiteRestrictions, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Site, (site) => site.agencySiteRestrictions, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.agencySiteRestrictions2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;
}
