import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Workers } from "./Workers";
import { Site } from "./Site";
import { TotalAgencyPay } from "./TotalAgencyPay";

@Index("fk_total_agency_pay_data_site_id", ["siteId"], {})
@Index("fk_total_agency_pay_data_agency_id_idx", ["agencyId"], {})
@Index("fk_total_agency_pay_data_client_id_idx", ["clientId"], {})
@Index("fk_total_agency_pay_data_created_by_idx", ["createdBy"], {})
@Index(
  "fk_total_agency_pay_data_total_agency_pay_id_idx",
  ["totalAgencyPayId"],
  {}
)
@Index("fk_total_agency_pay_data_updated_by_idx", ["updatedBy"], {})
@Index("fk_total_agency_pay_data_worker_id", ["workerId"], {})
@Entity("total_agency_pay_data")
export class TotalAgencyPayData {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "total_agency_pay_id", unsigned: true })
  totalAgencyPayId: string;

  @Column("bigint", { name: "agency_id", nullable: true, unsigned: true })
  agencyId: string | null;

  @Column("bigint", { name: "client_id", nullable: true, unsigned: true })
  clientId: string | null;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("varchar", { name: "employee_id", nullable: true, length: 250 })
  employeeId: string | null;

  @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
  siteId: string | null;

  @Column("decimal", { name: "tap_value", precision: 10, scale: 2 })
  tapValue: string;

  @Column("date", { name: "start_date", nullable: true })
  startDate: string | null;

  @Column("int", { name: "week", nullable: true })
  week: number | null;

  @Column("date", { name: "end_date", nullable: true })
  endDate: string | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(
    () => AgencyDetails,
    (agencyDetails) => agencyDetails.totalAgencyPayData,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(
    () => ClientDetails,
    (clientDetails) => clientDetails.totalAgencyPayData,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.totalAgencyPayData, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Site, (site) => site.totalAgencyPayData, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(
    () => TotalAgencyPay,
    (totalAgencyPay) => totalAgencyPay.totalAgencyPayData,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "total_agency_pay_id", referencedColumnName: "id" }])
  totalAgencyPay: TotalAgencyPay;

  @ManyToOne(() => User, (user) => user.totalAgencyPayData2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @ManyToOne(() => Workers, (workers) => workers.totalAgencyPayData, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
