import {
    Column,
    <PERSON>ti<PERSON>,
    Index,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";
import { RateCard } from "./RateCard";

@Index("fk_rate_card_data_created_by_idx", ["createdBy"], {})
@Index("fk_rate_card_data_rate_card_id_idx", ["rateCardId"], {})
@Index("fk_rate_card_data_updated_by_idx", ["updatedBy"], {})
@Entity("rate_card_data")
export class RateCardData {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "rate_card_id", unsigned: true })
    rateCardId: string;

    @Column("decimal", { name: "pay_rate", precision: 10, scale: 2 })
    payRate: string;

    @Column("decimal", { name: "charge_rate", precision: 10, scale: 2 })
    chargeRate: string;

    @Column("decimal", {
        name: "performance_low",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    performanceLow: string | null;

    @Column("decimal", {
        name: "performance_high",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    performanceHigh: string | null;

    @Column("tinyint", {
        name: "supervisor_rate",
        width: 1,
        default: () => "'0'",
    })
    supervisorRate: boolean;

    @Column("varchar", { name: "pay_type", nullable: true, length: 45 })
    payType: string | null;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @ManyToOne(() => User, (user) => user.rateCardData, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => RateCard, (rateCard) => rateCard.rateCardData, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "rate_card_id", referencedColumnName: "id" }])
    rateCard: RateCard;

    @ManyToOne(() => User, (user) => user.rateCardData2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;
}
