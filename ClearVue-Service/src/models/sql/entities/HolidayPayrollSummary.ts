import {
  Column,
  Entity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { PayrollMeta } from "./PayrollMeta";
import { Site } from "./Site";
import { Workers } from "./Workers";

@Index("fk_holiday_payroll_summary_agency_id", ["agencyId"], {})
@Index("fk_holiday_payroll_summary_client_id", ["clientId"], {})
@Index("fk_holiday_payroll_summary_created_by", ["createdBy"], {})
@Index("fk_holiday_payroll_summary_payroll_meta_id", ["payrollMetaId"], {})
@Index("fk_holiday_payroll_summary_site_id", ["siteId"], {})
@Index("fk_holiday_payroll_summary_updated_by", ["updatedBy"], {})
@Index("fk_holiday_payroll_summary_worker_id", ["workerId"], {})
@Entity("holiday_payroll_summary")
export class HolidayPayrollSummary {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "payroll_meta_id", unsigned: true })
  payrollMetaId: string;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("bigint", { name: "client_id", unsigned: true })
  clientId: string;

  @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
  siteId: string | null;

  @Column("bigint", { name: "agency_id", unsigned: true })
  agencyId: string;

  @Column("date", { name: "start_date", nullable: true })
  startDate: string | null;

  @Column("date", { name: "end_date", nullable: true })
  endDate: string | null;

  @Column("int", { name: "week", nullable: true })
  week: number | null;

  @Column("decimal", {
    name: "total_app_levy_with_wtr",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalAppLevyWithWtr: string | null;

  @Column("decimal", {
    name: "app_levy_difference",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  appLevyDifference: string | null;

  @Column("decimal", {
    name: "balance",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  balance: string | null;

  @Column("decimal", {
    name: "accrual_value",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  accrualValue: string | null;

  @Column("decimal", {
    name: "worker_holiday_accrual_value",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  workerHolidayAccrualValue: string | null;

  @Column("decimal", {
    name: "total_worked_charge",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalWorkedCharge: string | null;

  @Column("decimal", {
    name: "holiday_employment_cost",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  holidayEmploymentCost: string | null;

  @Column("decimal", {
    name: "wtr_cost",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  wtrCost: string | null;

  @Column("decimal", {
    name: "worked_hours_saving",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  workedHoursSaving: string | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @Column("enum", {
    name: "calculated_pathway",
    nullable: true,
    enum: ["0|0", "0|1", "1|0", "1|1"],
    default: () => "'0|0'",
  })
  calculatedPathway: "0|0" | "0|1" | "1|0" | "1|1" | null;

  @ManyToOne(
    () => AgencyDetails,
    (agencyDetails) => agencyDetails.holidayPayrollSummaries,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(
    () => ClientDetails,
    (clientDetails) => clientDetails.holidayPayrollSummaries,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.holidayPayrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(
    () => PayrollMeta,
    (payrollMeta) => payrollMeta.holidayPayrollSummaries,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "payroll_meta_id", referencedColumnName: "id" }])
  payrollMeta: PayrollMeta;

  @ManyToOne(() => Site, (site) => site.holidayPayrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.holidayPayrollSummaries2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @ManyToOne(() => Workers, (workers) => workers.holidayPayrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
