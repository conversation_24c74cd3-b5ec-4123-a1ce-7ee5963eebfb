import {
  Column,
  <PERSON>tity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";

@Index("fk_user_user_id", ["userId"], {})
@Entity("login_attempt")
export class LoginAttempt {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("datetime", { name: "attempt_at", nullable: true })
  attemptAt: Date | null;

  @Column("datetime", { name: "blocked_at", nullable: true })
  blockedAt: Date | null;

  @ManyToOne(() => User, (user) => user.loginAttempts, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: User;
}
