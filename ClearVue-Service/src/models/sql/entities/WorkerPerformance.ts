import {
    Column,
    <PERSON><PERSON>ty,
    Index,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
} from "typeorm";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Site } from "./Site";
import { WorkerPerformanceData } from "./WorkerPerformanceData";

@Index("fk_worker_performance_client_id", ["clientId"], {})
@Index("fk_worker_performance_created_by_idx", ["createdBy"], {})
@Index("fk_worker_performance_site_id", ["siteId"], {})
@Index("fk_worker_performance_updated_by_idx", ["updatedBy"], {})
@Entity("worker_performance")
export class WorkerPerformance {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("varchar", { name: "filename", length: 45 })
    filename: string;

    @Column("varchar", { name: "aws_path", length: 250 })
    awsPath: string;

    @Column("enum", { name: "status", nullable: true, enum: ["Processed"] })
    status: "Processed" | null;

    @Column("bigint", { name: "client_id", unsigned: true })
    clientId: string;

    @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
    siteId: string | null;

    @Column("bigint", { name: "agency_id", nullable: true, unsigned: true })
    agencyId: string | null;

    @Column("int", { name: "week", nullable: true })
    week: number | null;

    @Column("date", { name: "start_date", nullable: true })
    startDate: string | null;

    @Column("date", { name: "end_date", nullable: true })
    endDate: string | null;

    @Column("enum", { name: "uploaded_by", enum: ["CLIENT", "AGENCY"] })
    uploadedBy: "CLIENT" | "AGENCY";

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @ManyToOne(
        () => ClientDetails,
        (clientDetails) => clientDetails.workerPerformances,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.workerPerformances, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Site, (site) => site.workerPerformances, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.workerPerformances2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;

    @OneToMany(
        () => WorkerPerformanceData,
        (workerPerformanceData) => workerPerformanceData.workerPerformance
    )
    workerPerformanceData: WorkerPerformanceData[];
}
