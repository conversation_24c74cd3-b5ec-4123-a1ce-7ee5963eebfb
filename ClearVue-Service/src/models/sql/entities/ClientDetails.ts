import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyClientAssociation } from "./AgencyClientAssociation";
import { Booking } from "./Booking";
import { User } from "./User";
import { Sector } from "./Sector";
import { ClientFinancialRules } from "./ClientFinancialRules";
import { ClientYearlyRules } from "./ClientYearlyRules";
import { CreditDues } from "./CreditDues";
import { Departments } from "./Departments";
import { FtpConfigurations } from "./FtpConfigurations";
import { FtpCredentials } from "./FtpCredentials";
import { HolidayPayrollSummary } from "./HolidayPayrollSummary";
import { JobAssociation } from "./JobAssociation";
import { LosRule } from "./LosRule";
import { Message } from "./Message";
import { MessageAdmin } from "./MessageAdmin";
import { Payroll } from "./Payroll";
import { PayrollDetailedSummary } from "./PayrollDetailedSummary";
import { PayrollMeta } from "./PayrollMeta";
import { PayrollSummary } from "./PayrollSummary";
import { RateCard } from "./RateCard";
import { Region } from "./Region";
import { Shift } from "./Shift";
import { Site } from "./Site";
import { SurveyResult } from "./SurveyResult";
import { TimeAndAttendance } from "./TimeAndAttendance";
import { TimeAndAttendanceData } from "./TimeAndAttendanceData";
import { TotalAgencyPay } from "./TotalAgencyPay";
import { TotalAgencyPayData } from "./TotalAgencyPayData";
import { TrainingRules } from "./TrainingRules";
import { WorkerPerformance } from "./WorkerPerformance";
import { WorkerPerformanceData } from "./WorkerPerformanceData";
import { Workers } from "./Workers";

@Index("fk_client_details_created_by_idx", ["createdBy"], {})
@Index("fk_client_details_updated_by_idx", ["updatedBy"], {})
@Index("fk_sector_id_idx", ["sectorId"], {})
@Entity("client_details")
export class ClientDetails {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 250 })
  name: string;

  @Column("bigint", { name: "sector_id", nullable: true, unsigned: true })
  sectorId: string | null;

  @Column("json", { name: "address", nullable: true })
  address: object | null;

  @Column("varchar", { name: "post_code", nullable: true, length: 45 })
  postCode: string | null;

  @Column("varchar", { name: "city", nullable: true, length: 250 })
  city: string | null;

  @Column("varchar", { name: "country", nullable: true, length: 250 })
  country: string | null;

  @Column("text", { name: "resource", nullable: true })
  resource: string | null;

  @Column("enum", {
    name: "weekday_start",
    enum: ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"],
  })
  weekdayStart: "SUN" | "MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT";

  @Column("enum", {
    name: "booking_format",
    nullable: true,
    enum: ["HOURS", "HEADS"],
  })
  bookingFormat: "HOURS" | "HEADS" | null;

  @Column("tinyint", {
    name: "worker_invite_email",
    width: 1,
    default: () => "'0'",
  })
  workerInviteEmail: boolean;

  @Column("tinyint", {
    name: "worker_performance",
    width: 1,
    default: () => "'0'",
  })
  workerPerformance: boolean;

  @Column("tinyint", {
    name: "worker_training",
    width: 1,
    default: () => "'0'",
  })
  workerTraining: boolean;

  @Column("tinyint", {
    name: "rate_card_lookup",
    width: 1,
    default: () => "'0'",
  })
  rateCardLookup: boolean;

  @Column("tinyint", {
    name: "hide_ratings",
    width: 1,
    default: () => "'0'",
  })
  hideRatings: boolean;

  @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
  createdBy: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @OneToMany(
    () => AgencyClientAssociation,
    (agencyClientAssociation) => agencyClientAssociation.client
  )
  agencyClientAssociations: AgencyClientAssociation[];

  @OneToMany(() => Booking, (booking) => booking.client)
  bookings: Booking[];

  @ManyToOne(() => User, (user) => user.clientDetails, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Sector, (sector) => sector.clientDetails, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "sector_id", referencedColumnName: "id" }])
  sector: Sector;

  @ManyToOne(() => User, (user) => user.clientDetails2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @OneToMany(
    () => ClientFinancialRules,
    (clientFinancialRules) => clientFinancialRules.client
  )
  clientFinancialRules: ClientFinancialRules[];

  @OneToMany(
    () => ClientYearlyRules,
    (clientYearlyRules) => clientYearlyRules.client
  )
  clientYearlyRules: ClientYearlyRules[];

  @OneToMany(() => CreditDues, (creditDues) => creditDues.client)
  creditDues: CreditDues[];

  @OneToMany(() => Departments, (departments) => departments.client)
  departments: Departments[];

  @OneToMany(
    () => FtpConfigurations,
    (ftpConfigurations) => ftpConfigurations.client
  )
  ftpConfigurations: FtpConfigurations[];

  @OneToMany(() => FtpCredentials, (ftpCredentials) => ftpCredentials.client)
  ftpCredentials: FtpCredentials[];

  @OneToMany(
    () => HolidayPayrollSummary,
    (holidayPayrollSummary) => holidayPayrollSummary.client
  )
  holidayPayrollSummaries: HolidayPayrollSummary[];

  @OneToMany(() => JobAssociation, (jobAssociation) => jobAssociation.client)
  jobAssociations: JobAssociation[];

  @OneToMany(() => LosRule, (losRule) => losRule.client)
  losRules: LosRule[];

  @OneToMany(() => Message, (message) => message.client)
  messages: Message[];

  @OneToMany(() => MessageAdmin, (messageAdmin) => messageAdmin.client)
  messageAdmins: MessageAdmin[];

  @OneToMany(() => Payroll, (payroll) => payroll.client)
  payrolls: Payroll[];

  @OneToMany(
    () => PayrollDetailedSummary,
    (payrollDetailedSummary) => payrollDetailedSummary.client
  )
  payrollDetailedSummaries: PayrollDetailedSummary[];

  @OneToMany(() => PayrollMeta, (payrollMeta) => payrollMeta.client)
  payrollMetas: PayrollMeta[];

  @OneToMany(() => PayrollSummary, (payrollSummary) => payrollSummary.client)
  payrollSummaries: PayrollSummary[];

  @OneToMany(() => RateCard, (rateCard) => rateCard.client)
  rateCards: RateCard[];

  @OneToMany(() => Region, (region) => region.client)
  regions: Region[];

  @OneToMany(() => Shift, (shift) => shift.client)
  shifts: Shift[];

  @OneToMany(() => Site, (site) => site.client)
  sites: Site[];

  @OneToMany(() => SurveyResult, (surveyResult) => surveyResult.client)
  surveyResults: SurveyResult[];

  @OneToMany(
    () => TimeAndAttendance,
    (timeAndAttendance) => timeAndAttendance.client
  )
  timeAndAttendances: TimeAndAttendance[];

  @OneToMany(
    () => TimeAndAttendanceData,
    (timeAndAttendanceData) => timeAndAttendanceData.client
  )
  timeAndAttendanceData: TimeAndAttendanceData[];

  @OneToMany(() => TotalAgencyPay, (totalAgencyPay) => totalAgencyPay.client)
  totalAgencyPays: TotalAgencyPay[];

  @OneToMany(
    () => TotalAgencyPayData,
    (totalAgencyPayData) => totalAgencyPayData.client
  )
  totalAgencyPayData: TotalAgencyPayData[];

  @OneToMany(() => TrainingRules, (trainingRules) => trainingRules.client)
  trainingRules: TrainingRules[];

  @OneToMany(() => User, (user) => user.client)
  users: User[];

  @OneToMany(
    () => WorkerPerformance,
    (workerPerformance) => workerPerformance.client
  )
  workerPerformances: WorkerPerformance[];

  @OneToMany(
    () => WorkerPerformanceData,
    (workerPerformanceData) => workerPerformanceData.client
  )
  workerPerformanceData: WorkerPerformanceData[];

  @OneToMany(() => Workers, (workers) => workers.client)
  workers: Workers[];
}
