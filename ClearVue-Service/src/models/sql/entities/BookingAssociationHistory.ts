import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { User } from "./User";
import { Booking } from "./Booking";
import { BookingAssociation } from "./BookingAssociation";

@Index("FK_booking_association_history_agency_details", ["agencyId"], {})
@Index("FK_booking_association_history_archived_user", ["archivedBy"], {})
@Index("FK_booking_association_history_booking", ["bookingId"], {})
@Index("FK_booking_association_history_booking_association", ["bookingAssociationId"], {})
@Index("FK_booking_association_history_original_created_user", ["originalCreatedBy"], {})
@Index("FK_booking_association_history_original_updated_user", ["originalUpdatedBy"], {})
@Entity("booking_association_history")
export class BookingAssociationHistory {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "booking_association_id", unsigned: true })
  bookingAssociationId: string;

  @Column("bigint", { name: "agency_id", unsigned: true })
  agencyId: string;

  @Column("json", { name: "requested_workers_heads" })
  requestedWorkersHeads: object;

  @Column("float", { name: "requested_workers_total", precision: 12 })
  requestedWorkersTotal: number;

  @Column("json", { name: "requested_supervisors_heads" })
  requestedSupervisorsHeads: object;

  @Column("float", { name: "requested_supervisors_total", precision: 12 })
  requestedSupervisorsTotal: number;

  @Column("float", { name: "requested_total", precision: 12 })
  requestedTotal: number;

  @Column("json", { name: "fulfilled_workers_heads", nullable: true })
  fulfilledWorkersHeads: object | null;

  @Column("float", {
    name: "fulfilled_workers_total",
    nullable: true,
    precision: 12,
  })
  fulfilledWorkersTotal: number | null;

  @Column("json", { name: "fulfilled_supervisors_heads", nullable: true })
  fulfilledSupervisorsHeads: object | null;

  @Column("float", {
    name: "fulfilled_supervisors_total",
    nullable: true,
    precision: 12,
  })
  fulfilledSupervisorsTotal: number | null;

  @Column("float", { name: "fulfilled_total", nullable: true, precision: 12 })
  fulfilledTotal: number | null;

  @Column("bigint", { name: "booking_id", unsigned: true })
  bookingId: string;

  @Column("enum", { name: "status", enum: ["0", "1", "2"] })
  status: "0" | "1" | "2";

  @Column("bigint", { name: "original_created_by", unsigned: true })
  originalCreatedBy: string;

  @Column("datetime", { name: "original_created_at" })
  originalCreatedAt: Date;

  @Column("bigint", { name: "original_updated_by", unsigned: true })
  originalUpdatedBy: string;

  @Column("datetime", { name: "original_updated_at" })
  originalUpdatedAt: Date;

  @Column("datetime", {
    name: "archived_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  archivedAt: Date | null;

  @Column("bigint", { name: "archived_by", unsigned: true })
  archivedBy: string;

  @ManyToOne(
    () => AgencyDetails,
    (agencyDetails) => agencyDetails.bookingAssociationHistories,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(() => User, (user) => user.bookingAssociationHistories, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "archived_by", referencedColumnName: "id" }])
  archivedBy2: User;

  @ManyToOne(() => Booking, (booking) => booking.bookingAssociationHistories, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "booking_id", referencedColumnName: "id" }])
  booking: Booking;

  @ManyToOne(
    () => BookingAssociation,
    (bookingAssociation) => bookingAssociation.bookingAssociationHistories,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "booking_association_id", referencedColumnName: "id" }])
  bookingAssociation: BookingAssociation;

  @ManyToOne(() => User, (user) => user.bookingAssociationHistories2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "original_created_by", referencedColumnName: "id" }])
  originalCreatedBy2: User;

  @ManyToOne(() => User, (user) => user.bookingAssociationHistories3, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "original_updated_by", referencedColumnName: "id" }])
  originalUpdatedBy2: User;
}
