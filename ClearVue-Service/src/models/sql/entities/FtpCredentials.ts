import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { FtpConfigurations } from "./FtpConfigurations";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";

@Index("fk_ftp_credentials_client_id", ["clientId"], {})
@Index("fk_ftp_credentials_created_by", ["createdBy"], {})
@Index("fk_ftp_credentials_updated_by", ["updatedBy"], {})
@Entity("ftp_credentials")
export class FtpCredentials {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "client_id", unsigned: true })
  clientId: string;

  @Column("enum", { name: "protocol", enum: ["FTP", "SFTP"] })
  protocol: "FTP" | "SFTP";

  @Column("varchar", { name: "ftp_host", length: 255 })
  ftpHost: string;

  @Column("int", { name: "ftp_port", default: () => "'21'" })
  ftpPort: number;

  @Column("varchar", { name: "ftp_username", length: 100 })
  ftpUsername: string;

  @Column("varchar", { name: "ftp_password", length: 100 })
  ftpPassword: string;

  @Column("varchar", { name: "remote_directory", length: 255 })
  remoteDirectory: string;

  @Column("varchar", {
    name: "notification_email",
    nullable: true,
    length: 250,
  })
  notificationEmail: string | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @OneToMany(
    () => FtpConfigurations,
    (ftpConfigurations) => ftpConfigurations.ftpCredential
  )
  ftpConfigurations: FtpConfigurations[];

  @ManyToOne(
    () => ClientDetails,
    (clientDetails) => clientDetails.ftpCredentials,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.ftpCredentials, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => User, (user) => user.ftpCredentials2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
