import {
    Column,
    <PERSON><PERSON><PERSON>,
    Index,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Region } from "./Region";
import { Site } from "./Site";

@Index("fk_training_rules_client_id", ["clientId"], {})
@Index("fk_training_rules_created_by_idx", ["createdBy"], {})
@Index("fk_training_rules_region_id", ["regionId"], {})
@Index("fk_training_rules_site_id", ["siteId"], {})
@Index("fk_training_rules_updated_by_idx", ["updatedBy"], {})
@Entity("training_rules")
export class TrainingRules {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "client_id", unsigned: true })
    clientId: string;

    @Column("bigint", { name: "region_id", nullable: true, unsigned: true })
    regionId: string | null;

    @Column("bigint", { name: "site_id", unsigned: true })
    siteId: string;

    @Column("int", { name: "threshold_week" })
    thresholdWeek: number;

    @Column("int", { name: "evaluation_week" })
    evaluationWeek: number;

    @Column("int", { name: "limited_hours_threshold_week" })
    limitedHoursThresholdWeek: number;

    @Column("int", { name: "limited_hours_evaluation_week" })
    limitedHoursEvaluationWeek: number;

    @Column("decimal", { name: "max_training_hours", precision: 10, scale: 2 })
    maxTrainingHours: string;

    @Column("decimal", { name: "performance_threshold", precision: 10, scale: 2 })
    performanceThreshold: string;

    @Column("decimal", { name: "credit_rate", precision: 5, scale: 2 })
    creditRate: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @ManyToOne(
        () => ClientDetails,
        (clientDetails) => clientDetails.trainingRules,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.trainingRules, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Region, (region) => region.trainingRules, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "region_id", referencedColumnName: "id" }])
    region: Region;

    @ManyToOne(() => Site, (site) => site.trainingRules, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.trainingRules2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;
}
