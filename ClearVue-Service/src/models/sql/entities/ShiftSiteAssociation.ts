import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";
import { Shift } from "./Shift";
import { Site } from "./Site";

@Index("unique_shift_site", ["shiftId", "siteId"], { unique: true })
@Index("fk_shift_site_association_created_by_id_idx", ["createdBy"], {})
@Index("fk_shift_site_association_shift_id_idx", ["shiftId"], {})
@Index("fk_shift_site_association_site_id_idx", ["siteId"], {})
@Index("fk_shift_site_association_updated_by_idx", ["updatedBy"], {})
@Entity("shift_site_association")
export class ShiftSiteAssociation {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "shift_id", unsigned: true })
  shiftId: string;

  @Column("bigint", { name: "site_id", unsigned: true })
  siteId: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.shiftSiteAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Shift, (shift) => shift.shiftSiteAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "shift_id", referencedColumnName: "id" }])
  shift: Shift;

  @ManyToOne(() => Site, (site) => site.shiftSiteAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.shiftSiteAssociations2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
