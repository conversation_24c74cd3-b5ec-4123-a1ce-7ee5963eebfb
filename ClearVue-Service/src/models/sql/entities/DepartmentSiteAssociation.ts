import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";
import { Departments } from "./Departments";
import { Site } from "./Site";

@Index("unique_department_site", ["departmentId", "siteId"], { unique: true })
@Index("fk_department_site_association_created_by_id_idx", ["createdBy"], {})
@Index("fk_department_site_association_department_id_idx", ["departmentId"], {})
@Index("fk_department_site_association_site_id_idx", ["siteId"], {})
@Index("fk_department_site_association_updated_by_idx", ["updatedBy"], {})
@Entity("department_site_association")
export class DepartmentSiteAssociation {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "department_id", unsigned: true })
  departmentId: string;

  @Column("bigint", { name: "site_id", unsigned: true })
  siteId: string;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.departmentSiteAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(
    () => Departments,
    (departments) => departments.departmentSiteAssociations,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "department_id", referencedColumnName: "id" }])
  department: Departments;

  @ManyToOne(() => Site, (site) => site.departmentSiteAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.departmentSiteAssociations2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
