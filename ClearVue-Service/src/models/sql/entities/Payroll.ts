import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { PayrollMeta } from "./PayrollMeta";
import { Site } from "./Site";
import { Workers } from "./Workers";

@Index("fk_payroll_agency_id", ["agencyId"], {})
@Index("fk_payroll_client_id", ["clientId"], {})
@Index("fk_payroll_created_by", ["createdBy"], {})
@Index("fk_payroll_site_id", ["siteId"], {})
@Index("fk_payroll_time_and_attendance_data_id", ["payrollMetaId"], {})
@Index("fk_payroll_updated_by", ["updatedBy"], {})
@Index("fk_payroll_worker_id", ["workerId"], {})
@Entity("payroll")
export class Payroll {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "payroll_meta_id", unsigned: true })
  payrollMetaId: string;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("bigint", { name: "client_id", unsigned: true })
  clientId: string;

  @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
  siteId: string | null;

  @Column("bigint", { name: "agency_id", unsigned: true })
  agencyId: string;

  @Column("tinyint", { name: "under_twentyone", nullable: true, width: 1 })
  underTwentyone: boolean | null;

  @Column("tinyint", { name: "under_twentytwo", nullable: true, width: 1 })
  underTwentytwo: boolean | null;

  @Column("tinyint", { name: "within_twelveweeks", nullable: true, width: 1 })
  withinTwelveweeks: boolean | null;

  @Column("tinyint", { name: "limited_hours", width: 1, default: () => "'0'" })
  limitedHours: boolean;

  @Column("decimal", {
    name: "holiday_pay_type_value",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  holidayPayTypeValue: string | null;

  @Column("decimal", {
    name: "total_hours",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalHours: string | null;

  @Column("decimal", {
    name: "supervisor_hours",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  supervisorHours: string | null;

  @Column("decimal", {
    name: "non_supervisor_hours",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  nonSupervisorHours: string | null;

  @Column("decimal", { name: "total_charge", precision: 10, scale: 2 })
  totalCharge: string;

  @Column("decimal", {
    name: "supervisor_charges",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  supervisorCharges: string | null;

  @Column("decimal", {
    name: "non_supervisor_charges",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  nonSupervisorCharges: string | null;

  @Column("decimal", { name: "total_pay", precision: 10, scale: 2 })
  totalPay: string;

  @Column("decimal", {
    name: "other_assignment_pay",
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  otherAssignmentPay: string;

  @Column("tinyint", {
    name: "flagged_supervisor",
    width: 1,
    default: () => "'0'",
  })
  flaggedSupervisor: boolean;

  @Column("float", { name: "national_insurance", precision: 12 })
  nationalInsurance: number;

  @Column("decimal", { name: "holiday", precision: 10, scale: 2 })
  holiday: string;

  @Column("decimal", { name: "apprenticeship_levy", precision: 10, scale: 2 })
  apprenticeshipLevy: string;

  @Column("float", { name: "discount", nullable: true, precision: 12 })
  discount: number | null;

  @Column("decimal", { name: "pension", precision: 10, scale: 2 })
  pension: string;

  @Column("decimal", {
    name: "actual_cost_to_employ",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  actualCostToEmploy: string | null;

  @Column("decimal", {
    name: "training_hours",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  trainingHours: string | null;

  @Column("decimal", {
    name: "training_employment_cost",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  trainingEmploymentCost: string | null;

  @Column("decimal", {
    name: "total_agency_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalAgencyMargin: string | null;

  @Column("decimal", {
    name: "actual_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  actualMargin: string | null;

  @Column("decimal", {
    name: "total_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalMargin: string | null;

  @Column("decimal", {
    name: "rate_card_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  rateCardMargin: string | null;

  @Column("decimal", {
    name: "credit_per_hour",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  creditPerHour: string | null;

  @Column("decimal", { name: "clearvue_savings", precision: 10, scale: 2 })
  clearvueSavings: string;

  @Column("date", { name: "start_date", nullable: true })
  startDate: string | null;

  @Column("date", { name: "end_date", nullable: true })
  endDate: string | null;

  @Column("int", { name: "week", nullable: true })
  week: number | null;

  @Column("tinyint", { name: "pension_opt_out_prev", nullable: true, width: 1 })
  pensionOptOutPrev: boolean | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @ManyToOne(() => AgencyDetails, (agencyDetails) => agencyDetails.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(() => ClientDetails, (clientDetails) => clientDetails.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => PayrollMeta, (payrollMeta) => payrollMeta.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "payroll_meta_id", referencedColumnName: "id" }])
  payrollMeta: PayrollMeta;

  @ManyToOne(() => Site, (site) => site.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.payrolls2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @ManyToOne(() => Workers, (workers) => workers.payrolls, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
