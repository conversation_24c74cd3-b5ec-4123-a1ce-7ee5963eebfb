import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";
import { Workers } from "./Workers";

@Index("fk_pension_status_log_created_by", ["createdBy"], {})
@Index("fk_pension_status_log_worker_id", ["workerId"], {})
@Entity("pension_status_log")
export class PensionStatusLog {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("enum", {
    name: "from",
    nullable: true,
    enum: ["OPTED_OUT", "OPTED_IN"],
  })
  from: "OPTED_OUT" | "OPTED_IN" | null;

  @Column("enum", {
    name: "to",
    nullable: true,
    enum: ["OPTED_OUT", "OPTED_IN"],
  })
  to: "OPTED_OUT" | "OPTED_IN" | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.pensionStatusLogs, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => Workers, (workers) => workers.pensionStatusLogs, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
