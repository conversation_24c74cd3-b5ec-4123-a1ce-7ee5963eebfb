import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { FtpCredentials } from "./FtpCredentials";

@Index("fk_ftp_configurations_client_id", ["clientId"], {})
@Index("fk_ftp_configurations_created_by", ["createdBy"], {})
@Index("fk_ftp_configurations_ftp_credential_id", ["ftpCredentialId"], {})
@Index("fk_ftp_configurations_updated_by", ["updatedBy"], {})
@Entity("ftp_configurations")
export class FtpConfigurations {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "client_id", unsigned: true })
  clientId: string;

  @Column("enum", { name: "ftp_script_type", enum: ["TNA", "WORKERS_UPLOAD"] })
  ftpScriptType: "TNA" | "WORKERS_UPLOAD";

  @Column("varchar", { name: "cron_expression", length: 100 })
  cronExpression: string;

  @Column("varchar", { name: "sqs_queue_name", nullable: true, length: 255 })
  sqsQueueName: string | null;

  @Column("varchar", { name: "sqs_queue_url", nullable: true, length: 255 })
  sqsQueueUrl: string | null;

  @Column("bigint", { name: "ftp_credential_id", unsigned: true })
  ftpCredentialId: string;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @ManyToOne(
    () => ClientDetails,
    (clientDetails) => clientDetails.ftpConfigurations,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.ftpConfigurations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(
    () => FtpCredentials,
    (ftpCredentials) => ftpCredentials.ftpConfigurations,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "ftp_credential_id", referencedColumnName: "id" }])
  ftpCredential: FtpCredentials;

  @ManyToOne(() => User, (user) => user.ftpConfigurations2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
