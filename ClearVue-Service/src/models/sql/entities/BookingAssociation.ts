import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { Booking } from "./Booking";
import { User } from "./User";
import { BookingAssociationHistory } from "./BookingAssociationHistory";

@Index("FK_booking_association_agency_details", ["agencyId"], {})
@Index("FK_booking_association_booking", ["bookingId"], {})
@Index("FK_booking_association_user", ["createdBy"], {})
@Index("FK_booking_association_user_2", ["updatedBy"], {})
@Entity("booking_association")
export class BookingAssociation {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "agency_id", unsigned: true })
  agencyId: string;

  @Column("json", { name: "requested_workers_heads" })
  requestedWorkersHeads: object;

  @Column("float", { name: "requested_workers_total", precision: 12 })
  requestedWorkersTotal: number;

  @Column("json", { name: "requested_supervisors_heads" })
  requestedSupervisorsHeads: object;

  @Column("float", { name: "requested_supervisors_total", precision: 12 })
  requestedSupervisorsTotal: number;

  @Column("float", { name: "requested_total", precision: 12 })
  requestedTotal: number;

  @Column("json", { name: "fulfilled_workers_heads", nullable: true })
  fulfilledWorkersHeads: object | null;

  @Column("float", {
    name: "fulfilled_workers_total",
    nullable: true,
    precision: 12,
  })
  fulfilledWorkersTotal: number | null;

  @Column("json", { name: "fulfilled_supervisors_heads", nullable: true })
  fulfilledSupervisorsHeads: object | null;

  @Column("float", {
    name: "fulfilled_supervisors_total",
    nullable: true,
    precision: 12,
  })
  fulfilledSupervisorsTotal: number | null;

  @Column("float", { name: "fulfilled_total", nullable: true, precision: 12 })
  fulfilledTotal: number | null;

  @Column("bigint", { name: "booking_id", unsigned: true })
  bookingId: string;

  @Column("enum", {
    name: "status",
    enum: ["0", "1", "2"],
    default: () => "'0'",
  })
  status: "0" | "1" | "2";

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date | null;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @ManyToOne(
    () => AgencyDetails,
    (agencyDetails) => agencyDetails.bookingAssociations,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(() => Booking, (booking) => booking.bookingAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "booking_id", referencedColumnName: "id" }])
  booking: Booking;

  @ManyToOne(() => User, (user) => user.bookingAssociations, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => User, (user) => user.bookingAssociations2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @OneToMany(
    () => BookingAssociationHistory,
    (bookingAssociationHistory) => bookingAssociationHistory.bookingAssociation
  )
  bookingAssociationHistories: BookingAssociationHistory[];
}
