import {
    Column,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { Departments } from "./Departments";
import { PayrollMeta } from "./PayrollMeta";
import { Shift } from "./Shift";
import { Site } from "./Site";
import { Workers } from "./Workers";

@Index("fk_payroll_detailed_summary_agency_id", ["agencyId"], {})
@Index("fk_payroll_detailed_summary_client_id", ["clientId"], {})
@Index("fk_payroll_detailed_summary_created_by", ["createdBy"], {})
@Index("fk_payroll_detailed_summary_department_id", ["departmentId"], {})
@Index("fk_payroll_detailed_summary_payroll_meta_id", ["payrollMetaId"], {})
@Index("fk_payroll_detailed_summary_shift_id", ["shiftId"], {})
@Index("fk_payroll_detailed_summary_site_id", ["siteId"], {})
@Index("fk_payroll_detailed_summary_updated_by", ["updatedBy"], {})
@Index("fk_payroll_detailed_summary_worker_id", ["workerId"], {})
@Entity("payroll_detailed_summary")
export class PayrollDetailedSummary {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "payroll_meta_id", unsigned: true })
    payrollMetaId: string;

    @Column("bigint", { name: "worker_id", unsigned: true })
    workerId: string;

    @Column("bigint", { name: "client_id", unsigned: true })
    clientId: string;

    @Column("bigint", { name: "agency_id", unsigned: true })
    agencyId: string;

    @Column("bigint", { name: "shift_id", nullable: true, unsigned: true })
    shiftId: string | null;

    @Column("bigint", { name: "department_id", nullable: true, unsigned: true })
    departmentId: string | null;

    @Column("varchar", {
        name: "department_cost_centre",
        nullable: true,
        length: 100,
    })
    departmentCostCentre: string | null;

    @Column("bigint", { name: "site_id", unsigned: true })
    siteId: string;

    @Column("int", { name: "week" })
    week: number;

    @Column("date", { name: "start_date" })
    startDate: string;

    @Column("date", { name: "end_date" })
    endDate: string;

    @Column("varchar", { name: "pay_type", length: 45 })
    payType: string;

    @Column("tinyint", {
        name: "adjustment",
        nullable: true,
        width: 1,
        default: () => "'0'",
    })
    adjustment: boolean | null;

    @Column("tinyint", {
        name: "pay_correction",
        nullable: true,
        width: 1,
        default: () => "'0'",
    })
    payCorrection: boolean | null;

    @Column("decimal", {
        name: "total_hours",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    totalHours: string | null;

    @Column("decimal", { name: "total_charge", precision: 10, scale: 2 })
    totalCharge: string;

    @Column("decimal", { name: "actual_cost_to_employ", precision: 10, scale: 2 })
    actualCostToEmploy: string;

    @Column("decimal", { name: "total_margin", precision: 10, scale: 2 })
    totalMargin: string;

    @Column("decimal", {
        name: "actual_margin",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    actualMargin: string | null;

    @Column("decimal", {
        name: "rate_card_margin",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    rateCardMargin: string | null;

    @Column("decimal", {
        name: "credit_per_hour",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    creditPerHour: string | null;

    @Column("decimal", {
        name: "clearvue_savings",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    clearvueSavings: string | null;

    @Column("decimal", { name: "total_pay", precision: 10, scale: 2 })
    totalPay: string;

    @Column("decimal", {
        name: "national_insurance",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    nationalInsurance: string | null;

    @Column("decimal", {
        name: "pension",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    pension: string | null;

    @Column("decimal", {
        name: "apprenticeship_levy",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    apprenticeshipLevy: string | null;

    @Column("decimal", {
        name: "holiday",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    holiday: string | null;

    @Column("decimal", {
        name: "holiday_pay_type_value",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    holidayPayTypeValue: string | null;

    @Column("decimal", {
        name: "holiday_employment_cost",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    holidayEmploymentCost: string | null;

    @Column("tinyint", { name: "under_twentyone", nullable: true, width: 1 })
    underTwentyone: boolean | null;

    @Column("tinyint", { name: "under_twentytwo", nullable: true, width: 1 })
    underTwentytwo: boolean | null;

    @Column("tinyint", { name: "within_twelveweeks", nullable: true, width: 1 })
    withinTwelveweeks: boolean | null;

    @Column("decimal", {
        name: "other_assignment_pay_value",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    otherAssignmentPayValue: string | null;

    @Column("decimal", {
        name: "credit_value",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    creditValue: string | null;

    @Column("decimal", {
        name: "accrual_value",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    accrualValue: string | null;

    @Column("decimal", {
        name: "wtr_cost",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    wtrCost: string | null;

    @Column("decimal", {
        name: "total_cost",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    totalCost: string | null;

    @Column("decimal", {
        name: "app_levy_difference",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    appLevyDifference: string | null;

    @Column("decimal", {
        name: "app_levy_with_wtr",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    appLevyWithWtr: string | null;

    @Column("decimal", {
        name: "total_worked_charge",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    totalWorkedCharge: string | null;

    @Column("decimal", {
        name: "worked_hours_charge",
        nullable: true,
        precision: 10,
        scale: 2,
    })
    workedHoursCharge: string | null;

    @Column("bigint", { name: "created_by", unsigned: true })
    createdBy: string;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "updated_by", unsigned: true })
    updatedBy: string;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @ManyToOne(
        () => AgencyDetails,
        (agencyDetails) => agencyDetails.payrollDetailedSummaries,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
    agency: AgencyDetails;

    @ManyToOne(
        () => ClientDetails,
        (clientDetails) => clientDetails.payrollDetailedSummaries,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
    client: ClientDetails;

    @ManyToOne(() => User, (user) => user.payrollDetailedSummaries, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(
        () => Departments,
        (departments) => departments.payrollDetailedSummaries,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "department_id", referencedColumnName: "id" }])
    department: Departments;

    @ManyToOne(
        () => PayrollMeta,
        (payrollMeta) => payrollMeta.payrollDetailedSummaries,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([{ name: "payroll_meta_id", referencedColumnName: "id" }])
    payrollMeta: PayrollMeta;

    @ManyToOne(() => Shift, (shift) => shift.payrollDetailedSummaries, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "shift_id", referencedColumnName: "id" }])
    shift: Shift;

    @ManyToOne(() => Site, (site) => site.payrollDetailedSummaries, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.payrollDetailedSummaries2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;

    @ManyToOne(() => Workers, (workers) => workers.payrollDetailedSummaries, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
    worker: Workers;
}
