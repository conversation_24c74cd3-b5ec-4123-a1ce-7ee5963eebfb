import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Message } from "./Message";
import { Workers } from "./Workers";

@Index("idx_message_likes_message_id", ["messageId"], {})
@Index("idx_message_likes_worker_id", ["workerId"], {})
@Index("uk_message_likes_worker_id_message_id", ["workerId", "messageId"], {
  unique: true,
})
@Entity("message_reaction")
export class MessageReaction {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "message_id", unsigned: true })
  messageId: string;

  @Column("bigint", { name: "worker_id", unsigned: true })
  workerId: string;

  @Column("enum", {
    name: "reaction",
    nullable: true,
    enum: ["LIKE", "DISLIKE"],
  })
  reaction: "LIKE" | "DISLIKE" | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(() => Message, (message) => message.messageReactions, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "message_id", referencedColumnName: "id" }])
  message: Message;

  @ManyToOne(() => Workers, (workers) => workers.messageReactions, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "worker_id", referencedColumnName: "id" }])
  worker: Workers;
}
