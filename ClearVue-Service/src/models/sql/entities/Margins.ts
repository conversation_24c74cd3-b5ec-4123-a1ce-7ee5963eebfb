import {
    Column,
    <PERSON><PERSON><PERSON>,
    Index,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyClientAssociation } from "./AgencyClientAssociation";
import { User } from "./User";
import { Site } from "./Site";

@Index("fk_margins_created_by", ["createdBy"], {})
@Index("fk_margins_site_id", ["siteId"], {})
@Index("fk_margins_updated_by", ["updatedBy"], {})
@Index("idx_agency_client_association_id", ["agencyClientAssociationId"], {})
@Index("unique_margin", ["agencyClientAssociationId", "siteId", "los"], {
    unique: true,
})
@Entity("margins")
export class Margins {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
    id: string;

    @Column("bigint", { name: "agency_client_association_id", unsigned: true })
    agencyClientAssociationId: string;

    @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
    siteId: string | null;

    @Column("decimal", {
        name: "los",
        nullable: true,
        comment: "In Terms Of Years",
        precision: 4,
        scale: 2,
    })
    los: string | null;

    @Column("decimal", { name: "margin", precision: 3, scale: 2 })
    margin: string;

    @Column("decimal", {
        name: "overtime_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    overtimeMargin: string | null;

    @Column("decimal", {
        name: "transport_fee",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    transportFee: string | null;

    @Column("decimal", {
        name: "ssp",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    ssp: string | null;

    @Column("decimal", {
        name: "training_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    trainingMargin: string | null;

    @Column("decimal", {
        name: "induction_training_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    inductionTrainingMargin: string | null;

    @Column("decimal", {
        name: "bh_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    bhMargin: string | null;

    @Column("decimal", {
        name: "nsp_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    nspMargin: string | null;

    @Column("decimal", {
        name: "supervisor_standard_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    supervisorStandardMargin: string | null;

    @Column("decimal", {
        name: "supervisor_overtime_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    supervisorOvertimeMargin: string | null;

    @Column("decimal", {
        name: "supervisor_permanent_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    supervisorPermanentMargin: string | null;

    @Column("decimal", {
        name: "suspension_margin",
        nullable: true,
        precision: 3,
        scale: 2,
        default: () => "'0.00'",
    })
    suspensionMargin: string | null;

    @Column("datetime", {
        name: "created_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    createdAt: Date;

    @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
    createdBy: string | null;

    @Column("datetime", {
        name: "updated_at",
        default: () => "CURRENT_TIMESTAMP",
    })
    updatedAt: Date;

    @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
    updatedBy: string | null;

    @ManyToOne(
        () => AgencyClientAssociation,
        (agencyClientAssociation) => agencyClientAssociation.margins,
        { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
    )
    @JoinColumn([
        { name: "agency_client_association_id", referencedColumnName: "id" },
    ])
    agencyClientAssociation: AgencyClientAssociation;

    @ManyToOne(() => User, (user) => user.margins, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
    createdBy2: User;

    @ManyToOne(() => Site, (site) => site.margins, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
    site: Site;

    @ManyToOne(() => User, (user) => user.margins2, {
        onDelete: "NO ACTION",
        onUpdate: "NO ACTION",
    })
    @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
    updatedBy2: User;
}
