import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";
import { Permissions } from "./Permissions";

@Index("fk_features_created_by_idx", ["createdBy"], {})
@Index("fk_features_updated_by_idx", ["updatedBy"], {})
@Index("uk_features_code", ["code"], { unique: true })
@Index("uk_features_name", ["name"], { unique: true })
@Entity("features")
export class Features {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", unique: true, length: 250 })
  name: string;

  @Column("varchar", { name: "code", unique: true, length: 250 })
  code: string;

  @Column("bigint", { name: "created_by", nullable: true, unsigned: true })
  createdBy: string | null;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("datetime", {
    name: "updated_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.features, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => User, (user) => user.features2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;

  @OneToMany(() => Permissions, (permissions) => permissions.feature)
  permissions: Permissions[];
}
