import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { FtpProcessLogs } from "./FtpProcessLogs";

@Index("fk_ftp_sqs_message_logs_ftp_process_log_id", ["ftpProcessLogId"], {})
@Entity("ftp_sqs_message_logs")
export class FtpSqsMessageLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "ftp_process_log_id", unsigned: true })
  ftpProcessLogId: string;

  @Column("varchar", { name: "file_name", length: 255 })
  fileName: string;

  @Column("json", { name: "sqs_message_payload" })
  sqsMessagePayload: object;

  @Column("enum", {
    name: "status",
    enum: ["IN_QUEUE", "PROCESSING", "SUCCEED", "FAILED"],
  })
  status: "IN_QUEUE" | "PROCESSING" | "SUCCEED" | "FAILED";

  @Column("text", {
    name: "api_response",
    nullable: true,
    comment: "Response from the API",
  })
  apiResponse: string | null;

  @Column("bigint", { name: "payroll_meta_id", nullable: true, unsigned: true })
  payrollMetaId: string | null;

  @Column("timestamp", {
    name: "created_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date | null;

  @Column("timestamp", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @ManyToOne(
    () => FtpProcessLogs,
    (ftpProcessLogs) => ftpProcessLogs.ftpSqsMessageLogs,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "ftp_process_log_id", referencedColumnName: "id" }])
  ftpProcessLog: FtpProcessLogs;
}
