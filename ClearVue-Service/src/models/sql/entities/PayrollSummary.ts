import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { AgencyDetails } from "./AgencyDetails";
import { ClientDetails } from "./ClientDetails";
import { User } from "./User";
import { PayrollMeta } from "./PayrollMeta";
import { Site } from "./Site";

@Index("fk_payroll_summary_agency_id", ["agencyId"], {})
@Index("fk_payroll_summary_client_id", ["clientId"], {})
@Index("fk_payroll_summary_created_by", ["createdBy"], {})
@Index("fk_payroll_summary_payroll_meta_id", ["payrollMetaId"], {})
@Index("fk_payroll_summary_site_id", ["siteId"], {})
@Index("fk_payroll_summary_updated_by", ["updatedBy"], {})
@Entity("payroll_summary")
export class PayrollSummary {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "payroll_meta_id", unsigned: true })
  payrollMetaId: string;

  @Column("bigint", { name: "client_id", unsigned: true })
  clientId: string;

  @Column("bigint", { name: "site_id", nullable: true, unsigned: true })
  siteId: string | null;

  @Column("bigint", { name: "agency_id", unsigned: true })
  agencyId: string;

  @Column("decimal", {
    name: "total_holiday_pay",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalHolidayPay: string | null;

  @Column("decimal", {
    name: "total_wtr_costs",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalWtrCosts: string | null;

  @Column("decimal", {
    name: "total_hours",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalHours: string | null;

  @Column("decimal", {
    name: "supervisor_hours",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  supervisorHours: string | null;

  @Column("decimal", {
    name: "non_supervisor_hours",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  nonSupervisorHours: string | null;

  @Column("decimal", { name: "total_charge", precision: 10, scale: 2 })
  totalCharge: string;

  @Column("decimal", {
    name: "supervisor_charges",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  supervisorCharges: string | null;

  @Column("decimal", {
    name: "non_supervisor_charges",
    nullable: true,
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  nonSupervisorCharges: string | null;

  @Column("int", {
    name: "identified_supervisors",
    nullable: true,
    unsigned: true,
    default: () => "'0'",
  })
  identifiedSupervisors: number | null;

  @Column("decimal", { name: "total_pay", precision: 10, scale: 2 })
  totalPay: string;

  @Column("decimal", {
    name: "other_assignment_pay",
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  otherAssignmentPay: string;

  @Column("decimal", {
    name: "total_agency_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalAgencyMargin: string | null;

  @Column("decimal", {
    name: "actual_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  actualMargin: string | null;

  @Column("decimal", {
    name: "total_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalMargin: string | null;

  @Column("decimal", {
    name: "rate_card_margin",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  rateCardMargin: string | null;

  @Column("decimal", {
    name: "credit_per_hour",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  creditPerHour: string | null;

  @Column("decimal", { name: "clearvue_savings", precision: 10, scale: 2 })
  clearvueSavings: string;

  @Column("date", { name: "start_date", nullable: true })
  startDate: string | null;

  @Column("date", { name: "end_date", nullable: true })
  endDate: string | null;

  @Column("int", { name: "week", nullable: true })
  week: number | null;

  @Column("decimal", {
    name: "total_worked_charge",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  totalWorkedCharge: string | null;

  @Column("decimal", {
    name: "holiday_employment_cost",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  holidayEmploymentCost: string | null;

  @Column("decimal", {
    name: "self_bill_value",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  selfBillValue: string | null;

  @Column("decimal", {
    name: "holiday_charge",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  holidayCharge: string | null;

  @Column("decimal", {
    name: "accrual_value",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  accrualValue: string | null;

  @Column("enum", {
    name: "calculated_pathway",
    nullable: true,
    enum: ["0|0", "0|1", "1|0", "1|1"],
    default: () => "'0|0'",
  })
  calculatedPathway: "0|0" | "0|1" | "1|0" | "1|1" | null;

  @Column("int", {
    name: "adjustment_count",
    nullable: true,
    default: () => "'0'",
  })
  adjustmentCount: number | null;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("datetime", {
    name: "created_at",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("bigint", { name: "updated_by", nullable: true, unsigned: true })
  updatedBy: string | null;

  @Column("datetime", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @ManyToOne(
    () => AgencyDetails,
    (agencyDetails) => agencyDetails.payrollSummaries,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "agency_id", referencedColumnName: "id" }])
  agency: AgencyDetails;

  @ManyToOne(
    () => ClientDetails,
    (clientDetails) => clientDetails.payrollSummaries,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "client_id", referencedColumnName: "id" }])
  client: ClientDetails;

  @ManyToOne(() => User, (user) => user.payrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: User;

  @ManyToOne(() => PayrollMeta, (payrollMeta) => payrollMeta.payrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "payroll_meta_id", referencedColumnName: "id" }])
  payrollMeta: PayrollMeta;

  @ManyToOne(() => Site, (site) => site.payrollSummaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "site_id", referencedColumnName: "id" }])
  site: Site;

  @ManyToOne(() => User, (user) => user.payrollSummaries2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: User;
}
