import { Column, <PERSON><PERSON><PERSON>, OneTo<PERSON>any, PrimaryGeneratedColumn } from "typeorm";
import { FtpSqsMessageLogs } from "./FtpSqsMessageLogs";

@Entity("ftp_process_logs")
export class FtpProcessLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "ftp_configuration_id", unsigned: true })
  ftpConfigurationId: string;

  @Column("tinyint", {
    name: "process_status_id",
    comment:
      "0->INITIAL, 1->PRODUCING, 2->PRODUCING_DONE, 3->CONSUMING, 4->CONSUMING_DONE, 5->READY_FOR_NOTIFICATION, 6->PROCESSING_NOTIFICATION, 7->NOTIFICATION_SENT, 8->COMPLETE",
  })
  processStatusId: number;

  @Column("text", { name: "error", nullable: true })
  error: string | null;

  @Column("tinyint", {
    name: "report_generation_status",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  reportGenerationStatus: boolean | null;

  @Column("varchar", {
    name: "generated_report_filepath",
    nullable: true,
    length: 512,
  })
  generatedReportFilepath: string | null;

  @Column("timestamp", { name: "lock_timestamp", nullable: true })
  lockTimestamp: Date | null;

  @Column("timestamp", {
    name: "created_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date | null;

  @Column("timestamp", {
    name: "updated_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedAt: Date | null;

  @OneToMany(
    () => FtpSqsMessageLogs,
    (ftpSqsMessageLogs) => ftpSqsMessageLogs.ftpProcessLog
  )
  ftpSqsMessageLogs: FtpSqsMessageLogs[];
}
