import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { User } from "./User";

@Index("fk_user_activity_user_id", ["userId"], {})
@Entity("user_activity")
export class UserActivity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: false, unsigned: true })
  userId: string;

  @Column("datetime", { 
    name: "last_activity_at", 
    nullable: false,
    default: () => "CURRENT_TIMESTAMP"
  })
  lastActivityAt: Date;

  @Column("datetime", { 
    name: "auth_time", 
    nullable: false 
  })
  authTime: Date;

  @Column("varchar", { 
    name: "session_id", 
    length: 255,
    nullable: false 
  })
  sessionId: string;

  @Column("datetime", {
    name: "created_at",
    nullable: false,
    default: () => "CURRENT_TIMESTAMP",
  })
  createdAt: Date;

  @Column("datetime", {
    name: "updated_at",
    nullable: false,
    default: () => "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP"
  })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.userActivities, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: User;
}
