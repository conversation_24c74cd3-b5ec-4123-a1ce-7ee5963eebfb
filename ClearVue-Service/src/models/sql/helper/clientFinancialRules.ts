import { getRepository } from 'typeorm';
import { ClientFinancialRules } from '..';
import e from 'express';

export const addNewFinancialRuleHelper: any = async (data) => {
    const clientFinancialRulesRepository = getRepository(ClientFinancialRules);
    let response = await clientFinancialRulesRepository.insert({
        clientId: data.client_id,
        financialYearStart: data.finacial_year_start,
        financialYearEnd: data.finacial_year_end,
        startDate: data.start_date,
        endDate: data.end_date,
        totalWeeks: data.total_weeks,
        niPercent: data.ni_percent,
        niThreshold: data.ni_threshold,
        pensionPercent: data.pension_percent,
        pensionThreshold: data.pension_threshold,
        appLevyPercent: data.app_levy_percent,
        createdBy: data.user_id,
        updatedBy: data.user_id,
    });
    return response.generatedMaps[0];
};

export const fetchFinancialRules: any = async (client_id, sort_type, rule_id = null, finacial_year_start = null, finacial_year_end = null) => {
    let whereCondition = `clientFinancialRules.clientId = :client_id`;
    whereCondition += finacial_year_start ? ` AND clientFinancialRules.financialYearStart <= :finacial_year_start` : "";
    whereCondition += finacial_year_end ? ` AND clientFinancialRules.financialYearEnd >= :finacial_year_end` : "";
    whereCondition += rule_id ? ` AND clientFinancialRules.id = :rule_id` : "";

    let whereClauseValue = { "client_id": client_id, "finacial_year_start": finacial_year_start, "finacial_year_end": finacial_year_end, "rule_id": rule_id };

    const clientFinancialRulesRepository = getRepository(ClientFinancialRules);
    const queryBuilder = clientFinancialRulesRepository.createQueryBuilder('clientFinancialRules');

    queryBuilder.select([
        'clientFinancialRules.id as id',
        'clientFinancialRules.clientId as client_id',
        'clientFinancialRules.financialYearStart as finacial_year_start',
        'clientFinancialRules.financialYearEnd as finacial_year_end',
        "date_format(clientFinancialRules.startDate,'%Y-%m-%d') as start_date",
        "date_format(clientFinancialRules.endDate,'%Y-%m-%d') as end_date",
        'clientFinancialRules.totalWeeks as total_weeks',
        'clientFinancialRules.niPercent as ni_percent',
        'clientFinancialRules.niThreshold as ni_threshold',
        'clientFinancialRules.pensionPercent as pension_percent',
        'clientFinancialRules.pensionThreshold as pension_threshold',
        'clientFinancialRules.appLevyPercent as app_levy_percent',
        'CONCAT(SUBSTRING(clientFinancialRules.financialYearStart, -2), "|", SUBSTRING(clientFinancialRules.financialYearEnd, -2)) as financial_year',
    ]);

    queryBuilder.where(whereCondition, whereClauseValue).orderBy('clientFinancialRules.id', sort_type.toUpperCase());

    const financialRules = await queryBuilder.execute();

    return financialRules;

};

export const deleteFinancialRuleHelper: any = async (clientId: string, id: string, includeCurrYear = false) => {
    // Get the repository
    const clientFinancialRulesRepository = getRepository(ClientFinancialRules);
    let whereCondition = 'clientId = :clientId';
    if (includeCurrYear) {
        whereCondition += ` AND id >= :id`;
    }
    else {
        whereCondition += ` AND id > :id`;
    }

    // Delete all rules for the client with id >= the given id
    const deleteResult = await clientFinancialRulesRepository
        .createQueryBuilder()
        .delete()
        .from(ClientFinancialRules)
        .where(whereCondition, {
            clientId,
            id
        })
        .execute();

    return deleteResult;

};

export const updateFinancialRuleHelper: any = async (yearlyRuleId, payload, loggedInUser) => {
    // Get repository
    const clientFinancialRulesRepository = getRepository(ClientFinancialRules);

    // Update the current rule
    const resp = await clientFinancialRulesRepository.update(
        { id: yearlyRuleId, clientId: payload.client_id },
        {
            startDate: payload.start_date,
            endDate: payload.end_date,
            totalWeeks: payload.total_weeks,
            niPercent: payload.ni_percent,
            niThreshold: payload.ni_threshold,
            pensionPercent: payload.pension_percent,
            pensionThreshold: payload.pension_threshold,
            appLevyPercent: payload.app_levy_percent,
            financialYearStart: payload.finacial_year_start,
            financialYearEnd: payload.finacial_year_end,
            updatedBy: loggedInUser.user_id
        }
    );
    // Log the number of affected rows
    return resp;

};