import { worker } from 'cluster';
import { response } from 'express';
import { getRepository, In } from 'typeorm';
import { User, ResetPasswordToken, ClientDetails, AgencyDetails, Site, Region, MessageAdmin } from '../';
import { agency, ErrorResponse, UserType } from '../../../common';
import { UserSiteAssociation, UserRegionAssociation } from '..';
let _ = require("lodash");

/**
 * Get user object as per user email
 * @param  {string} email
 */
export const getUserByEmail: any = async (email: string) => {
    const userRepository = getRepository(User);
    return await userRepository.findOne(
        {
            where:
                { email: email }
        }
    );
};

/**
 * Get user object as per Firebase UID
 * @param  {string} firebaseUid
 */
export const getUserByFirebaseUid: any = async (firebaseUid: string) => {
    const userRepository = getRepository(User);
    return await userRepository.findOne(
        {
            where:
                { firebaseUid: firebaseUid }
        }
    );
};

export const addClientAdminToUser: any = async (user) => {
    const userRepository = getRepository(User);
    let response = await userRepository.insert({
        userTypeId: user.user_type,
        clientId: user.clientId,
        name: user.admin_name,
        email: user.admin_email,
        countryCode: user.country_code,
        mobile: user.admin_contact_number,
        createdBy: user.user_id,
        updatedBy: user.user_id
    });
    return response.generatedMaps[0];
}

export const updateClientAdminUser: any = async (user) => {
    const userRepository = getRepository(User);
    let oldData = await userRepository.findOne({ where: { clientId: user.client_id } });
    let response = await userRepository.save({
        id: oldData.id,
        clientId: user.client_id,
        userTypeId: user.user_type,
        email: oldData.email,
        name: user.admin_name,
        countryCode: user.country_code,
        mobile: user.contact_number,
        updatedBy: user.user_id
    });
    return response;
}

/**
 * Update user password and set verification status to true
 * @param  {number} userId
 * @param  {string} password
 */
export const updatePasswordAndVerificationStatus: any = async (userId: number, password: string) => {
    const userRepository = getRepository(User);

    let result = await userRepository.createQueryBuilder("user")
        .update<User>(User, {
            password: password,
            isVerified: 1,
            updatedAt: new Date(),
            updatedBy: String(userId)
        })
        .where("user.id = :id", { id: userId })
        .updateEntity(true)
        .execute();

    return result.affected;
};

/**
 * create user
 */
export const createUser: any = async (body) => {
    const userRepository = getRepository(User);
    let response = await userRepository.save(body);

    let company_name;
    if (response[0].agencyId === null) {
        let name = await getRepository(ClientDetails).findOne({ select: ['name'], where: { id: response[0].clientId } });
        company_name = name.name;
    } else {
        let name = await getRepository(AgencyDetails).findOne({ select: ['name'], where: { id: response[0].agencyId } });
        company_name = name.name;
    }
    return { company_name, response };
};

export const getAllUsers: any = async (loggedInUser) => {
    let selectQuery = getRepository(User).createQueryBuilder("user")
        .innerJoin("user.userType_2", "user_type")
        .select(["user.id AS user_id", "user.user_type_id AS user_type_id", "user_type.type as user_type", "user.client_id as client_id", "user_type.name as user_type_name", "user.agency_id AS agency_id",
            "user.name AS name", "user.email AS email", "user.country_code AS country_code", "user.mobile AS mobile", "user.resource as resource"])
        .where(`user.id = ${loggedInUser.user_id}`)
    let response = {
        region_id: null,
        site_id: null,
        message_admin_id: null
    };

    if (parseInt(loggedInUser.user_type_id) === UserType.CLIENT_SITE || parseInt(loggedInUser.user_type_id) === UserType.AGENCY_SITE) {
        let site = await getRepository(UserSiteAssociation).findOne({ select: ["siteId"], where: { userId: loggedInUser.user_id } });
        response["site_id"] = site.siteId;

    } else if (parseInt(loggedInUser.user_type_id) === UserType.CLIENT_REGIONAL || parseInt(loggedInUser.user_type_id) === UserType.AGENCY_REGIONAL) {
        let region = await getRepository(UserRegionAssociation).findOne({ select: ["regionId"], where: { userId: loggedInUser.user_id } });
        response["region_id"] = region.regionId;

    } else if (parseInt(loggedInUser.user_type_id) === UserType.MESSAGE_ADMIN) {
        let message_admin = await getRepository(MessageAdmin).findOne({ select: ["id"], where: { userId: loggedInUser.user_id } });
        response["message_admin_id"] = message_admin.id;
    }

    return { ...response, ... await selectQuery.getRawOne() };
};

/**
 * Add record in reset-password-token table
 * @param  {string} token
 * @param  {number} userId
 */
export const addResetPasswordToken = async (token: string, userId: number) => {
    const resetPasswordRepository = getRepository(ResetPasswordToken);
    return await resetPasswordRepository.save({
        token: token,
        userId: String(userId)
    });
}


/**
 * Remove record from reset-password-token table
 * @param  {string} token
 */
export const removeResetPasswordToken = async (token: string) => {
    const resetPasswordRepository = getRepository(ResetPasswordToken);
    return await resetPasswordRepository.delete({ token: token });
};

export const updateUserHelper = async (id, data) => {
    const userRepository = getRepository(User);
    return await userRepository.update(id, data)
}

export const getAdminUserDetailsHelper = async (whereClause, whereClauseString) => {
    const userRepository = getRepository(User);
    let response = await userRepository.createQueryBuilder('user')
        .select(['user.id as user_id',
            'user.name as user_name',
            'user.country_code as country_code',
            'user.mobile as mobile',
            'user.email as email',
            'user.is_active as is_active',
            "date_format(user.created_at,'%Y-%m-%d') as created_at"
        ])
        .where(whereClause, whereClauseString)
        .getRawMany();
    return response;
}
export const getUsers = async (whereClause, whereClauseString) => {
    const usersRepository = getRepository(User);
    let reponse = await usersRepository.createQueryBuilder("user")
        .innerJoin("user.userType_2", "user_type")
        .leftJoin("user.client", "client_details")
        .leftJoin("user.agency", "agency_details")
        .select(["user.id AS user_id",
            "user.user_type_id AS user_type_id",
            "user_type.type as user_type",
            "user_type.name as user_type_name",
            "user.agency_id AS agency_id",
            "user.client_id AS client_id",
            "user.name AS name",
            "user.email AS email",
            "user.country_code AS country_code",
            "user.mobile AS mobile",
            "user.resource as resource",
            "client_details.name as client_name",
            "agency_details.name as agency_name"])
        .where(whereClause, whereClauseString)
        .getRawMany();
    return reponse;
}

export const getUserById = async (userId) => {
    const usersRepository = getRepository(User);
    return await usersRepository.createQueryBuilder("user")
        .innerJoin("user.userType_2", "user_type")
        .leftJoin("user.client", "client_details")
        .leftJoin("user.agency", "agency_details")
        .select([
            "user.id AS id",
            "user.user_type_id AS user_type_id",
            "user.national_insurance_number as national_insurance_number",
            "user.email AS email",
            `user.resource as resource`,
            `user.documents AS documents`,
            `user.is_active AS is_active`,
            "client_details.name as client_name",
            "agency_details.name as agency_name",
            "user.client_id as client_id",
            "user.agency_id as agency_id"
        ])
        .where("user.id = :userId", { userId })
        .getRawOne();
}

export const getAgencyClientsByUserId = async (userId: number) => {
    const userRepository = getRepository(User);

    const queryBuilder = userRepository.createQueryBuilder("u")
        .innerJoin("u.agency", "a") // Join agency from user
        .innerJoin("a.agencyClientAssociations", "aca") // Join agencyClientAssociations from agency
        .select("aca.clientId", "client_id")
        .where("u.id = :userId", { userId });

    return await queryBuilder.getRawMany();
};

export const revokeUserProfileAccessHelper = async (user_id, loggedInUser) => {
    return await getRepository(User).update(user_id, {
        password: null,
        updatedAt: new Date(Date.now()),
        updatedBy: loggedInUser.user_id
    });
}

export const nationalInsuranceNumberExistsHelper: any = async (nationalInsuranceNumber: string) => {
    return await getRepository(User).count({ where: { nationalInsuranceNumber } })
}


export const getRequestedUserEmailCounts: any = async (emailList: string[]) => {
    let totalUser = await getRepository(User).count(
        {
            where: { email: In(emailList) }
        }
    );
    return { totalUser };
}

export const getUserByNationalInsuranceNumber: any = async (nationalInsuranceNumber: string) => {
    return await getRepository(User).findOne({ select: ['id'], where: { nationalInsuranceNumber } });
}

export const createWorkerUser: any = async (requestPayload, loggedInUser) => {
    let response = await getRepository(User).insert({
        userTypeId: UserType.AGENCY_WORKER.toString(),
        nationalInsuranceNumber: requestPayload.national_insurance_number,
        name: requestPayload.first_name + ' ' + (requestPayload.last_name ? requestPayload.last_name : requestPayload.surname || ''),
        email: requestPayload.email,
        password: requestPayload.password || null,
        countryCode: requestPayload.country_code || '',
        mobile: requestPayload.mobile || '',
        createdBy: loggedInUser,
        createdAt: new Date()
    })
    return response.generatedMaps[0]
}

export const addWorkerUserInBulk: any = async (workerUserData) => {
    return await getRepository(User).save(workerUserData);
}

export const addWorkerUserInBulkTransaction = async (transactionalEntityManager, workerUserData) => {
    return await transactionalEntityManager.getRepository(User).save(workerUserData);
};

export const getUserIdByNationalInsuranceNumber: any = async (nationalInsuranceNumbers) => {
    return await getRepository(User).find({
        select: ['id', 'nationalInsuranceNumber'],
        where: { nationalInsuranceNumber: In(nationalInsuranceNumbers) }
    });
}


export const updateUser: any = async (id, userDetails) => {
    return await getRepository(User).update(id, userDetails);
}

export const updateUserTransaction = async (transactionalEntityManager, id, userDetails) => {
    return await transactionalEntityManager.getRepository(User).update({ id }, userDetails);
};

export const getWorkerUserDetails: any = async (id, worker_id) => {
    return await getRepository(User).createQueryBuilder('user')
        .innerJoin('user.workers3', 'worker')
        .select(['DISTINCT(user.id) as user_id', "date_format(worker.assignment_date,'%Y-%m-%d') as assignment_date", 'worker.id as worker_id', 'user.name as name', 'user.email as email', 'worker.post_code as post_code', 'worker.transport as transport', 'worker.other_assignment as other_assignment', 'worker.pension_opt_out as pension_opt_out', 'worker.availability as availability', 'worker.hours as hours', 'IFNULL(user.documents,"") as documents', 'worker.language as language', "date_format(worker.in_actived_at,'%Y-%m-%d %H:%i:%s') as in_actived_at"])
        .where(`user.id = :user_id AND worker.id = :worker_id`, { "user_id": id, "worker_id": worker_id })
        .getRawOne();
}

export const getAdminEmailsFromSiteId: any = async (siteId) => {
    return await getRepository(User).createQueryBuilder('user')
        .innerJoin('user.userSiteAssociations', 'user_site')
        .select(['user.email as email'])
        .where(`user_site.site_id = :site_id AND user.user_type_id = :user_type_id`, { "site_id": siteId, "user_type_id": UserType.CLIENT_SITE })
        .getRawMany()
}

export const getAgencyAdminEmailByAgencyId: any = async (agencyId) => {
    return await getRepository(User).findOne({
        select: ['email'], where: {
            agencyId
        }
    })
}

export const setUserProfileStatusHelper = async (user_id, loggedInUser, is_active) => {
    return await getRepository(User).update(user_id, {
        isActive: is_active,
        updatedAt: new Date(Date.now()),
        updatedBy: loggedInUser.user_id
    });
}

export const getRequestedUserEmail: any = async (emailList, userId) => {
    const userRepository = getRepository(User).createQueryBuilder('user');

    let userData = await userRepository.select('user.id as user_id, user.email')
        .where("user.email IN(:...emailList)", { emailList })
        .getRawMany();

    let individualworkerUser = await userRepository.innerJoin('user.workers3', 'worker')
        .select('user.id as user_id, user.email, worker.id, worker.device_token, worker.language, date_format(worker.start_date,"%Y-%m-%d") as start_date')
        .where("user.email IN(:...emailList)", { emailList })
        .andWhere(`worker.employee_id IS NULL`)
        .getRawMany();

    let existingWorkerUser = await getRepository(User)
        .createQueryBuilder("user")
        .select('user.email,site.name as site_name')
        .innerJoin("workers", "worker1", "worker1.user_id = user.id AND worker1.employee_id IS NOT NULL AND worker1.created_by = :createdBy", { createdBy: userId })
        .innerJoin("job", "job", "job.id = worker1.job_id")
        .innerJoin("job_association", "job_assoc", "job_assoc.job_id = job.id")
        .innerJoin("site", "site", "site.id = job_assoc.site_id")
        .where("user.email IN (:...emails)", { emails: emailList })
        .getRawMany();

    return { userData, existingWorkerUser, individualworkerUser }
}


export const getUserClientAgency: any = async (user_id) => {
    return await getRepository(User).findOne({
        select: ['clientId', 'agencyId'], where: {
            id: user_id
        }
    })
}


export const getUserByEmailList: any = async (emailList) => {
    if (!emailList.length) {
        return [];
    }
    const userRepository = getRepository(User).createQueryBuilder('user')
        .leftJoin('user.workers3', 'worker')
        .leftJoin('worker.client', 'client');

    let userData = await userRepository.select('user.id as user_id, user.email, user.user_type_id, worker.client_id as client_id, user.national_insurance_number as national_insurance_number, worker.agency_id as agency_id, client.name as client_name')
        .where("user.email IN(:...emailList)", { emailList })
        .getRawMany();
    return userData
}

export const checkAuthorisedResourceAccess: any = async (payload, loggedInUser) => {
    const userDetails = await getAllUsers(loggedInUser);
    const userTypeId = parseInt(loggedInUser.user_type_id);

    const permissionDenied = (isError, data) => ({ isError, error: isError ? [403, ErrorResponse.PermissionDenied] : null, payload: data });

    const clientCheck = [UserType.CLIENT_ADMIN, UserType.CLIENT_REGIONAL, UserType.CLIENT_SITE].includes(userTypeId);
    const agencyCheck = [UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE].includes(userTypeId);

    if (clientCheck && parseInt(userDetails.client_id) !== parseInt(payload.client_id)) {
        return permissionDenied(1, null);
    }

    if (agencyCheck) {
        if (payload.agency_id && parseInt(userDetails.agency_id) !== parseInt(payload.agency_id)) {
            return permissionDenied(1, null);
        }
        payload.agency_id = payload.agency_id || userDetails.agency_id;
    }

    const isRegional = [UserType.CLIENT_REGIONAL, UserType.AGENCY_REGIONAL].includes(userTypeId);
    const isSite = [UserType.CLIENT_SITE, UserType.AGENCY_SITE].includes(userTypeId);
    const isAdmin = [UserType.CLIENT_ADMIN, UserType.AGENCY_ADMIN].includes(userTypeId);

    const regionId = isRegional ? userDetails.region_id : payload.region_id || null;
    const siteId = isSite ? userDetails.site_id : payload.site_id || null;

    if ((regionId && payload.region_id && parseInt(payload.region_id) !== parseInt(regionId)) ||
        (siteId && payload.site_id && parseInt(payload.site_id) !== parseInt(siteId))) {
        return permissionDenied(1, null);
    }

    if (regionId) payload["region_id"] = regionId;
    if (siteId) payload["site_id"] = siteId;

    return permissionDenied(0, payload);
}