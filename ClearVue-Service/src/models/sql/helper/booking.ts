import { getRepository } from 'typeorm';
import { Booking } from '../';
import _ from 'lodash';

export const createBookingHelper: any = async (payload) => {
    let bookingRepository = getRepository(Booking);
    let response = await bookingRepository.insert(payload);
    return _.map(response.identifiers, 'id');
}

export const updateBookingStatusHelper: any = async (id, data) => {
    return await getRepository(Booking).update(id, data);
}

export const getBookingById: any = async (id) => {
    return await getRepository(Booking).findOne({
        select: ['id', 'clientId', 'siteId', 'regionId', 'departmentId', 'shiftTypeId', 'status', 'startDate', 'endDate'],
        where: { id: id }
    })
}

export const getbookingDetailsForEmail: any = async (whereClause, whereClauseValue) => {
    return await getRepository(Booking).createQueryBuilder('booking')
        .innerJoin('booking.bookingAssociations', 'booking_association')
        .innerJoin('booking_association.agency', 'agency_details')
        .leftJoin('agency_details.users', 'user')
        .innerJoin('booking.client', 'client_details')
        .select(['booking.id as booking_id', 'user.email as email', 'agency_details.name as agency_name', 'client_details.name as client_name', 'LOWER(client_details.booking_format) as booking_format',
            'booking.start_date as start_date', 'booking.end_date as end_date', 'booking_association.requested_total as requested_total', 'booking_association.requested_workers_total as requested_workers_total',
            'booking_association.requested_supervisors_total as requested_supervisors_total', 'booking.site_id as site_id', 'booking.client_id as client_id', 'booking_association.agency_id as agency_id'])
        .where(whereClause, whereClauseValue)
        .getRawMany();
}

export const updateBooking = async (bookingData, transactionManager) => {
    return await transactionManager.getRepository(Booking).save(bookingData);
};

export const getFulfilmentAndLossCount: any = async (whereClause, whereClauseValue) => {

    return getRepository(Booking).createQueryBuilder("bk")
        .innerJoin('bk.bookingAssociations', 'ba')
        .select('IFNULL(SUM(`ba`.`requested_total`),0) AS requested_total')
        .addSelect('IFNULL(SUM(`ba`.`fulfilled_total`),0) AS fulfilled_total')
        .addSelect('IFNULL(SUM(`ba`.`requested_supervisors_total`),0) AS requested_supervisors_total')
        .addSelect('IFNULL(SUM(`ba`.`fulfilled_supervisors_total`),0) AS fulfilled_supervisors_total')
        .where(whereClause, whereClauseValue)
        .getRawOne();
}


export const deleteBookingById: any = async (id: number) => {
    return await getRepository(Booking).createQueryBuilder("booking")
        .delete()
        .where('id = :id', { id })
        .execute();
};