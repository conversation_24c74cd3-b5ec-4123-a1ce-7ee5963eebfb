import { getConnection, getRepository } from 'typeorm';

import { BookingAssociation, BookingAssociationHistory } from '../';


// Service function to store previous version
export const storePreviousBookingAssociation = async (oldData: BookingAssociation, archivedBy) => {
    const previousAssociation = new BookingAssociationHistory();

    // Map old data to previous association
    previousAssociation.bookingAssociationId = oldData.id;
    previousAssociation.agencyId = oldData.agencyId;
    previousAssociation.requestedWorkersHeads = oldData.requestedWorkersHeads;
    previousAssociation.requestedWorkersTotal = oldData.requestedWorkersTotal;
    previousAssociation.requestedSupervisorsHeads = oldData.requestedSupervisorsHeads;
    previousAssociation.requestedSupervisorsTotal = oldData.requestedSupervisorsTotal;
    previousAssociation.requestedTotal = oldData.requestedTotal;
    previousAssociation.fulfilledWorkersHeads = oldData.fulfilledWorkersHeads;
    previousAssociation.fulfilledWorkersTotal = oldData.fulfilledWorkersTotal;
    previousAssociation.fulfilledSupervisorsHeads = oldData.fulfilledSupervisorsHeads;
    previousAssociation.fulfilledSupervisorsTotal = oldData.fulfilledSupervisorsTotal;
    previousAssociation.fulfilledTotal = oldData.fulfilledTotal;
    previousAssociation.bookingId = oldData.bookingId;
    previousAssociation.status = oldData.status;
    previousAssociation.originalCreatedBy = oldData.createdBy;
    previousAssociation.originalCreatedAt = oldData.createdAt;
    previousAssociation.originalUpdatedBy = oldData.updatedBy;
    previousAssociation.originalUpdatedAt = oldData.updatedAt;
    previousAssociation.archivedBy = archivedBy;

    return await getRepository(BookingAssociationHistory).save(previousAssociation);
};


export const deleteBookingAssociationHistoryByBookingId = async (bookingId: number) => {
    // Get all booking associations for this booking
    const bookingAssociations = await getRepository(BookingAssociation)
        .find({ where: { bookingId: bookingId } });

    // Delete booking association history records first
    if (bookingAssociations.length > 0) {
        const associationIds = bookingAssociations.map(ba => ba.id);
        await getRepository(BookingAssociationHistory)
            .createQueryBuilder()
            .delete()
            .where('booking_association_id IN (:...ids)', { ids: associationIds })
            .execute();
    }
};