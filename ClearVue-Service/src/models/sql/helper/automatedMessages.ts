import { getRepository, getManager, In } from 'typeorm';
import { Workers, Message, MessageReceiverWorkers } from '../';
import { AutomatedMessagesLabels } from '../../../common';


/**
 * Get worker details who completes mentioned timeline
 */
export const getTimelineQualifiedWorkerDetails: any = async () => {
    let subquery = await getLatestWorkerIdsSubquery();
    return getRepository(Workers).createQueryBuilder('workers')
        .select([
            'workers.id AS id',
            'workers.client_id AS client_id',
            'workers.agency_id AS agency_id',
            'workers.device_token AS device_token',
            'workers.language AS language',

            'DATEDIFF(NOW(), workers.assignment_date) AS duration'
        ])
        .innerJoin('workers.user', 'user')
        .where(`workers.id IN (${subquery})`)
        .andWhere('DATEDIFF(NOW(), workers.assignment_date) IN (7, 14, 28, 56, 84)') // Removed 182, 273, 364 days for 26, 39, 52 week surveys
        .andWhere('user.password IS NOT NULL')  // Select Mobile App Registred workers Only.
        .andWhere('workers.is_active = 1')
        .getRawMany()
}

/**
 * Get worker details who have work anniversary with the current date
 */
export const getWorkAnniversaryQualifiedWorkerDetails: any = async () => {
    let subquery = await getLatestWorkerIdsSubquery();
    return getRepository(Workers).createQueryBuilder('workers')
        .select([
            'workers.id AS id',
            'workers.client_id AS client_id',
            'workers.agency_id AS agency_id',
            'workers.device_token AS device_token',
            'workers.language AS language',
        ])
        .innerJoin('workers.user', 'user')
        .where(`workers.id IN (${subquery})`)
        .andWhere('DATE_FORMAT(assignment_date, "%m-%d") = DATE_FORMAT(NOW(), "%m-%d")')
        .andWhere('user.password IS NOT NULL')  // Select Mobile App Registred workers Only.
        .andWhere('workers.is_active = 1')
        .andWhere('assignment_date < curdate()')
        .getRawMany()
}


/**
 * Get Timeline message details from the database
 */
export const getTimelineRelatedMessagesDetails: any = async () => {
    return getRepository(Message).createQueryBuilder('message')
        .select([
            `message.id`,
            `message.label`,
            `message.from`,
            `message.body`,
            `message.title`
        ])
        .where(`label IN ('${AutomatedMessagesLabels.NEW_STARTER_WEEK_1}', '${AutomatedMessagesLabels.NEW_STARTER_WEEK_2}','${AutomatedMessagesLabels.NEW_STARTER_WEEK_4}', '${AutomatedMessagesLabels.NEW_STARTER_WEEK_8}','${AutomatedMessagesLabels.NEW_STARTER_WEEK_12}', '${AutomatedMessagesLabels.NEW_STARTER_WEEK_26}','${AutomatedMessagesLabels.NEW_STARTER_WEEK_39}', '${AutomatedMessagesLabels.NEW_STARTER_WEEK_52}', '${AutomatedMessagesLabels.ANNUAL_WORK_ANNIVERSARY}')`)
        .andWhere("message.type = 'SYSTEM_DEFAULT'")
        .getMany()
}


/**
 * Return Query to get latest workers associated with user_id.
 */
export const getLatestWorkerIdsSubquery: any = async () => {
    return getRepository(Workers)
        .createQueryBuilder('workers')
        .select('MAX(workers.id)')
        .groupBy('workers.user_id')
        .getQuery();
}


/**
 * Get worker details who have birthday on the current date
 */
export const getBirthdayWorkerDetails: any = async () => {
    let subquery = await getLatestWorkerIdsSubquery();

    return getRepository(Workers).createQueryBuilder('workers')
        .select([
            'workers.id AS id',
            'workers.client_id AS client_id',
            'workers.agency_id AS agency_id',
            'workers.device_token AS device_token',
            'workers.language AS language',
        ])
        .innerJoin('workers.user', 'user')
        .where(`workers.id IN (${subquery})`)
        .andWhere('DATE_FORMAT(date_of_birth, "%m-%d") = DATE_FORMAT(NOW(), "%m-%d")')
        .andWhere('user.password IS NOT NULL')  // Select Mobile App Registred workers Only.
        .andWhere('workers.is_active = 1')
        .getRawMany()
}

/**
 * Get system message details by label
 */
export const getMessageDetailsByLabel: any = async (label: string) => {
    return getRepository(Message).createQueryBuilder('message')
        .select([
            `message.id`,
            `message.label`,
            `message.from`,
            `message.body`,
            `message.title`
        ])
        .where(`label = '${label}'`)
        .andWhere("message.type = 'SYSTEM_DEFAULT'")
        .getOne()
}



/**
 * Get client_id or agency_id specific system default message details by label.
 * @param  {string} label
 * @param  {number} id
 * @param  {string} idType
 */
export const getSystemDefaultMessageByLabelAndId: any = async (label: string, id: number, idType: string) => {
    return getRepository(Message).createQueryBuilder('message')
        .select([
            `message.id`,
            `message.label`,
            `message.from`,
            `message.body`,
            `message.title`,
            `message.titleTranslations`,
            `message.${idType}`
        ])
        .where("message.label = :label", { label: label })
        .andWhere(`message.${idType} = :id`, { id: id })
        .andWhere("message.type = 'SYSTEM_DEFAULT'")
        .getOne();
};

/**
 * Get worker details which are inactive for last week or last 2 weeks.
 */
export const getWorkerDetailsWhoRemainInactive: any = async (week: number = 1) => {
    let subquery = await getLatestWorkerIdsSubquery();

    return getManager().query(`
    SELECT 
        workers.id as id,
        workers.client_id as client_id,
        workers.agency_id as agency_id, 
        workers.device_token as device_token, 
        workers.language AS language,
        IF(user.password IS NOT NULL, 1, 0) as is_worker_registered
    FROM
        workers 
            INNER JOIN 
        user ON user.id = workers.user_id
    WHERE
    workers.id NOT IN (SELECT DISTINCT
                (worker_id)
            FROM
                time_and_attendance_data
            WHERE
                total_charge > 0   
                AND assignment_date >= DATE_SUB(CURDATE(),
                INTERVAL DAYOFWEEK(CURDATE()) + ${week === 1 ? 6 : 13} DAY)
                AND end_date <= DATE_SUB(CURDATE(),
                INTERVAL DAYOFWEEK(CURDATE()) DAY))
            AND workers.id IN (${subquery})
            AND workers.type = 'TEMPORARY'
            AND workers.is_active = 1;
  `);
}


/**
 * Get worker details whose start date(Now assignment_date) is on the current date
 */
export const getWorkersWhoseStartDateIsCurrentDate: any = async () => {
    let subquery = await getLatestWorkerIdsSubquery();

    return getRepository(Workers).createQueryBuilder('workers')
        .select([
            'workers.id AS id',
            'workers.client_id AS client_id',
            'workers.agency_id AS agency_id',
            'workers.device_token AS device_token',
            'workers.language AS language',
        ])
        .innerJoin('workers.user', 'user')
        .where(`workers.id IN (${subquery})`)
        .andWhere('assignment_date = CURDATE()')
        .andWhere('user.password IS NOT NULL')  // Select Mobile App Registred workers Only.
        .andWhere('workers.is_active = 1')
        .getRawMany()
}


/**
 * Get workers by provided worker ids
 */
export const getWorkersByIds: any = async (workerIds: Array<number>) => {
    return getRepository(Workers).createQueryBuilder('workers')
        .select([
            'workers.id AS id',
            'workers.client_id AS client_id',
            'workers.agency_id AS agency_id',
            'workers.device_token AS device_token',
            'workers.language AS language',
        ])
        .innerJoin('workers.user', 'user')
        .where({ id: In(workerIds) })
        .andWhere('user.password IS NOT NULL')  // Select Mobile App Registred workers Only.
        .getRawMany()
}


/*
* Provide worker specific automated messages(type=SYSTEM) details form workerId
* @param  {number} workerId
*/
export const getWorkerAutomatedMessageInfoToRemove: any = async (workerId: number) => {
    return getRepository(MessageReceiverWorkers).createQueryBuilder('message_receiver_workers')
        .select([
            'message_receiver_workers.id AS id',
            'message_receiver_workers.worker_id AS worker_id',
            'message.id AS message_id',
            'message.receiver AS receiver'
        ])
        .innerJoin('message_receiver_workers.message', 'message')
        .where(`message_receiver_workers.worker_id = ${workerId}`)
        .andWhere(`message.type = 'SYSTEM'`)
        .getRawMany();
};


/*
* Delete MessageReceiverWorkers table raws by array of ids
* @param  {Array<number>} ids
*/
export const deleteMessageReceiverWorkersRawsById: any = async (ids: Array<number>) => {
    return await getRepository(MessageReceiverWorkers).delete(ids);
};


/*
* update existing SYSTEM_DEFAULT message from `message` table for the given messageId.
* @param  {} id
* @param  {} payload
*/
export const updateSystemMessage: any = async (id, payload) => {
    return await getRepository(Message).update({ id }, payload);
};