import { getRepository, In } from 'typeorm';
import { FtpCredentials, FtpConfigurations, UserRegionAssociation, UserSiteAssociation } from '..';
import { AgencyClientAssociation, ClientDetails, MessageAdmin, TrainingRules, User } from '../';
import { UserType } from '../../../common';
import { fetchStartDateYearlyRules } from '.';

export const addNewClient: any = async (data) => {
    const clientDetailsRepository = getRepository(ClientDetails);
    let response = await clientDetailsRepository.insert({
        name: data.name,
        sectorId: data.sectorId,
        address: {
            address_line_1: data.address_line_1,
            address_line_2: data.address_line_2 || '',
            address_line_3: data.address_line_3 || ''
        },
        postCode: data.postCode,
        city: data.city,
        country: data.country,
        createdBy: data.user_id,
        updatedBy: data.user_id,
        weekdayStart: data.weekday_start,
        workerInviteEmail: data.worker_invite_email,
        workerPerformance: data.workerPerformance,
        workerTraining: data.workerTraining,
        rateCardLookup: data.rateCardLookup,
        bookingFormat: data.booking_format
    });
    return response.generatedMaps[0];
};

export const updateExistingClient: any = async (data, id) => {
    const clientDetailsRepository = getRepository(ClientDetails);
    data.updatedAt = new Date();
    return await clientDetailsRepository.update(id, data);
};

export const updateFtpConfig: any = async (data, clientId) => {
    const ftpCredentialsRepository = getRepository(FtpCredentials);
    const ftpConfigurationsRepository = getRepository(FtpConfigurations);

    // Update ftp_credentials
    const credentialData = {
        ftpHost: data.ftp_host,
        ftpUsername: data.ftp_username,
        ftpPassword: data.ftp_password,
        remoteDirectory: data.remote_directory,
        notificationEmail: data.notification_email,
        updatedBy: data.user_id,
        updatedAt: new Date()
    };

    // Get existing credentials first to get the ID
    const credentialsUpdateResponse = await ftpCredentialsRepository.update(
        { clientId: clientId },
        credentialData
    );

    // Get the credentials ID
    const existingCredentials = await ftpCredentialsRepository.findOne({ where: { clientId: clientId } });
    const credentialId = existingCredentials?.id;

    // Update ftp_configurations
    const existingConfigurations = await ftpConfigurationsRepository.find({ where: { clientId } });
    const updatedConfigurations = [];

    // Handle TNA configuration
    if (data.tna_ftp) {
        const tnaConfig = existingConfigurations.find(config => config.ftpScriptType === 'TNA');
        if (tnaConfig) {
            await ftpConfigurationsRepository.update(
                { id: tnaConfig.id },
                {
                    cronExpression: data.tna_cron_expression,
                    updatedBy: data.user_id,
                    updatedAt: new Date()
                }
            );
        } else {
            updatedConfigurations.push({
                clientId,
                ftpScriptType: 'TNA',
                ftpCredentialId: credentialId,
                cronExpression: data.tna_cron_expression,
                createdBy: data.user_id,
                updatedBy: data.user_id
            });
            await ftpConfigurationsRepository.insert(updatedConfigurations);
        }
    } else {
        const tnaConfig = existingConfigurations.find(config => config.ftpScriptType === 'TNA');
        if (tnaConfig) {
            await ftpConfigurationsRepository.delete({ id: tnaConfig.id });
        }
    }

    // Handle WORKERS_UPLOAD configuration
    if (data.worker_ftp) {
        const workerConfig = existingConfigurations.find(config => config.ftpScriptType === 'WORKERS_UPLOAD');
        if (workerConfig) {
            await ftpConfigurationsRepository.update(
                { id: workerConfig.id },
                {
                    cronExpression: data.worker_cron_expression,
                    updatedBy: data.user_id,
                    updatedAt: new Date()
                }
            );
        } else {
            updatedConfigurations.push({
                clientId,
                ftpScriptType: 'WORKERS_UPLOAD',
                ftpCredentialId: credentialId,
                cronExpression: data.worker_cron_expression,
                createdBy: data.user_id,
                updatedBy: data.user_id
            });
            await ftpConfigurationsRepository.insert(updatedConfigurations);
        }
    } else {
        const workerConfig = existingConfigurations.find(config => config.ftpScriptType === 'WORKERS_UPLOAD');
        if (workerConfig) {
            await ftpConfigurationsRepository.delete({ id: workerConfig.id });
        }
    }

    return { credentialsUpdateResponse, updatedConfigurations };
};


export const getFtpConfigByClientId: any = async (clientId, ftpScriptType = null) => {
    const ftpCredentialsRepository = getRepository(FtpCredentials);
    const ftpConfigurationsRepository = getRepository(FtpConfigurations);

    // Get ftp_credentials
    const ftpCredentials = await ftpCredentialsRepository.findOne({
        where: { clientId: clientId }
    });

    if (!ftpCredentials) {
        return null; // No credentials found
    }

    // Define query conditions for ftp_configurations
    const configQueryConditions: any = { clientId: clientId };
    if (ftpScriptType) {
        configQueryConditions.ftpScriptType = ftpScriptType;
    }

    // Get ftp_configurations linked to this client
    const ftpConfigurations = await ftpConfigurationsRepository.find({
        where: configQueryConditions
    });

    return {
        credentials: ftpCredentials,
        configurations: ftpConfigurations
    };
};


export const getAllClients: any = async (loggedInUser, page, limit, sortBy, sortType) => {
    const userType = parseInt(loggedInUser.user_type_id);

    let queryBuilder, repository, whereClause;
    let repo_meta_name;

    if ([UserType.AGENCY_ADMIN, UserType.AGENCY_REGIONAL, UserType.AGENCY_SITE].includes(userType)) {
        const userRepository = getRepository(User);
        const agencyclientAssocRepository = getRepository(AgencyClientAssociation);
        repo_meta_name = "agency_client_association";

        const data = await userRepository.createQueryBuilder("user")
            .select("user.agency_id", "agency_id")
            .where("user.id = :id", { id: parseInt(loggedInUser.user_id) })
            .getRawOne();

        const agencyId = parseInt(data.agency_id);
        repository = agencyclientAssocRepository;
        let subQWhereStr = "agency_client_association.agencyId = :agencyId";
        let subQWhereClause = { agencyId }
        whereClause = { agencyId };
        if (loggedInUser.client_id) {
            subQWhereClause["clientId"] = +loggedInUser.client_id
            subQWhereStr += " AND client_details.id = :clientId"
        }

        queryBuilder = repository.createQueryBuilder("agency_client_association")
            .innerJoin("agency_client_association.client", "client_details")
            .innerJoin("client_details.sector", "sector")
            .leftJoin(
                qb => qb
                    .select([
                        'training_rules.client_id AS client_id',
                        'COUNT(*) AS training_rule_count'
                    ])
                    .from('training_rules', 'training_rules')
                    .groupBy('training_rules.client_id'),
                'training_rules',
                'client_details.id = training_rules.client_id'
            )
            .leftJoin('ftp_credentials', 'ftp_credentials', 'ftp_credentials.client_id = client_details.id')
            .select([
                "agency_client_association.id as association_id",
                "agency_client_association.is_restricted as is_restricted",
                "agency_client_association.comment_restricted as comment_restricted",
                "agency_client_association.total_assignment_pay as total_assignment_pay",
                "agency_client_association.holiday_activation as holiday_activation",
                "agency_client_association.holiday_cost_removed as holiday_cost_removed",
                "client_details.id as client_id",
                "client_details.name as client_name",
                "client_details.sector_id as sector_id",
                "sector.value as sector_name",
                "client_details.address as address",
                "client_details.post_code as post_code",
                "client_details.city as city",
                "client_details.country as country",
                "client_details.created_at as created_at",
                "client_details.weekday_start AS weekday_start",
                "client_details.worker_invite_email AS worker_invite_email",
                "client_details.worker_performance AS worker_performance",
                "client_details.worker_training AS worker_training",
                "client_details.rate_card_lookup AS rate_card_lookup",
                "client_details.booking_format AS booking_format",
                'IFNULL(training_rules.training_rule_count, 0) AS training_rule_count',
                'ftp_credentials.id AS ftp_credentials_id',
                'ftp_credentials.ftp_host AS ftp_host',
                'ftp_credentials.ftp_username AS ftp_username',
                'ftp_credentials.ftp_password AS ftp_password',
                'ftp_credentials.remote_directory AS remote_directory',
                'ftp_credentials.notification_email AS notification_email',
            ])
            .where(subQWhereStr, subQWhereClause);
    } else {
        repository = getRepository(ClientDetails);
        whereClause = {};
        repo_meta_name = "client_details";

        queryBuilder = repository.createQueryBuilder("client_details")
            .innerJoin('client_details.sector', 'sector')
            .leftJoin(
                qb => qb
                    .select([
                        'training_rules.client_id AS client_id',
                        'COUNT(*) AS training_rule_count'
                    ])
                    .from('training_rules', 'training_rules')
                    .groupBy('training_rules.client_id'),
                'training_rules',
                'client_details.id = training_rules.client_id'
            )
            .leftJoin(
                qb => qb
                    .select([
                        'client_yearly_rules.client_id AS client_id',
                        'COUNT(*) AS client_yearly_rules_count'
                    ])
                    .from('client_yearly_rules', 'client_yearly_rules')
                    .groupBy('client_yearly_rules.client_id'),
                'client_yearly_rules',
                'client_details.id = client_yearly_rules.client_id'
            )
            .leftJoin(
                qb => qb
                    .select([
                        'client_financial_rules.client_id AS client_id',
                        'COUNT(*) AS client_financial_rules_count'
                    ])
                    .from('client_financial_rules', 'client_financial_rules')
                    .groupBy('client_financial_rules.client_id'),
                'client_financial_rules',
                'client_details.id = client_financial_rules.client_id'
            )
            .leftJoin('ftp_credentials', 'ftp_credentials', 'ftp_credentials.client_id = client_details.id')
            .select([
                'client_details.id AS client_id',
                'client_details.name AS client_name',
                "client_details.sector_id as sector_id",
                'sector.value as sector_name',
                'client_details.address as address',
                'client_details.post_code as post_code',
                'client_details.city as city',
                'client_details.country as country',
                'client_details.created_at AS created_at',
                'client_details.weekday_start AS weekday_start',
                'client_details.worker_invite_email AS worker_invite_email',
                "client_details.worker_performance AS worker_performance",
                "client_details.worker_training AS worker_training",
                "client_details.rate_card_lookup AS rate_card_lookup",
                'client_details.booking_format AS booking_format',
                'IFNULL(training_rules.training_rule_count, 0) AS training_rule_count',
                'IFNULL(client_yearly_rules.client_yearly_rules_count, 0) AS client_yearly_rules_count',
                'IFNULL(client_financial_rules.client_financial_rules_count, 0) AS client_financial_rules_count',
                'ftp_credentials.id AS ftp_credentials_id',
                'ftp_credentials.ftp_host AS ftp_host',
                'ftp_credentials.ftp_username AS ftp_username',
                'ftp_credentials.ftp_password AS ftp_password',
                'ftp_credentials.remote_directory AS remote_directory',
                'ftp_credentials.notification_email AS notification_email',
            ]);
    }

    // Add pagination and sorting
    queryBuilder
        .orderBy(sortBy, sortType)
        .addOrderBy(`${repo_meta_name}.id`, sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit);

    const [result, count] = await Promise.all([
        queryBuilder.execute(),
        repository.count({ where: whereClause })
    ]);

    // Process results to fetch FTP configurations
    for (let client of result) {
        client = await fetchAndMergeConfigurations(client);
    }

    result["count"] = count;
    return result;
};



const fetchAndMergeConfigurations = async (client) => {
    const ftpConfigurationsRepository = getRepository(FtpConfigurations);

    const ftpConfigurations = await ftpConfigurationsRepository.find({
        where: { ftpCredentialId: client.ftp_credentials_id },
    });

    client.tna_ftp = false;
    client.worker_ftp = false;
    client.tna_cron_expression = null;
    client.worker_cron_expression = null;
    for (const config of ftpConfigurations) {
        if (config.ftpScriptType === 'TNA') {
            client.tna_ftp = true;
            client.tna_cron_expression = config.cronExpression;
        } else if (config.ftpScriptType === 'WORKERS_UPLOAD') {
            client.worker_ftp = true;
            client.worker_cron_expression = config.cronExpression;
        }
    }
    return client;
};



export const getClientsById: any = async (clientId, requiredYearlyRules = true) => {
    const clientDetailsRepository = getRepository(ClientDetails);

    // Fetch only client details and basic FTP credentials
    let rawClientDetails = await clientDetailsRepository.createQueryBuilder('client_details')
        .innerJoin('client_details.sector', 'sector')
        .leftJoin('ftp_credentials', 'ftp_credentials', 'ftp_credentials.client_id = client_details.id')
        .select([
            'client_details.id AS id',
            'client_details.name AS name',
            'client_details.sector_id AS sector_id',
            'sector.value AS sector_name',
            'client_details.address AS address',
            'client_details.post_code AS post_code',
            'client_details.city AS city',
            'client_details.country AS country',
            "date_format(client_details.created_at,'%Y-%m-%d') AS created_at",
            'client_details.resource AS resource',
            'client_details.weekday_start AS weekday_start',
            'client_details.worker_invite_email AS worker_invite_email',
            'client_details.worker_performance AS worker_performance',
            'client_details.worker_training AS worker_training',
            'client_details.rate_card_lookup AS rate_card_lookup',
            'client_details.hide_ratings AS hide_ratings',
            'client_details.booking_format AS booking_format',
            'ftp_credentials.id AS ftp_credentials_id', // For later lookup
            'ftp_credentials.ftp_host AS ftp_host',
            'ftp_credentials.ftp_username AS ftp_username',
            'ftp_credentials.ftp_password AS ftp_password',
            'ftp_credentials.remote_directory AS remote_directory',
            'ftp_credentials.notification_email AS notification_email',
        ])
        .where({ id: clientId })
        .getRawOne();

    if (!rawClientDetails) {
        return null;
    }

    // Fetch and merge FTP configurations
    rawClientDetails = await fetchAndMergeConfigurations(rawClientDetails);

    if (!requiredYearlyRules) {
        return rawClientDetails;
    }

    return rawClientDetails;
};

export const getClientByNames: any = async (names) => {
    const clientDetailsRepository = getRepository(ClientDetails);
    return await clientDetailsRepository.find(
        {
            where: { name: In(names) },
            select: ['id', 'name']
        }
    );
};


export const getClientsByWeekdayStart: any = async (weekdayStart) => {
    const clientDetailsRepository = getRepository(ClientDetails);
    return await clientDetailsRepository.find(
        {
            where: { weekdayStart: weekdayStart },
            select: ['id']
        }
    );
};


export const getClientUsersHelper: any = async (whereClause, whereClauseValue) => {
    return await getRepository(User).createQueryBuilder('user')
        .innerJoin('user.userType_2', 'user_type')
        .leftJoin('user.userSiteAssociations', 'user_site_association')
        .leftJoin('user_site_association.site', 'site')
        .leftJoin('user.messageAdmins', 'message_admin')
        .leftJoin('user.userRegionAssociations', 'userRegionAssociations')
        .leftJoin('userRegionAssociations.region', 'region')
        .select(['user.id as id',
            'user.name as user_name',
            'user.user_type_id as user_type_id',
            'user_type.type as user_type',
            'user_type.name as user_type_name',
            'user.name as name',
            'user.email as email',
            'CASE WHEN user.password is null THEN 0 ELSE 1 END as is_able_access',
            "IFNULL(user.country_code,'') as country_code",
            "IFNULL(user.mobile,'') as mobile",
            "IFNULL(site.id,'') as site_id",
            "IFNULL(site.name,'') as site_name",
            "IFNULL(region.id,'') as region_id",
            "IFNULL(region.name,'') as region_name",
            "IFNULL(message_admin.id,'') as message_admin_id",
        ])
        .where(whereClause, whereClauseValue)
        .getRawMany();
}

export const addClientSiteUser: any = async (payload, loggedInUser) => {
    const userRepository = await getRepository(User).findOne({ select: ['clientId'], where: { id: loggedInUser.user_id } });
    const clientRepository = await getRepository(ClientDetails).findOne({ select: ['name'], where: { id: userRepository.clientId } });
    const clientId = userRepository.clientId;
    let obj = {
        "userTypeId": payload.client_role,
        "clientId": clientId,
        "name": payload.name,
        "email": payload.email,
        "mobile": payload.phone,
        "countryCode": payload.country_code
    }
    let response = await getRepository(User).insert(obj);
    response.generatedMaps[0]['company_name'] = clientRepository.name;
    let client_user_id = response.generatedMaps[0].id;
    await getRepository(UserSiteAssociation).insert({ userId: client_user_id, siteId: payload.id, createdBy: loggedInUser.user_id, updatedBy: loggedInUser.user_id })
    return response.generatedMaps[0]
}

export const addClientRegionUser: any = async (payload, loggedInUser) => {
    const userRepository = await getRepository(User).findOne({ select: ['clientId'], where: { id: loggedInUser.user_id } });
    const clientRepository = await getRepository(ClientDetails).findOne({ select: ['name'], where: { id: userRepository.clientId } });
    const clientId = userRepository.clientId;
    let obj = {
        "userTypeId": payload.client_role,
        "clientId": clientId,
        "name": payload.name,
        "email": payload.email,
        "mobile": payload.phone,
        "countryCode": payload.country_code
    }
    let response = await getRepository(User).insert(obj);
    response.generatedMaps[0]['company_name'] = clientRepository.name;
    let client_user_id = response.generatedMaps[0].id;
    await getRepository(UserRegionAssociation).insert({ userId: client_user_id, regionId: payload.id, createdBy: loggedInUser.user_id, updatedBy: loggedInUser.user_id })
    return response.generatedMaps[0]
}

export const updateClientUserHelper: any = async (client_user_id, payload) => {
    return await getRepository(User).update(client_user_id, payload);
}

export const getClientUsersByIDHelper: any = async (whereClause, whereClauseValue) => {
    const userRepository1 = getRepository(User);
    let response = await userRepository1.createQueryBuilder('user')
        .innerJoin('user.userType_2', 'user_type')
        .innerJoin('user.client', 'client_details')
        .leftJoin('user.userRegionAssociations', 'userRegionAssociations')
        .leftJoin('userRegionAssociations.region', 'region')
        .leftJoin('user.userSiteAssociations', 'user_site_association')
        .leftJoin('user_site_association.site', 'site')
        .select(['user.id as id',
            'user.name as user_name',
            'user.user_type_id as user_type_id',
            'user_type.type as user_type',
            'user_type.name as user_type_name',
            'user.name as name',
            'user.email as email',
            'client_details.name as client_name',
            'CASE WHEN user.password is null THEN 0 ELSE 1 END as is_able_access',
            'user.country_code as country_code',
            "IFNULL(user.mobile,'') as mobile",
            'site.id as site_id',
            'site.name as site_name',
            'region.id as region_id',
            'region.name as region_name'
        ])
        .where(whereClause, whereClauseValue)
        .getRawOne();
    return response;
}

export const removeUserSiteAssociation: any = async (userId, siteId) => {
    return await getRepository(UserSiteAssociation).delete({ userId, siteId })
}

export const generateUserSiteAssociation: any = async (userId: string, siteId: string, loggedInUserId) => {
    return await getRepository(UserSiteAssociation).insert({ userId, siteId, createdBy: loggedInUserId, updatedBy: loggedInUserId })
}

export const getClientUserIdById: any = async (clientId) => {
    return await getRepository(User).createQueryBuilder('user')
        .innerJoin('user.client', 'client')
        .select('user.id')
        .where(`user.client_id = '${clientId}' AND user.user_type_id = '${UserType.CLIENT_ADMIN}'`)
        .getRawOne();
};


export const getAgencyUserIdById: any = async (agencyId) => {
    return await getRepository(User).createQueryBuilder('user')
        .innerJoin('user.agency', 'agency')
        .select('user.id')
        .where(`user.agency_id = '${agencyId}' AND user.user_type_id = '${UserType.AGENCY_ADMIN}'`)
        .getRawOne();
};

export const addClientMessageUser: any = async (payload, loggedInUser) => {
    const userRepository = await getRepository(User).findOne({ select: ['clientId'], where: { id: loggedInUser.user_id } });
    const clientRepository = await getRepository(ClientDetails).findOne({ select: ['name'], where: { id: userRepository.clientId } });
    const clientId = userRepository.clientId;
    let obj = {
        "userTypeId": payload.client_role,
        "clientId": clientId,
        "name": payload.name,
        "email": payload.email,
        "mobile": payload.phone,
        "countryCode": payload.country_code,
        "createdBy": loggedInUser.user_id,
        "updatedBy": loggedInUser.user_id
    }
    let response = await getRepository(User).insert(obj);
    response.generatedMaps[0]['company_name'] = clientRepository.name;
    let client_user_id = response.generatedMaps[0].id;
    await getRepository(MessageAdmin).insert({ userId: client_user_id, clientId: clientId, createdBy: loggedInUser.user_id, updatedBy: loggedInUser.user_id })
    return response.generatedMaps[0]
}

export const removeUserRegionAssociation: any = async (userId, regionId) => {
    return await getRepository(UserRegionAssociation).delete({ userId, regionId })
}

export const generateUserRegionAssociation: any = async (userId: string, regionId: string, loggedInUserId) => {
    return await getRepository(UserRegionAssociation).insert({ userId, regionId, createdBy: loggedInUserId, updatedBy: loggedInUserId })
}

export const updateworkerInviteEmail: any = async (id, body) => {
    const clientDetailsRepository = getRepository(ClientDetails);
    body.updatedAt = new Date();
    return await clientDetailsRepository.update({ id }, body);
};

export const addNewTrainingRuleHelper: any = async (data) => {
    const trainingRulesRepository = getRepository(TrainingRules);
    let response = await trainingRulesRepository.insert({
        clientId: data.client_id,
        regionId: data.region_id,
        siteId: data.site_id,
        thresholdWeek: data.threshold_week,
        evaluationWeek: data.evaluation_week,
        limitedHoursThresholdWeek: data.limited_hours_threshold_week || null,
        limitedHoursEvaluationWeek: data.limited_hours_evaluation_week || null,
        maxTrainingHours: data.max_training_hours,
        performanceThreshold: data.performance_threshold,
        creditRate: data.credit_rate,
        createdBy: data.user_id,
        updatedBy: data.user_id,
    });
    return response.generatedMaps[0];
};

export const fetchTrainingRules: any = async (params) => {
    let whereCondition = `trainingRules.clientId = :client_id`;
    whereCondition += params.region_id ? ` AND trainingRules.regionId = :region_id` : "";
    whereCondition += params.site_id ? ` AND trainingRules.siteId = :site_id` : "";

    let whereClauseValue = { "client_id": params.client_id, "region_id": params.region_id, "site_id": params.site_id }

    const trainingRulesRepository = getRepository(TrainingRules);
    const queryBuilder = trainingRulesRepository.createQueryBuilder('trainingRules');

    queryBuilder.innerJoin('trainingRules.site', 'site')

    queryBuilder.select([
        'trainingRules.id as id',
        'trainingRules.clientId as client_id',
        'trainingRules.regionId as region_id',
        'trainingRules.siteId as site_id',
        'site.name as site_name',
        'trainingRules.thresholdWeek as threshold_week',
        'trainingRules.evaluationWeek as evaluation_week',
        'trainingRules.limitedHoursThresholdWeek as limited_hours_threshold_week',
        'trainingRules.limitedHoursEvaluationWeek as limited_hours_evaluation_week',
        'trainingRules.maxTrainingHours as max_training_hours',
        'trainingRules.performanceThreshold as performance_threshold',
        'trainingRules.creditRate as creditRate',
    ]);

    queryBuilder.where(whereCondition, whereClauseValue);

    return await queryBuilder.getRawMany();
};


export const findRegionSiteShiftDepartmentComboExist: any = async (
    client_id: number,
    region_id: number,
    site_id: number,
    shift_id: number,
    department_id: number,
) => {

    let whereCondition = `client_details.id = :client_id`;
    whereCondition += region_id ? ` AND regions.id = :region_id` : "";
    whereCondition += site_id ? ` AND sites.id = :site_id` : "";
    whereCondition += shift_id ? ` AND shift_site_associations.shift_id = :shift_id` : "";
    whereCondition += department_id ? ` AND department_site_associations.department_id = :department_id` : "";
    let whereClauseValue = { "client_id": client_id, "region_id": region_id, "site_id": site_id, "shift_id": shift_id, "department_id": department_id }

    const query = getRepository(ClientDetails).createQueryBuilder('client_details')
        .innerJoin('client_details.regions', 'regions')
        .innerJoin('regions.sites', 'sites')
        .leftJoin('sites.shiftSiteAssociations', 'shift_site_associations')
        .leftJoin('sites.departmentSiteAssociations', 'department_site_associations')
        .where(whereCondition, whereClauseValue)

    return await query.getRawMany();
}

export const getTrainingRuleById: any = async (trainingRuleId, clientId) => {
    const clientDetailsRepository = getRepository(TrainingRules);
    return await clientDetailsRepository.find(
        {
            where: { id: trainingRuleId, clientId: clientId }
        }
    );
};

export const updateTrainingRuleById: any = async (trainingRuleId, data) => {
    const trainingRuleRepository = getRepository(TrainingRules);
    let body = {
        clientId: data.client_id,
        regionId: data.region_id,
        siteId: data.site_id,
        thresholdWeek: data.threshold_week,
        evaluationWeek: data.evaluation_week,
        limitedHoursThresholdWeek: data.limited_hours_threshold_week || null,
        limitedHoursEvaluationWeek: data.limited_hours_evaluation_week || null,
        maxTrainingHours: data.max_training_hours,
        performanceThreshold: data.performance_threshold,
        creditRate: data.credit_rate,
        updatedBy: data.user_id,
        updatedAt: new Date()
    }
    return await trainingRuleRepository.update({ id: trainingRuleId }, body);
};

export const removeTrainingRuleById: any = async (trainingRuleId, clientId) => {
    return await getRepository(TrainingRules).delete({ id: trainingRuleId, clientId: clientId })
}

export const addFtpConfig: any = async (data, clientId) => {
    const ftpCredentialsRepository = getRepository(FtpCredentials);
    const ftpConfigurationsRepository = getRepository(FtpConfigurations);

    // Insert into ftp_credentials
    const credentialResponse = await ftpCredentialsRepository.insert({
        clientId: clientId,
        ftpHost: data.ftp_host,
        ftpPort: data.ftp_port || 22, // Default port 21 if not provided
        ftpUsername: data.ftp_username,
        ftpPassword: data.ftp_password,
        remoteDirectory: data.remote_directory,
        notificationEmail: data.notification_email,
        createdBy: data.user_id,
        updatedBy: data.user_id
    });

    const ftpCredentialId = credentialResponse.generatedMaps[0]?.id;

    // Insert into ftp_configurations
    let configurations = [];
    if (data.tna_ftp) {
        configurations.push({
            clientId: clientId,
            ftpScriptType: 'TNA',
            ftpCredentialId: ftpCredentialId,
            queueUrl: data.tna_queue_url, // Assuming the TNA queue URL is part of data
            cronExpression: data.tna_cron_expression,
            createdBy: data.user_id,
            updatedBy: data.user_id
        });
    }

    if (data.worker_ftp) {
        configurations.push({
            clientId: clientId,
            ftpScriptType: 'WORKERS_UPLOAD',
            ftpCredentialId: ftpCredentialId,
            queueUrl: data.worker_queue_url, // Assuming the worker queue URL is part of data
            cronExpression: data.worker_cron_expression,
            createdBy: data.user_id,
            updatedBy: data.user_id
        });
    }

    if (configurations.length) {
        await ftpConfigurationsRepository.insert(configurations);
    }

    return { ftpCredentialId, configurations };
};


export const removeFtpConfigByClientId: any = async (clientId) => {
    const ftpCredentialsRepository = getRepository(FtpCredentials);
    const ftpConfigurationsRepository = getRepository(FtpConfigurations);

    // Delete from ftp_configurations first (dependent table)
    await ftpConfigurationsRepository.delete({ clientId: clientId });

    // Then delete from ftp_credentials
    return await ftpCredentialsRepository.delete({ clientId: clientId });
};