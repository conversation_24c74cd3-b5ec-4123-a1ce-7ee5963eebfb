import { getRepository } from 'typeorm';
import { PensionStatusLog } from '../';

/**
 * create pension status log
 */
export const createPensionStatusLog: any = async (body) => {
    const pensionStatusLogRepository = getRepository(PensionStatusLog);
    await pensionStatusLogRepository.save(body);
};

export const createPensionStatusLogTransaction = async (transactionalEntityManager, body) => {
    const pensionStatusLogRepository = transactionalEntityManager.getRepository(PensionStatusLog);
    await pensionStatusLogRepository.save(body);
};
