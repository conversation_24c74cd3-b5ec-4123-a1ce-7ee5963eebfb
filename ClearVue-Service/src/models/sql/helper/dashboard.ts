const moment = require('moment');
import { getRepository, In, getManager, getConnection } from 'typeorm';
import { Workers, BookingAssociation, TimeAndAttendanceData, Payroll } from "../";
import { databaseSeparator, ComplinaceCardsType, PayTypes, dateTimeFormates } from '../../../common';
import { addTrendsAnalysisRegion, extractValuesFromString } from '../../../utils';
import { config } from '../../../configurations';
import { ComplianceApproval } from '../entities/ComplianceApproval';

/**
 * Get worker demographics details with grouping with nationality/gender/post_code
 */
export const getWorkerDemographicsDetails: any = async (requestArgs, whereClauseString, whereClauseValue, groupBy = "workers.nationality") => {
    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select(`COUNT(DISTINCT(workers.id)) AS value`)
            .addSelect(`${groupBy} AS label`)
            .groupBy(`label`)
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select(`COUNT(DISTINCT(workers.id)) AS value`)
            .addSelect(`${groupBy} AS label`)
            .groupBy(`label`)
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select(`COUNT(DISTINCT(workers.id)) AS value`)
            .addSelect(`${groupBy} AS label`)
            .groupBy(`label`)
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select(`COUNT(DISTINCT(workers.id)) AS value`)
            .addSelect(`${groupBy} AS label`)
            .groupBy(`label`)
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    }

    return response;
};


/**
 * Get workers start date & inactivated date to calculate legth of services
 */
export const getStartAndInactivatedDateForTheWorkers: any = async (requestArgs, whereClauseString: string, whereClauseValue: any) => {

    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    }

    return response;
};


/**
 * Get agency wise worker demographics details with grouping with nationality
 */
export const getAgencyWiseWorkerDemographicsDetails: any = async (requestArgs, whereClauseString, whereClauseValue) => {

    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .innerJoin("workers.agency", "agency_details")
            .select('COUNT(DISTINCT(workers.id)) AS value')
            .addSelect('workers.nationality AS nationality')
            .addSelect("CONCAT(agency_details.id , '" + databaseSeparator + "', agency_details.name) AS agency_detail")
            .groupBy('workers.nationality')
            .addGroupBy('agency_details.id')
            .orderBy('agency_details.id')
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("workers.agency", "agency_details")
            .select('COUNT(DISTINCT(workers.id)) AS value')
            .addSelect('workers.nationality AS nationality')
            .addSelect("CONCAT(agency_details.id , '" + databaseSeparator + "', agency_details.name) AS agency_detail")
            .groupBy('workers.nationality')
            .addGroupBy('agency_details.id')
            .orderBy('agency_details.id')
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("workers.agency", "agency_details")
            .select('COUNT(DISTINCT(workers.id)) AS value')
            .addSelect('workers.nationality AS nationality')
            .addSelect("CONCAT(agency_details.id , '" + databaseSeparator + "', agency_details.name) AS agency_detail")
            .groupBy('workers.nationality')
            .addGroupBy('agency_details.id')
            .orderBy('agency_details.id')
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.agency", "agency_details")
            .select('COUNT(DISTINCT(workers.id)) AS value')
            .addSelect('workers.nationality AS nationality')
            .addSelect("CONCAT(agency_details.id , '" + databaseSeparator + "', agency_details.name) AS agency_detail")
            .groupBy('workers.nationality')
            .addGroupBy('agency_details.id')
            .orderBy('agency_details.id')
            .where(whereClauseString, whereClauseValue)
            .getRawMany();
    }
    return response;
};


/**
 * Get agency wise workers start date & inactivated date to calculate legth of services
 */
export const getStartAndInactivatedDateForTheAgencyWiseWorkers: any = async (requestArgs, whereClauseString: string, whereClauseValue: any) => {
    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .addSelect("workers.agency_id AS worker_agency_id")
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .orderBy('workers.agency_id')
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .addSelect("workers.agency_id AS worker_agency_id")
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .orderBy('workers.agency_id')
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .addSelect("workers.agency_id AS worker_agency_id")
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .orderBy('workers.agency_id')
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('workers.start_date')
            .addSelect('workers.in_actived_at')
            .addSelect("workers.agency_id AS worker_agency_id")
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .orderBy('workers.agency_id')
            .getRawMany();
    }

    return response;
};


/**
 * Get shift fulfillment from the the booking association
 */
export const getShiftFulfillmentFromBookingAssociation: any = async (whereClauseString: string, whereClauseValue: any, startDate: string, endDate: string) => {

    return getRepository(BookingAssociation).createQueryBuilder("booking_association")
        .innerJoin("booking_association.booking", "booking")
        .select('booking_association.agency_id')
        .addSelect("IFNULL(booking_association.requested_total, 0) AS requested_total")
        .addSelect("IFNULL(booking_association.fulfilled_total, 0) AS fulfilled_total")
        .where(whereClauseString, whereClauseValue)
        .andWhere(`booking.start_date >= :start_date`, { "start_date": startDate })
        .andWhere(`booking.end_date <= :end_date`, { "end_date": endDate })
        .getRawMany();
};


/**
 * Get shift utilisation from the the T&A Data
 */
export const getShiftUtilisationDetailsModel: any = async (startDate: string, endDate: string, whereClause: string, whereClauseValue: any) => {
    let [whereClauseString, params] = extractValuesFromString(whereClause, whereClauseValue);
    return getManager().query(`
    SELECT 
        COUNT(worker_id) AS worker_counts, avg_days
    FROM
        (SELECT 
            worker_id, CAST(AVG(no_of_days) AS DECIMAL (0)) AS avg_days
        FROM
            (SELECT 
                time_and_attendance_data.worker_id,
                time_and_attendance_data.start_date,
                (IF(SUM(day_1) > 0, 1, 0) + IF(SUM(day_2) > 0, 1, 0) + IF(SUM(day_3) > 0, 1, 0) + IF(SUM(day_4) > 0, 1, 0) + IF(SUM(day_5) > 0, 1, 0) + IF(SUM(day_6) > 0, 1, 0) + IF(SUM(day_7) > 0, 1, 0)) AS no_of_days
        FROM
            time_and_attendance_data
        LEFT JOIN workers ON time_and_attendance_data.worker_id = workers.id
        WHERE
            ${whereClauseString} AND time_and_attendance_data.start_date >= ? AND time_and_attendance_data.end_date <= ? AND workers.type = ?
        GROUP BY worker_id , start_date) AS temp_table
        GROUP BY worker_id) AS temp_table_2
    GROUP BY avg_days;
    `, [...params, startDate, endDate, config.TEMPORARY_WORKER]);
};

/**
 * Get total spend broken down by agency.
 */
export const getActivityTotalSpendByAgencyHelper: any = async (startDate: string, endDate: string, whereClauseString: string, whereClauseValue: any) => {
    return await getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin("time_and_attendance_data.worker", "workers")
        .select(['time_and_attendance_data.agency_id as label', 'CAST(IFNULL(SUM(time_and_attendance_data.total_charge), 0) AS DOUBLE) as count'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`time_and_attendance_data.start_date >= :start_date`, { "start_date": startDate })
        .andWhere(`time_and_attendance_data.end_date <= :end_date`, { "end_date": endDate })
        .addGroupBy(`time_and_attendance_data.agency_id`)
        .getRawMany();
}

/**
 * Get workers working hours from the T&A data
 */
export const getWorkersWorkingHours: any = async (startDate: string, endDate: string, whereClauseString: string, whereClauseValue: any) => {
    return await getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin("time_and_attendance_data.worker", "workers")
        .select("time_and_attendance_data.agency_id, CAST(IFNULL(sum(weekly_hours), 0) AS DOUBLE) as weekly_hours")
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`time_and_attendance_data.start_date >= :start_date`, { "start_date": startDate })
        .andWhere(`time_and_attendance_data.end_date <= :end_date`, { "end_date": endDate })
        .andWhere(`time_and_attendance_data.pay_type NOT IN (:...pay_types)`, { "pay_types": [PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES] })
        .groupBy('time_and_attendance_data.agency_id')
        .getRawMany();
};


/**
 * Get workers day wise shift utilisation details from the T&A data
 */
export const getWorkersDayWiseShiftUtilisationDetails: any = async (startDate: string, endDate: string, whereClause: string, whereClauseValue: any) => {
    let [whereClauseString, params] = extractValuesFromString(whereClause, whereClauseValue);
    return getManager().query(`
        SELECT 
            COUNT(worker_id) AS worker_counts, avg_days, agency_id
        FROM
            (SELECT 
                worker_id, CAST(AVG(no_of_days) AS DECIMAL (0)) AS avg_days, agency_id
            FROM
                (SELECT 
                    time_and_attendance_data.worker_id,
                    time_and_attendance_data.start_date,
                    (IF(SUM(day_1) > 0, 1, 0) + IF(SUM(day_2) > 0, 1, 0) + IF(SUM(day_3) > 0, 1, 0) + IF(SUM(day_4) > 0, 1, 0) + IF(SUM(day_5) > 0, 1, 0) + IF(SUM(day_6) > 0, 1, 0) + IF(SUM(day_7) > 0, 1, 0)) AS no_of_days,
                    time_and_attendance_data.agency_id
            FROM
                time_and_attendance_data
            LEFT JOIN workers ON time_and_attendance_data.worker_id = workers.id
            WHERE
                ${whereClauseString} AND time_and_attendance_data.start_date >= ? AND time_and_attendance_data.end_date <= ? AND workers.type = ?
                GROUP BY worker_id , start_date , agency_id) AS temp_table
                GROUP BY worker_id , agency_id) AS temp_table_2
            GROUP BY avg_days , agency_id;
    `, [...params, startDate, endDate, config.TEMPORARY_WORKER]);

};


/**
 * Get workers count as per agencies
 */
export const getTotalWorkers: any = async (requestArgs, whereClauseString, whereClauseValue) => {

    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawMany();
    }
    return response;
};

/**
 * Get workers for leavers calculation
 */
export const getWorkersLeaversDetails: any = async (requestArgs, whereClauseString, whereClauseValue, startDate, endDate) => {

    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawMany();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawMany();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawMany();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .addSelect('workers.agency_id AS agency_id')
            .groupBy('workers.agency_id')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawMany();
    }
    return response;
};


/**
 * Get new started workers count and inactivated workers count for a given date range for new starter retention details
 * @param  {string} whereClauseString
 * @param  {any} whereClauseValue
 * @param  {string} startDate
 * @param  {string} endDate
 */
export const getNewStarterRetentionData: any = async (requestArgs: any, whereClauseString: string, whereClauseValue: any, startDate: string, endDate: string) => {

    let inactiveWorkerQuery: any = getWorkersQuery(requestArgs, "COUNT(workers.id) AS inactive_workers_count");
    let activeWorkerQuery: any = getWorkersQuery(requestArgs, "COUNT(workers.id) AS active_workers_count");

    let inactiveWorkersCount: any = await inactiveWorkerQuery.where(whereClauseString, whereClauseValue)
        .andWhere(`workers.assignment_date >= DATE_SUB(DATE(:start_date), INTERVAL :interval_days DAY) AND workers.assignment_date <= DATE_SUB(DATE(:end_date), INTERVAL :interval_days DAY)`, { "start_date": startDate, "end_date": endDate, "interval_days": config.NEW_STARTER_RETENTION_INTERVAL_DAYS })
        .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
        .getRawOne();

    let totalActivatedWorkersCount: any = await activeWorkerQuery.where(whereClauseString, whereClauseValue)
        .andWhere(`workers.assignment_date >= DATE_SUB(DATE(:start_date), INTERVAL :interval_days DAY) AND workers.assignment_date <= DATE_SUB(DATE(:end_date), INTERVAL :interval_days DAY)`, { "start_date": startDate, "end_date": endDate, "interval_days": config.NEW_STARTER_RETENTION_INTERVAL_DAYS })
        .getRawOne();

    return [inactiveWorkersCount.inactive_workers_count, totalActivatedWorkersCount.active_workers_count]
}


/**
 * Get new started workers count and inactivated workers count for a given date range for new starter retention details agency wise.
 * @param  {string} whereClauseString
 * @param  {any} whereClauseValue
 * @param  {string} startDate
 * @param  {string} endDate
 */
export const getAgencyWiseNewStarterRetentionData: any = async (requestArgs: any, whereClauseString: string, whereClauseValue: any, startDate: string, endDate: string) => {

    let inactiveWorkerQuery: any = getWorkersQuery(requestArgs, "workers.agency_id AS agency_id, COUNT(workers.id) AS inactive_workers_count");
    let activeWorkerQuery: any = getWorkersQuery(requestArgs, "workers.agency_id AS agency_id, COUNT(workers.id) AS active_workers_count");

    let inactiveWorkersCount: any = await inactiveWorkerQuery.where(whereClauseString, whereClauseValue)
        .andWhere(`workers.assignment_date >= DATE_SUB(DATE(:start_date), INTERVAL :interval_days DAY) AND workers.assignment_date <= DATE_SUB(DATE(:end_date), INTERVAL :interval_days DAY)`, { "start_date": startDate, "end_date": endDate, "interval_days": config.NEW_STARTER_RETENTION_INTERVAL_DAYS })
        .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .groupBy('workers.agency_id')
        .getRawMany();

    let totalActivatedWorkersCount: any = await activeWorkerQuery.where(whereClauseString, whereClauseValue)
        .andWhere(`workers.assignment_date >= DATE_SUB(DATE(:start_date), INTERVAL :interval_days DAY) AND workers.assignment_date <= DATE_SUB(DATE(:end_date), INTERVAL :interval_days DAY)`, { "start_date": startDate, "end_date": endDate, "interval_days": config.NEW_STARTER_RETENTION_INTERVAL_DAYS })
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .groupBy('workers.agency_id')
        .getRawMany();

    return [inactiveWorkersCount, totalActivatedWorkersCount]
}


/**
 * Get date wise inactivated workers count as per agencies
 */
export const getInactivatedWorkersPerAgencyByStartDate: any = async (requestArgs, whereClauseString: any, whereClauseValue: any, startDate: string, endDate: string) => {

    let inactiveWorkerQuery: any = getWorkersQuery(requestArgs, "COUNT(workers.id) AS inactive_workers_count, workers.agency_id AS agency_id");

    let inactiveWorkers: any = await inactiveWorkerQuery.where(whereClauseString, whereClauseValue)
        .andWhere(`workers.assignment_date >= DATE_SUB(DATE(:start_date), INTERVAL :interval_days DAY) AND workers.assignment_date <= DATE_SUB(DATE(:end_date), INTERVAL :interval_days DAY)`, { "start_date": startDate, "end_date": endDate, "interval_days": config.NEW_STARTER_RETENTION_INTERVAL_DAYS })
        .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .groupBy('workers.agency_id')
        .getRawMany();

    return inactiveWorkers;
};


/**
 * Get workers leavers count as per date range
 */
export const getWorkersLeaversCountByDateRange: any = async (requestArgs: any, whereClauseString: string, whereClauseValue: any, startDate: string, endDate: string) => {

    const workersRepository = getRepository(Workers);

    let response;

    if ((requestArgs.region_id)) {
        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawOne();
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawOne();
    } else if (requestArgs.shift_id) {
        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawOne();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS workers_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .andWhere(`workers.in_actived_at >= :start_date AND workers.in_actived_at <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .getRawOne();
    }
    return response['workers_count'] || 0;
};


export const getWorkForcePoolUtilizationTotalWorkers: any = async (requestArgs, whereClauseString: string, whereClauseValue: any, isForActiveWorkers = 1, startDate = "", endDate = "") => {
    const workersRepository = getRepository(Workers);
    let response;

    if (isForActiveWorkers) {
        whereClauseString += `AND ((workers.in_actived_at is null AND workers.is_active = 1) OR workers.in_actived_at > :start_date) AND workers.assignment_date <= :end_date`
        whereClauseValue = { ...whereClauseValue, "start_date": startDate, "end_date": endDate }
    }

    if ((requestArgs.region_id)) {

        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS total_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawOne();

    } else if (requestArgs.site_id || requestArgs.department_id) {

        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS total_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawOne();

    } else if (requestArgs.shift_id) {

        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .innerJoin("workers.job", "job")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS total_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawOne();
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        response = await workersRepository.createQueryBuilder("workers")
            .select('IFNULL(COUNT(DISTINCT(workers.id)), 0) AS total_count')
            .where(whereClauseString, whereClauseValue)
            .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
            .getRawOne();
    }

    return response;
};

export const getWorkForcePoolUtilizationActiveWorkers: any = async (whereClauseString: string, whereClauseValue: any, startDate, endDate) => {

    return await getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .select("IFNULL(COUNT(DISTINCT(time_and_attendance_data.worker_id)), 0) AS active_workers")
        .where(whereClauseString, whereClauseValue)
        .andWhere(`time_and_attendance_data.start_date >= :start_date and time_and_attendance_data.end_date <= :end_date`,
            { "start_date": startDate, "end_date": endDate }
        )
        .getRawOne();

}

export const getPoolUtilizationInactiveWorkers: any = async (whereClauseString: string, whereClauseValue: any, startDate, endDate) => {

    return await getRepository(Workers).createQueryBuilder("workers")
        .innerJoin("workers.timeAndAttendanceData", "time_and_attendance_data")
        .select("IFNULL(COUNT(DISTINCT(workers.id)), 0) AS inactive_workers")
        .addSelect("time_and_attendance_data")
        .where(whereClauseString, whereClauseValue)
        .andWhere(`time_and_attendance_data.start_date >= :start_date and time_and_attendance_data.end_date <= :end_date`, { "start_date": startDate, "end_date": endDate })
        .getRawOne();

}


export const getHeaderCumulativeClearVueSavings: any = async (requestArgs, whereClauseString: string, whereClauseValue: any, matchedRule = null) => {
    const payrollRepository = getRepository(Payroll);

    // Initialize query builder
    let query = payrollRepository.createQueryBuilder("payroll")
        .innerJoin("payroll.worker", "workers")
        .select(['ROUND(IFNULL(SUM(payroll.clearvue_savings),0),2) as cumulative_savings'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER });

    // For filters: For region filtered dashboard
    if (requestArgs.region_id) {
        query = query.innerJoin("payroll.site", "site");

        if (requestArgs.region_id && requestArgs.department_id) {
            query = query
                .innerJoin("workers.job", "job")
                .innerJoin("job.jobAssociations", "job_association");
        } else if (requestArgs.region_id && requestArgs.shift_id) {
            query = query
                .innerJoin("workers.job", "job");
        }
    }
    // For filters: For site and department filters
    else if (requestArgs.site_id || requestArgs.department_id) {
        query = query
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association");
    }
    // For filters: For shift filter
    else if (requestArgs.shift_id) {
        query = query
            .innerJoin("workers.job", "job");
    }

    // Check if matchedRule exists and add date filter
    if (matchedRule) {
        query = query.andWhere(`payroll.start_date >= :start_date and payroll.end_date <= :end_date`, {
            "start_date": matchedRule["start_date"],
            "end_date": matchedRule["end_date"]
        });
    }
    // Execute query
    return await query.getRawOne();
}

export const getPreviousWeekClearVueSavings: any = async (requestArgs, whereClauseString: string, whereClauseValue: any) => {
    const payrollRepository = getRepository(Payroll);
    /*  For filters: For region filtered dashboard*/

    let startDateQuery = payrollRepository.createQueryBuilder("payroll")
        .select("payroll.start_date")
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .orderBy("payroll.start_date", "DESC")

    // Select last uploaded data start date
    if (requestArgs.region_id) {

        if (requestArgs.region_id && requestArgs.department_id) {

            startDateQuery.innerJoin("payroll.site", "site")
                .innerJoin("payroll.worker", "workers")
                .innerJoin("workers.job", "job")
                .innerJoin("job.jobAssociations", "job_association")
        }

        else if (requestArgs.region_id && requestArgs.shift_id) {

            startDateQuery.innerJoin("payroll.site", "site")
                .innerJoin("payroll.worker", "workers")
                .innerJoin("workers.job", "job")
        }
        else {
            startDateQuery.innerJoin("payroll.site", "site").innerJoin("payroll.worker", "workers")
        }

    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For filters: For site and department filters*/

        startDateQuery.innerJoin("payroll.worker", "workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")

    } else if (requestArgs.shift_id) {
        /*  For filters: For shift filter*/

        startDateQuery.innerJoin("payroll.worker", "workers")
            .innerJoin("workers.job", "job")
    } else if (requestArgs.client_id) {
        startDateQuery.innerJoin("payroll.worker", "workers")
    }

    let startDate = await startDateQuery.getRawOne();

    if (!startDate) { // Return for not finding the last uploaded record
        return {
            startDate: "",
            dbResponse: { "clearvue_savings": 0 }
        }
    }

    let query = payrollRepository.createQueryBuilder("payroll")
        .select(['ROUND(IFNULL(SUM(payroll.clearvue_savings),0),2) as clearvue_savings'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`payroll.start_date >= :date_1 AND payroll.end_date<= DATE(DATE_ADD( :date_2,  INTERVAL 7 DAY))`,
            { "date_1": moment(startDate.start_date).utc().format(dateTimeFormates.YYYYMMDD), "date_2": moment(startDate.start_date).utc().format("YYYY-MM_DD") }
        )

    if (requestArgs.region_id) {

        if (requestArgs.region_id && requestArgs.department_id) {

            query.innerJoin("payroll.site", "site")
                .innerJoin("payroll.worker", "workers")
                .innerJoin("workers.job", "job")
                .innerJoin("job.jobAssociations", "job_association")
        } else if (requestArgs.region_id && requestArgs.shift_id) {

            query.innerJoin("payroll.site", "site")
                .innerJoin("payroll.worker", "workers")
                .innerJoin("workers.job", "job")
        } else {
            query.innerJoin("payroll.site", "site").innerJoin("payroll.worker", "workers")
        }
    } else if (requestArgs.site_id || requestArgs.department_id) {
        /*  For filters: For site and department filters*/

        query.innerJoin("payroll.worker", "workers")
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")

    } else if (requestArgs.shift_id) {
        /*  For filters: For shift filter*/

        query.innerJoin("payroll.worker", "workers")
            .innerJoin("workers.job", "job")

    } else if (requestArgs.client_id) {
        query.innerJoin("payroll.worker", "workers")
    }

    return {
        startDate: startDate.start_date,
        dbResponse: await query.getRawOne()
    };
}

export const getWorkersTotalWorkingHours: any = async (whereClauseString, whereClauseValue, start_date, end_date) => {
    return await getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin('time_and_attendance_data.worker', 'worker')
        .select(['IFNULL(SUM(time_and_attendance_data.weekly_hours),0) as working_hours'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`worker.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`time_and_attendance_data.start_date >= :start_date and time_and_attendance_data.end_date <= :end_date`, { "start_date": start_date, "end_date": end_date })
        .andWhere(`time_and_attendance_data.pay_type NOT IN (:...pay_types)`, { "pay_types": [PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES] })
        .getRawOne()
}

export const getWorkersCountForAverageWorkingHours: any = async (whereClauseString, whereClauseValue, start_date, end_date) => {
    const TimeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);
    let response = await TimeAndAttendanceDataRepository.createQueryBuilder("time_and_attendance_data")
        .innerJoin('time_and_attendance_data.worker', 'worker')
        .select(['IFNULL(COUNT(DISTINCT(time_and_attendance_data.worker_id)),0) as count'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`worker.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`time_and_attendance_data.start_date >= :start_date and time_and_attendance_data.end_date <= :end_date`, { "start_date": start_date, "end_date": end_date })
        .groupBy("time_and_attendance_data.start_date")
        .getRawMany()
    if (response.length) {
        return response.reduce((total, currentValue) => {
            return parseInt(total) + parseInt(currentValue.count);
        }, 0);
    }
    return 0;

}
/**
 * Get T&A data available workers
 */
export const getTADataAvailableWorkers: any = async (whereClauseString: string, whereClauseValue: any, startDate: string, endDate: string) => {

    return await getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin("time_and_attendance_data.worker", "workers")
        .select("IFNULL(COUNT(DISTINCT(time_and_attendance_data.worker_id)), 0) AS active_workers")
        .addSelect('time_and_attendance_data.agency_id')
        .where(whereClauseString, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .andWhere(`time_and_attendance_data.start_date >= :start_date`, { "start_date": startDate })
        .andWhere(`time_and_attendance_data.end_date <= :end_date`, { "end_date": endDate })
        .groupBy('time_and_attendance_data.agency_id')
        .getRawMany();
};

/**
 * Get total spend group by weeks
 * @param  {string} start_date
 * @param  {string} end_date
 * @param  {string} whereClauseString
 */
export const getTotalSpendTrendsAnalytics: any = async (start_date: string, end_date: string, whereClauseString: string, whereClauseValue: any, region_id = null, client_id) => {
    let selectQuery = getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin("time_and_attendance_data.worker", "worker")
        .select(['SUM(time_and_attendance_data.total_charge) as charges'])
        .addSelect(['pay_type', 'time_and_attendance_data.start_date', 'time_and_attendance_data.end_date'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`time_and_attendance_data.start_date >= :start_date`, { "start_date": start_date })
        .andWhere(`time_and_attendance_data.end_date <= :end_date`, { "end_date": end_date })
        .groupBy('time_and_attendance_data.start_date')
        .addGroupBy('pay_type')
    selectQuery = await addTrendsAnalysisRegion(selectQuery, region_id, client_id);
    return await selectQuery.getRawMany()
}

/**
 * Get total hours group by weeks
 * @param  {string} start_date
 * @param  {string} end_date
 * @param  {string} whereClauseString
 */
export const getTotalHoursTrendsAnalytics: any = async (start_date: string, end_date: string, whereClauseString: string, whereClauseValue: any, region_id = null, client_id) => {
    let selectQuery = getRepository(TimeAndAttendanceData).createQueryBuilder("time_and_attendance_data")
        .innerJoin("time_and_attendance_data.worker", "worker")
        .select(['SUM(time_and_attendance_data.weekly_hours) as hours'])
        .addSelect(['pay_type', 'time_and_attendance_data.start_date', 'time_and_attendance_data.end_date'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`time_and_attendance_data.start_date >= :start_date`, { "start_date": start_date })
        .andWhere(`time_and_attendance_data.end_date <= :end_date`, { "end_date": end_date })
        .andWhere(`time_and_attendance_data.pay_type NOT IN (:...pay_types)`, { "pay_types": [PayTypes.EXPENSES] })
        .groupBy('time_and_attendance_data.start_date')
        .addGroupBy('pay_type')

    selectQuery = await addTrendsAnalysisRegion(selectQuery, region_id, client_id);
    return await selectQuery.getRawMany()
}

/**
 * Get total heads group by weeks
 * @param  {string} start_date
 * @param  {string} end_date
 * @param  {string} whereClauseString
 * @param  {any} whereClauseValue
 */
export const getTotalHeadsTrendsAnalytics: any = async (start_date: string, end_date: string, whereClauseString: string, whereClauseValue: any, region_id = null, client_id) => {
    let selectQuery = getRepository(BookingAssociation).createQueryBuilder("booking_association")
        .innerJoin("booking_association.booking", "booking")
        .select(['SUM(booking_association.fulfilled_total) as heads', 'start_date', 'end_date'])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`booking.start_date >= :start_date`, { "start_date": start_date })
        .andWhere(`booking.end_date <= :end_date`, { "end_date": end_date })
        .groupBy('start_date')
    selectQuery = await addTrendsAnalysisRegion(selectQuery, region_id, client_id);
    return await selectQuery.getRawMany()
}

/**
 * Get total leavers group by weeks
 * @param  {string} start_date
 * @param  {string} end_date
 * @param  {string} whereClauseString
 * @param  {any} whereClauseValue
 */
export const getTotalLeaversTrendsAnalytics: any = async (start_date: string, end_date: string, whereClauseString: string, whereClauseValue: any, region_id = null, client_id) => {
    let selectQuery = getRepository(Workers).createQueryBuilder("workers")
        .select(['COUNT(DISTINCT(workers.id)) as total', "date_format(workers.in_actived_at,'%Y-%m-%d') as in_actived_at"])
        .where(whereClauseString, whereClauseValue)
        .andWhere(`in_actived_at >= :start_date`, { "start_date": start_date })
        .andWhere(`in_actived_at <= :end_date`, { "end_date": end_date })
        .groupBy('CAST(in_actived_at AS DATE)')
    selectQuery = await addTrendsAnalysisRegion(selectQuery, region_id, client_id);
    if (whereClauseString.includes('site_id') || region_id) {
        selectQuery = selectQuery.innerJoin('workers.timeAndAttendanceData', 'timeAndAttendanceData');
    }
    return await selectQuery.getRawMany()
}


/**\
 * Get workers query as per provided filters
 * @param  {} requestArgs
 */
const getWorkersQuery = (requestArgs: any, selectQuery: string) => {
    let query;
    const workersRepository = getRepository(Workers);

    if ((requestArgs.region_id)) {

        /*  For filters: 
                - For region filtered dashboard
            Tables:
                - Job
                - Job Association
                - Site
        */
        query = workersRepository.createQueryBuilder("workers")
            .select(selectQuery)
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association")
            .innerJoin("job_association.site", "site");

    } else if (requestArgs.site_id || requestArgs.department_id) {

        /*  For: 
                - Site admin dashboards
                - Department filtered dashboard
            Tables:
                - Worker
                - Job
                - Job Association
        */
        query = workersRepository.createQueryBuilder("workers")
            .select(selectQuery)
            .innerJoin("workers.job", "job")
            .innerJoin("job.jobAssociations", "job_association");

    } else if (requestArgs.shift_id) {

        /*  For: 
                - Shift filtered dashboard
            Tables:
                - Worker
                - Job
        */
        query = workersRepository.createQueryBuilder("workers")
            .select(selectQuery)
            .innerJoin("workers.job", "job");
    } else {
        /*  For: 
                - Client main dashboard
                - Agency main dashboard
            Tables:
                - Worker
                - Job
        */
        query = workersRepository.createQueryBuilder("workers").select(selectQuery);
    }

    return query;
}

/**
 * Get workers With Sixty Plus Hours
 */
export const getWorkersWithSixtyPlusHours: any = async (
    client_id: number,
    agency_id: number,
    region_id: number,
    site_id: number,
    startDate: string,
    endDate: string,
    page: number,
    limit: number,
    sortBy: string,
    sortType: "ASC" | "DESC",
    countOnly: boolean = false) => {

    let whereCondition = `payroll.start_date >= :start_date AND payroll.end_date <= :end_date AND payroll.total_hours >= 60 AND workers.is_active = 1 AND workers.client_id = :client_id`;
    whereCondition += agency_id ? ` AND workers.agency_id = :agency_id` : "";
    whereCondition += site_id ? ` AND site.id = :site_id` : "";
    whereCondition += region_id ? ` AND site.region_id = :region_id` : "";

    let whereClauseValue = { "start_date": startDate, "end_date": endDate, "client_id": client_id, "agency_id": agency_id, "site_id": site_id, "region_id": region_id }

    let response = [];
    if (!countOnly) {
        response = await getRepository(Payroll).createQueryBuilder("payroll")
            .innerJoin("payroll.worker", "workers")
            .leftJoin("workers.agency", "agency_details")
            .leftJoin("workers.job", "job")
            .leftJoin("job.jobAssociations", "job_association")
            .leftJoin("job_association.site", "site")
            .select([
                "DISTINCT workers.id as worker_id",
                "workers.employee_id as employee_id",
                "agency_details.name as agency_name",
                "site.id as site_id",
                "site.name as site_name",
                "payroll.total_hours as total_hours",
                "date_format(payroll.start_date, '%Y-%m-%d') as start_date",
                "date_format(payroll.end_date, '%Y-%m-%d') as end_date"
            ])
            .where(whereCondition, whereClauseValue)
            .orderBy(sortBy, sortType)
            .offset((page - 1) * limit)
            .limit(limit)
            .execute();
    }

    let responseCount = await getRepository(Payroll).createQueryBuilder("payroll")
        .innerJoin("payroll.worker", "workers")
        .leftJoin("workers.job", "job")
        .leftJoin("job.jobAssociations", "job_association")
        .leftJoin("job_association.site", "site")
        .select(['DISTINCT workers.id as id', 'payroll.start_date as start_date'])
        .where(whereCondition, whereClauseValue)
        .execute();

    return { "workers": response, "count": responseCount.length };
};

/**
 * Get workers based on card specific where condition
 */
export const getWorkersBasedOnCardCondition: any = async (
    client_id: number,
    agency_id: number,
    region_id: number,
    site_id: number,
    startDate: string,
    endDate: string,
    page: number,
    limit: number,
    sortBy: string,
    sortType: "ASC" | "DESC",
    whereCondition: string,
    countOnly: boolean = false) => {

    whereCondition += ` AND workers.client_id = :client_id AND workers.is_active = 1`;
    whereCondition += agency_id ? ` AND workers.agency_id = :agency_id AND workers.type = 'TEMPORARY'` : "";
    whereCondition += site_id ? ` AND site.id = :site_id` : "";
    whereCondition += region_id ? ` AND site.region_id = :region_id` : "";

    let whereClauseValue = { "client_id": client_id, "agency_id": agency_id, "site_id": site_id, "region_id": region_id, "start_date": startDate, "end_date": endDate }

    let response = [];
    if (!countOnly) {
        const query = getRepository(Workers).createQueryBuilder("workers")
            .leftJoin("workers.agency", "agency_details")
            .leftJoin("workers.job", "job")
            .leftJoin("job.jobAssociations", "job_association")
            .leftJoin("job_association.site", "site")
            .innerJoin("workers.payrolls", "payroll", `payroll.start_date >= :start_date AND payroll.end_date <= :end_date`, { "start_date": startDate, "end_date": endDate })
            .select([
                "DISTINCT workers.id as worker_id",
                "workers.employee_id as employee_id",
                "agency_details.name as agency_name",
                "site.name as site_name",
                "date_format(workers.assignment_date,'%Y-%m-%d') as assignment_date",
                "workers.student_visa as student_visa",
                "workers.limited_hours as limited_hours",
                "payroll.total_hours as total_hours",
                "date_format(payroll.start_date, '%Y-%m-%d') as start_date",
                "date_format(payroll.end_date, '%Y-%m-%d') as end_date",
                "DATEDIFF(payroll.end_date, workers.assignment_date) as days_between_assignment_and_payroll_end",
            ])
            .where(whereCondition, whereClauseValue)
            .orderBy(sortBy, sortType);

        if (limit) {
            query.offset((page - 1) * limit)
                .limit(limit);
        }

        response = await query.execute();
    }

    let responseCount = await getRepository(Workers).createQueryBuilder("workers")
        .leftJoin("workers.job", "job")
        .leftJoin("job.jobAssociations", "job_association")
        .leftJoin("job_association.site", "site")
        .innerJoin("workers.payrolls", "payroll", `payroll.start_date >= :start_date AND payroll.end_date <= :end_date`, { "start_date": startDate, "end_date": endDate })
        .select(["DISTINCT workers.id as id", "payroll.start_date as payroll_start_date"])
        .where(whereCondition, whereClauseValue)
        .getRawMany();

    return { "workers": response, "count": responseCount.length };
};

/**
 * Get workers With Same house number and post code > 4
 */
export const getWorkersWithSamePostCodeAndHouseNumbers: any = async (
    client_id: number,
    agency_id: number,
    region_id: number,
    site_id: number,
    page: number,
    limit: number,
    sortBy: string,
    sortType: "ASC" | "DESC",
    allApproved = 0,
    countOnly: boolean = false,
    complianceCardId: number = 2) => {

    let whereCondition = `w1.client_id = :client_id AND w1.is_active = 1`;
    whereCondition += agency_id ? ` AND w1.agency_id = :agency_id AND w1.type = 'TEMPORARY'` : "";
    whereCondition += site_id ? ` AND site.id = :site_id` : "";
    whereCondition += region_id ? ` AND site.region_id = :region_id` : "";

    let whereClauseValue = { "client_id": client_id, "agency_id": agency_id, "site_id": site_id, "region_id": region_id, "complianceCardId": complianceCardId }
    const workersRepository = getRepository(Workers);

    const subQuery = workersRepository
        .createQueryBuilder("w2")
        .select("w2.house_number, w2.post_code")
        .groupBy("w2.house_number, w2.post_code")
        .having("COUNT(*) > 4")
        .andWhere("w2.house_number IS NOT NULL AND w2.house_number <> ''")
        .andWhere("w2.post_code IS NOT NULL AND w2.post_code <> ''")
        .andWhere("w2.is_active = 1");

    let response = [];
    let count = 0;
    if (!countOnly) {
        const approvalSubQuery = getRepository(ComplianceApproval)
            .createQueryBuilder("approval")
            .select("MAX(approval.id)", "maxId")
            .where("approval.worker_id = w1.id")
            .andWhere("approval.compliance_card_id = :complianceCardId");

        let query = workersRepository.createQueryBuilder("w1")
            .innerJoin(`(${subQuery.getQuery()})`, "w2", "w1.house_number = w2.house_number AND w1.post_code = w2.post_code")
            .leftJoin("w1.agency", "agency_details")
            .leftJoin("w1.job", "job")
            .leftJoin("job.jobAssociations", "job_association")
            .leftJoin("job_association.site", "site")
            .leftJoin(ComplianceApproval, "approval", `approval.id = (${approvalSubQuery.getQuery()})`)
            .select([
                "DISTINCT w1.id as worker_id",
                "w1.employee_id as employee_id",
                "agency_details.name as agency_name",
                "site.name as site_name",
                "w1.house_number as house_number",
                "w1.post_code as post_code",
                "w1.student_visa as student_visa",
                "COALESCE(approval.client_approval_status, 0) as client_approval_status",
                "COALESCE(approval.agency_approval_status, 0) as agency_approval_status",
                "COALESCE(date_format(approval.client_approval_at,'%Y-%m-%d'), '')  as client_approval_at",
                "COALESCE(date_format(approval.agency_approval_at,'%Y-%m-%d'), '') as agency_approval_at",
                "COALESCE(approval.is_reset, 0) as is_approval_reset",
            ])
            .where(whereCondition, whereClauseValue)
            .orderBy(sortBy, sortType)
            .offset((page - 1) * limit)
            .limit(limit);

        // If allApproved is true, filter for both approvals being true (1)
        if (allApproved) {
            query = query.andWhere("approval.client_approval_status = 1 AND approval.agency_approval_status = 1");
        } else {
            query = query.andWhere("(approval.id IS NULL OR (approval.client_approval_status is null or approval.agency_approval_status is null) OR NOT (approval.client_approval_status = 1 AND approval.agency_approval_status = 1))");
        }

        response = await query.getRawMany();
        count = await query.getCount();
        return { "workers": response, "count": count };
    }

    let responseCount = await getRepository(Workers).createQueryBuilder("w1")
        .innerJoin(`(${subQuery.getQuery()})`, "w2", "w1.house_number = w2.house_number AND w1.post_code = w2.post_code")
        .leftJoin("w1.job", "job")
        .leftJoin("job.jobAssociations", "job_association")
        .leftJoin("job_association.site", "site")
        .select(['w1.id as id'])
        .where(whereCondition, whereClauseValue)
        .execute();
    return { "workers": response, "count": responseCount.length };
};


/**
 * Get workers who have had 1 day or less with no hours in a 14 day period
 * workers must not work for more than 12 consecutive days and must have at least 2 days of rest in a 14 day period.
 * So, We check if worker worked more the 12 days continues, we need to fetch them
 */
export const getWorkersWithConsecutiveDaysCard: any = async (
    client_id: number,
    agency_id: number,
    region_id: number,
    site_id: number,
    startDate: string,
    endDate: string,
    page: number,
    limit: number,
    sortBy: string,
    sortType: "ASC" | "DESC",
    countOnly: boolean = false) => {

    let whereCondition = `data.client_id = :client_id`;
    whereCondition += agency_id ? ` AND data.agency_id = :agency_id` : "";
    whereCondition += site_id ? ` AND data.site_id = :site_id` : "";
    whereCondition += region_id ? ` AND data.region_id = :region_id` : "";

    let whereClauseValue = { "client_id": client_id, "agency_id": agency_id, "site_id": site_id, "region_id": region_id }

    const newStartDate = moment(startDate).subtract(14, 'days').format(dateTimeFormates.YYYYMMDD);
    const newEndDate = moment(endDate).add(14, 'days').format(dateTimeFormates.YYYYMMDD);

    const query = getRepository(TimeAndAttendanceData)
        .createQueryBuilder('data')
        .select([
            'data.worker_id AS workerId',
            'data.start_date AS startDate',
            'data.end_date AS endDate',
            'SUM(data.day_1) AS day1',
            'SUM(data.day_2) AS day2',
            'SUM(data.day_3) AS day3',
            'SUM(data.day_4) AS day4',
            'SUM(data.day_5) AS day5',
            'SUM(data.day_6) AS day6',
            'SUM(data.day_7) AS day7',
        ])
        .where('data.start_date <= :newEndDate', { newEndDate })
        .andWhere('data.end_date >= :newStartDate', { newStartDate })
        .andWhere('data.pay_type NOT IN (:...pay_types)', { pay_types: [PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES] })
        .andWhere(whereCondition, whereClauseValue)
        .groupBy('data.worker_id, data.start_date, data.end_date');

    const results = await query.getRawMany();

    // Format the workers to dateWiseWorkHours(date: hour) from the daywise and calculate consecutive days
    const formattedResults = results.map(result => {
        const { startDate, endDate, ...rest } = result;
        const dates = [];
        let currentDate = moment(startDate);

        while (currentDate <= moment(endDate)) {
            dates.push(currentDate.format(dateTimeFormates.YYYYMMDD));
            currentDate = currentDate.add(1, 'day');
        }

        const dateWiseWorkHours = dates.reduce((obj, date) => {
            obj[date] = parseFloat(result[`day${moment(date).day() + 1}`]) || 0;
            return obj;
        }, {});

        return {
            startDate,
            endDate,
            workerId: rest.workerId,
            ...dateWiseWorkHours
        };
    });

    // Merge objects with the same workerId value and combine their properties into a single object
    const mergedResults = formattedResults.reduce((acc, curr) => {
        const existingResult = acc.find(obj => obj.workerId === curr.workerId);

        if (existingResult) {
            Object.keys(curr).forEach(key => {
                if (key !== 'workerId') {
                    existingResult[key] = curr[key];
                }
            });
        } else {
            acc.push(curr);
        }

        return acc;
    }, []);

    // Find the lowest and highest dates within the object and add keys for all the dates(value = 0 if date not inside object) in between
    // Result is an object with worker IDs as keys, and each worker ID maps to an object containing the sorted dates and values.
    const result = mergedResults.reduce((acc, obj) => {
        const { startDate, endDate, workerId, ...rest } = obj;
        const sortedDates = Object.entries(rest)
            .sort(([date1], [date2]) => (date1 > date2 ? 1 : -1))
            .reduce((acc, [date, value]) => {
                acc[date] = value;
                return acc;
            }, {});

        const dates = Object.keys(sortedDates);
        if (dates.length >= 14) {
            let consecutiveCount = 0;
            let startDate;

            // Find groups of at least 13 consecutive dates with at least one non-zero values (Max 12 days without rest allowed)
            Object.entries(sortedDates).forEach(([date, value]) => {
                if (value !== 0) {
                    if (consecutiveCount === 0) {
                        startDate = date;
                    }
                    consecutiveCount++;
                } else {
                    consecutiveCount = 0;
                    startDate = null;
                }

                if (consecutiveCount >= 13) {
                    const endDate = date;
                    acc[workerId] = { startDate, endDate, consecutiveCount };
                }
            });
        }

        return acc;
    }, {});

    const workerIds = Object.keys(result);

    // Create base query builder function to avoid duplication
    const baseQueryBuilder = getRepository(Workers).createQueryBuilder("workers")
        .select([
            "DISTINCT workers.id as worker_id",
            "workers.employee_id as employee_id",
            "agency_details.name as agency_name",
            "site.name as site_name",
        ])
        .leftJoin("workers.agency", "agency_details")
        .leftJoin("workers.job", "job")
        .leftJoin("job.jobAssociations", "job_association")
        .leftJoin("job_association.site", "site")
        .whereInIds([...workerIds])
        .andWhere(`workers.is_active = 1`);


    let workersDetails = [];

    if (!countOnly) {
        if (sortBy === 'consecutive_days') {
            // Sort workerIds by consecutiveCount according to sortType
            const sortedWorkerIds = Object.entries(result)
                .sort(([, aObj], [, bObj]) => {
                    const a = aObj as { consecutiveCount: number };
                    const b = bObj as { consecutiveCount: number };
                    return (sortType === 'ASC') ? (a.consecutiveCount - b.consecutiveCount) : (b.consecutiveCount - a.consecutiveCount);
                }).map(([workerId]) => workerId);

            // Apply pagination to sortedWorkerIds
            const paginatedWorkerIds = sortedWorkerIds.slice((page - 1) * limit, (page - 1) * limit + limit);

            // Get worker details for paginated results
            workersDetails = await baseQueryBuilder
                .whereInIds([...paginatedWorkerIds])
                .execute();

            // Sort the results to match the order of paginatedWorkerIds
            workersDetails.sort(
                (a, b) => paginatedWorkerIds.indexOf(String(a.worker_id)) - paginatedWorkerIds.indexOf(String(b.worker_id))
            );

        } else {
            // Get worker details with pagination and sorting
            workersDetails = await baseQueryBuilder
                .orderBy(sortBy, sortType)
                .offset((page - 1) * limit)
                .limit(limit)
                .execute();
        }
    }

    let responseCount = await getRepository(Workers).createQueryBuilder("workers")
        .select(['workers.id as id'])
        .whereInIds([...workerIds])
        .andWhere(`workers.is_active = 1`)
        .execute();

    return {
        workers: workersDetails,
        count: responseCount.length,
        consecutiveDays: result
    };
};

/**
 * Get workers With Same house number and post code > 4
 */
export const getWorkersWithSameSortCodeAndAccountNumbers: any = async (
    client_id: number,
    agency_id: number,
    region_id: number,
    site_id: number,
    page: number,
    limit: number,
    sortBy: string,
    sortType: "ASC" | "DESC",
    allApproved = 0,
    countOnly: boolean = false,
    complianceCardId: number = 6) => {

    let whereCondition = `w1.client_id = :client_id AND w1.is_active = 1`;
    whereCondition += agency_id ? ` AND w1.agency_id = :agency_id AND w1.type = 'TEMPORARY'` : "";
    whereCondition += site_id ? ` AND site.id = :site_id` : "";
    whereCondition += region_id ? ` AND site.region_id = :region_id` : "";

    let whereClauseValue = { "client_id": client_id, "agency_id": agency_id, "site_id": site_id, "region_id": region_id, "complianceCardId": complianceCardId }
    const workersRepository = getRepository(Workers);

    const subQuery = workersRepository
        .createQueryBuilder("w2")
        .select("w2.sort_code, w2.account_number")
        .groupBy("w2.sort_code, w2.account_number")
        .having("COUNT(*) > 1")
        .andWhere("w2.sort_code IS NOT NULL AND w2.sort_code <> ''")
        .andWhere("w2.account_number IS NOT NULL AND w2.account_number <> ''")
        .andWhere("w2.is_active = 1");

    let response = [];
    let count = 0;

    if (!countOnly) {
        const approvalSubQuery = getRepository(ComplianceApproval)
            .createQueryBuilder("approval")
            .select("MAX(approval.id)", "maxId")
            .where("approval.worker_id = w1.id")
            .andWhere("approval.compliance_card_id = :complianceCardId");

        let query = workersRepository.createQueryBuilder("w1")
            .innerJoin(`(${subQuery.getQuery()})`, "w2", "w1.sort_code = w2.sort_code AND w1.account_number = w2.account_number")
            .leftJoin("w1.agency", "agency_details")
            .leftJoin("w1.job", "job")
            .leftJoin("job.jobAssociations", "job_association")
            .leftJoin("job_association.site", "site")
            .leftJoin(ComplianceApproval, "approval", `approval.id = (${approvalSubQuery.getQuery()})`)
            .select([
                "DISTINCT w1.id as worker_id",
                "w1.employee_id as employee_id",
                "agency_details.name as agency_name",
                "site.name as site_name",
                "w1.sort_code as sort_code",
                "w1.account_number as account_number",
                "COALESCE(approval.client_approval_status, 0) as client_approval_status",
                "COALESCE(approval.agency_approval_status, 0) as agency_approval_status",
                "COALESCE(date_format(approval.client_approval_at,'%Y-%m-%d'), '')  as client_approval_at",
                "COALESCE(date_format(approval.agency_approval_at,'%Y-%m-%d'), '') as agency_approval_at",
                "COALESCE(approval.is_reset, 0) as is_approval_reset"
            ])
            .where(whereCondition, whereClauseValue)
            .orderBy(sortBy, sortType)
            .offset((page - 1) * limit)
            .limit(limit)

        if (sortBy === 'sort_code') query.addOrderBy('account_number', sortType);

        // If allApproved is true, filter for both approvals being true (1)
        if (allApproved) {
            query = query.andWhere("approval.client_approval_status = 1 AND approval.agency_approval_status = 1");
        } else {
            query = query.andWhere("(approval.id IS NULL OR (approval.client_approval_status is null or approval.agency_approval_status is null) OR NOT (approval.client_approval_status = 1 AND approval.agency_approval_status = 1))");
        }

        response = await query.execute();
        count = await query.getCount();
        return { "workers": response, "count": count };
    }

    let responseCount = await getRepository(Workers).createQueryBuilder("w1")
        .innerJoin(`(${subQuery.getQuery()})`, "w2", "w1.sort_code = w2.sort_code AND w1.account_number = w2.account_number")
        .leftJoin("w1.job", "job")
        .leftJoin("job.jobAssociations", "job_association")
        .leftJoin("job_association.site", "site")
        .select(['w1.id as id'])
        .where(whereCondition, whereClauseValue)
        .execute();

    return { "workers": response, "count": responseCount.length };
};