import { getRepository } from 'typeorm';
import { HolidayPayrollSummary } from '..';

export const addHolidayPayrollSummaryData: any = async (data) => {
    const holidayPayrollSummaryRepository = getRepository(HolidayPayrollSummary);
    const response = await holidayPayrollSummaryRepository.insert(data);
    return response.generatedMaps[0];
};

export const deleteHolidayPayrollSummaryByMetaId: any = async (payrollMetaId) => {
    const holidayPayrollSummaryRepository = getRepository(HolidayPayrollSummary);
    return await holidayPayrollSummaryRepository.delete({ payrollMetaId });
};