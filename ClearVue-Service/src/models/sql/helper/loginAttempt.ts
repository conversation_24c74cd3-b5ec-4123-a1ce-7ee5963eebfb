import { getRepository, In } from 'typeorm';
import { LoginAttempt } from '../entities/LoginAttempt';

/**
 * Check blocked login attempt
 * @param  {number} userId
 */
export const checkBlockedLoginAttempt: any = async (userId: number) => {
    const loginAttemptRepository = getRepository(LoginAttempt);
    return await loginAttemptRepository.createQueryBuilder("loginAttempt")
        .where('loginAttempt.userId = :userId', { userId })
        .andWhere('loginAttempt.blockedAt > :now', { now: new Date() })
        .getOne();
};

/**
 * Count login attempts
 * @param  {number} userId
 * @param  {String} attemptAtPeriod
 */
export const countLoginAttempts: any = async (userId: number, attemptAtPeriod: String) => {
    const loginAttemptRepository = getRepository(LoginAttempt);
    return await loginAttemptRepository.createQueryBuilder("loginAttempt")
        .where('loginAttempt.userId = :userId', { userId })
        .andWhere('loginAttempt.attemptAt > :attemptAtPeriod', { attemptAtPeriod })
        .getCount();
};

/**
 * Insert login data
 * @param  {Object} insertLoginPayload
 */
export const insertLoginData: any = async (insertLoginPayload: Object) => {
    const loginAttemptRepository = getRepository(LoginAttempt);
    return await loginAttemptRepository.save(insertLoginPayload);
};

/**
 * Remove the user's login attempts
 * @param  {number} userId
 */
export const removeUserFromLoginAttempt: any = async (userId: number) => {
    const loginAttemptRepository = getRepository(LoginAttempt);
    return await loginAttemptRepository.createQueryBuilder("loginAttempt")
        .delete()
        .where('userId = :userId', { userId })
        .execute();
};
