import { getRepository } from 'typeorm';
import { ClientYearlyRules } from '../';
import e from 'express';

export const addNewStartDateYearlyRuleHelper: any = async (data) => {
    const clientYearlyRulesRepository = getRepository(ClientYearlyRules);
    let response = await clientYearlyRulesRepository.insert({
        clientId: data.client_id,
        financialYearStart: data.finacial_year_start,
        financialYearEnd: data.finacial_year_end,
        startDate: data.start_date,
        endDate: data.end_date,
        totalWeeks: data.total_weeks,
        createdBy: data.user_id,
        updatedBy: data.user_id,
    });
    return response.generatedMaps[0];
};

export const fetchStartDateYearlyRules: any = async (client_id: string, finacial_year_start: string | null = null, do_transformation = true) => {
    let whereCondition = `clientYearlyRules.clientId = :client_id`;
    whereCondition += finacial_year_start ? ` AND clientYearlyRules.financialYearStart = :finacial_year_start` : "";

    let whereClauseValue = { "client_id": client_id, "finacial_year_start": finacial_year_start };

    const clientYearlyRulesRepository = getRepository(ClientYearlyRules);
    const queryBuilder = clientYearlyRulesRepository.createQueryBuilder('clientYearlyRules');

    queryBuilder.select([
        'clientYearlyRules.id as id',
        'clientYearlyRules.clientId as client_id',
        'clientYearlyRules.financialYearStart as finacial_year_start',
        'clientYearlyRules.financialYearEnd as finacial_year_end',
        "date_format(clientYearlyRules.startDate,'%Y-%m-%d') as start_date",
        "date_format(clientYearlyRules.endDate,'%Y-%m-%d') as end_date",
        'clientYearlyRules.totalWeeks as total_weeks',
        'CONCAT(SUBSTRING(clientYearlyRules.financialYearStart, -2), "|", SUBSTRING(clientYearlyRules.financialYearEnd, -2)) as financial_year',
    ]);

    queryBuilder.where(whereCondition, whereClauseValue);

    const yearlyRules = await queryBuilder.execute();
    if (do_transformation) {
        const transformedResults = yearlyRules.reduce((acc, curr) => {
            acc[curr.finacial_year_start] = curr;
            return acc;
        }, {} as Record<string, any>);
        return transformedResults;
    } else {
        return yearlyRules;
    }
};

export const getSingleClientYearlyRule: any = async (client_id: string, yearlyRuleId: string) => {
    // Find the rule to be deleted to get its financial year start
    const clientYearlyRulesRepository = getRepository(ClientYearlyRules);
    const ruleToDelete = await clientYearlyRulesRepository
        .createQueryBuilder('clientYearlyRules')
        .where('clientYearlyRules.id = :yearlyRuleId AND clientYearlyRules.clientId = :client_id', { yearlyRuleId, client_id })
        .getOne();
    return ruleToDelete;
};

export const deleteStartDateYearlyRuleHelper: any = async (clientId: string, id: string, includeCurrYear = false) => {
    // Get the repository
    const clientYearlyRulesRepository = getRepository(ClientYearlyRules);
    let whereCondition = 'clientId = :clientId';
    if (includeCurrYear) {
        whereCondition += ` AND id >= :id`;
    }
    else {
        whereCondition += ` AND id > :id`;
    }

    // Delete all rules for the client with id >= the given id
    const deleteResult = await clientYearlyRulesRepository
        .createQueryBuilder()
        .delete()
        .from(ClientYearlyRules)
        .where(whereCondition, {
            clientId,
            id
        })
        .execute();

    return deleteResult;

};

export const updateStartDateYearlyRuleHelper: any = async (yearlyRuleId, payload, loggedInUser) => {
    // Get repository
    const clientYearlyRulesRepository = getRepository(ClientYearlyRules);

    // Update the current rule
    const resp = await clientYearlyRulesRepository.update(
        { id: yearlyRuleId, clientId: payload.client_id },
        {
            startDate: payload.start_date,
            endDate: payload.end_date,
            totalWeeks: payload.total_weeks,
            financialYearStart: payload.finacial_year_start,
            financialYearEnd: payload.finacial_year_end,
            updatedBy: loggedInUser.user_id
        }
    );
    // Log the number of affected rows
    return resp;

};