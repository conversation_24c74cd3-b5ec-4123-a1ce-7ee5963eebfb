import { getRepository } from 'typeorm';
import { PayrollDetailedSummary } from '../entities/PayrollDetailedSummary';
import { PensionStatus } from '../../../common/enum';

/**
 * Insert payroll detailed summary data in bulk using transaction
 * @param data Array of payroll detailed summary records
 * @param queryRunner Active transaction query runner
 * @returns Result of bulk insert operation
 */
export const insertPayrollDetailedSummary = async (data, queryRunner) => {
    if (queryRunner) {
        // Use the provided queryRunner to maintain transaction context
        return await queryRunner.manager.insert(PayrollDetailedSummary, data);
    } else {
        // Fallback to original implementation if no queryRunner provided
        const PayrollDetailedSummaryRepository = getRepository(PayrollDetailedSummary);
        const response = await PayrollDetailedSummaryRepository.insert(data);
        return response.generatedMaps[0];
    }
};

export const getPayrollDetialedSummaryByPayrollMetaId = async (payrollMetaId) => {
    const payrollRepository = getRepository(PayrollDetailedSummary);
    return await payrollRepository.createQueryBuilder('pds')
        .innerJoin('pds.worker', 'worker')
        .innerJoin('pds.client', 'client')
        .innerJoin('pds.shift', 'shift')
        .innerJoin('pds.department', 'department')
        .innerJoin('payroll', 'payroll', 'payroll.payroll_meta_id = pds.payrollMetaId AND payroll.worker_id = worker.id')
        .select([
            'date_format(pds.startDate,"%Y-%m-%d") as start_date',
            'client.name AS client',
            'shift.name AS shift',
            'department.name AS department',
            'worker.employeeId AS employee_id',
            'pds.payType AS pay_type',
            'pds.adjustment AS adjustment',
            'pds.payCorrection AS pay_correction',
            'SUM(pds.totalHours) AS total_hours',
            'SUM(pds.totalCharge) AS total_charge',
            'SUM(pds.actualCostToEmploy) AS actual_employment_costs',
            'SUM(pds.totalMargin) AS total_margin',
            'ROUND(CASE WHEN SUM(pds.total_hours) = 0 THEN 0 ELSE SUM(pds.totalMargin) / SUM(pds.totalHours) END, 2) AS actual_margin_per_hour',
            'SUM(pds.rateCardMargin) AS rate_card_margin',
            'SUM(pds.clearvueSavings) AS total_savings',
            'ROUND(CASE WHEN SUM(pds.totalHours) = 0 THEN 0 ELSE SUM(pds.clearvueSavings) / SUM(pds.totalHours) END, 2) AS credit_per_hour',
            'SUM(pds.totalPay) AS total_pay',
            'SUM(pds.nationalInsurance) AS national_insurance',
            'SUM(pds.pension) AS pension',
            'SUM(pds.apprenticeshipLevy) AS apprenticeship_levy',
            'SUM(pds.holiday) AS wtr',
            'IFNULL(SUM(pds.holidayPayTypeValue), "") AS holiday_pay_value',
            'IFNULL(SUM(pds.holidayEmploymentCost), "") AS paid_holiday_employment_cost',
            'worker.pensionOptOut AS pension_opt_out',
            'payroll.pension_opt_out_prev AS pension_opt_out_prev',
            'pds.underTwentyone AS under_twentyone',
            'pds.underTwentytwo AS under_twentytwo',
            'pds.withinTwelveweeks AS within_twelveweeks',
            'SUM(pds.otherAssignmentPayValue) AS other_assignment_pay_value',
            'SUM(pds.creditValue) AS credit_value',
            'SUM(pds.totalCost) AS total_cost',
            '(SELECT COUNT(*) FROM pension_status_log WHERE worker_id = worker.id) AS pension_status_count',
            'SUM(pds.accrualValue) AS accrual_value'
        ])
        .addSelect('(SELECT MAX(date_format(pst.created_at, "%Y-%m-%d")) FROM pension_status_log pst WHERE pst.worker_id = worker.id AND worker.pension_opt_out = :pension_opt_out AND pst.to = :to)', 'last_date_opt_in')
        .where('pds.payrollMetaId = :payroll_meta_id', { payroll_meta_id: payrollMetaId, pension_opt_out: '0', to: PensionStatus.OPTED_IN })
        .groupBy('worker.id, pds.payType, pds.adjustment, pds.payCorrection, pds.week, client.name, shift.name, department.name, worker.employeeId, worker.pensionOptOut, payroll.pension_opt_out_prev, pds.underTwentyone, pds.underTwentytwo, pds.withinTwelveweeks')
        .orderBy('employee_id', 'ASC')
        .execute();
};

export const deletePayrollDetailedSummaryByMetaId: any = async (payrollMetaId) => {
    const PayrollDetailedSummaryRepository = getRepository(PayrollDetailedSummary);
    return await PayrollDetailedSummaryRepository.delete({ payrollMetaId });
};