import { getRepository, In } from 'typeorm';
import { Region, Site, SurveyQuestions, LosRule, AgencySiteRestrictions } from '../';
import { SurveyResult } from '../entities/SurveyResult';
import { config } from '../../../configurations';
import { UserType } from '../../../common';

/**
 * create site
 */

export const addSite: any = async (data) => {
    const siteRepository = getRepository(Site);
    return await siteRepository.save(data);
};

export const getSites: any = async (whereClause, whereClauseValue) => {
    const siteRepository = getRepository(Site);
    let response = await siteRepository.createQueryBuilder('site')
        .innerJoin('site.region', 'region')
        .leftJoin('site.userSiteAssociations', 'user_site_association')
        .leftJoin('site.losRules', 'los_rules')
        .select(['DISTINCT(site.id) AS id', 'site.name AS name', 'site.region_id AS region_id',
            'site.address AS address', 'site.post_code AS post_code', 'site.city AS city',
            'site.country AS country', 'region.name AS region_name',
            'los_rules.name AS rule_name',
            'los_rules.roleType AS role_type',
            "date_format(los_rules.startTaxYear,'%Y-%m-%d') as start_tax_year",
            'los_rules.payType AS pay_type',
            'los_rules.preTwelveWeek AS pre_twelve_week',
            'los_rules.postTwelveWeek AS post_twelve_week',
            'los_rules.los AS los',
            'site.cost_centre AS cost_centre',
            'site.created_at AS created_at', 'site.created_by AS created_by',
            'site.updated_at AS updated_at', 'site.updated_by AS updated_by'])
        .where(whereClause, whereClauseValue)
        .orderBy('site.name', 'ASC')
        .getRawMany();
    return response;

};

export const getSitesForDropdown: any = async (whereClause, whereClauseValue) => {
    const siteRepository = getRepository(Site);
    let response = await siteRepository.createQueryBuilder('site')
        .select(['site.id AS id', 'site.name AS name'])
        .where(whereClause, whereClauseValue)
        .orderBy('site.name', 'ASC')
        .getRawMany();
    return response;
};

export const getRegionIdFromSite: any = async (adminId) => {
    const siteRepository = getRepository(Site);
    let response = await siteRepository.createQueryBuilder('site')
        .innerJoin('site.userSiteAssociations', 'user_site_association')
        .select(['site.region_id AS regionId'])
        .where('user_site_association.userId = :adminId', { adminId })
        .getRawMany();
    return response;
};
export const getSitesByNames: any = async (names, clientIds) => {
    const siteRepository = getRepository(Site);
    return await siteRepository.find(
        {
            where: { name: In(names), clientId: In(clientIds) },
            select: ['id', 'name']
        }
    );
};
/**
 * get site By Id
 */
export const getSiteById: any = async (id) => {
    const siteRepository = getRepository(Site);
    return await siteRepository.createQueryBuilder("site")
        .leftJoin('site.userSiteAssociations', 'user_site_association')
        .where("site.id = :id", { id })
        .select(['name', 'region_id', 'client_id', 'GROUP_CONCAT(user_site_association.user_id) as site_admins', 'cost_centre'])
        .getRawOne();
};

/**
 * update site
 */
export const updateSite: any = async (id, body) => {
    const siteRepository = getRepository(Site);
    body.updatedAt = new Date();
    return await siteRepository.update({ id }, body);
};

/**
 * Average Ratings for dashboard.
 */
export const getDashboardRatingsHelper: any = async (whereClause1: string, whereClauseValue1: any, whereClause2: string, whereClauseValue2: any, whereClause3: string, whereClauseValue3: any, loggedInUser) => {
    let response: any = {};
    let query;
    let isWorkerJoined = false;
    const { shift_id, department_id } = whereClauseValue3;

    let averageRatingQuery = await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.site', 'site')
        .select(['IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings'])
        .where(whereClause1, whereClauseValue1)

    let reviewsCountQuery = await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.site', 'site')
        .select(['count(distinct(survey_result.survey_id)) as reviews_count'])
        .where(whereClause1, whereClauseValue1)
        .groupBy(`CONCAT(survey_result.worker_id, '|||', survey_result.survey_id, '|||', survey_result.created_at)`);

    // whereClause1 += ` GROUP BY survey_question.label`;

    let labelWiseRatingsQuery = await getRepository(SurveyQuestions).createQueryBuilder('survey_question')
        .leftJoin('survey_question.surveyResults', 'survey_result', whereClause2, whereClauseValue2)
        .leftJoin('survey_result.site', 'site')
        .select(['survey_question.label as label, IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings'])
        .where(whereClause1, whereClauseValue1)
        .groupBy('survey_question.label');

    if (whereClause2 && whereClauseValue2) {
        averageRatingQuery = averageRatingQuery.andWhere(whereClause2, whereClauseValue2);
        reviewsCountQuery = reviewsCountQuery.andWhere(whereClause2, whereClauseValue2);
    }

    if (shift_id || department_id) {
        averageRatingQuery = averageRatingQuery.innerJoin('survey_result.worker', 'worker').innerJoin('worker.job', 'job').andWhere(whereClause3, whereClauseValue3);
        reviewsCountQuery = reviewsCountQuery.innerJoin('survey_result.worker', 'worker').innerJoin('worker.job', 'job').andWhere(whereClause3, whereClauseValue3);
        labelWiseRatingsQuery = labelWiseRatingsQuery.innerJoin('survey_result.worker', 'worker').innerJoin('worker.job', 'job').andWhere(whereClause3, whereClauseValue3);
        isWorkerJoined = true;
    }

    if (department_id) {
        averageRatingQuery = averageRatingQuery.innerJoin('job.jobAssociations', 'job_association');
        reviewsCountQuery = reviewsCountQuery.innerJoin('job.jobAssociations', 'job_association');
        labelWiseRatingsQuery = labelWiseRatingsQuery.innerJoin('job.jobAssociations', 'job_association');
    }

    if (![UserType.CLIENT_ADMIN, UserType.CLIENT_SITE, UserType.CLIENT_REGIONAL].includes(parseInt(loggedInUser.user_type_id))) {
        if (!isWorkerJoined) {
            averageRatingQuery = averageRatingQuery.innerJoin('survey_result.worker', 'worker');
            reviewsCountQuery = reviewsCountQuery.innerJoin('survey_result.worker', 'worker');
            labelWiseRatingsQuery = labelWiseRatingsQuery.innerJoin('survey_result.worker', 'worker');
        }

        averageRatingQuery = averageRatingQuery.andWhere(`worker.type = :type`, { "type": config.TEMPORARY_WORKER });
        reviewsCountQuery = reviewsCountQuery.andWhere(`worker.type = :type`, { "type": config.TEMPORARY_WORKER });
        labelWiseRatingsQuery = labelWiseRatingsQuery.andWhere(`worker.type = :type`, { "type": config.TEMPORARY_WORKER })
    }

    response.average_rating = await averageRatingQuery.getRawOne();
    response.reviews_count = await reviewsCountQuery.getRawMany();
    response.label_wise_ratings = await labelWiseRatingsQuery.getRawMany();

    return response;
};

/**
 * Individual site ratings with labels.
 */
export const getSiteRatingsWithLabelHelper: any = async (whereClause: string, whereClauseValue: any, type = 'TEMPORARY') => {
    return await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.site', 'site')
        .innerJoin('survey_result.worker', 'worker')
        .select(['DISTINCT(site.id) as site_id', 'site.name as site_name',
            'COUNT(survey_result.id) as reviews_count',
            'survey_question.label as label', 'IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings'])
        .where(whereClause, whereClauseValue)
        .andWhere(`worker.type = :type`, { "type": type })
        .groupBy('site.name, survey_question.label')
        .getRawMany()
};

/**
 * Individual site ratings with labels.
 */
export const getAverageSiteRatings: any = async (whereClause: string, whereClauseValue: any, type = 'TEMPORARY') => {
    return await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.site', 'site')
        .innerJoin('survey_result.worker', 'worker')
        .select(['DISTINCT(site.id) as site_id', 'site.name as site_name',
            'IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings'])
        .where(whereClause, whereClauseValue)
        .andWhere(`worker.type = :type`, { "type": type })
        .groupBy('site.name')
        .getRawMany()
};


/**
 * Individual site reviews count.
 */
export const getSiteWiseReviewsCount: any = async (whereClause: string, whereClauseValue: any, type = 'TEMPORARY') => {
    return await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.worker', 'worker')
        .select([`survey_result.site_id as site_id, count(distinct(CONCAT(survey_result.worker_id, '|||', survey_result.survey_id, '|||', survey_result.created_at))) as reviews_count`])
        .where(whereClause, whereClauseValue)
        .andWhere(`worker.type = :type`, { "type": type })
        .groupBy(`survey_result.site_id`)
        .getRawMany();
};

/**
 * Average Ratings for dashboard with worker type wise filter.
 */
export const getWorkerTypeWiseDashboardRatingsHelper: any = async (whereClause1: string, whereClauseValue1: any, whereClause2: string, whereClauseValue2: any) => {
    let response: any = {};
    response.average_rating = await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.worker', 'worker')
        .innerJoin('survey_result.site', 'site')
        .select(['IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings', 'worker.type as type'])
        .where(whereClause1, whereClauseValue1)
        .andWhere(whereClause2, whereClauseValue2)
        .groupBy('worker.type')
        .getRawMany();

    response.reviews_count = await getRepository(SurveyResult).createQueryBuilder('survey_result')
        .innerJoin('survey_result.question', 'survey_question')
        .innerJoin('survey_result.worker', 'worker')
        .innerJoin('survey_result.site', 'site')
        .select(['count(distinct(survey_result.survey_id)) as reviews_count', 'worker.type as type'])
        .where(whereClause1, whereClauseValue1)
        .andWhere(whereClause2, whereClauseValue2)
        .groupBy(`CONCAT(survey_result.worker_id, '|||', survey_result.survey_id, '|||', survey_result.created_at)`)
        .addGroupBy('worker.type')
        .getRawMany();

    // whereClause1 += ` GROUP BY survey_question.label`;

    response.label_wise_rating = await getRepository(SurveyQuestions).createQueryBuilder('survey_question')
        .leftJoin('survey_question.surveyResults', 'survey_result', whereClause2, whereClauseValue2)
        .innerJoin('survey_result.worker', 'worker')
        .leftJoin('survey_result.site', 'site')
        .select(['survey_question.label as label, IFNULL(FORMAT(avg(survey_result.rating),1),0.0) as ratings', 'worker.type as type'])
        .where(whereClause1, whereClauseValue1)
        .groupBy(`survey_question.label`)
        .addGroupBy('worker.type')
        .getRawMany();
    return response;
};

/**
 * get site names by id
 */
export const getSiteNamesById: any = async (siteIds) => {
    return await getRepository(Site).find(
        {
            where: `id IN (${siteIds.join(",")})`,
            select: ['id', 'name']
        }
    );
}

export const getSitesWithLowerCase: any = async (clientId) => {
    return await getRepository(Site).createQueryBuilder('site')
        .select(['site.id AS id', 'LOWER(site.name) AS name'])
        .where(`site.clientId = :client_id`, { "client_id": clientId })
        .orderBy('site.name', 'ASC')
        .getRawMany();
};

export const addWtrData: any = async (data) => {
    const losRuleRepository = getRepository(LosRule);
    return await losRuleRepository.save(data);
};

export const getRuleBySiteId: any = async (id) => {
    const losRuleRepository = getRepository(LosRule);
    return await losRuleRepository.createQueryBuilder("los_rule")
        .where("los_rule.siteId = :id", { id })
        .select(['id', 'role_type', 'start_tax_year', 'pay_type'])
        .getRawMany();
};

export const deleteRuleById: any = async (ids) => {
    const losRuleRepository = getRepository(LosRule);
    return await losRuleRepository.createQueryBuilder()
        .delete()
        .from(LosRule)
        .where('id IN (:...ids)', { ids })
        .execute();
};

export const addAndUpdateWtrData: any = async (data) => {
    const losRuleRepository = getRepository(LosRule);
    const updatedRecords = [];

    for (const element of data) {
        const { clientId, siteId, roleType, payType } = element;

        // Check if the record already exists
        const existingRecord = await losRuleRepository.findOne({ clientId, siteId, roleType, payType });
        if (existingRecord) {
            // Update the existing record with new data
            Object.assign(existingRecord, element);
            const updatedRecord = await losRuleRepository.save(existingRecord);
            updatedRecords.push(updatedRecord);
        } else {
            // Create a new record
            const newRecord = losRuleRepository.create(element);
            const insertedRecord = await losRuleRepository.save(newRecord);
            updatedRecords.push(insertedRecord);
        }
    }
    return updatedRecords;
};

export const getRuleBySiteAndRoleType: any = async (id, roleType, payType) => {
    const losRuleRepository = getRepository(LosRule);
    return await losRuleRepository
        .createQueryBuilder("los_rule")
        .where("los_rule.siteId = :id", { id })
        .andWhere("los_rule.roleType = :roleType", { roleType })
        .andWhere("los_rule.payType = :payType", { payType })
        .select([
            "los_rule.id as id",
            "los_rule.siteId as site_id",
            "los_rule.name as name",
            "los_rule.roleType as role_type",
            "los_rule.startTaxYear as start_tax_year",
            "los_rule.payType as pay_type",
            "los_rule.preTwelveWeek as pre_twelve_week",
            "los_rule.postTwelveWeek as post_twelve_week",
            "los_rule.los as los",
        ])
        .getRawOne();
};

export const syncSiteRestrictions = async (transactionalEntityManager, associationId, siteRestrictionsData) => {
    const repository = transactionalEntityManager.getRepository(AgencySiteRestrictions);

    // Delete existing restrictions
    await repository.delete({ agencyClientAssociationId: associationId });

    // Insert new restrictions
    if (siteRestrictionsData.length > 0) {
        await repository.insert(siteRestrictionsData);
    }
};

export const getSiteRestrictionsList = async (associationId) => {
    const repository = getRepository(AgencySiteRestrictions);
    return await repository.find({
        where: { agencyClientAssociationId: associationId },
        relations: ['site'],
        select: ['site']
    });
};
