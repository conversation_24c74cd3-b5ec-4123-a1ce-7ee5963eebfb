import { getConnection, getRepository } from 'typeorm';
import { Booking, BookingAssociation } from '../';
import { BookingStatus } from '../../../common';
import { storePreviousBookingAssociation } from './bookingAssociationHistory';

export const createBookingAssociationHelper: any = async (data) => {
    let bookingAssociationRepository = getRepository(BookingAssociation);
    let response = await bookingAssociationRepository.insert(data);

    return response.generatedMaps[0];
};

export const getBookingHelper: any = async (whereClause, whereClauseValue) => {
    let bookingRepository = getRepository(BookingAssociation);
    let response = await bookingRepository.createQueryBuilder('booking_association')
        .innerJoin('booking_association.booking', 'booking')
        .innerJoin('booking_association.agency', 'agency_details')
        .innerJoin('booking.client', 'client_details')
        .innerJoin('booking.department', 'departments')
        .innerJoin('booking.site', 'site')
        .innerJoin('booking.region', 'region')
        .innerJoin('booking.shiftType', 'shift')
        .select([
            'booking_association.id as id',
            'booking.id as booking_id',
            'booking.client_id as client_id',
            'client_details.name as client_name',
            'client_details.booking_format as booking_format',
            'booking_association.agency_id as agency_id',
            'agency_details.name as agency_name',
            'booking.site_id as site_id',
            'site.name as site_name',
            'booking.department_id as department_id',
            'departments.name as department_name',
            'booking.shift_type_id as shift_type_id',
            'shift.name as shift_name',
            'booking.region_id as region_id',
            'region.name as region_name',
            "date_format(booking.start_date,'%Y-%m-%d') as start_date",
            "date_format(booking.end_date,'%Y-%m-%d') as end_date",
            'booking.required_workers_heads as required_workers_heads',
            'booking.required_workers_total as required_workers_total',
            'booking.required_supervisors_heads as required_supervisors_heads',
            'booking.required_supervisors_total as required_supervisors_total',
            'booking.total as total',
            'booking_association.requested_workers_heads as requested_workers_heads',
            'booking_association.requested_supervisors_heads as requested_supervisors_heads',
            'booking_association.fulfilled_workers_heads as fulfilled_workers_heads',
            'booking_association.fulfilled_supervisors_heads as fulfilled_supervisors_heads',
            'booking_association.requested_workers_total as requested_workers_total',
            'booking_association.requested_supervisors_total as requested_supervisors_total',
            'booking_association.requested_total as requested_total',
            'booking_association.fulfilled_total as fulfilled_total',
            'booking_association.fulfilled_workers_total as fulfilled_workers_total',
            'booking_association.fulfilled_supervisors_total as fulfilled_supervisors_total'
        ]).where(whereClause, whereClauseValue)
        .getRawMany()
    return response;
}

export const updateBookingHelper: any = async (id, agency, data) => {
    let associationId = await getRepository(BookingAssociation).findOne({ select: ['id'], where: { agencyId: agency, bookingId: id } });
    let bookingRepository = getRepository(BookingAssociation);
    return await bookingRepository.update(associationId.id, data);
}

export const getBookingAssociationDetails: any = async (whereClause) => {
    return await getRepository(BookingAssociation).findOne({
        select: ['id', 'agencyId', 'requestedTotal', 'bookingId'],
        where: whereClause
    })
}

export const getBookingByClientHelper: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType) => {

    let response = await getRepository(BookingAssociation).createQueryBuilder('ba')
        .innerJoin('ba.booking', 'bk')
        .innerJoin('bk.client', 'cdl')
        .innerJoin('bk.region', 'rn')
        .innerJoin('bk.department', 'dep')
        .innerJoin('bk.site', 'st')
        .innerJoin('bk.shiftType', 'sft')
        .select([
            'bk.id AS id',
            'bk.client_id AS client_id',
            'cdl.name as client_name',
            'cdl.booking_format as booking_format',
            'COUNT(ba.id) AS association_count',
            "date_format(bk.start_date,'%Y-%m-%d') as start_date",
            "date_format(bk.end_date,'%Y-%m-%d') as end_date",
            'bk.department_id as department_id',
            'dep.name as department_name',
            'bk.shift_type_id as shift_type_id',
            'sft.name as shift_name',
            'bk.region_id as region_id',
            'rn.name as region_name',
            'bk.site_id as site_id',
            'st.name as site_name',
            'bk.total as total',
            'bk.status as status'
        ])
        .groupBy('bk.id')
        .orderBy(sortBy, sortType.toUpperCase())
        .addOrderBy('bk.created_at', sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)
        .where(whereClause, whereClauseValue)
        .execute();

    let responseLength = await getRepository(BookingAssociation).createQueryBuilder('ba')
        .innerJoin('ba.booking', 'bk')
        .innerJoin('bk.client', 'cdl')
        .innerJoin('bk.region', 'rn')
        .innerJoin('bk.department', 'dep')
        .innerJoin('bk.site', 'st')
        .innerJoin('bk.shiftType', 'sft')
        .select(['bk.id AS id'])
        .groupBy('bk.id')
        .where(whereClause, whereClauseValue)
        .execute();
    response["count"] = responseLength.length;

    return response;
}

export const getBookingByAgencyHelper: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType) => {

    let response = await getRepository(BookingAssociation).createQueryBuilder('ba')
        .innerJoin('ba.booking', 'bk')
        .innerJoin('bk.client', 'cdl')
        .innerJoin('bk.region', 'rn')
        .innerJoin('bk.department', 'dep')
        .innerJoin('bk.site', 'st')
        .innerJoin('bk.shiftType', 'sft')
        .select([
            'bk.id AS id',
            'bk.client_id AS client_id',
            'cdl.name as client_name',
            'cdl.booking_format as booking_format',
            'COUNT(ba.id) AS association_count',
            "date_format(bk.start_date,'%Y-%m-%d') as start_date",
            "date_format(bk.end_date,'%Y-%m-%d') as end_date",
            'bk.department_id as department_id',
            'dep.name as department_name',
            'bk.shift_type_id as shift_type_id',
            'sft.name as shift_name',
            'bk.region_id as region_id',
            'rn.name as region_name',
            'bk.site_id as site_id',
            'st.name as site_name',
            'bk.total as total',
            'ba.status as status',
            'ba.requested_total as requested_total',
            'ba.fulfilled_total as fulfilled_total'
        ])
        .groupBy('bk.id')
        .orderBy(sortBy, sortType.toUpperCase())
        .addOrderBy('bk.id', sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)
        .where(whereClause, whereClauseValue)
        .execute();

    let responseLength = await getRepository(BookingAssociation).createQueryBuilder('ba')
        .innerJoin('ba.booking', 'bk')
        .innerJoin('bk.client', 'cdl')
        .innerJoin('bk.region', 'rn')
        .innerJoin('bk.department', 'dep')
        .innerJoin('bk.site', 'st')
        .innerJoin('bk.shiftType', 'sft')
        .select(['bk.id AS id'])
        .groupBy('bk.id')
        .where(whereClause, whereClauseValue)
        .execute();
    response["count"] = responseLength.length;

    return response;
}

export const updateBookingAssociationDetails = async (data, loggedInUser, transactionManager) => {
    try {

        // Fetch current records
        const currentAssociations = await transactionManager
            .getRepository(BookingAssociation)
            .findByIds(data.map(item => item.id));


        // Store previous versions
        for (const oldAssociation of currentAssociations) {
            await storePreviousBookingAssociation(oldAssociation, loggedInUser.user_id);
        }

        // Perform the update
        const result = await transactionManager
            .getRepository(BookingAssociation)
            .save(data);

        return result;

    } catch (error) {
        throw error;
    }
};

// Function to handle transformation for both workers and supervisors
export const handleTransformation = async (heads, total, supervisorFlag, rest) => {
    if (total !== 0 && total !== "") {
        const parsedHeads = JSON.parse(heads);
        const transformedRow = { ...rest, requested: total, total_fulfillment: total, supervisor_flag: supervisorFlag ? "yes" : "no" };

        // Map the days to their respective names
        const daysMap = {
            1: 'sun',
            2: 'mon',
            3: 'tue',
            4: 'wed',
            5: 'thu',
            6: 'fri',
            7: 'sat',
        };

        Object.keys(daysMap).forEach((key) => {
            transformedRow[daysMap[key]] = Number(parsedHeads[key]) || 0;
        });

        return transformedRow;
    }
    return null;
};


export const getOpenBookingByAgencyHelper: any = async (whereClause, whereClauseValue, sortBy, sortType) => {
    let response = await getRepository(BookingAssociation).createQueryBuilder('ba')
        .innerJoin('ba.booking', 'bk')
        .innerJoin('bk.client', 'cdl')
        .innerJoin('bk.region', 'rn')
        .innerJoin('bk.department', 'dep')
        .innerJoin('bk.site', 'st')
        .innerJoin('bk.shiftType', 'sft')
        .select([
            'bk.id AS id',
            'cdl.name as client_name',
            'cdl.booking_format as booking_format',
            'st.name as site',
            'st.id as site_id',
            'dep.name as department',
            'rn.name as region',
            'sft.name as shift',
            'ba.requested_workers_total as requested_workers_total',
            'ba.requested_supervisors_total as requested_supervisors_total',
            'ba.requested_total as requested_total',
            "date_format(bk.start_date,'%d/%m/%Y') as start_date",
            "date_format(bk.end_date,'%d/%m/%Y') as end_date",
            'ba.requested_workers_heads as requested_workers_heads',
            'ba.requested_supervisors_heads as requested_supervisors_heads',
        ])
        .groupBy('bk.id')
        .orderBy(sortBy, sortType.toUpperCase())
        .addOrderBy('bk.id', sortType.toUpperCase())
        .where(whereClause, whereClauseValue)
        .andWhere(`ba.status = :status`, { "status": String(BookingStatus.OPEN) })
        .execute();

    // Transform the response data
    let transformedResponse = [];
    const transformations = response.map(async (row) => {
        const { requested_workers_heads, requested_workers_total, requested_supervisors_heads, requested_supervisors_total, ...rest } = row;

        // Handle regular worker request
        const workersTransformedRow = await handleTransformation(requested_workers_heads, requested_workers_total, false, rest);
        if (workersTransformedRow) transformedResponse.push(workersTransformedRow);

        // Handle supervisor request
        const supervisorsTransformedRow = await handleTransformation(requested_supervisors_heads, requested_supervisors_total, true, rest);
        if (supervisorsTransformedRow) transformedResponse.push(supervisorsTransformedRow);
    });

    await Promise.all(transformations);
    return transformedResponse;
}

export const deleteBookingAssociationByBookingId = async (bookingId: number) => {
    return await getRepository(BookingAssociation).createQueryBuilder("ba")
        .delete()
        .where('booking_id = :bookingId', { bookingId })
        .execute();
};


export const getBookingsDataForTnaFulfilment = async (whereClause, whereClauseValue) => {
    const bookingData = await getRepository(Booking)
        .createQueryBuilder('b')
        .select([
            'b.id as booking_id',
            'ba.id as booking_association_id',
            'b.client_id as client_id',
            'ba.agency_id as agency_id',
            'b.shift_type_id as shift_id',
            'b.department_id as department_id',
            'ba.requested_workers_heads as requested_workers_heads',
            'ba.fulfilled_workers_heads as fulfilled_workers_heads',
            'ba.requested_supervisors_heads as requested_supervisors_heads',
            'ba.fulfilled_supervisors_heads as fulfilled_supervisors_heads',
            'ba.fulfilled_workers_total as fulfilled_workers_total',
            'ba.fulfilled_supervisors_total as fulfilled_supervisors_total',
            'ba.fulfilled_total as fulfilled_total',
            'b.status as status',
            "date_format(b.start_date,'%d/%m/%Y') as start_date",
            "date_format(b.end_date,'%d/%m/%Y') as end_date"
        ])
        .innerJoin('booking_association', 'ba', 'ba.booking_id = b.id')
        .where(whereClause, whereClauseValue)
        .getRawMany();
    return bookingData;
}