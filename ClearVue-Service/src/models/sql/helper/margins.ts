import { getRepository } from 'typeorm';
import { Margins } from '../';

export const insertMargins: any = async (transactionalEntityManager, body) => {
    const agencyAssociationRepository = transactionalEntityManager.getRepository(Margins);
    return await agencyAssociationRepository.save(body);
};


export const createMargin: any = async (body) => {
    const marginsRepository = getRepository(Margins);
    return await marginsRepository.save(body);
};

export const getMarginsListHelper: any = async (whereConditions) => {
    const marginsRepository = getRepository(Margins);
    return await marginsRepository.find({
        where: whereConditions
    });
};

export const getMarginById: any = async (id) => {
    const marginsRepository = getRepository(Margins);
    return await marginsRepository.findOne({
        where: { id },
        relations: ['agencyClientAssociation']
    });
};

export const updateMargin: any = async (id, body, transactionalEntityManager = null) => {
    const marginsRepository = transactionalEntityManager ? transactionalEntityManager.getRepository(Margins) : getRepository(Margins);
    await marginsRepository.update({ id }, body);
    return await getMarginById(id);
};

export const deleteMargins: any = async (whereConditions) => {
    const marginsRepository = getRepository(Margins);
    return await marginsRepository.delete(whereConditions);
};

export const deleteMarginsBySiteIds: any = async (transactionalEntityManager, siteIds, agencyClientAssociationId) => {
    const marginsRepository = transactionalEntityManager.getRepository(Margins);
    return await marginsRepository
        .createQueryBuilder()
        .delete()
        .where('site_id IN (:...siteIds)', { siteIds })
        .andWhere('agency_client_association_id = :associationId', {
            associationId: agencyClientAssociationId
        })
        .execute();
}; 