import { getRepository, In, MoreThan } from 'typeorm';
import { TimeAndAttendanceData } from '../entities/TimeAndAttendanceData';
import { AgencyClientAssociation } from '../entities/AgencyClientAssociation';
import { config } from '../../../configurations';
import { TotalAgencyPayData } from '../entities/TotalAgencyPayData';
import { BookingFormat } from '../../../common/enum';
import { PayrollMeta } from '../entities/PayrollMeta';
import { HolidayPayrollSummary } from '../entities/HolidayPayrollSummary';
import { PayTypes } from '../../../common/enum';

export const addTimeAndAttendanceData: any = async (data) => {
    const timeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);
    let response = await timeAndAttendanceDataRepository.insert(data);
    return response.generatedMaps[0];
};

export const getTimeAndAttendanceDetail: any = async (id, page, limit, sortBy, sortType) => {
    const timeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);
    return await timeAndAttendanceDataRepository
        .createQueryBuilder('time_and_attendance_data')
        // .innerJoin('time_and_attendance_data.client', 'client')
        .innerJoin('time_and_attendance_data.agency', 'agency')
        .innerJoin('time_and_attendance_data.worker', 'worker')
        // .innerJoin('time_and_attendance_data.site', 'site')
        .innerJoin('time_and_attendance_data.job', 'job')
        .innerJoin('time_and_attendance_data.timeAndAttendance', 'timeAndAttendance')
        .where('time_and_attendance_data.timeAndAttendanceId = :id', { id })
        .select(['time_and_attendance_data.id AS id', 'time_and_attendance_data.hoursApproved AS hour_approved',
            'time_and_attendance_data.agencyId AS agency_id', 'time_and_attendance_data.clientId AS client_id',
            'agency.name AS agency_name', 'time_and_attendance_data.clientName AS client_name',
            'time_and_attendance_data.siteName AS site_name', 'time_and_attendance_data.siteId AS site_id',
            'worker.firstName AS worker_first_name', 'worker.lastName AS worker_last_name', 'time_and_attendance_data.workerId AS worker_id',
            'job.name AS job_name', 'time_and_attendance_data.jobId AS job_id',
            'timeAndAttendance.name AS time_and_attendance_name', 'time_and_attendance_data.timeAndAttendanceId AS time_and_attendance_id',
            'timeAndAttendance.path AS time_and_attendance_path', 'timeAndAttendance.status AS time_and_attendance_status',
            'time_and_attendance_data.date AS date', 'time_and_attendance_data.paymentWeek AS payment_week'])
        .orderBy(sortBy, sortType)
        .offset((page - 1) * limit)
        .limit(limit)
        .execute();
};


export const getTimeAndAttendanceDataCount: any = async (timeAndAttendanceId) => {
    const timeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);
    return await timeAndAttendanceDataRepository.count({ timeAndAttendanceId })
};

export const getStandardAndOvertimeHourAndPay: any = async (whereClause, whereClauseValue) => {
    return await getRepository(TimeAndAttendanceData)
        .createQueryBuilder("tadata")
        .innerJoin("tadata.worker", "workers")
        .leftJoin(PayrollMeta, "payrollMeta", "tadata.timeAndAttendanceId = payrollMeta.timeAndAttendanceId")
        .leftJoin(HolidayPayrollSummary, "holidayPayrollSummary", "payrollMeta.id = holidayPayrollSummary.payrollMetaId AND workers.id = holidayPayrollSummary.workerId")
        .select('SUM(tadata.total_charge) AS total')
        .addSelect('SUM(CASE WHEN tadata.pay_type != :expensesPayType THEN tadata.weekly_hours ELSE 0 END) AS weekly_hours')
        .addSelect('tadata.pay_type AS pay_type')
        .addSelect('SUM(holidayPayrollSummary.wtr_cost) AS wtr_costs')
        .where(whereClause, whereClauseValue)
        .andWhere(`workers.type = :type`, { "type": config.TEMPORARY_WORKER })
        .setParameter("expensesPayType", PayTypes.EXPENSES)
        .groupBy("tadata.pay_type")
        .execute();
}

export const getWorkerShiftsCompleted: any = async (workerList) => {
    return getRepository(TimeAndAttendanceData).count({
        where: {
            workerId: In(workerList),
            weeklyHours: MoreThan(0)
        }
    })
}

export const addtotalAgencyPayData: any = async (data) => {
    const totalAgencyPayDataRepository = getRepository(TotalAgencyPayData);
    let response = await totalAgencyPayDataRepository.insert(data);
    return response.generatedMaps[0];
};


export const getDistinctWorkersWithTNAForSpecifiedDateRange: any = async (lastFourWeekDateRange, clientIdList, week) => {

    let queryBuilder = getRepository(TimeAndAttendanceData)
        .createQueryBuilder('time_and_attendance_data')
        .select('DISTINCT worker_id')
        .where('client_id IN (:...clientIdList)', { clientIdList })
        .andWhere('(start_date = :startDate2 AND end_date = :endDate2)', {
            startDate2: lastFourWeekDateRange.w2.start_date,
            endDate2: lastFourWeekDateRange.w2.end_date
        })

    for (let i = week + 1; i > 2; i--) {

        queryBuilder = queryBuilder.orWhere(`(start_date = :startKey AND end_date = :endKey)`, {
            startKey: lastFourWeekDateRange[`w${i}`].start_date,
            endKey: lastFourWeekDateRange[`w${i}`].end_date
        });
    }

    const response = await queryBuilder.orderBy('worker_id', 'DESC').execute();
    return response.map(obj => obj.worker_id);
}


export const getMaxInactivatedAt = async (workerIds) => {
    const result = await getRepository(TimeAndAttendanceData)
        .createQueryBuilder('tad')
        .innerJoin("tad.worker", "workers")
        .select(['workers.employeeId AS employee_id', 'tad.worker_id', 'MAX(date_format(tad.end_date, "%Y-%m-%d")) AS inactivated_at'])
        .where('tad.worker_id IN (:...workerIds)', { workerIds })
        .groupBy('tad.worker_id')
        .orderBy('inactivated_at', 'DESC')
        .execute();

    const foundWorkerIds = result.map((row) => row.worker_id);
    const missingWorkerIds = workerIds.filter((id) => !foundWorkerIds.includes(id));

    return { workersWithInactivationDate: result, missingWorkerIds: missingWorkerIds };
};

/**
 * Generate worker wise fulfilment days object by combining all paytypes hours.
 */
export const getTimeAndAttendanceDataForShiftFulfilment = async (time_and_attendance_id, fulfilmentType = BookingFormat.HEADS) => {
    const weekDays = ['1', '2', '3', '4', '5', '6', '7'];

    const selectClauses = weekDays.map(day => {
        const sumClause = fulfilmentType === BookingFormat.HEADS
            ? `IF(SUM(data.day_${day}) > 0, 1, 0) as "${day}"`
            : `SUM(data.day_${day}) as "${day}"`;
        return sumClause;
    });

    const result = await getRepository(TimeAndAttendanceData)
        .createQueryBuilder('data')
        .innerJoin('data.worker', 'worker')
        .select([
            'data.department_id',
            'data.shift_id',
            'data.worker_id',
            `CASE WHEN worker.workers_supervisor_status IS NOT NULL THEN 'YES' ELSE 'NO' END AS is_supervisor`,
            ...selectClauses,
        ])
        .where('data.time_and_attendance_id = :id', { id: time_and_attendance_id })
        .groupBy('data.department_id, data.shift_id, data.worker_id')
        .getRawMany();

    return result;
};

export const getAdjustmentWorkersList: any = async (tnaId, client_id, agency_id, sortBy, sortType) => {
    const timeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);

    // Get paginated data
    const query = await timeAndAttendanceDataRepository
        .createQueryBuilder('tna_data')
        .innerJoin('tna_data.worker', 'worker')
        .leftJoin('tna_data.shift', 'shift')
        .leftJoin('tna_data.department', 'department')
        .leftJoin('tna_data.site', 'site')
        .leftJoin('tna_data.client', 'client')
        .where('tna_data.timeAndAttendanceId = :tnaId', { tnaId })
        .andWhere('tna_data.adjustment = :adjustment', { adjustment: true })
        .select([
            'worker.employee_id AS employee_id',
            'worker.payroll_ref AS payroll_ref',
            'worker.firstName AS first_name',
            'worker.lastName AS last_name',
            'client.name AS client_name',
            'site.name AS site_name',
            'shift.name AS shift_name',
            'department.name AS department_name',
            'tna_data.day_1 AS sun',
            'tna_data.day_2 AS mon',
            'tna_data.day_3 AS tue',
            'tna_data.day_4 AS wed',
            'tna_data.day_5 AS thu',
            'tna_data.day_6 AS fri',
            'tna_data.day_7 AS sat',
            'tna_data.weeklyHours AS week_hours',
            'tna_data.payRate AS pay_rate',
            'tna_data.chargeRate AS charge_rate',
            'tna_data.payType AS pay_type',
            `IF(tna_data.adjustment = 1, 'Yes', 'No') AS adjustment`,
            `IF(tna_data.payCorrection = 1, 'Yes', 'No') AS pay_correction`,
            'tna_data.standardPay AS standard_pay',
            'tna_data.overtimePay AS overtime_pay',
            'tna_data.standardCharge AS standard_charges',
            'tna_data.overtimeCharge AS overtime_charges',
            'tna_data.totalCharge AS total_charges'
        ])
        .orderBy(sortBy, sortType);

    if (client_id) query.andWhere('tna_data.clientId = :clientId', { clientId: client_id });
    if (agency_id) query.andWhere('tna_data.agencyId = :agencyId', { agencyId: agency_id });

    return query.getRawMany();
};
