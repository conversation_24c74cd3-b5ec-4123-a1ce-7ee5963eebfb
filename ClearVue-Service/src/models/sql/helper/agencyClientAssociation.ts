import { getRepository } from 'typeorm';
import { AgencyClientAssociation } from '../';
import { Margins } from '../';
import { Not, IsNull } from 'typeorm';

/**
 * create agency association
 */
export const createAgencyAssociation: any = async (transactionalEntityManager, body) => {
    const agencyAssociationRepository = transactionalEntityManager.getRepository(AgencyClientAssociation);
    return await agencyAssociationRepository.save(body);
};

/**
 * update agency association
 */
export const updateAgencyAssociation: any = async (id, body) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    body.updatedAt = new Date();
    return await agencyAssociationRepository.update({ id }, body);
};

/**
 * get agency association list
 */
export const getAgencyAssociationList: any = async (page, limit, sort_by, sort_type, whereClause, whereClauseValue) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    const response = await agencyAssociationRepository
        .createQueryBuilder('agency_client_association')
        .innerJoin('agency_client_association.client', 'client')
        .innerJoin('agency_client_association.agency', 'agency')
        .leftJoin('margins', 'margins', 'margins.agency_client_association_id = agency_client_association.id')
        .leftJoin('agency_client_association.agencySiteRestrictions', 'site_restrictions')
        .select([
            'agency_client_association.id AS id',
            'margins.margin AS margin',
            'margins.overtime_margin AS overtime_margin',
            'margins.transport_fee AS transport_fee',
            'margins.ssp AS ssp',
            'margins.induction_training_margin AS induction_training_margin',
            'margins.training_margin AS training_margin',
            'margins.bh_margin AS bh_margin',
            'margins.nsp_margin AS nsp_margin',
            'margins.supervisor_standard_margin AS supervisor_standard_margin',
            'margins.supervisor_overtime_margin AS supervisor_overtime_margin',
            'margins.supervisor_permanent_margin AS supervisor_permanent_margin',
            'margins.suspension_margin AS suspension_margin',
            'agency_client_association.is_restricted AS is_restricted',
            'agency_client_association.comment_restricted AS comment_restricted',
            'agency_client_association.total_assignment_pay AS total_assignment_pay',
            'agency_client_association.holiday_activation AS holiday_activation',
            'agency_client_association.holiday_cost_removed AS holiday_cost_removed',
            'agency_client_association.currency AS currency',
            'client.id AS client_id',
            'client.name AS client_name',
            'client.city AS client_city',
            'client.sector AS client_sector',
            'client.country AS client_country',
            'agency.id AS agency_id',
            'agency.name AS agency_name',
            'agency.city AS agency_city',
            'agency.country AS agency_country',
            'COUNT(DISTINCT site_restrictions.id) AS restricted_sites_count'
        ])
        .where(whereClause, whereClauseValue)
        .andWhere('margins.site_id IS NULL AND margins.los IS NULL')
        .groupBy('agency_client_association.id')
        .orderBy(sort_by, sort_type)
        .addOrderBy('agency_client_association.id', sort_type.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)
        .execute();

    response["count"] = await agencyAssociationRepository.createQueryBuilder('agency_client_association')
        .leftJoin('margins', 'margins', 'margins.agency_client_association_id = agency_client_association.id')
        .where(whereClause, whereClauseValue)
        .andWhere('margins.site_id IS NULL AND margins.los IS NULL')
        .getCount();

    return response;
};

/**
 * get agency association By Id
 */
export const getAgencyAssociationById: any = async (id) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    return await agencyAssociationRepository.createQueryBuilder("agency_client_association")
        .where("agency_client_association.id = :id", { id })
        .select(['id', 'client_id', 'agency_id', 'currency'])
        .getRawOne();
};

/**
 * get agency association By agencyId and clientId
 */
export const getAgencyAssociationByAgencyIdAndClientId: any = async (agencyId, clientId) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    return await agencyAssociationRepository.createQueryBuilder("agency_client_association")
        .leftJoin('agency_client_association.margins', 'margins')
        .where("agency_client_association.agency_id = :agencyId AND agency_client_association.client_id = :clientId", { agencyId, clientId })
        .andWhere('margins.site_id IS NULL AND margins.los IS NULL')
        .orderBy("agency_client_association.id", "DESC")
        .limit(1)
        .select([
            'agency_client_association.id as id',
            'margins.margin as margin',
            'margins.overtime_margin as overtime_margin',
            'margins.transport_fee as transport_fee',
            'margins.ssp as ssp',
            'margins.induction_training_margin as induction_training_margin',
            'margins.training_margin as training_margin',
            'margins.bh_margin as bh_margin',
            'margins.nsp_margin as nsp_margin',
            'margins.supervisor_standard_margin as supervisor_standard_margin',
            'margins.supervisor_overtime_margin as supervisor_overtime_margin',
            'margins.supervisor_permanent_margin as supervisor_permanent_margin',
            'margins.suspension_margin as suspension_margin',
            'agency_client_association.total_assignment_pay as total_assignment_pay',
            'agency_client_association.holiday_activation as holiday_activation',
            'agency_client_association.holiday_cost_removed as holiday_cost_removed'
        ])
        .getRawOne();
};

export const getAgencyAssociationWithDetailedMarginsByAgencyIdAndClientId = async (agencyId, clientId, siteId) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    const marginsRepository = getRepository(Margins);

    // Get the agency association
    const association = await agencyAssociationRepository.findOne({
        where: {
            agencyId,
            clientId
        },
        select: [
            'id',
            'totalAssignmentPay',
            'holidayActivation',
            'holidayCostRemoved'
        ]
    });

    if (!association) return null;
    // Get all margins for this association
    const margins = await marginsRepository.find({
        where: [
            { agencyClientAssociationId: association.id, siteId: null, los: null },  // Default association margin
            { agencyClientAssociationId: association.id, siteId: siteId, los: null }, // Site specific default margin
            { agencyClientAssociationId: association.id, siteId: siteId, los: Not(IsNull()) } // Site and LOS specific margins
        ],
        order: {
            siteId: "ASC",
            los: "ASC"
        }
    });
    // Add margins to association object
    return {
        ...association,
        margins
    };
};

/**
 * get agency association By clientId
 */
export const getAgencyAssociationByClientId: any = async (clientId) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    return await agencyAssociationRepository.createQueryBuilder("agency_client_association")
        .innerJoin('agency_client_association.agency', 'agency')
        .where("agency_client_association.client_id = :clientId", { clientId })
        .select(['agency_client_association.id as id', 'agency.id as agency_id', 'agency.name as agency_name'])
        .getRawMany();
};

/**
 * get agency association with agencyName and clientName
 */
export const getAgencyAssociationByAgencyNameAndClientName: any = async (agencyName, clientName) => {
    const agencyAssociationRepository = getRepository(AgencyClientAssociation);
    return await agencyAssociationRepository.createQueryBuilder("agency_client_association")
        .innerJoin('agency_client_association.client', 'client')
        .innerJoin('agency_client_association.agency', 'agency')
        .where("agency.name = :agencyName AND client.name = :clientName", { agencyName, clientName })
        .select(['agency_client_association.agency_id AS agencyId', 'client.name AS clientName', 'agency_client_association.client_id AS clientId', 'agency.name AS agencyName'])
        .getRawOne();
};

/**
 * get associated clients by agencyId.
 */
export const getAssociatedClients: any = async (agencyId) => {
    return await getRepository(AgencyClientAssociation).find({
        select: ['clientId'], where: {
            agencyId
        }
    })
};


/**
 * get associated agencies by clientId.
 */
export const getAssociatedAgencies: any = async (clientId) => {
    return await getRepository(AgencyClientAssociation).find({
        select: ['agencyId'], where: {
            clientId
        }
    })
};

