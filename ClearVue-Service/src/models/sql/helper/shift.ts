import { getRepository, In, Not, Repository } from 'typeorm';
import { Shift } from '../entities/Shift';
import { ShiftSiteAssociation } from '../entities/ShiftSiteAssociation';

export const addShiftHelper: any = async (data) => {
    const shiftRepository = getRepository(Shift);
    let response = await shiftRepository.insert(data);
    return response.generatedMaps[0];
};

export const updateShift: any = async (id, body) => {
    const shiftRepository = getRepository(Shift);
    return await shiftRepository.update({ id }, body);
};

export const getShiftHelper: any = async (whereClause, whereClauseValue) => {
    const shiftRepository = getRepository(Shift);
    let selectQuery = shiftRepository.createQueryBuilder('shift')
        .leftJoin(
            qb => (
                qb.subQuery()
                    .from(ShiftSiteAssociation, 'ssa')
                    .select([
                        'ssa.shift_id AS shift_id',
                        `GROUP_CONCAT(CONCAT(site.id, ":", site.name, ":", region.id, ":", region.name, ":", site.region_id) SEPARATOR ";") AS associations`
                    ])
                    .leftJoin('ssa.site', 'site')
                    .leftJoin('site.region', 'region')
                    .where('site.client_id = :clientId', { clientId: whereClauseValue.client_id })
                    .groupBy('ssa.shift_id')
            ),
            'subQ',
            'subQ.shift_id = shift.id')
        .leftJoin('shift.shiftSiteAssociations', 'shiftSiteAssociations')
        .leftJoin('shiftSiteAssociations.site', 'site')
        .leftJoin('site.region', 'region')
        .select([
            'DISTINCT(shift.id) AS id',
            'shift.name AS name',
            'shift.created_by AS createdBy',
            'shift.created_at AS createdAt',
            'shift.updated_by AS updatedBy',
            'shift.updated_at AS updatedAt',
            'shift.client_id AS clientId',
            'subQ.associations',
        ])
        .where(whereClause, whereClauseValue)
        .groupBy('shift.id')
        .orderBy('shift.name', 'ASC');

    const results = await selectQuery.getRawMany();

    // Process associations
    const processedResults = results.map(result => {
        const associations = result.associations ? result.associations.split(';').map(association => {
            const [siteId, siteName, regionId, regionName, regionIdFromSite] = association.split(':');
            return {
                siteId,
                siteName,
                regionId,
                regionName,
            };
        }) : [];

        return {
            ...result,
            associations,
        };
    });

    return processedResults;
};


export const getShiftsByNames: any = async (names, clientId) => {
    return await getRepository(Shift).createQueryBuilder('shift')
        .select(['shift.id as id', 'LOWER(shift.name) as name'])
        .where('shift.name IN (:...names)', { names })
        .andWhere(`shift.client_id =  ${clientId}`)
        .getRawMany();
};

/**
 * get shift By whereclase
 */
export const getShiftByWhereClause: any = async (whereClause) => {
    const shiftRepository = getRepository(Shift);
    return await shiftRepository.findOne(
        {
            where: whereClause,
            select: ['id']
        }
    );
};

/**
 * get shifts by where clause
 */
export const getShiftNames: any = async (shiftIds) => {
    return await getRepository(Shift).find(
        {
            where: `id IN (${shiftIds.join(",")})`,
            select: ['id', 'name']
        }
    );
};


export const getShifWithLowerCase: any = async (client_id) => {
    let selectQuery = getRepository(Shift).createQueryBuilder('shift')
        .select(['shift.id AS id', 'LOWER(shift.name) AS name'])
        .where(`shift.client_id = :client_id`, { "client_id": client_id })
        .orderBy('shift.name', 'ASC')
    return selectQuery.getRawMany();
};

/**
 * get shift By Id
 */
export const getShiftById: any = async (id) => {
    const shftRepository = getRepository(Shift);
    return await shftRepository.createQueryBuilder("shft")
        .where("shft.id = :id", { id })
        .select(['name', 'client_id'])
        .getRawOne();
};


/**
 * Queries for shift IDs based on provided names and client_id, 
 *      Returns mapping of found names to their IDs and a list of names not found in the database.
 */
export const getShiftIdsListByNames: any = async (names, client_id) => {
    const shiftRepository = getRepository(Shift);
    const foundShifts = await shiftRepository.find({
        where: {
            name: In(names),
            clientId: client_id
        },
        select: ['id', 'name']
    });

    const foundNames = foundShifts.map((shift) => shift.name.toLowerCase());

    const notFoundNames = names.filter((name) => !foundNames.includes(name));

    const FoundShiftNameIdMapping = Object.fromEntries(foundShifts.map(obj => [obj.name.toLowerCase(), obj.id]));

    return {
        shiftNameIdMappings: FoundShiftNameIdMapping,
        notFoundShiftNames: notFoundNames
    };
};


/**
 * create shift_site_association 
 */
export const createShiftSiteAssociation: any = async (bodyArray) => {
    const response = await getRepository(ShiftSiteAssociation).insert(bodyArray);
    return response.identifiers;
};

export const getShiftSitSiteAssociationsByShiftSitId = async (shiftId) => {
    const dsaRepository = getRepository(ShiftSiteAssociation);
    return await dsaRepository.find({ where: { shiftId } });
};

export const removeShiftSitSiteAssociations = async (shiftId, siteIdsToRemove) => {
    const dsaRepository = getRepository(ShiftSiteAssociation);

    // Delete associations where shiftSitId matches and siteId is in siteIdsToRemove
    await dsaRepository
        .createQueryBuilder()
        .delete()
        .from(ShiftSiteAssociation)
        .where('shiftId = :shiftId', { shiftId })
        .andWhere('siteId IN (:...siteIds)', { siteIds: siteIdsToRemove })
        .execute();
};

export const getAssociatedShiftsWithSite = async (client_id, site_id?) => {
    const departmentRepository = getRepository(ShiftSiteAssociation);

    let query = departmentRepository
        .createQueryBuilder('ssa')
        .innerJoin('ssa.shift', 'shift')
        .where('shift.client_id = :client_id', { client_id });

    if (site_id) query = query.andWhere('ssa.site_id = :site_id', { site_id });

    let shifts = await query.getMany();
    return shifts.map(obj => obj.shiftId)
};