import { getRepository } from 'typeorm';
import { TimeAndAttendanceData } from '../entities/TimeAndAttendanceData';
import { Workers } from '../entities/Workers';
import { WorkerPerformanceData } from '../entities/WorkerPerformanceData';
import { getDurationBetweenDates, getWeekRangeForGivenDate } from '../../../utils';
import { Booking } from '../entities/Booking';
import { BookingAssociation } from '../entities/BookingAssociation';
import { BookingAssociationHistory } from '../entities/BookingAssociationHistory';
import { Site } from '../entities/Site';
import { JobAssociation } from '../entities/JobAssociation';
import { Job } from '../entities/Job';
import { config } from '../../../configurations';
import { PayTypes, WorkerTypes } from '../../../common';
import { PayrollMeta } from '../entities/PayrollMeta';
import { HolidayPayrollSummary } from '../entities/HolidayPayrollSummary';

export const buildPerformanceWhereCondition = async (
    workerIds: number[],
    clientId: number,
    agencyId: number | null,
    siteIds: unknown[],
    startDate: string,
    endDate: string
) => {
    let whereCondition = 'wpd.worker_id IN (:workerIds) AND wpd.client_id = :clientId AND wpd.start_date >= :startDate AND wpd.end_date <= :endDate AND wpd.performance_number > 1';

    // Add site condition based on whether we have siteIds
    if (siteIds.length > 0) {
        whereCondition += ' AND wpd.site_id IN (:siteIds)';
    }

    // Add agency condition if agencyId is provided
    if (agencyId) {
        whereCondition += ' AND wpd.agency_id = :agencyId';
    }

    const whereConditionValue = {
        workerIds,
        clientId,
        agencyId,
        siteIds,
        startDate,
        endDate
    };

    return {
        whereCondition,
        whereConditionValue
    };
};

export const getWorkersShiftCounts = async (
    startDate: string,
    endDate: string,
    clientId: number,
    filters: {
        regionId?: number,
        siteId?: number,
        agencyId?: number,
        departmentId?: number,
        shiftId?: number
    }
) => {
    const { regionId, siteId, agencyId, departmentId, shiftId } = filters;

    let whereClause = `start_date >= :startDate 
        AND end_date <= :endDate 
        AND client_id = :clientId`;

    const whereParams: any = {
        startDate,
        endDate,
        clientId
    };

    if (regionId) {
        whereClause += ` AND region_id = :regionId`;
        whereParams.regionId = regionId;
    }
    if (siteId) {
        whereClause += ` AND site_id = :siteId`;
        whereParams.siteId = siteId;
    }
    if (agencyId) {
        whereClause += ` AND agency_id = :agencyId`;
        whereParams.agencyId = agencyId;
    }
    if (departmentId) {
        whereClause += ` AND department_id = :departmentId`;
        whereParams.departmentId = departmentId;
    }
    if (shiftId) {
        whereClause += ` AND shift_id = :shiftId`;
        whereParams.shiftId = shiftId;
    }

    const results = await getRepository(TimeAndAttendanceData)
        .createQueryBuilder('data')
        .select([
            'data.worker_id AS workerId',
            `(IF(SUM(day_1) > 0, 1, 0) + 
              IF(SUM(day_2) > 0, 1, 0) + 
              IF(SUM(day_3) > 0, 1, 0) + 
              IF(SUM(day_4) > 0, 1, 0) + 
              IF(SUM(day_5) > 0, 1, 0) + 
              IF(SUM(day_6) > 0, 1, 0) + 
              IF(SUM(day_7) > 0, 1, 0)) AS shiftCount`
        ])
        .where(whereClause, whereParams)
        .andWhere(`data.pay_type NOT IN (:...pay_types)`, { "pay_types": [PayTypes.STANDARD_BONUS, PayTypes.SPECIAL_BONUS, PayTypes.SP, PayTypes.NSP, PayTypes.HOLIDAY, PayTypes.EXPENSES] })
        .groupBy('data.worker_id')
        .having('shiftCount >= 1')
        .getRawMany();

    return results;
};

export const getWorkersGroupedByShiftCounts = async (
    startDate: string,
    endDate: string,
    clientId: number,
    filters: {
        regionId?: number,
        siteId?: number,
        agencyId?: number,
        departmentId?: number,
        shiftId?: number
    }
) => {
    const results = await getWorkersShiftCounts(startDate, endDate, clientId, filters);

    // Group workers by shift count
    const groupedWorkers = Array.from({ length: 7 }, (_, i) => ({
        shiftCount: i + 1,
        workers: []
    }));

    results.forEach(result => {
        const count = Math.min(parseInt(result.shiftCount), 7);
        groupedWorkers[count - 1].workers.push(result.workerId);
    });

    return groupedWorkers;
};


export const getPerformanceNewStartersGraphHelper = async (
    params: {
        start_date: string;
        end_date: string;
        client_id: number;
        agency_id?: number;
        siteIds: number[];
        department_id?: number;
        shift_id?: number;
    },
    weekday_start: number,
    twelveWeeksAgoWeekStartDate?: string
) => {
    const { start_date, end_date, client_id, agency_id, siteIds, department_id, shift_id } = params;

    // Step 1: Filter worker_ids from both tables
    const workerPerformanceRepo = getRepository(WorkerPerformanceData);
    const timeAttendanceRepo = getRepository(TimeAndAttendanceData);

    const workerPerformanceIds = await workerPerformanceRepo
        .createQueryBuilder('wpd')
        .select('wpd.worker_id')
        .where('wpd.start_date >= :start_date', { start_date })
        .andWhere('wpd.end_date <= :end_date', { end_date })
        .andWhere('wpd.client_id = :client_id', { client_id })
        .andWhere(siteIds.length ? 'wpd.site_id IN (:siteIds)' : '1=1', { siteIds })
        .andWhere(agency_id ? 'wpd.agency_id = :agency_id' : '1=1', { agency_id })
        .andWhere('wpd.performance_number > 1') // WARNING: This query currently assumes a single-week date range.
        // If multi-week ranges are introduced, the logic for calculating average performance
        // (specifically how weeks with <=1 or >1 days are handled) will need revision to ensure accuracy. (Applies to all 3 Performance reporting graphs and boxes data)
        .getRawMany();

    const timeAttendanceIds = await timeAttendanceRepo
        .createQueryBuilder('tad')
        .select('tad.worker_id')
        .where('tad.start_date >= :start_date', { start_date })
        .andWhere('tad.end_date <= :end_date', { end_date })
        .andWhere('tad.client_id = :client_id', { client_id })
        .andWhere(siteIds.length ? 'tad.site_id IN (:siteIds)' : '1=1', { siteIds })
        .andWhere(agency_id ? 'tad.agency_id = :agency_id' : '1=1', { agency_id })
        .andWhere(department_id ? 'tad.department_id = :department_id' : '1=1', { department_id })
        .andWhere(shift_id ? 'tad.shift_id = :shift_id' : '1=1', { shift_id })
        .getRawMany();

    const commonWorkerIds = workerPerformanceIds
        .map(w => w.worker_id)
        .filter(id => timeAttendanceIds.map(t => t.worker_id).includes(id));

    if (commonWorkerIds.length === 0) {
        return [];
    }

    // Step 2: Further filter by assignment_date
    const workersRepo = getRepository(Workers);
    let workersQuery = workersRepo
        .createQueryBuilder('workers')
        .where('workers.id IN (:...workerIds)', { workerIds: commonWorkerIds });

    // Only apply date filter if twelveWeeksAgoWeekStartDate is provided
    if (twelveWeeksAgoWeekStartDate) {
        workersQuery = workersQuery.andWhere('workers.assignment_date BETWEEN :twelveWeeksAgoWeekStartDate AND :end_date', {
            twelveWeeksAgoWeekStartDate,
            end_date
        });
    }

    const filteredWorkers = await workersQuery.getMany();
    if (filteredWorkers.length === 0) {
        return [];
    }

    if (twelveWeeksAgoWeekStartDate) {
        // Original 12-week grouping logic
        const groupedWorkers = Array.from({ length: 12 }, () => []);
        filteredWorkers.forEach(worker => {
            const dates = getWeekRangeForGivenDate(worker.assignmentDate, weekday_start);
            const assignmentWeekStart = dates.start_date;
            const lengthOfService = getDurationBetweenDates(assignmentWeekStart, end_date);
            const weekDiff = Math.ceil(lengthOfService.asWeeks);

            if (weekDiff > 0 && weekDiff <= 12) {
                groupedWorkers[weekDiff - 1].push(worker);
            }
        });

        return groupedWorkers;
    } else {
        // Group workers into 6 time periods (6mo-5yr)
        const groupedWorkers = Array.from({ length: 6 }, () => []);

        filteredWorkers.forEach(worker => {
            const dates = getWeekRangeForGivenDate(worker.assignmentDate, weekday_start);
            const assignmentWeekStart = dates.start_date;
            const lengthOfService = getDurationBetweenDates(assignmentWeekStart, end_date);

            if (lengthOfService.asYears >= 0 && lengthOfService.asYears < 1) {
                groupedWorkers[0].push(worker);
            } else if (lengthOfService.asYears >= 1 && lengthOfService.asYears < 2) {
                groupedWorkers[1].push(worker);
            } else if (lengthOfService.asYears >= 2 && lengthOfService.asYears < 3) {
                groupedWorkers[2].push(worker);
            } else if (lengthOfService.asYears >= 3 && lengthOfService.asYears < 4) {
                groupedWorkers[3].push(worker);
            } else if (lengthOfService.asYears >= 4 && lengthOfService.asYears < 5) {
                groupedWorkers[4].push(worker);
            } else if (lengthOfService.asYears >= 5 && lengthOfService.asYears < 6) {
                groupedWorkers[5].push(worker);
            }
        });

        return groupedWorkers;
    }
};

export const getAverageShiftCounts = async (params, groupedWorkers) => {
    // const { startDate, endDate, clientId, regionId, siteId, agencyId, departmentId, shiftId } = params;
    const { start_date, end_date, client_id, region_id, site_id, agency_id, department_id, shift_id } = params;

    // Get the shift counts
    const shiftCounts = await getWorkersShiftCounts(start_date, end_date, client_id, {
        regionId: region_id,
        siteId: site_id,
        agencyId: agency_id,
        departmentId: department_id,
        shiftId: shift_id
    });

    // Group the shift counts by the 12 groups and calculate averages
    const averageShiftCounts = groupedWorkers.map((group, index) => {

        if (group.length === 0) return 0; // Handle empty groups

        const groupShiftCounts = group
            .map(worker => {
                const found = shiftCounts.find(shift => shift.workerId === worker.id);
                return found ? Number(found.shiftCount) : null; // Return null instead of 0
            })
            .filter(count => count !== null); // Filter out workers not found in shiftCounts

        if (groupShiftCounts.length === 0) return 0; // Handle case where no workers have shifts

        const totalShifts = groupShiftCounts.reduce((sum, count) => sum + count, 0);
        return totalShifts / groupShiftCounts.length;
    });

    return averageShiftCounts;
};

export const getBookingGraphData = async (
    clientId: number,
    filters: {
        regionId?: number,
        siteId?: number,
        agencyId?: number,
        departmentId?: number,
        shiftId?: number,
        startDate: string,
        endDate: string
    }
) => {
    const { regionId, siteId, agencyId, departmentId, shiftId, startDate, endDate } = filters;


    // Build base query conditions
    let whereCondition = `booking.client_id = :clientId`;
    const whereParams: any = { clientId };

    if (regionId) {
        whereCondition += ` AND booking.region_id = :regionId`;
        whereParams.regionId = regionId;
    }
    if (siteId) {
        whereCondition += ` AND booking.site_id = :siteId`;
        whereParams.siteId = siteId;
    }
    if (departmentId) {
        whereCondition += ` AND booking.department_id = :departmentId`;
        whereParams.departmentId = departmentId;
    }
    if (shiftId) {
        whereCondition += ` AND booking.shift_type_id = :shiftId`;
        whereParams.shiftId = shiftId;
    }

    // Updated query to include site_name and department_name
    const bookingsData = await getRepository(Booking)
        .createQueryBuilder('booking')
        .select([
            'booking.*',
            'site.name as site_name',
            'shift.name as shift_name',
            'department.name as department_name'
        ])
        .leftJoin('site', 'site', 'booking.site_id = site.id')
        .leftJoin('shift', 'shift', 'booking.shift_type_id = shift.id')
        .leftJoin('departments', 'department', 'booking.department_id = department.id')
        .where(whereCondition, whereParams)
        .andWhere('booking.start_date >= :startDate', { startDate })
        .andWhere('booking.end_date <= :endDate', { endDate })
        .getRawMany();


    const result = [];
    const emptyList = [0, 0, 0, 0, 0, 0, 0];

    for (const booking of bookingsData) {

        // Get booking associations
        let associationsQuery = getRepository(BookingAssociation)
            .createQueryBuilder('ba')
            .where('ba.booking_id = :bookingId', { bookingId: booking.id });

        if (agencyId) {
            associationsQuery = associationsQuery.andWhere('ba.agency_id = :agencyId', { agencyId });
        }

        const bookingAssociations = await associationsQuery.getMany();

        // Get historical data
        let historyQuery = getRepository(BookingAssociationHistory)
            .createQueryBuilder('bah')
            .where('bah.booking_id = :bookingId', { bookingId: booking.id });

        if (agencyId) {
            historyQuery = historyQuery.andWhere('bah.agency_id = :agencyId', { agencyId });
        }

        const historicalData = await historyQuery
            .orderBy('bah.archived_at', 'ASC')
            .groupBy('bah.booking_association_id')
            .getMany();


        // Calculate total values
        const totalRequestedWorkers = bookingAssociations.reduce((sum, ba) => sum + (ba.requestedWorkersTotal || 0), 0);
        const totalHistoricalRequestedWorkers = historicalData.reduce((sum, h) => sum + (h.requestedWorkersTotal || 0), 0);
        const totalFulfilledWorkers = bookingAssociations.reduce((sum, ba) => sum + (ba.fulfilledWorkersTotal || 0), 0);


        // Skip if all values are 0
        if (totalRequestedWorkers === 0 && totalHistoricalRequestedWorkers === 0 && totalFulfilledWorkers === 0) {
            continue;
        }

        const total_requested_workers = historicalData.length > 0 ? totalHistoricalRequestedWorkers : totalRequestedWorkers;
        const total_amended_workers = totalRequestedWorkers;

        // Process data for graph
        const graphData = {
            booking_id: booking.id,
            site_name: booking.site_name,
            shift_name: booking.shift_name,
            department_name: booking.department_name,
            total_requested_workers: total_requested_workers,
            total_amended_workers: total_amended_workers,
            total_fulfilled_workers: totalFulfilledWorkers,
            fulfilment_percentage_requested: total_requested_workers > 0 ? Math.min(parseFloat(((totalFulfilledWorkers / total_requested_workers) * 100).toFixed(2)), 100) : 0,
            fulfilment_percentage_amended: total_amended_workers > 0 ? Math.min(parseFloat(((totalFulfilledWorkers / total_amended_workers) * 100).toFixed(2)), 100) : 0,
            requested: sumDayValues(
                historicalData.length > 0
                    ? historicalData.map(h => h.requestedWorkersHeads)
                    : bookingAssociations.map(ba => ba.requestedWorkersHeads)
            ),
            amended: sumDayValues(bookingAssociations.map(ba => ba.requestedWorkersHeads)),
            fulfilled: sumDayValues(bookingAssociations.map(ba => ba.fulfilledWorkersHeads)),
        };

        result.push(graphData);
    }

    return result;
};

// Helper function to sum day values from multiple JSON objects - returns array of 7 values
const sumDayValues = (jsonArray: any[]): number[] => {
    const result = [0, 0, 0, 0, 0, 0, 0];

    jsonArray.forEach(json => {
        if (!json) return;
        const parsed = typeof json === 'string' ? JSON.parse(json) : json;
        for (let i = 1; i <= 7; i++) {
            result[i - 1] += parseInt(parsed[i.toString()] || 0);
        }
    });

    return result;
};

export const clientWiseShiftFulFillment = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string,
    forPastWeek: boolean = false,
) => {
    // sub query condition
    const subQueryWhere = {
        condition: "b.client_id = :clientId",
        values: { clientId },
    };
    // main query condition
    const where = {
        condition: "s.client_id = :clientId",
        values: { clientId },
    };

    if (siteId) {
        where.condition += " AND s.id= :siteId";
        where.values["siteId"] = siteId;
    }
    if (agencyId) {
        subQueryWhere.condition += " AND ba.agency_id = :agencyId"
        subQueryWhere.values['agencyId'] = agencyId
    }
    if (regionId) {
        subQueryWhere.condition += " AND  b.region_id = :regionId";
        subQueryWhere.values["regionId"] = regionId;

        where.condition += " AND s.region_id = :regionId";
        where.values["regionId"] = regionId;
    }

    if (startDate && endDate) {
        subQueryWhere.condition +=
            " AND b.start_date >= :startDate AND b.end_Date <= :endDate";
        subQueryWhere.values["startDate"] = startDate;
        subQueryWhere.values["endDate"] = endDate;
    }
    const siteRepository = getRepository(Site);

    const query = siteRepository
        .createQueryBuilder("s")
        .leftJoinAndSelect(
            (qb) =>
                qb
                    .subQuery()
                    .select([
                        "subQ.site_id",
                        "IFNULL(SUM(subQ.requested_total), 0) AS requested_total",
                        "IFNULL(SUM(subQ.fulfilled_total), 0) AS fulfilled_total",
                        "IFNULL(SUM(subQ.requested_supervisors_total), 0) AS requested_supervisors_total",
                        "IFNULL(SUM(subQ.fulfilled_supervisors_total), 0) AS fulfilled_supervisors_total",
                    ])
                    .from(
                        (subQuery) =>
                            subQuery
                                .select([
                                    "b.site_id",
                                    "requested_total",
                                    "fulfilled_total",
                                    "requested_supervisors_total",
                                    "fulfilled_supervisors_total",
                                ])
                                .from(Booking, "b")
                                .innerJoin(BookingAssociation, "ba", "ba.booking_id = b.id")
                                .where(subQueryWhere.condition, subQueryWhere.values)
                                .orderBy("b.site_id", "DESC"),
                        "subQ"
                    )
                    .groupBy("subQ.site_id"),
            "sb",
            "sb.site_id = s.id"
        )
        .where(where.condition, where.values).select([
            "s.id",
            "s.name",
            `IFNULL(sb.requested_total, 0) AS ${forPastWeek ? "past_requested_total" : "requested_total"}`,
            `IFNULL(sb.fulfilled_total, 0) AS ${forPastWeek ? "past_fulfilled_total" : "fulfilled_total"}`,
            `IFNULL(sb.requested_supervisors_total, 0) AS ${forPastWeek ? "past_requested_supervisors_total" : "requested_supervisors_total"}`,
            `IFNULL(sb.fulfilled_supervisors_total, 0) AS ${forPastWeek ? "past_fulfilled_supervisors_total" : "fulfilled_supervisors_total"}`,
        ]);

    return query.getRawMany();
};

export const siteWiseSpendNHours = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string,
    forPastWeek: boolean = false
) => {
    const siteRepository = getRepository(Site);

    const query = siteRepository
        .createQueryBuilder("s")
        .leftJoin(
            (qb) => {
                const subQuery = qb
                    .subQuery()
                    .select([
                        "taad.client_id",
                        "taad.site_id",
                        "CASE WHEN taad.pay_type != :expensesPayType THEN taad.weekly_hours ELSE 0 END as weekly_hours",
                        "taad.total_charge",
                        "hps.wtr_cost",
                    ])
                    .from(TimeAndAttendanceData, "taad")
                    .innerJoin(Workers, "w", "taad.worker_id = w.id")
                    .leftJoin(
                        PayrollMeta,
                        "pm",
                        "taad.time_and_attendance_id = pm.time_and_attendance_id"
                    )
                    .leftJoin(
                        HolidayPayrollSummary,
                        "hps",
                        "pm.id = hps.payroll_meta_id AND w.id = hps.worker_id"
                    )
                    .where("taad.client_id = :clientId", { clientId })
                    .andWhere("taad.start_date >= :startDate", { startDate })
                    .andWhere("taad.end_date <= :endDate", { endDate })
                    .andWhere("w.type = :workerType", {
                        workerType: config.TEMPORARY_WORKER,
                    })
                    .setParameter("expensesPayType", PayTypes.EXPENSES); // Exclude expenses pay type in hours
                if (agencyId) {
                    subQuery.andWhere("taad.agency_id = :agencyId", { agencyId });
                }
                if (regionId) {
                    subQuery.andWhere("taad.region_id = :regionId", { regionId });
                }
                return subQuery;
            },
            "sd",
            "sd.site_id = s.id"
        )
        .where("s.client_id = :clientId", { clientId });

    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }

    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }

    query.groupBy("s.id").select([
        "s.id",
        "s.name",
        `IFNULL(SUM(sd.total_charge), 0) AS ${forPastWeek ? "past_spend" : "spend"}`,
        `IFNULL(SUM(sd.weekly_hours), 0) AS ${forPastWeek ? "past_hours" : "hours"}`,
        `IFNULL(SUM(sd.wtr_cost), 0) AS ${forPastWeek ? "past_wtr_cost" : "wtr_cost"}`,
    ]);

    return query.getRawMany();
};
export const siteWiseOverSpendNHours = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string
) => {
    const siteRepository = getRepository(Site);

    const query = siteRepository
        .createQueryBuilder("s")
        .select([
            "s.id",
            "s.name",
            "IFNULL(SUM(sd.total_charge), 0) AS over_spend",
            "IFNULL(SUM(sd.weekly_hours), 0) AS over_hours",
            "IFNULL(SUM(sd.wtr_cost), 0) AS over_wtr_cost",
        ])
        .leftJoin(
            (qb) => {
                const subQuery = qb
                    .subQuery()
                    .select([
                        "taad.client_id",
                        "taad.site_id",
                        "taad.weekly_hours",
                        "taad.total_charge",
                        "hps.wtr_cost",
                    ])
                    .from(TimeAndAttendanceData, "taad")
                    .innerJoin(Workers, "w", "taad.worker_id = w.id")
                    .leftJoin(
                        PayrollMeta,
                        "pm",
                        "taad.time_and_attendance_id = pm.time_and_attendance_id"
                    )
                    .leftJoin(
                        HolidayPayrollSummary,
                        "hps",
                        "pm.id = hps.payroll_meta_id AND w.id = hps.worker_id"
                    )
                    .where("taad.client_id = :clientId", { clientId })
                    .andWhere("taad.start_date >= :startDate", { startDate })
                    .andWhere("taad.end_date <= :endDate", { endDate })
                    .andWhere("taad.pay_type = :payType", { payType: PayTypes.OVERTIME }) // Automatically exclude expenses pay type in hours;
                    .andWhere("w.type = :workerType", {
                        workerType: config.TEMPORARY_WORKER,
                    });
                if (agencyId) {
                    subQuery.andWhere("taad.agency_id = :agencyId", { agencyId });
                }
                if (regionId) {
                    subQuery.andWhere("taad.region_id = :regionId", { regionId });
                }
                return subQuery;
            },
            "sd",
            "sd.site_id = s.id"
        )
        .where("s.client_id = :clientId", { clientId });

    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }

    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }

    query.groupBy("s.id");

    return query.getRawMany();
};
const calculateAveHours = (hoursArr: Array<any>, workersArr: Array<any>, forPastWeek: boolean) => {
    const workerCountMap = workersArr.reduce((map, workerCount) => {
        map[workerCount.s_id] = workerCount;
        return map;
    }, []);

    return hoursArr.map((workingHours) => {
        let totalWorkerCount = +(
            workerCountMap[workingHours.s_id][`${forPastWeek ? 'past_worker_count' : 'worker_count'}`] ?? "0"
        );

        if (forPastWeek) {
            const { past_weekly_hours: totalWorkingHours, ...otherDetails } = workingHours;
            const aveHours = (+totalWorkingHours && +totalWorkerCount > 0)
                ? +Math.max(+totalWorkingHours / totalWorkerCount, 0).toFixed(2)
                : 0;
            return {
                ...otherDetails,
                past_ave_hours: aveHours,
                past_total_hours: +totalWorkingHours,
                past_total_workers: totalWorkerCount,
            };
        } else {
            const { weekly_hours: totalWorkingHours, ...otherDetails } = workingHours;
            const aveHours = (+totalWorkingHours && totalWorkerCount > 0)
                ? +Math.max(+totalWorkingHours / totalWorkerCount, 0).toFixed(2)
                : 0;
            return {
                ...otherDetails,
                ave_hours: aveHours,
                total_hours: +totalWorkingHours,
                total_workers: totalWorkerCount,
            };

        }
    });
}
export const siteWiseAveHrs = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string,
    forPastWeek: boolean = false
) => {
    const siteRepository = getRepository(Site);

    let query = siteRepository
        .createQueryBuilder("s")
        .leftJoin(
            (qb) => {
                const subQuery = qb
                    .subQuery()
                    .select(["taad.client_id", "taad.site_id", "taad.weekly_hours"])
                    .from(TimeAndAttendanceData, "taad")
                    .where("taad.client_id = :clientId", { clientId })
                    .andWhere("taad.start_date >= :startDate", { startDate })
                    .andWhere("taad.end_date <= :endDate", { endDate })
                    .andWhere("taad.pay_type NOT IN (:...excludedPayTypes)", {
                        excludedPayTypes: [
                            PayTypes.STANDARD_BONUS,
                            PayTypes.SPECIAL_BONUS,
                            PayTypes.SP,
                            PayTypes.NSP,
                            PayTypes.HOLIDAY,
                            PayTypes.EXPENSES
                        ],
                    });

                // Conditionally add the agency filter if provided
                if (agencyId) {
                    subQuery.andWhere("taad.agency_id = :agencyId", { agencyId });
                }
                // Conditionally add the region filter if provided
                if (regionId) {
                    subQuery.andWhere("taad.region_id = :regionId", { regionId });
                }

                return subQuery;
            },
            "sd",
            "sd.site_id = s.id"
        )
        .where("s.client_id = :clientId", { clientId });

    // Apply the region filter to the main query only if `regionId` is provided
    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }

    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }

    query.groupBy("s.id").select([
        "s.id",
        "s.name",
        `IFNULL(SUM(sd.weekly_hours), 0) AS ${forPastWeek ? "past_weekly_hours" : "weekly_hours"}`,
    ]);

    const totalHoursResult = await query.getRawMany();

    // Reused the query varaiable as the last query result has been stored
    query = siteRepository
        .createQueryBuilder("s")
        .leftJoin(
            (qb) => {
                const subQuery = qb
                    .subQuery()
                    .select([
                        "taad.site_id",
                        "COUNT(DISTINCT(taad.worker_id)) AS worker_count", // Fix COUNT issue
                    ])
                    .from(TimeAndAttendanceData, "taad")
                    .where("taad.client_id = :clientId", { clientId })
                    .andWhere("taad.start_date >= :startDate", { startDate })
                    .andWhere("taad.end_date <= :endDate", { endDate })

                // Apply optional agency filter
                if (agencyId) {
                    subQuery.andWhere("taad.agency_id = :agencyId", { agencyId });
                }
                // Apply optional region_id filter
                if (regionId) {
                    subQuery.andWhere("taad.region_id = :regionId", { regionId });
                }

                subQuery.groupBy("taad.site_id, taad.start_date");

                return subQuery;
            },
            "sd",
            "sd.site_id = s.id"
        )
        .where("s.client_id = :clientId", { clientId });

    // Apply optional region_id filter for `site`
    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }

    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }
    query.groupBy("s.id").select([
        "s.id",
        "s.name",
        `IFNULL(SUM(sd.worker_count), 0) AS ${forPastWeek ? "past_worker_count" : "worker_count"}`,
    ]);

    const totalWorkerCountResult = await query.getRawMany();

    return calculateAveHours(totalHoursResult, totalWorkerCountResult, forPastWeek)
};

export const siteWiseLeaverCount = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string
) => {
    const siteRepository = getRepository(Site);

    const query = siteRepository
        .createQueryBuilder("s")
        .select([
            "s.id",
            "s.name",
            "IFNULL(SUM(sq.workers_count), 0) AS leavers_count",
        ])
        .innerJoin(
            (qb) => {
                const subQuery = qb
                    .subQuery()
                    .select([
                        "ja.site_id",
                        "w.id AS worker_id",
                        "COUNT(w.id) AS workers_count",
                    ])
                    .from(Workers, "w")
                    .innerJoin(Job, "j", "j.id = w.job_id")
                    .innerJoin(JobAssociation, "ja", "ja.job_id = j.id")
                    .where("w.client_id = :clientId", { clientId })
                    .andWhere("w.is_active = :isActive", { isActive: 0 })
                    .andWhere("w.in_actived_at >= :startDate", { startDate })
                    .andWhere("w.in_actived_at <= :endDate AND w.type = :workerType", { endDate, workerType: config.TEMPORARY_WORKER });
                if (agencyId) {
                    subQuery.andWhere(`w.agency_id = :agencyId`, { agencyId })
                }

                subQuery.groupBy("w.id");

                return subQuery;
            },
            "sq",
            "sq.site_id = s.id"
        )
        .where("s.client_id = :clientId", { clientId });

    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }
    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }

    query.groupBy("s.id").orderBy("s.id");

    return query.getRawMany();
};
export const siteWiseShiftUtilisation = (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string
) => {
    const siteRepo = getRepository(Site);

    const query = siteRepo.createQueryBuilder('s')
        .leftJoin(
            (qb) =>
                qb
                    .select([
                        'sub2.site_id AS site_id',
                        'sub2.days AS days',
                        'COUNT(sub2.worker_id) AS worker_counts',
                    ])
                    .from((subQb) =>
                        subQb
                            .select([
                                'sub1.site_id AS site_id',
                                'sub1.worker_id AS worker_id',
                                'CAST(AVG(sub1.no_of_days) AS DECIMAL(0)) AS days',
                            ])
                            .from((innerQb) => {
                                const innerQuery = innerQb
                                    .select([
                                        'taad.site_id AS site_id',
                                        'taad.worker_id AS worker_id',
                                        'taad.start_date AS start_date',
                                        `(IF(SUM(day_1) > 0, 1, 0) + IF(SUM(day_2) > 0, 1, 0) + IF(SUM(day_3) > 0, 1, 0) + IF(SUM(day_4) > 0, 1, 0) + IF(SUM(day_5) > 0, 1, 0) + IF(SUM(day_6) > 0, 1, 0) + IF(SUM(day_7) > 0, 1, 0)) AS no_of_days`,
                                    ])
                                    .from('time_and_attendance_data', 'taad')
                                    .leftJoin('workers', 'w', 'taad.worker_id = w.id')
                                    .where('(w.in_actived_at IS NULL OR w.in_actived_at >= :inactiveDate)', {
                                        inactiveDate: startDate,
                                    })
                                    .andWhere('w.start_date <= :workerStartDate', { workerStartDate: endDate })
                                    .andWhere('taad.client_id = :clientId', { clientId })
                                    .andWhere('taad.start_date >= :startDate', { startDate })
                                    .andWhere('taad.end_date <= :endDate', { endDate })
                                    .andWhere('w.type = :workerType', { workerType: config.TEMPORARY_WORKER })
                                if (agencyId) {
                                    innerQuery.andWhere('taad.agency_id = :agencyId', { agencyId })
                                }
                                return innerQuery.groupBy('taad.worker_id, taad.start_date, taad.site_id')
                            },
                                'sub1',
                            )
                            .groupBy('sub1.worker_id, sub1.site_id'),
                        'sub2',
                    )
                    .groupBy('sub2.days, sub2.site_id'),
            'subQ',
            'subQ.site_id = s.id',
        )
        .where('s.client_id = :clientId', { clientId });
    if (regionId) {
        query.andWhere("s.region_id = :regionId", { regionId });
    }

    if (siteId) {
        query.andWhere("s.id = :siteId", { siteId });
    }

    query.groupBy("s.id").orderBy("s.id").select([
        's.id AS s_id',
        's.name AS s_name',
        `COALESCE(
          JSON_OBJECT(
            'day_1', MAX(CASE WHEN subQ.days = 1 THEN subQ.worker_counts ELSE 0 END),
            'day_2', MAX(CASE WHEN subQ.days = 2 THEN subQ.worker_counts ELSE 0 END),
            'day_3', MAX(CASE WHEN subQ.days = 3 THEN subQ.worker_counts ELSE 0 END),
            'day_4', MAX(CASE WHEN subQ.days = 4 THEN subQ.worker_counts ELSE 0 END),
            'day_5', MAX(CASE WHEN subQ.days = 5 THEN subQ.worker_counts ELSE 0 END),
            'day_6', MAX(CASE WHEN subQ.days = 6 THEN subQ.worker_counts ELSE 0 END),
            'day_7', MAX(CASE WHEN subQ.days = 7 THEN subQ.worker_counts ELSE 0 END)
          ), '{}'
        ) AS shift_utilisation`,
    ]);

    return query.getRawMany();
};

export const siteWisePoolUtilisation = async (
    clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string
) => {
    const siteRepository = getRepository(Site);

    const query = siteRepository.createQueryBuilder('s')
        .leftJoin(qb => {
            return qb
                .select('jobAssociation.site_id', 'site_id')
                .addSelect('IFNULL(COUNT(DISTINCT worker.id), 0)', 'total_worker')
                .from(Workers, 'worker')
                .innerJoin('worker.job', 'job')
                .innerJoin('job.jobAssociations', 'jobAssociation')
                .where('worker.client_id = :clientId', { clientId })
                .andWhere('worker.type = :type', { type: WorkerTypes.TEMPORARY })
                .andWhere('worker.assignment_date <= :endDate', { endDate })
                .andWhere('((worker.in_actived_at IS NULL AND worker.is_active = 1) OR (worker.in_actived_at >= :startDate))', { startDate })
                .andWhere(agencyId ? 'worker.agency_id = :agencyId' : '1=1', { agencyId })
                .groupBy('jobAssociation.site_id');
        }, 'total', 'total.site_id = s.id')
        .leftJoin(qb => {
            return qb
                .select('taad.site_id', 'site_id')
                .addSelect('IFNULL(COUNT(DISTINCT taad.worker_id), 0)', 'active_worker')
                .from(TimeAndAttendanceData, 'taad')
                .where('taad.client_id = :clientId', { clientId })
                .andWhere('taad.start_date >= :startDate', { startDate })
                .andWhere('taad.end_date <= :endDate', { endDate })
                .andWhere(agencyId ? 'taad.agency_id = :agencyId' : '1=1', { agencyId })
                .groupBy('taad.site_id');
        }, 'active', 'active.site_id = s.id')
        .select([
            's.id AS s_id',
            's.name AS s_name',
            'COALESCE(total.total_worker, 0) AS pool_total_workers',
            'COALESCE(active.active_worker, 0) AS pool_active_workers',
        ])
        .where('s.client_id = :clientId', { clientId })
        .andWhere(regionId ? 's.region_id = :regionId' : '1=1', { regionId })
        .andWhere(siteId ? 's.id = :siteId' : '1=1', { siteId })
        .orderBy('s.id');

    return query.getRawMany();

};

export const siteWisePerformance = async (clientId: number,
    regionId: number,
    startDate: string,
    endDate: string,
    agencyId: string,
    siteId: string
) => {
    const siteRepo = getRepository(Site);

    const query = siteRepo
        .createQueryBuilder('s')
        .select([
            's.id AS s_id',
            's.name AS s_name',
            'IFNULL(AVG(sq.performance_number), 0) AS performance'
        ])
        .leftJoin(
            qb => {
                const innerQb = qb
                    .select([
                        'wpd.site_id AS site_id',
                        'wpd.worker_id AS worker_id',
                        'IFNULL(AVG(wpd.performance_number), 0) AS performance_number',
                    ])
                    .from(WorkerPerformanceData, 'wpd')
                    .where('wpd.client_id = :clientId', { clientId })
                    .andWhere('wpd.start_date >= :startDate And wpd.end_date <= :endDate', { startDate, endDate })
                    .andWhere('wpd.performance_number > 1')
                if (agencyId) {
                    innerQb.andWhere(`wpd.agency_id = :agencyId`, { agencyId })
                }
                return innerQb.groupBy('wpd.site_id, wpd.worker_id')
            },
            'sq',
            'sq.site_id = s.id'
        )
        .where('s.client_id = :clientId', { clientId })
    if (regionId) query.andWhere('s.region_id = :regionId', { regionId })
    if (siteId) query.andWhere('s.id = :siteId', { siteId })
    query.groupBy('s.id').orderBy("s.id")
    return query.getRawMany()
}