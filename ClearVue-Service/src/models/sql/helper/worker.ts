import { getManager, getRepository, In, get<PERSON>onnection, <PERSON><PERSON>han } from 'typeorm';
import { JobAssociation } from '../';
import { AddWorkerDTO, WorkerTypes, UserType, WorkerSupervisorStatus } from '../../../common';
import { User } from '../entities/User';
import { getLatestWorkerIdsSubquery } from '../helper/automatedMessages';
import { Workers } from '../entities/Workers';
import { MessageReceiverWorkers } from '../entities/MessageReceiverWorkers';
import { WorkerTraining } from '../entities/WorkerTraining';
import { config } from '../../../configurations';
import { stripEmojis } from "../../../utils";
import { WorkerPerformance } from '../entities/WorkerPerformance';
import { WorkerPerformanceData } from '../entities/WorkerPerformanceData';



/**
 * Method to add new worker
 * @param  {AddWorkerDTO} data
 * @param {number} userId
 */
export const addNewWorker: any = async (data, userId, loggedInUserId) => {
    const workersRepository = getRepository(Workers);

    let payload = {
        firstName: data.first_name,
        lastName: data.last_name || data.surname || '',
        userId: userId,
        postCode: data.post_code,
        payrollRef: data.payroll_ref,
        employeeId: data.employee_id,
        dateOfBirth: data.date_of_birth,
        jobId: data.job_id,
        mobile: data.mobile,
        countryCode: data.country_code,
        nationalInsuranceNumber: data.national_insurance_number,
        nationality: data.nationality,
        orientation: data.orientation,
        startDate: data.start_date,
        assignmentDate: data.assignment_date,
        clientId: data.client_id || null,
        agencyId: data.agency_id || null,
        createdBy: String(loggedInUserId),
        updatedBy: String(loggedInUserId),
        isActive: data.is_active,
        type: data.type || WorkerTypes.TEMPORARY,
        workersSupervisorStatus: data.workers_supervisor_status || null,
        transport: data.transport,
        otherAssignment: data.other_assignment,
        pensionOptOut: data.pension_opt_out,
        studentVisa: data.student_visa,
        houseNumber: stripEmojis(data.house_number),
        sortCode: data.sort_code || null,
        accountNumber: data.account_number || null,
        limitedHours: data.limited_hours || false,
    };

    let response = await workersRepository.insert(payload);
    return response.generatedMaps[0];
};

export const addNewWorkers: any = async (data) => {
    const workersRepository = getRepository(Workers);
    let response = await workersRepository.insert(data);
    return response.identifiers;
};

export const addNewWorkersTransaction = async (transactionalEntityManager, data) => {
    const workersRepository = transactionalEntityManager.getRepository(Workers);
    let response = await workersRepository.insert(data);
    return response.identifiers;
};


export const updateWorkers: any = async (data, loggedInUser) => {
    if (data.client_id) {
        const workersRepository = getRepository(Workers);
        return await workersRepository.update(data.workers, { clientId: data.client_id });
    } else if (data.agency_id) {
        const workersRepository = getRepository(Workers);
        return await workersRepository.update(data.workers, { agencyId: data.agency_id });
    } else if (data.job_id) {
        const workersRepository = getRepository(Workers);
        return await workersRepository.update(data.workers, { jobId: data.job_id });
    } else if (data.hasOwnProperty('is_active') && data.is_active !== null) {
        let dataToUpdate = {}
        if (data.is_active === 0 || data.is_active === false) {
            dataToUpdate = { isActive: data.is_active, inActivedAt: new Date(), inactivatedBy: loggedInUser.user_id }
        } else {
            dataToUpdate = { isActive: data.is_active, inActivedAt: null, inactivatedBy: null }
        }
        const workersRepository = getRepository(Workers);
        return await workersRepository.update(data.workers, dataToUpdate);
    }
}

export const updateWorkerDetail: any = async (id, data) => {
    const workersRepository = getRepository(Workers);
    return await workersRepository.update(id, data);
}

export const getWorkers: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType, isLimitedView) => {
    const workersRepository = getRepository(Workers);
    let subquery = await getLatestWorkerIdsSubquery();

    let query = workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .leftJoin('workers.agency', 'agency')
        .leftJoin('workers.client', 'client')
        .innerJoin('workers.user', 'user')
        .leftJoin('job.jobAssociations', 'job_association')
        .select([
            'DISTINCT workers.id as id',
            'workers.first_name as first_name',
            'workers.last_name as last_name',
            'workers.type as type',
            'workers.workers_supervisor_status as workers_supervisor_status',
            'workers.payroll_ref as payroll_ref',
            'workers.employee_id as employee_id',
            'LEFT(SUBSTRING_INDEX(workers.post_code, " ", 1), 4) as post_code',
            "date_format(workers.start_date,'%Y-%m-%d') as start_date",
            "date_format(workers.assignment_date,'%Y-%m-%d') as assignment_date",
            'workers.agency_id as agency_id',
            `IFNULL(agency.name, '') as agency_name`,
            'workers.client_id as client_id',
            `IFNULL(client.name, '') as client_name`,
            'workers.job_id as job_id',
            'IF(workers.device_token IS NOT NULL, 1, 0) is_app_downloaded',
            'workers.is_active as is_active',
            'workers.pension_opt_out as pension_opt_out',
            'workers.transport as transport',
            'workers.other_assignment as other_assignment',
            'workers.limited_hours as limited_hours',
            'workers.internal_chargeback as internal_chargeback',
            'workers.house_number as house_number',
            'workers.student_visa as student_visa',
            'IF(workers.sort_code IS NOT NULL OR workers.account_number IS NOT NULL, 1, 0) as has_banking_details',
            "date_format(workers.in_actived_at,'%Y-%m-%d') as in_actived_at",
            "date_format(workers.created_at,'%Y-%m-%d') as created_at"
        ])
        .where(`workers.id IN (${subquery})`)
        .andWhere(whereClause, whereClauseValue)
        .orderBy(sortBy, sortType)
        .addOrderBy('workers.id', sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)


    if (!isLimitedView) {
        query.addSelect([
            'user.email as email',
            'workers.national_insurance_number as national_insurance_number',
            "date_format(workers.date_of_birth,'%Y-%m-%d') as date_of_birth",
            'workers.post_code as full_post_code',
            'workers.nationality as nationality',
            'workers.orientation as orientation',
        ])
    }

    let response = await query.execute();
    let responseCount = await workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .innerJoin('workers.user', 'user')
        .leftJoin('job.jobAssociations', 'job_association').select(['DISTINCT workers.id as id']).where(`workers.id IN (${subquery})`).andWhere(whereClause, whereClauseValue).execute()
    response["count"] = responseCount.length;

    return response;
}

export const searchWorkersList: any = async (
    first_name,
    last_name,
    email,
    employeeId,
    payrollRef,
    pensionOptOut,
    otherAssignment,
    appDownloaded,
    nationalInsuranceNumber,
    bankingDetails,
    limitedHours,
    studentVisa,
    assignmentDate,
    siteId,
    roleId,
    supervisorStatus,
    los,
    booleanFields,
) => {

    if (!email && !employeeId && !payrollRef && !nationalInsuranceNumber && !first_name && !last_name && !booleanFields.under_twentyone && !booleanFields.under_twentytwo && !booleanFields.within_twelveweeks && !appDownloaded && !pensionOptOut && !otherAssignment && !bankingDetails && !limitedHours && !booleanFields.returning_worker && !studentVisa && !assignmentDate && !siteId && !roleId && !supervisorStatus && !los) {
        return [];
    }

    let subquery = await getLatestWorkerIdsSubquery();
    let whereCondition = `workers.id IN (${subquery})`;
    whereCondition += first_name ? ` AND workers.first_name = :first_name` : "";
    whereCondition += last_name ? ` AND workers.last_name = :last_name` : "";
    whereCondition += email ? ` AND user.email = :email` : "";
    whereCondition += employeeId ? ` AND workers.employee_id = :employee_id` : "";
    whereCondition += payrollRef ? ` AND workers.payroll_ref = :payroll_ref` : "";
    whereCondition += pensionOptOut ? ` AND workers.pension_opt_out = :pension_opt_out` : "";
    whereCondition += otherAssignment ? ` AND workers.other_assignment = :other_assignment` : "";
    whereCondition += limitedHours ? ` AND workers.limited_hours = :limited_hours` : "";
    whereCondition += bankingDetails ? (bankingDetails.toLowerCase() == "yes" ? ` AND (workers.account_number IS NOT NULL AND workers.sort_code IS NOT NULL)` : ` AND (workers.account_number IS NULL OR workers.sort_code IS NULL)`) : "";
    whereCondition += appDownloaded ? (appDownloaded.toLowerCase() == "yes" ? ` AND workers.device_token IS NOT NULL` : ` AND workers.device_token IS NULL`) : "";
    whereCondition += nationalInsuranceNumber ? ` AND workers.national_insurance_number = :national_insurance_number` : "";
    whereCondition += booleanFields.under_twentyone ? ` AND TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()) < 21` : "";
    whereCondition += booleanFields.under_twentytwo ? ` AND TIMESTAMPDIFF(YEAR, workers.date_of_birth, CURDATE()) < 22` : "";
    whereCondition += booleanFields.within_twelveweeks ? ` AND TIMESTAMPDIFF(WEEK, workers.assignment_date, CURDATE()) < 12` : "";
    whereCondition += booleanFields.returning_worker ? ` AND workers.is_returning_worker = 1` : "";
    whereCondition += studentVisa ? ` AND workers.student_visa = :student_visa` : "";
    whereCondition += assignmentDate ? ` AND DATE(workers.assignment_date) = DATE(:assignment_date)` : "";
    whereCondition += siteId ? ` AND job_association.site_id = :site_id` : "";
    whereCondition += roleId ? ` AND workers.job_id = :role_id` : "";
    whereCondition += los ? ` AND workers.id IN (SELECT w.id FROM workers w WHERE TIMESTAMPDIFF(YEAR, w.assignment_date, CURDATE()) + (TIMESTAMPDIFF(MONTH, w.assignment_date, CURDATE()) % 12) / 12 >= :los_min AND TIMESTAMPDIFF(YEAR, w.assignment_date, CURDATE()) + (TIMESTAMPDIFF(MONTH, w.assignment_date, CURDATE()) % 12) / 12 < :los_max)` : "";
    whereCondition += supervisorStatus ? (supervisorStatus === 'BOTH' ? ` AND workers.workers_supervisor_status IS NOT NULL` : ` AND workers.workers_supervisor_status = :supervisor_status`) : "";

    let whereClauseValue = {
        "first_name": first_name,
        "last_name": last_name,
        "email": email,
        "employee_id": employeeId,
        "payroll_ref": payrollRef,
        "pension_opt_out": pensionOptOut == "yes",
        "other_assignment": otherAssignment == "yes",
        "national_insurance_number": nationalInsuranceNumber,
        "limited_hours": limitedHours == "yes",
        "student_visa": studentVisa == "yes",
        "assignment_date": assignmentDate,
        "site_id": siteId,
        "role_id": roleId,
        "supervisor_status": supervisorStatus
    }

    // Handle LOS range values
    if (los) {
        const losValue = parseFloat(los);
        whereClauseValue["los_min"] = losValue;
        whereClauseValue["los_max"] = losValue + 1;
    }
    let queryBuilder = getRepository(Workers).createQueryBuilder('workers')
        .innerJoin('workers.user', 'user')
        .select(['DISTINCT workers.id as id'])
        .where(whereCondition, whereClauseValue);

    if (siteId) queryBuilder = queryBuilder.innerJoin('workers.job', 'job').leftJoin('job.jobAssociations', 'job_association');

    let response = await queryBuilder.execute();

    return response;
}

export const getWorkersWithoutPagination: any = async (whereClause, whereClauseValue) => {
    const workersRepository = getRepository(Workers);
    let subquery = await getLatestWorkerIdsSubquery();

    let response = await workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .leftJoin('job.jobAssociations', 'job_association')
        .select([
            'DISTINCT workers.id as id',
            'CONCAT(workers.first_name, " ", workers.last_name, " - ", IFNULL(workers.employee_id, "")) as display_name'
        ])
        .where(`workers.id IN (${subquery})`)
        .andWhere(whereClause, whereClauseValue)
        .orderBy("display_name", "ASC")
        .execute()

    let responseCount = await workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .leftJoin('job.jobAssociations', 'job_association').select(['DISTINCT workers.id as id']).where(`workers.id IN (${subquery})`).andWhere(whereClause, whereClauseValue).execute()
    response["count"] = responseCount.length;

    return response;
}

export const getWorkerByNationalInsuranceNumber: any = async (nationalInsuranceNumber) => {
    const workersRepository = getRepository(Workers);
    return await workersRepository.find(
        {
            where: { nationalInsuranceNumber: In(nationalInsuranceNumber) },
            select: ['id', 'nationalInsuranceNumber']
        }
    );
}

export const getWorkerByEmployeeIdAndAgencyId: any = async (employeeIds, payrollRefs, payload) => {

    // Additional conditions for filtering total_agency_pay_data table
    const whereClause2 = 'tap_data.employeeId IN (:employee_id_array) AND tap_data.startDate >= :startDate AND tap_data.endDate <= :endDate AND tap_data.agencyId= :agencyId AND tap_data.clientId = :clientId AND tap_data.siteId = :siteId';

    let whereClause1 = employeeIds.length || payrollRefs.length ? (
        employeeIds.length && payrollRefs.length ?
            `(workers.employeeId IN (:employee_id_array) OR workers.payrollRef IN (:payroll_ref_array) AND workers.agencyId= :agencyId AND workers.clientId = :clientId) ` :
            employeeIds.length ?
                `(workers.employeeId IN (:employee_id_array)  AND workers.agencyId= :agencyId AND workers.clientId = :clientId) ` :
                `(workers.payrollRef IN (:payroll_ref_array)  AND workers.agencyId= :agencyId AND workers.clientId = :clientId) `) : "";
    let whereClauseValue1 = { "employee_id_array": employeeIds, "payroll_ref_array": payrollRefs, };

    let whereClauseValue2 = {
        "agencyId": payload.agency_id,
        "startDate": payload.start_date,
        "endDate": payload.end_date,
        "clientId": payload.client_id,
        "siteId": payload.site_id
    }

    const workersRepository = getRepository(Workers);
    return await workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .leftJoin('job.jobAssociations', 'job_association')
        .leftJoin('workers.totalAgencyPayData', 'tap_data', whereClause2, { ...whereClauseValue1, ...whereClauseValue2 })
        .leftJoinAndSelect('workers.holidayPayrollSummaries', 'holiday_summary')
        .select([
            'workers.id as id',
            'workers.employeeId as employee_id',
            'workers.payrollRef as payroll_ref',
            'workers.agencyId as agency_id',
            'workers.first_name as first_name',
            'workers.last_name as last_name',
            'workers.start_date as start_date',
            'workers.date_of_birth as dob',
            'workers.pension_opt_out as pension_opt_out',
            'workers.transport as transport',
            'workers.other_assignment as other_assignment',
            'workers.limited_hours as limited_hours',
            'workers.workers_supervisor_status AS supervisor_status',
            'workers.training_qualification_status AS training_qualification_status',
            'workers.is_active as is_active',
            "date_format(workers.in_actived_at,'%Y-%m-%d') as in_actived_at",
            "date_format(workers.assignment_date,'%Y-%m-%d') as assignment_date",
            'job.type as type',
            'job.shift_id as shift_id',
            'job_association.departmentId as department_id',
            'tap_data.tapValue as tap_value',
            'SUM(IFNULL(holiday_summary.accrual_value, 0)) as accrual_value'
        ])
        .where(whereClause1, { ...whereClauseValue1, ...whereClauseValue2 })
        .groupBy('workers.id')
        .execute();
}


export const getWorkerByWorkerId: any = async (id) => {
    const workersRepository = getRepository(Workers);
    return await workersRepository.findOne(
        {
            where: { id: id }
        }
    );
}


export const getWorkerDetailsHelper: any = async (whereClause, whereClauseValue) => {
    return await getRepository(Workers).createQueryBuilder('workers')
        .leftJoin('workers.agency', 'agency_details')
        .innerJoin('workers.client', 'client_details')
        .innerJoin('workers.user', 'user')
        .leftJoin('workers.job', 'job')
        .innerJoin('job.shift', 'shift')
        .innerJoin('job.jobAssociations', 'job_assoc')
        .innerJoin('job_assoc.site', 'site')
        .innerJoin('job_assoc.department', 'department')
        .select([
            'workers.id as id',
            'workers.first_name as first_name',
            'workers.last_name as last_name',
            'user.id as user_id',
            'user.email as email',
            'user.documents as documents',
            'workers.type as type',
            'workers.workers_supervisor_status as workers_supervisor_status',
            'workers.national_insurance_number as national_insurance_number',
            'workers.payroll_ref as payroll_ref',
            'workers.employee_id as employee_id',
            "date_format(workers.date_of_birth,'%Y-%m-%d') as date_of_birth",
            'workers.post_code as post_code',
            'LEFT(SUBSTRING_INDEX(workers.post_code, " ", 1), 4) as partial_post_code',
            "date_format(workers.start_date,'%Y-%m-%d') as start_date",
            "date_format(workers.assignment_date,'%Y-%m-%d') as assignment_date",
            'workers.nationality as nationality',
            'workers.orientation as orientation',
            'workers.agency_id as agency_id',
            'agency_details.name as agency_name',
            'workers.client_id as client_id',
            'client_details.name as client_name',
            'workers.job_id as job_id',
            'job.name as job_name',
            'job.type as job_type',
            'department.name as department_name',
            'shift.name as shift_name',
            "job_assoc.site_id as site_id",
            "site.name as site_name",
            'workers.is_active as is_active',
            'workers.pension_opt_out as pension_opt_out',
            'workers.transport as transport',
            'workers.other_assignment as other_assignment',
            'workers.internal_chargeback as internal_chargeback',
            'workers.house_number as house_number',
            'workers.sort_code as sort_code',
            'workers.account_number as account_number',
            'workers.limited_hours as limited_hours',
            'workers.student_visa as student_visa',
            "IFNULL(date_format(workers.in_actived_at,'%Y-%m-%d'),'') as in_actived_at",
            "IFNULL(date_format(workers.created_at,'%Y-%m-%d'),'') as created_at"
        ])
        .where(whereClause, whereClauseValue)
        .getRawOne()
}

export const getWorkerHelper: any = async (whereClause, whereClauseValue) => {
    let workersRepository = getRepository(Workers);
    return await workersRepository.createQueryBuilder('workers')
        .innerJoin('workers.user', 'user')
        .select([
            'workers.id as id',
            'workers.first_name as first_name',
            'workers.last_name as last_name',
            'workers.type as worker_type',
            'user.id as user_id',
            'user.email as email',
            'user.password as password',
            'workers.country_code as country_code',
            'workers.mobile as mobile',
            'user.national_insurance_number as national_insurance_number',
            'workers.payroll_ref as payroll_ref',
            'workers.employee_id as employee_id',
            "date_format(workers.date_of_birth,'%Y-%m-%d') as date_of_birth",
            'workers.post_code as post_code',
            "date_format(workers.start_date,'%Y-%m-%d') as start_date",
            'workers.nationality as nationality',
            'workers.orientation as orientation',
            'workers.agency_id as agency_id',
            'workers.client_id as client_id',
            'workers.device_token as device_token',
            'workers.language as language',
            'workers.job_id as job_id',
            'workers.is_active as is_active',
            "date_format(workers.in_actived_at,'%Y-%m-%d') as in_actived_at",
            "date_format(workers.created_at,'%Y-%m-%d') as created_at"
        ])
        .where(whereClause, whereClauseValue)
        .orderBy('workers.created_at', 'DESC') // Order by created_at in descending order (latest first)
        .limit(1) // Only get the latest worker
        .getRawOne()

}

export const updateWorkerHelper: any = async (user_id, details) => {
    let workers = await getRepository(Workers).find({
        select: ['id'], where: { userId: user_id }
    })
    let workerId = workers.map(element => {
        return element.id
    })
    return await getRepository(Workers).update(workerId, details)
}

export const bulkUpdateUserDetails: any = async (updateUserEmailWhereClauseString, updateUserNameWhereClauseString, nationalInsuranceNumber) => {
    return await getRepository(User).query(`
    UPDATE user set email = CASE ${updateUserEmailWhereClauseString} END, name = CASE ${updateUserNameWhereClauseString} END where national_insurance_number in (${nationalInsuranceNumber})
    `)
}

export const bulkUpdateUserDetailsTransaction = async (transactionalEntityManager, updateUserEmailWhereClauseString, updateUserNameWhereClauseString, nationalInsuranceNumber) => {
    return await transactionalEntityManager.getRepository(User).query(`
        UPDATE user SET email = CASE ${updateUserEmailWhereClauseString} END, name = CASE ${updateUserNameWhereClauseString} END 
        WHERE national_insurance_number IN (${nationalInsuranceNumber});
    `);
};

export const bulkUpdateUserId: any = async (whereClause, nationalInsuranceNumber) => {
    return await getRepository(Workers).query(`
    UPDATE workers set user_id = CASE ${whereClause} END where national_insurance_number in (${nationalInsuranceNumber})
    `)
}

export const bulkUpdateUserIdTransaction = async (transactionalEntityManager, whereClause, nationalInsuranceNumber) => {
    return await transactionalEntityManager.getRepository(Workers).query(`
        UPDATE workers SET user_id = CASE ${whereClause} END 
        WHERE national_insurance_number IN (${nationalInsuranceNumber});
    `);
};

export const setNullNiNumberUser: any = async (nationalInsuranceNumber) => {
    return await getRepository(User).query(`
    UPDATE user set national_insurance_number = null where national_insurance_number like "ni_prefix%" and national_insurance_number in (${nationalInsuranceNumber})
    `)
}

export const setNullNiNumberUserTransaction = async (transactionalEntityManager, nationalInsuranceNumber) => {
    return await transactionalEntityManager.getRepository(User).query(`
        UPDATE user SET national_insurance_number = null 
        WHERE national_insurance_number LIKE "ni_prefix%" 
        AND national_insurance_number IN (${nationalInsuranceNumber});
    `);
};

export const setNullNiNumberWorker: any = async (nationalInsuranceNumber) => {
    return await getRepository(Workers).query(`
    UPDATE workers set national_insurance_number = null where national_insurance_number like "ni_prefix%" and national_insurance_number in (${nationalInsuranceNumber})
    `)
}

export const setNullNiNumberWorkerTransaction = async (transactionalEntityManager, nationalInsuranceNumber) => {
    return await transactionalEntityManager.getRepository(User).query(`
        UPDATE workers SET national_insurance_number = null 
        WHERE national_insurance_number LIKE "ni_prefix%" 
        AND national_insurance_number IN (${nationalInsuranceNumber});
    `);
};

export const getWorkersAsPerSelectedGroups = async (
    siteId: number, clientId: number, agencyId: number, shiftIds = [], jobIds = [], departmentIds = [], nationalityList = [], workerType = null
) => {

    let whereCondition = "workers.is_active = 1";
    let whereConditionValue: any = {};

    whereCondition += workerType ? ` AND workers.type = :worker_type` : "";
    whereCondition += shiftIds.length ? ` AND job.shift_id IN (:shift_ids) ` : "";
    whereCondition += jobIds.length ? ` AND job.id IN (:job_ids)` : "";
    whereCondition += ` AND job_association.site_id = :site_id`;
    whereCondition += ` AND workers.clientId = :client_id`;
    whereCondition += agencyId ? ` AND workers.agency_id = :agency_id` : "";
    whereCondition += departmentIds.length ? ` AND department_id IN (:department_ids)` : "";

    if (nationalityList.length) {
        whereCondition += nationalityList.length ? ` AND workers.nationality IN (:nationality_list) ` : "";
        whereConditionValue["nationality_list"] = nationalityList;
    }

    whereConditionValue = { ...whereConditionValue, "worker_type": workerType, "shift_ids": shiftIds, "job_ids": jobIds, "site_id": siteId, "client_id": clientId, "agency_id": agencyId, "department_ids": departmentIds };

    return await getRepository(Workers).createQueryBuilder('workers')
        .innerJoin('workers.job', 'job')
        .innerJoin("job.jobAssociations", "job_association")
        .select('DISTINCT workers.id as worker_id')
        .where(whereCondition, whereConditionValue)
        .getRawMany();
}

export const getWorkerIdfromUserId: any = async (where) => {
    return await getRepository(Workers).find({ select: ['id'], where });
}


export const getWorkerLengthOfServiceByWorkerId: any = async (id) => {
    return await getRepository(Workers).createQueryBuilder("worker")
        .select(["worker.assignment_date as assignment_date", "IFNULL(worker.in_actived_at, CURRENT_TIMESTAMP()) as end_date"])
        .where({ id })
        .getRawOne()
}

export const getWorkerStartDateById: any = async (id) => {
    return await getRepository(Workers).createQueryBuilder('workers')
        .select(['start_date', 'is_active', 'type'])
        .where(`workers.id = :id`, { "id": id })
        .getRawOne()
}


/**
 * update worker profile by userID
 */
export const updateWorkerProfile: any = async (id, data) => {
    data.updatedAt = new Date();
    return await getRepository(User).update({ id }, data)
};

export const getWorkerTrainingData: any = async (whereClause, whereClauseValue) => {
    return await getRepository(WorkerTraining).createQueryBuilder("training")
        .innerJoin("training.message", "message")
        .select([
            'training.id as training_id',
            'training.message_id as message_id',
            'message.name as message_name',
            `IFNULL(training.training_completed_at,'') as training_completed_at`
        ])
        .where(whereClause, whereClauseValue)
        .getRawMany()
}

/**
 * get list of worker groups by clientId and siteId
 */
export const getAllWorkerGroup: any = async (data) => {
    return await getRepository(JobAssociation).createQueryBuilder('job_association')
        .innerJoin('job_association.job', 'job')
        .leftJoin('job.shift', 'shift')
        .innerJoin('job_association.department', 'department')
        .select(['job.id AS job_id', 'job.name AS job_name',
            'shift.id AS shift_id', 'shift.name AS shift_name',
            'department.id AS department_id', 'department.name AS department_name'])
        .where(`job_association.clientId = :client_id`, { "client_id": data.client_id })
        .andWhere(`job_association.siteId = :site_id`, { "site_id": data.site_id })
        .getRawMany();
}


/**
 * @param  {} userId
 */
export const getWorkerAppreciationDataFromUserIdHelper: any = async (userId, agencyId) => {
    return getRepository(Workers).find({ select: ['appreciation'], where: agencyId ? { userId, agencyId } : { userId } })
}

export const getWorkerIdFromUserIdAndAgencyId: any = async (userId, agencyId) => {
    return await getRepository(Workers).find({ select: ['id'], where: { userId, agencyId } });
}


export const getWorkerDeviceTokens: any = async (workerIds: Array<number>) => {
    return getRepository(Workers).find({
        select: ['id', 'deviceToken'],
        where: { id: In(workerIds) }
    })
}

export const getWorkersData: any = async (workerIds: Array<number>) => {
    return getRepository(Workers).find({
        select: ['id', 'assignmentDate', 'employeeId'],
        where: { id: In(workerIds) }
    })
}

/**
 * Get worker details which are inactives
 */
export const getWorkerDetailsbyIds: any = async (workersID: Array<number>) => {

    const queryBuilder = getRepository(Workers)
        .createQueryBuilder('workers')
        .select([
            'workers.id as id',
            'workers.client_id as client_id',
            'workers.agency_id as agency_id',
            'workers.device_token as device_token',
            'workers.language AS language',
            'IF(user.password IS NOT NULL, 1, 0) as is_worker_registered'
        ])
        .innerJoin('user', 'user', 'user.id = workers.user_id')
        .where('workers.id IN (:...workersID)', { workersID });

    const result = await queryBuilder.getRawMany();
    return result;

}

export const trackWorkerTrainingHelper: any = async (updateClause, messageId, loggedInUser) => {
    const entityManager = getManager();

    return await entityManager.query(`
    UPDATE worker_training as wt inner join 
    workers as wk on wt.worker_id = wk.id  
    ${updateClause}
    where wt.message_id =${messageId} AND wk.user_id = ${loggedInUser.user_id};
    `)
}

export const getWorkerByUserIdAndMessageId: any = async (user_id, message_id) => {
    return await getRepository(WorkerTraining).createQueryBuilder('worker_training')
        .innerJoin('worker_training.worker', 'worker')
        .select(['worker.id as worker_id', 'worker.first_name as first_name', 'worker.last_name as last_name', `IFNULL(worker.agency_id,'') as agency_id`,
            `IFNULL(worker.client_id,'') as client_id`, `IFNULL(worker.job_id,'') as job_id`])
        .where(`worker.user_id = :user_id AND worker_training.message_id = :message_id`, { "user_id": user_id, "message_id": message_id })
        .getRawOne()
}

export const getWorkerDetailsByMessageIdAndUserId: any = async (message_id, user_id) => {
    return await getRepository(WorkerTraining).createQueryBuilder('worker_training')
        .innerJoin('worker_training.message', 'message')
        .innerJoin('worker_training.worker', 'worker')
        .select(['message.name as training_name', 'worker.agency_id as agency_id', 'worker.id as worker_id', 'worker.first_name as first_name', 'worker.last_name as last_name'])
        .where(`worker_training.message_id = :message_id and worker.user_id = :user_id`, { "user_id": user_id, "message_id": message_id })
        .getRawOne()
}


export const getDetailsWorkerId: any = async (workerId) => {
    return await getRepository(Workers).createQueryBuilder('worker')
        .leftJoin('worker.job', 'job')
        .leftJoin('job.jobAssociations', 'job_association')
        .select([`IFNULL(worker.client_id,'') as client_id`,
            `IFNULL(worker.agency_id,'') as agency_id`, `IFNULL(job_association.site_id,'') as site_id`])
        .where(`worker.id = :worker_id`, { "worker_id": workerId })
        .getRawOne()
}


/**
 * Bulk Re-Activate Workers
 * @param  {Array<number>} workerIds
 */
export const reActivateWorkers: any = async (workerIds: Array<number>, userId) => {
    return getConnection().createQueryBuilder()
        .update('workers')
        .set({
            isActive: 1,
            inActivedAt: null,
            updatedBy: userId,
            updatedAt: new Date()
        })
        .where({ id: In(workerIds) })
        .execute();
}


/**
 * Inactivate workers
 * @param  {Array<number>} workerIds
 */
export const inactivateWorkers: any = async (workerIds: Array<number>) => {
    return getConnection().createQueryBuilder()
        .update('workers')
        .set({
            isActive: 0,
            inActivedAt: new Date(),
            updatedBy: config.DEFAULT_SYSTEM_USER_ID,
            updatedAt: new Date()
        })
        .where({ id: In(workerIds) })
        .execute();
}

export const bulkinactivateWorkers: any = async (wkr) => {
    const inactivateWorkersPromises = wkr.map(async ({ worker_id, inactivated_at }) => {
        await getConnection()
            .createQueryBuilder()
            .update('workers')
            .set({
                isActive: 0,
                inActivedAt: inactivated_at ? new Date(inactivated_at) : new Date(),
                inactivatedBy: config.DEFAULT_SYSTEM_USER_ID,
                updatedBy: config.DEFAULT_SYSTEM_USER_ID,
                updatedAt: new Date()
            })
            .where({ id: worker_id })
            .execute();
    });

    await Promise.all(inactivateWorkersPromises);
}

export const getWorkersByNationalInsuranceNumber: any = async (nationalInsuranceNumber) => {
    return await getRepository(Workers).find({
        select: ['id'], where: {
            nationalInsuranceNumber
        }
    })
}

export const updateWorkerNationalInsuranceNumber: any = async (id, nationalInsuranceNumber) => {
    return await getRepository(Workers).update(id, { nationalInsuranceNumber })
}

export const getExistingNationalInsuranceWithAgency: any = async (nationalInsuranceNumber, agencyId) => {
    return await getRepository(Workers).find(
        {
            select: ['nationalInsuranceNumber'],
            where: {
                nationalInsuranceNumber: In(nationalInsuranceNumber),
                agencyId
            }
        })
}

export const getExistingEmployeeIdWithAgency: any = async (employeeIds, agencyId, type) => {
    return await getRepository(Workers).find(
        {
            select: ['employeeId'],
            where: {
                employeeId: In(employeeIds),
                agencyId,
                type
            }
        })
}

export const getExistingEmployeeIdWithClientORAgency: any = async (employeeIds, clientId, agencyId, type) => {
    return await getRepository(Workers).find(
        {
            select: ['employeeId'],
            where: {
                employeeId: In(employeeIds),
                clientId,
                agencyId,
                type
            }
        })
}

export const fetchWorkersByEmailsAndAgency = async (emailList: string[], agencyId: number) => {
    const workerRepository = getRepository(Workers);

    return await workerRepository.createQueryBuilder('workers')
        .innerJoinAndSelect('workers.user', 'user')
        .select('workers.id', 'worker_id')
        .addSelect('user.email', 'email')
        .where('user.userTypeId = :userTypeId', { userTypeId: UserType.AGENCY_WORKER })
        .andWhere('workers.agencyId = :agencyId', { agencyId })
        .andWhere('user.email IN (:...emailList)', { emailList })
        .orderBy('workers.userId', 'DESC')
        .distinctOn(['workers.userId'])
        .getRawMany();
}

/**
 * Get nationality of workers
 * @param  {string} whereClause
 * @param  {any} whereClauseValue
 */
export const getNationalityOfWorkers: any = async (whereClause: string, whereClauseValue: any, workerType) => {
    whereClause += workerType ? ` AND workers.type = :type` : "";
    whereClauseValue["type"] = workerType;

    return getRepository(Workers).createQueryBuilder("workers")
        .innerJoin('workers.job', 'job')
        .leftJoin('job.jobAssociations', 'job_association')
        .select('DISTINCT(nationality) AS nationality')
        .orderBy('nationality')
        .where(whereClause, whereClauseValue)
        .getRawMany();
}


export const getCompletedTrainingCount: any = async (workerIds: Array<number>) => {

    return getRepository(WorkerTraining).count({
        where: {
            workerId: In(workerIds),
            isTrainingCompleted: 1
        }
    });
}

export const getWorkerIdfromUserIdWithLimit: any = async (userId) => {
    return await getRepository(Workers).createQueryBuilder("workers")
        .select(['id', 'type', 'employee_id', 'client_id', 'agency_id'])
        .orderBy("id", "DESC")
        .where(`workers.user_id = :user_id`, { "user_id": userId })
        .limit(1)
        .getRawOne();
}

export const getClientUserIdFromWorkerUserIdWithLimit: any = async (userId) => {
    return await getRepository(User).createQueryBuilder("user")
        .innerJoin('user.client', 'clientDetails')
        .innerJoin('clientDetails.workers', 'workers')
        .select('user.id')
        .orderBy("workers.client_id", "DESC")
        .where(`workers.user_id = :user_id AND user.user_type_id = :user_type_id`, { "user_id": userId, "user_type_id": UserType.CLIENT_ADMIN })
        .limit(1)
        .getRawOne();
};

export const getAgencyUserIdFromWorkerUserIdWithLimit: any = async (userId) => {
    return await getRepository(User).createQueryBuilder("user")
        .innerJoin('user.agency', 'agencyDetails')
        .innerJoin('agencyDetails.workers', 'workers')
        .select('user.id')
        .orderBy("workers.agency_id", "DESC")
        .where(`workers.user_id = :user_id AND user.user_type_id = :user_type_id`, { "user_id": userId, "user_type_id": UserType.AGENCY_ADMIN })
        .limit(1)
        .getRawOne();
};

/**
 * Get worker name as per worker ids
 * @param  {} workerIds
 */
export const getWorkerNamesByIds: any = async (workerIds) => {
    return getRepository(Workers).createQueryBuilder("worker")
        .innerJoin('worker.user', 'user')
        .select(['user.name', 'worker.id AS id'])
        .where(`worker.id IN (:worker_ids)`, { "worker_ids": workerIds })
        .getRawMany();
}

export const getSitesByWorkerIds: any = async (workerIds) => {
    if (!workerIds.length) {
        return [];
    }
    return await getRepository(Workers).createQueryBuilder('worker')
        .innerJoin('worker.job', 'job')
        .innerJoin('job.jobAssociations', 'job_association')
        .select(['worker.id as worker_id', 'job_association.site_id as site_id'])
        .where(`worker.id IN (:worker_ids)`, { "worker_ids": workerIds })
        .getRawMany()
}


export const getExistingEmployeeId: any = async (whereClause, whereClauseValue) => {
    let availableEmployee = await getRepository(Workers).createQueryBuilder('workers')
        .innerJoin('workers.user', 'user')
        .leftJoin('workers.job', 'job')
        .innerJoin('job.shift', 'shift')
        .innerJoin('job.jobAssociations', 'job_assoc')
        .innerJoin('job_assoc.site', 'site')
        .innerJoin('job_assoc.department', 'department')
        .select([
            'workers.employee_id as employeeId',
            'workers.id as workerId',
            'workers.first_name as firstName',
            'workers.last_name as lastName',
            'workers.user_id as userId',
            'workers.is_active as isActive',
            'workers.pension_opt_out as pensionOptOut',
            'workers.workers_supervisor_status as workersSupervisorStatus',
            'workers.historical_employee_ids as historicalEmployeeIds',
            'workers.house_number as houseNumber',
            'workers.post_code as postCode',
            'workers.sort_code as sortCode',
            'workers.account_number as accountNumber',
            'user.email as email',
            "date_format(workers.start_date,'%Y-%m-%d') as startDate",
            "date_format(workers.assignment_date,'%Y-%m-%d') as assignmentDate",
            'job.id as jobId',
            'job.name as jobName',
            'job.type as jobType',
            'department.id as departmentId',
            'department.name as departmentName',
            'shift.id as shiftId',
            'shift.name as shiftName',
            "job_assoc.site_id as siteId",
            "site.name as siteName"])
        .where(whereClause, whereClauseValue)
        .getRawMany();

    return { availableEmployee }
}

export const updateWorkersData = async (data, id) => {
    return await getRepository(Workers).update({ id }, data);
}

export const updateWorkersDataTransaction = async (transactionalEntityManager, data, id) => {
    return await transactionalEntityManager.getRepository(Workers).update({ id }, data);
};


export const getExistingNationalInsuranceWithClient: any = async (nationalInsuranceNumber, clientId) => {
    return await getRepository(Workers).find(
        {
            select: ['nationalInsuranceNumber'],
            where: {
                nationalInsuranceNumber: In(nationalInsuranceNumber),
                clientId
            }
        })
}

export const updateOtherAssignment: any = async (employeeIds) => {
    await getConnection().createQueryBuilder()
        .update('workers')
        .set({ otherAssignment: 1 })
        .where('employee_id IN (:...employeeIds)', { employeeIds: employeeIds as any })
        .execute();
}

export const getActiveTemporaryWorkers = async (clientIds, assignmentDate) => {
    const workersRepository = getRepository(Workers);

    const result = await workersRepository.find({
        select: ['id'],
        where: {
            isActive: true,
            assignmentDate: LessThan(assignmentDate),
            type: 'TEMPORARY',
            clientId: In(clientIds),
        },
    });

    return result.map(obj => obj.id);
}

export const getWorkersWithoutAppDownload: any = async (client_id) => {
    let response = await getRepository(Workers).createQueryBuilder('workers')
        .innerJoin('workers.user', 'user')
        .select([
            'workers.id as id',
            'workers.first_name as first_name',
            'user.email as email'
        ])
        .where('workers.client_id = :clientId', { clientId: client_id })
        .andWhere('workers.device_token IS NULL')
        .andWhere('workers.is_active = 1')
        .execute();
    return response;
}

export const getWorkersByEmployeeIdAndAgencyIds: any = async (employeeIds, agencyIds) => {

    // Additional conditions for filtering total_agency_pay_data table
    const whereClause = `workers.employeeId IN (:employee_id_array) AND workers.agencyId IN (:agency_id_array)`;
    let whereClauseValue = {
        "employee_id_array": employeeIds,
        "agency_id_array": [...new Set(agencyIds)],
    }

    const workersRepository = getRepository(Workers);

    return await workersRepository.createQueryBuilder('workers')
        .select([
            'workers.id as id',
            'workers.employeeId as employee_id',
            'workers.agencyId as agency_id',
        ])
        .where(whereClause, whereClauseValue)
        .groupBy('workers.id')
        .execute();


}

export const getWorkerPerformance: any = async (filter) => {
    const WorkerPerformanceRepository = getRepository(WorkerPerformance);
    return await WorkerPerformanceRepository.find(filter);
};

export const createWorkerPerformance: any = async (data) => {
    const WorkerPerformanceRepository = getRepository(WorkerPerformance);
    let response = await WorkerPerformanceRepository.insert(data);
    return response.generatedMaps[0];
};

export const getWorkerPerformanceById: any = async (id) => {
    const WorkerPerformanceRepository = getRepository(WorkerPerformance);
    return await WorkerPerformanceRepository.findOne({ id });
};

export const deleteWorkerPerformanceById: any = async (id) => {
    const WorkerPerformanceRepository = getRepository(WorkerPerformance);
    return await WorkerPerformanceRepository.delete({ id });
};

export const deleteWorkerPerformanceDataById: any = async (workerPerformanceId) => {
    const WorkerPerformanceDataRepository = getRepository(WorkerPerformanceData);
    return await WorkerPerformanceDataRepository.delete({ workerPerformanceId });
};

export const addWorkerPerformanceData: any = async (data) => {
    const WorkerPerformanceDataRepository = getRepository(WorkerPerformanceData);
    let response = await WorkerPerformanceDataRepository.insert(data);
    return response.generatedMaps[0];
};

export const getWorkerPerformanceDataHelper: any = async (whereClause, whereClauseValue) => {
    const WorkerPerformanceDataRepository = getRepository(WorkerPerformance);
    let response = await WorkerPerformanceDataRepository.createQueryBuilder("worker_performance")
        .select('name')
        .where(whereClause, whereClauseValue)
        .getRawMany();
    return response;
};

export const deleteWorkerPerformanceDataHelper: any = async (workerPerformanceId) => {
    await getRepository(WorkerPerformanceData).createQueryBuilder()
        .delete()
        .where('workerPerformanceId = :workerPerformanceId', { workerPerformanceId })
        .execute();

    await getRepository(WorkerPerformance).createQueryBuilder()
        .delete()
        .where('id = :workerPerformanceId', { workerPerformanceId })
        .execute();

};


export const getLastTwoPerformancesByWorkerId = async (workerId) => {
    const workerPerformanceRepository = getRepository(WorkerPerformanceData);

    const performanceData = await workerPerformanceRepository.createQueryBuilder('worker_performance_data')
        .select('AVG(worker_performance_data.performance_number)', 'avgPerformance')
        .where('worker_performance_data.worker_id = :workerId', { workerId })
        .groupBy('worker_performance_data.start_date')
        .orderBy('worker_performance_data.start_date', 'DESC')
        .limit(2)
        .getRawMany();

    const [lastPerformance, secondLastPerformance] = performanceData.map(row => row.avgPerformance ? parseFloat(row.avgPerformance).toFixed(2) : null);

    return { lastPerformance, secondLastPerformance };
};


export const getWorkersPerformancesData = async (whereCondition, whereConditionValue) => {

    const performanceData = await getRepository(WorkerPerformanceData)
        .createQueryBuilder('wpd')
        .select([
            'wpd.id as id',
            'wpd.worker_performance_id as worker_performance_id',
            'wpd.agency_id as agency_id',
            'wpd.client_id as client_id',
            'wpd.worker_id as worker_id',
            'wpd.site_id as site_id',
            'wpd.performance_number as performance_number',
            'date_format(wpd.start_date,"%Y-%m-%d")  as start_date',
            'date_format(wpd.end_date,"%Y-%m-%d")  as end_date'
        ])
        .where(whereCondition, whereConditionValue)
        .getRawMany();

    return performanceData;
};


export const getGetFirstQualifyingPerformanceNumber = async (workerIds, performanceNumber, siteId, startDate, assignmentWeekStart) => {
    if (!workerIds.length) return {};

    const workerPerformanceRepository = getRepository(WorkerPerformanceData);

    const results = await workerPerformanceRepository.createQueryBuilder('wpd')
        .select([
            'wpd.worker_id',
            'date_format(wpd.end_date,"%Y-%m-%d")  as end_date',
            'wpd.performance_number'])
        .where('wpd.performance_number >= :performanceNumber', { performanceNumber })
        .andWhere('wpd.site_id = :siteId', { siteId })
        .andWhere('wpd.worker_id IN (:...workerIds)', { workerIds })
        .andWhere(`wpd.start_date <= :start_date`, { "start_date": startDate })
        .andWhere(`wpd.start_date >= :assignment_date_week_start`, { "assignment_date_week_start": assignmentWeekStart })
        .addSelect('ROW_NUMBER() OVER (PARTITION BY wpd.worker_id ORDER BY wpd.start_date ASC)', 'row_num')
        .orderBy('wpd.worker_id')
        .addOrderBy('wpd.start_date')
        .getRawMany();

    const filteredResults = results.filter(result => result.row_num === '1');

    const workerPerformanceMap = {};
    filteredResults.forEach(result => {
        workerPerformanceMap[result.worker_id] = {
            end_date: result.end_date,
            performance_number: Number(result.performance_number),
        };
    });
    return workerPerformanceMap;
};


export const updateWorkersQualificationStatus: any = async (workerIds: Array<number>, qualificationStatus, loggedInUserId) => {
    if (!workerIds.length) return;

    return getConnection().createQueryBuilder()
        .update('workers')
        .set({
            trainingQualificationStatus: qualificationStatus,
            updatedBy: loggedInUserId,
            updatedAt: new Date()
        })
        .where({ id: In(workerIds) })
        .execute();
}


export const getWorkersByCriteria: any = async (clientId, agencyId, type, employeeIdArray) => {
    let query = getRepository(Workers)
        .createQueryBuilder('workers')
        .leftJoin('workers.user', 'user')
        .leftJoin('workers.client', 'client')
        .select([
            'workers.employeeId as employeeId',
            'user.email as email',
            'user.nationalInsuranceNumber as nationalInsuranceNumber',
            'workers.agencyId as agencyId',
            'workers.clientId as clientId',
            'client.name as clientName'
        ])
        .where('workers.type = :type', { type })
        .andWhere('workers.employeeId IN (:...employeeIdArray)', { employeeIdArray });

    if (type == WorkerTypes.PERMANENT) {
        query = query.andWhere('workers.clientId = :clientId', { clientId }); // For permanent workers, agencyId should be null
    } else {
        query = query.andWhere('workers.agencyId = :agencyId', { agencyId }); // For temporary workers, include agencyId
    }

    return await query.getRawMany();
}