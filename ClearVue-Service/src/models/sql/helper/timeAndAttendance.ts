import { getRepository, In } from 'typeorm';
import { TimeAndAttendance } from '../entities/TimeAndAttendance';
import { TimeAndAttendanceData } from '../entities/TimeAndAttendanceData';
import { TotalAgencyPay } from '../entities/TotalAgencyPay';
import { TotalAgencyPayData } from '../entities/TotalAgencyPayData';
import { deletePayrollDataHelper } from './payroll';
import { getNumberValue } from '../../../utils';
import { Workers } from '../entities/Workers';
import { PayTypes } from '../../../common';
import { PayrollDetailedSummary } from '../entities/PayrollDetailedSummary';

export const createTimeAndAttendance: any = async (data) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.insert(data);
    return response.generatedMaps[0];
};

export const getTimeAndAttendanceList: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.createQueryBuilder("time_and_attendance")
        .offset((page - 1) * limit)
        .limit(limit)
        .select(['time_and_attendance.id AS id', 'time_and_attendance.name AS name', 'time_and_attendance.path AS path', 'time_and_attendance.status AS status'])
        .orderBy(sortBy, sortType)
        .where(whereClause, whereClauseValue)
        .getRawMany();
    return response;
}

export const getTimeAndAttendanceListWithPayrollSummary: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.createQueryBuilder("time_and_attendance")
        .innerJoin('time_and_attendance.agency', 'agency')
        .innerJoin('time_and_attendance.client', 'client')
        .innerJoin('time_and_attendance.site', 'site')
        .leftJoin('time_and_attendance.payrollMetas', 'payroll_meta')
        .leftJoin('payroll_meta.payrollSummaries', 'payroll_summary')
        .leftJoin('payroll_meta.payrolls', 'payroll')
        .leftJoin('total_agency_pay', 'tap', 'tap.client_id = client.id AND tap.agency_id = agency.id AND tap.site_id = site.id AND tap.start_date = time_and_attendance.start_date')
        .leftJoin(
            subQuery => subQuery
                .select('time_and_attendance_id')
                .addSelect('SUM(total_credit_due)', 'total_credits')
                .from('credit_dues', 'cd')
                .groupBy('time_and_attendance_id'),
            'credit_dues_summary',
            'credit_dues_summary.time_and_attendance_id = time_and_attendance.id'
        )
        .select([
            "date_format(time_and_attendance.created_at,'%Y-%m-%d') as time_and_attendance_created_at",
            "date_format(payroll_meta.created_at,'%Y-%m-%d') as payroll_meta_created_at",
            'time_and_attendance.name AS time_and_attendance_name',
            'payroll_meta.name AS payroll_meta_name',
            'time_and_attendance.status AS time_and_attendance_status',
            'payroll_meta.status AS payroll_meta_status',
            'time_and_attendance.id AS time_and_attendance_id',
            'payroll_summary.payroll_meta_id AS payroll_meta_id',
            'client.id AS client_id',
            'client.name AS client_name',
            'agency.id AS agency_id',
            'agency.name AS agency_name',
            'site.id AS site_id',
            'site.name AS site_name',
            'payroll_summary.total_hours AS total_hours',
            'payroll_summary.total_charge AS total_charge',
            'payroll_summary.total_pay AS total_pay',
            'payroll_summary.total_agency_margin AS total_agency_margin',
            'payroll_summary.actual_margin AS actual_margin',
            'payroll_summary.rate_card_margin AS rate_card_margin',
            'payroll_summary.credit_per_hour AS credit_per_hour',
            'payroll_summary.clearvue_savings AS clearvue_savings',
            "date_format(time_and_attendance.start_date,'%Y-%m-%d') as start_date",
            "date_format(time_and_attendance.end_date,'%Y-%m-%d') as end_date",
            'tap.id AS total_agency_pay_id',
            'tap.status AS total_agency_pay_status',
            "ROUND(SUM(payroll.actual_cost_to_employ),2) AS actual_employment_costs",
            'payroll_summary.total_holiday_pay AS total_holiday_pay',
            'payroll_summary.self_bill_value AS self_bill_value',
            'payroll_summary.holiday_charge AS holiday_charge',
            'payroll_summary.accrual_value AS accrual_value',
            'payroll_summary.total_worked_charge AS total_worked_charge',
            'payroll_summary.holiday_employment_cost AS holiday_employment_cost',
            'payroll_summary.total_wtr_costs AS total_wtr_costs',
            'payroll_summary.calculated_pathway AS calculated_pathway',
            'payroll_summary.other_assignment_pay AS other_assignment_pay_value',
            'payroll_summary.adjustment_count AS adjustment_count',
            'COALESCE(credit_dues_summary.total_credits, 0) AS training_credits'
        ])
        .where(whereClause, whereClauseValue)
        .groupBy('time_and_attendance.id')
        .orderBy(sortBy, sortType)
        .addOrderBy('time_and_attendance.id', sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)
        .execute();
    return response;
}

export const getPayrollInvoiceHelper: any = async (whereClause, whereClauseValue, sortBy, sortType) => {
    const PayrollDetailedSummaryRepository = getRepository(PayrollDetailedSummary);
    let response = await PayrollDetailedSummaryRepository.createQueryBuilder("payroll_detailed_summary")
        .innerJoin('payroll_detailed_summary.agency', 'agency')
        .innerJoin('payroll_detailed_summary.client', 'client')
        .innerJoin('payroll_detailed_summary.site', 'site')
        .innerJoin('payroll_detailed_summary.department', 'department')
        .leftJoin('payroll_detailed_summary.payrollMeta', 'payroll_meta')
        .select([
            'client.id AS client_id',
            'client.name AS client_name',
            'agency.id AS agency_id',
            'agency.name AS agency_name',
            'payroll_detailed_summary.payroll_meta_id AS payroll_meta_id',
            'site.name AS site_name',
            "date_format(payroll_meta.start_date,'%Y-%m-%d') as start_date",
            "date_format(payroll_meta.end_date,'%Y-%m-%d') as end_date",
            'payroll_meta.vat_code AS vat_code',
            'payroll_meta.vat_rate AS vat_rate',
            'payroll_meta.site_cost_centre AS site_cost_centre',
            'IFNULL(payroll_detailed_summary.department_cost_centre, department.name) AS department_cost_centre',
            'payroll_detailed_summary.pay_type AS pay_type',
            'ROUND(SUM(payroll_detailed_summary.total_hours), 2) AS total_hours',
            'ROUND(SUM(payroll_detailed_summary.total_cost), 2) AS total_cost'
        ])
        .where(whereClause, whereClauseValue)
        .groupBy('payroll_detailed_summary.payroll_meta_id')
        .addGroupBy('payroll_detailed_summary.department_id')
        .addGroupBy('payroll_detailed_summary.pay_type')
        .orderBy(sortBy, sortType)
        .execute();
    return response;
};


export const getSupervisorsWeeklyDataHelper: any = async (whereClause, whereClauseValue, page, limit, sortBy, sortType) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.createQueryBuilder("time_and_attendance")
        .innerJoin('time_and_attendance.agency', 'agency')
        .innerJoin('time_and_attendance.client', 'client')
        .innerJoin('time_and_attendance.site', 'site')
        .leftJoin('time_and_attendance.payrollMetas', 'payroll_meta')
        .leftJoin('payroll_meta.payrollSummaries', 'payroll_summary')
        .select([
            'payroll_summary.payroll_meta_id AS payroll_meta_id',
            "date_format(time_and_attendance.start_date,'%Y-%m-%d') as start_date",
            "date_format(time_and_attendance.end_date,'%Y-%m-%d') as end_date",
            'client.id AS client_id',
            'client.name AS client_name',
            'agency.id AS agency_id',
            'agency.name AS agency_name',
            'site.id AS site_id',
            'site.name AS site_name',
            'payroll_summary.supervisor_hours AS supervisor_hours',
            'payroll_summary.supervisor_charges AS supervisor_charges',
            'payroll_summary.identified_supervisors AS identified_supervisors',
            'payroll_summary.non_supervisor_hours AS non_supervisor_hours',
            'payroll_summary.non_supervisor_charges AS non_supervisor_charges',
            'IFNULL(payroll_summary.supervisor_charges, 0) + IFNULL(payroll_summary.non_supervisor_charges, 0) AS total_charge',
            'IFNULL(payroll_summary.supervisor_hours, 0) + IFNULL(payroll_summary.non_supervisor_hours, 0) AS total_hours'
        ])
        .where(whereClause, whereClauseValue)
        .andWhere('(payroll_summary.supervisor_hours IS NOT NULL AND payroll_summary.supervisor_charges IS NOT NULL AND payroll_summary.non_supervisor_hours IS NOT NULL AND payroll_summary.non_supervisor_charges IS NOT NULL)')
        .groupBy('time_and_attendance.id')
        .orderBy(sortBy, sortType)
        .addOrderBy('time_and_attendance.id', sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)
        .execute();
    return response;
}

export const getSupervisorsWeeklyDataCountHelper: any = async (whereClause, whereClauseValue) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.createQueryBuilder("time_and_attendance")
        .leftJoin('time_and_attendance.payrollMetas', 'payroll_meta')
        .innerJoin('time_and_attendance.site', 'site')
        .leftJoin('payroll_meta.payrollSummaries', 'payroll_summary')
        .select('COUNT(time_and_attendance.id) AS count')
        .where(whereClause, whereClauseValue)
        .andWhere('(payroll_summary.supervisor_hours IS NOT NULL AND payroll_summary.supervisor_charges IS NOT NULL AND payroll_summary.non_supervisor_hours IS NOT NULL AND payroll_summary.non_supervisor_charges IS NOT NULL)')
        .getRawOne();
    return response;
};

export const getTimeAndAttendanceById: any = async (id) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.findOne({ id });
};

export const getTimeAndAttendance: any = async (filter) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.findOne(filter);
};

export const getTimeAndAttendanceMultiple: any = async (filter) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.find(filter);
};

export const deleteTimeAndAttendanceById: any = async (id) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.delete({ id });
};

export const deleteTimeAndAttendanceDataById: any = async (timeAndAttendanceId) => {
    const TimeAndAttendanceDataRepository = getRepository(TimeAndAttendanceData);
    return await TimeAndAttendanceDataRepository.delete({ timeAndAttendanceId });
};

export const getTimeAndAttendanceCount: any = async (filter = {}) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.count(filter)
};

export const updateTimeAndAttendance: any = async (id, body) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    return await TimeAndAttendanceRepository.update({ id }, body);
};

export const getTimeAndAttendanceCountWithTotalPayrollSaving: any = async (whereClause, whereClauseValue) => {
    const TimeAndAttendanceRepository = getRepository(TimeAndAttendance);
    let response = await TimeAndAttendanceRepository.createQueryBuilder("time_and_attendance")
        .leftJoin('time_and_attendance.payrollMetas', 'payroll_meta')
        .innerJoin('time_and_attendance.site', 'site')
        .leftJoin('payroll_meta.payrollSummaries', 'payroll_summary')
        .select('COUNT(time_and_attendance.id) AS count')
        .addSelect('SUM(payroll_summary.clearvue_savings) AS total')
        .where(whereClause, whereClauseValue)
        .getRawOne();
    return response;
};

export const getTotalAgencyPayDataHelper: any = async (whereClause, whereClauseValue) => {
    const totalAgencyPayRepository = getRepository(TotalAgencyPay);
    let response = await totalAgencyPayRepository.createQueryBuilder("total_agency_pay")
        .select('name')
        .where(whereClause, whereClauseValue)
        .getRawOne();
    return response;
};

export const sumTotalAgencyPayValueForAllWorkers: any = async (tapId) => {
    const totalAgencyPayRepository = getRepository(TotalAgencyPayData);
    const sumResult = await totalAgencyPayRepository
        .createQueryBuilder('tap_data')
        .select('SUM(tap_data.tap_value)', 'total_sum')
        .where('tap_data.total_agency_pay_id = :tapId', { tapId })
        .getRawOne();

    // Extract the total sum from the result
    const totalSum = sumResult.total_sum;

    // Return the total sum
    return getNumberValue(totalSum);
};

export const updateTotalAgencyPayDataByEmployeeId: any = async (employeeId, updatePayload) => {
    const totalAgencyPayRepository = getRepository(TotalAgencyPayData);
    let response = await totalAgencyPayRepository
        .createQueryBuilder()
        .update(TotalAgencyPayData)
        .set(updatePayload)
        .where('employeeId = :employeeId', { employeeId })
        .execute();
    return response;
};


export const getTotalAgencyPay: any = async (filter) => {
    const TotalAgencyPayRepository = getRepository(TotalAgencyPay);
    return await TotalAgencyPayRepository.findOne(filter);
};

export const createTotalAgencyPay: any = async (data) => {
    const TotalAgencyPayRepository = getRepository(TotalAgencyPay);
    let response = await TotalAgencyPayRepository.insert(data);
    return response.generatedMaps[0];
};

export const deleteTotalAgencyPayDataById: any = async (totalAgencyPayId) => {
    const TotalAgencyPayDataRepository = getRepository(TotalAgencyPayData);
    return await TotalAgencyPayDataRepository.delete({ totalAgencyPayId });
};

export const deleteTotalAgencyPayById: any = async (id) => {
    const TotalAgencyPayRepository = getRepository(TotalAgencyPay);
    return await TotalAgencyPayRepository.delete({ id });
};

export const getTotalAgencyPayById: any = async (id) => {
    const TotalAgencyPayRepository = getRepository(TotalAgencyPay);
    return await TotalAgencyPayRepository.findOne({ id });
};

export const deleteTAPDataHelper: any = async (payload) => {
    await deletePayrollDataHelper(TotalAgencyPayData, payload.client_id, payload.site_id, payload.start_date, payload.agency_id)
    await deletePayrollDataHelper(TotalAgencyPay, payload.client_id, payload.site_id, payload.start_date, payload.agency_id)
};

export const collectWorkersWithSupervisorPaytypeAndNoSupervisorStatus: any = async (isSupervisor, employeeId, workersWithSupervisorPaytypeAndNoSupervisorStatus) => {
    if (!isSupervisor) {
        workersWithSupervisorPaytypeAndNoSupervisorStatus.add(employeeId)
    }
};

export const getWorkersTrainingData = async (clientId, agencyId, siteId, tnaStartDate, weekStartDate, weekEndDate, limitedHours = null) => {
    const workersRepository = getRepository(Workers);

    const trainingDataQuery = workersRepository.createQueryBuilder('workers')
        .innerJoin(TimeAndAttendanceData, 'time_and_attendance_data', 'workers.id = time_and_attendance_data.worker_id')
        .select(['workers.id AS worker_id', 'workers.employee_id AS employee_id', 'workers.assignment_date AS assignment_date', 'workers.limited_hours AS limited_hours'])
        .where('time_and_attendance_data.client_id = :clientId', { clientId })
        .andWhere('time_and_attendance_data.agency_id = :agencyId', { agencyId })
        .andWhere('time_and_attendance_data.site_id = :siteId', { siteId })
        .andWhere('time_and_attendance_data.start_date <= :tnaStartDate', { tnaStartDate }) // Training received before the current TNA upload
        .andWhere('workers.assignment_date BETWEEN :weekStartDate AND :weekEndDate', { weekStartDate, weekEndDate })
        .andWhere('time_and_attendance_data.pay_type = :payType', { payType: PayTypes.INDUCTION_TRAINING }) // Only worker who recieved training
        .groupBy('workers.id');

    if (limitedHours !== null) {
        trainingDataQuery.andWhere('workers.limited_hours = :limitedHours', { limitedHours });
    }

    return await trainingDataQuery.getRawMany();
};