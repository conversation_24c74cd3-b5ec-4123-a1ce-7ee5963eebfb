import { getRepository } from 'typeorm';
import { Payroll, TimeAndAttendanceData, PayrollSummary, PayrollMeta, TimeAndAttendance, PensionStatusLog, HolidayPayrollSummary, CreditDues } from '../';
import { DeletePayrollDTO } from '../../../common';
import { applyCommonFiltersToQuery } from '../../../utils';
import { PayTypes, PensionStatus } from '../../../common/enum';
import { updateWorkersQualificationStatus } from './worker';
import { getTimeAndAttendance, getTimeAndAttendanceMultiple } from './timeAndAttendance';
import { PayrollDetailedSummary } from '../entities/PayrollDetailedSummary';

export const addPayrollData = async (data, queryRunner = null) => {
    if (queryRunner) {
        // Use the provided queryRunner to maintain transaction context
        return await queryRunner.manager.insert(Payroll, data);
    } else {
        // Fallback to original implementation if no queryRunner provided
        const payrollRepository = getRepository(Payroll);
        const response = await payrollRepository.insert(data);
        return response.generatedMaps[0];
    }
};

export const getPayrollsByPayrollMetaId: any = async (payrollMetaId) => {
    const payrollRepository = getRepository(Payroll);
    return await payrollRepository.createQueryBuilder('payroll')
        .innerJoin('payroll.worker', 'worker')
        .leftJoinAndSelect('TotalAgencyPayData', 'tap_data', 'tap_data.employee_id = worker.employee_id AND tap_data.start_date = payroll.start_date AND tap_data.end_date = payroll.end_date')
        .leftJoin('time_and_attendance_data', 'tad', 'tad.start_date = payroll.start_date AND tad.end_date = payroll.end_date AND tad.worker_id = worker.id AND tad.pay_type = :pay_type', { pay_type: 'Holiday' })
        .leftJoin('pension_status_log', 'pst', 'pst.worker_id = worker.id AND worker.pension_opt_out = :pension_opt_out AND pst.to = :to', { pension_opt_out: '0', to: PensionStatus.OPTED_IN })
        .leftJoin('holiday_payroll_summary', 'holiday_summary', 'holiday_summary.payroll_meta_id = payroll.payrollMetaId AND holiday_summary.worker_id = worker.id')
        .select([
            'worker.nationalInsuranceNumber AS national_insurance_number',
            'worker.employeeId AS employee_id',
            'worker.id AS worker_id',
            'payroll.totalCharge AS total_charge',
            'payroll.actualCostToEmploy AS actual_employment_costs',
            'payroll.totalMargin AS total_margin',
            'payroll.actualMargin AS actual_margin_per_hour',
            'payroll.rateCardMargin AS rate_card_margin',
            'payroll.creditPerHour AS credit_per_hour',
            'payroll.clearvue_savings AS total_savings',
            'payroll.totalPay AS total_pay',
            'payroll.nationalInsurance AS national_insurance',
            'payroll.pension AS pension',
            'payroll.apprenticeshipLevy AS apprenticeship_levy',
            'payroll.holiday AS wtr',
            'payroll.client_id AS client_id',
            'payroll.agency_id AS agency_id',
            'payroll.start_date AS payroll_start_date',
            'IFNULL(payroll.holiday_pay_type_value, "") AS holiday_pay_value',
            'payroll.total_hours AS total_hours',
            'payroll.under_twentyone AS under_twentyone',
            'payroll.under_twentytwo AS under_twentytwo',
            'payroll.within_twelveweeks AS within_twelveweeks',
            'worker.date_of_birth AS date_of_birth',
            'worker.start_date AS start_date',
            'worker.assignment_date AS assignment_date',
            'worker.pension_opt_out AS pension_opt_out',
            'payroll.pension_opt_out_prev AS pension_opt_out_prev',
            'IFNULL(tap_data.tap_value, "") AS total_agency_pay',
            'holiday_summary.workedHoursSaving AS worked_hours_saving',
            'IFNULL(holiday_summary.holidayEmploymentCost, "") AS paid_holiday_employment_cost',
            'holiday_summary.wtr_cost AS wtr_cost',
            'holiday_summary.accrualValue AS client_wtr_accrual',
            'holiday_summary.totalWorkedCharge AS total_worked_charge',
            'holiday_summary.calculatedPathway AS calculated_pathway',
            'CASE WHEN tad.pay_type = "Holiday" THEN tad.standard_pay + tad.overtime_pay ELSE "" END AS holiday_pay_value',
            'IFNULL(MAX(date_format(pst.createdAt, "%Y-%m-%d")),"") AS last_date_opt_in',
            'payroll.other_assignment_pay AS other_assignment_pay_value'
            // "IFNULL(holiday_summary.total_worked_charge, payroll.total_charge) AS worked_hours_charge",
            // "ROUND(payroll.clearvue_savings - holiday_summary.wtr_cost, 2) AS credit_value",
            // "ROUND(IFNULL(holiday_summary.total_worked_charge, payroll.total_charge) + holiday_summary.wtr_cost - payroll.clearvue_savings, 2) AS total_cost",
            // "IF(holiday_summary.calculated_pathway = '1|1', holiday_summary.client_wtr_accrual, NULL) AS accrual_value"
        ])
        .where(`payroll.payrollMetaId = :payroll_meta_id`, { "payroll_meta_id": payrollMetaId })
        .groupBy('worker.id')
        .orderBy('employee_id', 'ASC')
        .execute()
}


export const getSupervisorDetailsByPayrollMetaId: any = async (payrollMetaId, page, limit, sortBy, sortType) => {

    let payrollRepository = getRepository(Payroll)
        .createQueryBuilder('payroll')
        .innerJoin('payroll.worker', 'worker')
        .leftJoin('worker.job', 'job')
        .innerJoin('job.shift', 'shift')
        .innerJoin('job.jobAssociations', 'job_assoc')
        .innerJoin('job_assoc.site', 'site')
        .innerJoin('job_assoc.department', 'department')
        .select([
            'worker.employeeId AS employee_id',
            'worker.id AS worker_id',
            'worker.firstName AS first_name',
            'worker.workers_supervisor_status AS workers_supervisor_status',
            'payroll.totalCharge AS total_charge',
            'payroll.supervisorCharges AS supervisor_charges',
            'payroll.nonSupervisorCharges AS non_supervisor_charges',
            'COALESCE(payroll.supervisorHours, 0) + COALESCE(payroll.nonSupervisorHours, 0) AS total_hours',
            'payroll.supervisorHours AS supervisor_hours',
            'payroll.nonSupervisorHours AS non_supervisor_hours',
            'payroll.totalPay AS total_pay',
            'job.name as job_name',
            'job.type as job_type',
            'site.name as site_name',
            'department.name as department_name',
            'shift.name as shift_name',
        ])
        .where(`payroll.payrollMetaId = :payroll_meta_id`, { "payroll_meta_id": payrollMetaId })
        .andWhere('(payroll.supervisorCharges IS NOT NULL AND payroll.nonSupervisorCharges IS NOT NULL AND payroll.supervisorHours IS NOT NULL AND payroll.nonSupervisorHours IS NOT NULL)')
        .andWhere('payroll.flaggedSupervisor = 1')
        .groupBy('worker.id')
        .offset((page - 1) * limit)
        .limit(limit)

    if (sortBy.toLowerCase() === "role") {
        payrollRepository.orderBy("job_name", sortType)
            .addOrderBy('shift_name', sortType)
            .addOrderBy('department_name', sortType)
    } else payrollRepository.orderBy(sortBy, sortType)

    return await payrollRepository.execute();
}

export const getSupervisorsWorkerDetailsCountHelper: any = async (payrollMetaId) => {
    return await getRepository(Payroll)
        .createQueryBuilder('payroll')
        .innerJoin('payroll.worker', 'worker')
        .leftJoin('worker.job', 'job')
        .innerJoin('job.shift', 'shift')
        .innerJoin('job.jobAssociations', 'job_assoc')
        .innerJoin('job_assoc.site', 'site')
        .innerJoin('job_assoc.department', 'department')
        .select('COUNT(worker.id) AS count')
        .where(`payroll.payrollMetaId = :payroll_meta_id`, { "payroll_meta_id": payrollMetaId })
        .andWhere('(payroll.supervisorCharges IS NOT NULL AND payroll.nonSupervisorCharges IS NOT NULL AND payroll.supervisorHours IS NOT NULL AND payroll.nonSupervisorHours IS NOT NULL)')
        .andWhere('payroll.flaggedSupervisor = 1')
        .getRawOne();
};

export const getStatusCountForWorkers = async (workerIds: number[]) => {
    const pensionStatusLogRepository = getRepository(PensionStatusLog);

    const statusCounts = await pensionStatusLogRepository
        .createQueryBuilder('pst')
        .select(['pst.worker_id', 'COUNT(pst.id) AS status_count'])
        .where('pst.worker_id IN (:...workerIds)', { workerIds })
        .groupBy('pst.worker_id')
        .getRawMany();

    return statusCounts;
};

export const deletePayrollByMetaId: any = async (payrollMetaId) => {
    const payrollRepository = getRepository(Payroll);
    return await payrollRepository.delete({ payrollMetaId });
};

export const deletePayrollData: any = async (payload: DeletePayrollDTO, loggedInUser) => {
    const dataParams = { startDate: payload.start_date, clientId: payload.client_id, siteId: payload.site_id };
    if (payload.agency_id) dataParams["agencyId"] = payload.agency_id;

    const timeAndAttendances = await getTimeAndAttendanceMultiple(dataParams);
    if (timeAndAttendances.length) await deleteCreditDuesBasedOnTnaIdHelper(timeAndAttendances.map(item => item.id), loggedInUser);

    const entities = [TimeAndAttendanceData, Payroll, PayrollDetailedSummary, PayrollSummary, HolidayPayrollSummary, PayrollMeta, TimeAndAttendance];
    for (const entity of entities) {
        await deletePayrollDataHelper(entity, payload.client_id, payload.site_id, payload.start_date, payload.agency_id);
    }
};

export const deletePayrollDataHelper: any = async (repository, client_id, site_id, start_date, agency_id) => {
    const repo = getRepository(repository).createQueryBuilder()
        .delete()
        .where('clientId = :client_id', { client_id })
        .andWhere('siteId = :site_id', { site_id })
        .andWhere('startDate = :start_date', { start_date })

    if (agency_id) repo.andWhere('agencyId = :agency_id', { agency_id })

    return await repo.execute();
};


export const getTrainingCostsAndHours = async (clientId, agencyId, siteId, workerIds, startDate, assignmentWeekStart, maxTrainingHours) => {
    const formattedResults = {};
    if (!workerIds.length) return formattedResults;

    const results = await getRepository(Payroll).createQueryBuilder('payroll')
        .select([
            'payroll.worker_id AS worker_id',
            'payroll.training_hours AS training_hours',
            'payroll.training_employment_cost AS training_employment_cost'
        ])
        .where('payroll.client_id = :clientId', { clientId })
        .andWhere('payroll.agency_id = :agencyId', { agencyId })
        .andWhere('payroll.site_id = :siteId', { siteId })
        .andWhere('payroll.worker_id IN (:...workerIds)', { workerIds })
        .andWhere('payroll.start_date <= :startDate', { startDate })
        .andWhere('payroll.start_date >= :assignment_date_week_start', { "assignment_date_week_start": assignmentWeekStart })
        .orderBy('payroll.start_date', 'ASC')
        .getRawMany();

    const workerData = results.reduce((acc, row) => {
        const { worker_id, training_hours, training_employment_cost } = row;
        if (!acc[worker_id]) {
            acc[worker_id] = {
                total_training_hours: 0,
                total_training_employment_cost: 0,
                training_data: []
            };
        }
        acc[worker_id].training_data.push({
            training_hours: Number(training_hours),
            training_employment_cost: Number(training_employment_cost)
        });
        return acc;
    }, {});

    for (const [worker_id, data] of Object.entries(workerData)) {
        let accumulatedHours = 0;
        let accumulatedCost = 0;
        for (const entry of (data as any).training_data) {
            const { training_hours, training_employment_cost } = entry;
            if (accumulatedHours + training_hours <= maxTrainingHours) {
                accumulatedHours += training_hours;
                accumulatedCost += training_employment_cost;
            } else {
                const remainingHours = maxTrainingHours - accumulatedHours;
                accumulatedCost += (training_employment_cost / training_hours) * remainingHours;
                accumulatedHours = maxTrainingHours;
                break;
            }
        }

        formattedResults[worker_id] = {
            total_training_employment_cost: accumulatedCost,
            total_training_hours: accumulatedHours
        };
    }

    return formattedResults;
};

export const addCreditDuesData: any = async (data) => {
    const creditDueRepository = getRepository(CreditDues);
    const response = await creditDueRepository.insert(data);
    return response.generatedMaps[0];
};


export const getCreditDuesData = async (whereClause, whereClauseValue, sortBy, sortType, page, limit) => {
    const creditDuesRepository = getRepository(CreditDues);

    const query = creditDuesRepository.createQueryBuilder('credit_dues')
        .innerJoin('credit_dues.worker', 'worker')
        .innerJoin('credit_dues.site', 'site')
        .innerJoin('credit_dues.agency', 'agency')
        .select([
            'worker.employeeId AS employee_id',
            'worker.id AS worker_id',
            'worker.limited_hours AS limited_hours',
            "date_format(worker.assignment_date,'%Y-%m-%d') as assignment_date",
            'site.id as site_id',
            'site.name as site_name',
            'agency.id as agency_id',
            'agency.name as agency_name',
            'credit_dues.training_employment_cost as actual_training_cost',
            'IFNULL(credit_dues.performance_number,"") as performance_target_achieved',
            'credit_dues.performance_threshold as performance_threshold',
            'IFNULL(credit_dues.achievement_week,"") as achievement_week',
            'CEIL(credit_dues.applied_credit_rate) as applied_credit_rate',
            'credit_dues.total_credit_due as total_credit_due',
        ])
        .where(whereClause, whereClauseValue)
        .orderBy(sortBy, sortType.toUpperCase())
        .offset((page - 1) * limit)
        .limit(limit)

    const data = await query.execute();
    const count = await query.getCount(); // Also return the total count of rows available without limit

    return { data: data, count: data.length, total: count };
};

export const deleteCreditDuesBasedOnTnaIdHelper: any = async (timeAndAttendanceIds, loggedInUser) => {

    const results = await getRepository(CreditDues).createQueryBuilder('credit_dues')
        .select('credit_dues.worker_id')
        .where('credit_dues.time_and_attendance_id IN (:...timeAndAttendanceIds)', { timeAndAttendanceIds })
        .getRawMany();

    // Revert worker's qualification status back to null
    await updateWorkersQualificationStatus(results.map(result => result.worker_id), null, loggedInUser.user_id);

    // delete credit dues for given TNA
    await getRepository(CreditDues).createQueryBuilder()
        .delete()
        .where('timeAndAttendanceId IN (:...timeAndAttendanceIds)', { timeAndAttendanceIds })
        .execute();
}
