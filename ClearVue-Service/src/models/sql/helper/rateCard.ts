import { getRepository } from 'typeorm';
import { RateCard, RateCardData } from '../';

/**
 * create Rate Card
 */
export const createRateCard: any = async (body) => {
    const rateCardRepository = getRepository(RateCard);
    const response = await rateCardRepository.insert(body);
    return response.generatedMaps[0];
};

/**
 * get Rate Card list
 */
export const getRateCardList: any = async (clientId, siteId) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.createQueryBuilder("rate_card")
        .innerJoin("rate_card.agency", "agency")
        .select([
            'rate_card.id as id',
            'rate_card.card_name as name',
            'rate_card.filename as filename',
            'rate_card.agency_id as agency_id',
            'agency.name as agency_name',
            'rate_card.id as id',
        ])
        .where('rate_card.clientId = :clientId AND rate_card.siteId = :siteId', { clientId, siteId })
        .getRawMany();
};

/**
 * get Rate Card list
 */
export const getRateCardData: any = async (clientId, siteId) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.createQueryBuilder("rate_card")
        .innerJoin("rate_card.rateCardData", "rcd")
        .select([
            'rcd.pay_rate as pay_rate',
            'rcd.charge_rate as charge_rate',
            'rcd.pay_type as pay_type',
            'rcd.performance_low as performance_low',
            'rcd.performance_high as performance_high',
            'rcd.supervisor_rate as supervisor_rate',
        ])
        .where('rate_card.clientId = :clientId AND rate_card.siteId = :siteId', { clientId, siteId })
        .getRawMany();
};

export const getRateCardForDropDown: any = async (clientId) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.createQueryBuilder("rate_card")
        .select(['id', 'name'])
        .where('rate_card.clientId = :clientId', { clientId })
        .orderBy('name', 'ASC')
        .getRawMany();
};


/**
 * get Rate Card By Id
 */
export const getRateCardById: any = async (id) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.findOne({ id });
};

/**
 * get Rate Card count
 */
export const getRateCardCount: any = async (clientId) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.count({ clientId })
};


export const getRateCard: any = async (filter) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.find(filter);
};


export const deleteRateCardById: any = async (id) => {
    const rateCardRepository = getRepository(RateCard);
    return await rateCardRepository.delete({ id });
};

export const addRateCardData: any = async (data) => {
    const rateCardRepository = getRepository(RateCardData);
    let response = await rateCardRepository.insert(data);
    return response.generatedMaps[0];
};

export const deleteRateCardDataById: any = async (rateCardId) => {
    const rateCardRepository = getRepository(RateCardData);
    return await rateCardRepository.delete({ rateCardId });
};

