import { getRepository, getConnection, In, getManager } from 'typeorm';
import { Message, WorkerTraining, MessageReceiverWorkers, Workers, MessageReaction, MessageComment } from '../';
import { defaultAppreciationJson, HallOfFameTypes, WorkerSideMessagesType, MessageType } from '../../../common';
import { Template } from '../entities/Template';

/**
 * Create new message
 * @param  {} payload
 * @param  {number} loggedInUser
 */
export const createMessage: any = async (payload, loggedInUser: number, params: any) => {
    return (await getRepository(Message).save({
        name: payload.name,
        title: payload.title,
        titleTranslations: payload.title_translations || null,
        type: payload.type,
        from: payload.from,
        body: payload.body,
        receiver: payload.send_to,
        sendBy: loggedInUser.toString(),
        createdBy: loggedInUser.toString(),
        updatedBy: loggedInUser.toString(),
        siteId: params.site_id,
        clientId: params.client_id,
        agencyId: params.agency_id || null,
        isCommentAllowed: payload.is_comment_allowed
    })).id;
};


export const createSystemTypeMessage: any = async (messageId: number, workerIds: Array<number>) => {
    let receivers = JSON.stringify([{ data: workerIds, type: "workers" }]);
    const entityManager = getManager();
    return await entityManager.query(`
    INSERT INTO message(name, title, title_translations, message.from, client_id, agency_id, label, body, receiver, send_by, created_by, updated_by, type)
    SELECT name, title, title_translations, message.from, client_id, agency_id, label, body, '${receivers}', send_by, created_by, updated_by, "SYSTEM" FROM message WHERE id = ${messageId};
    `)
}


/**
 * Update Hall of fame data for the workers.
 * @param  {string} fieldName
 * @param  {number} loggedInUser
 * @param  {Array<number>} workerIds
 */
export const updateHallOfFameDataForWorkers: any = async (fieldName: string, loggedInUser: number, workerIds: Array<number>) => {
    let appreciationJson = JSON.parse(defaultAppreciationJson);

    if (fieldName === HallOfFameTypes.AWARD) {
        appreciationJson.award = 1;
    } else if (fieldName === HallOfFameTypes.BADGE) {
        appreciationJson.badge = 1;
    } else if (fieldName === HallOfFameTypes.RECOGNITION) {
        appreciationJson.recognition = 1;
    }

    let caseStatement = `CASE WHEN appreciation IS NULL THEN '${JSON.stringify(appreciationJson)}' ELSE JSON_SET(appreciation, '$.${fieldName}', JSON_EXTRACT(appreciation, '$.${fieldName}') + 1) END`;

    await getConnection().createQueryBuilder()
        .update('workers')
        .set({
            appreciation: () => caseStatement,
            updatedBy: loggedInUser.toString(),
            updatedAt: new Date()
        })
        .where({ id: In(workerIds) })
        .execute();
};


/**
 * Add record in worker training
 * @param  {} messageId
 * @param  {Array<number>} workerIds
 * @param  {number} loggedInUser
 */
export const addWorkerTraining: any = async (messageId: number, workerIds: Array<number>, loggedInUser: number) => {
    let dbPayload = prepareMessageReceiverGroupPayload(workerIds, loggedInUser, messageId);
    await getRepository(WorkerTraining).save(dbPayload);
}


const prepareMessageReceiverGroupPayload = (workerIds, loggedInUser, messageId) => {
    let dbPayload = [];
    workerIds.forEach((workerId) => {
        dbPayload.push({
            messageId: messageId,
            workerId: workerId,
            createdBy: loggedInUser.toString(),
            updatedBy: loggedInUser.toString()
        })
    });

    return dbPayload;
}


/**
 * Add record in message receiver group for all the workers who are going to receive the message
 * @param  {} messageId
 * @param  {Array<number>} workerIds
 * @param  {number} loggedInUser
 */
export const addRecordInMessageReceiverGroup: any = async (messageId: number, workerIds: Array<number>, loggedInUser: number) => {
    let dbPayload = prepareMessageReceiverGroupPayload(workerIds, loggedInUser, messageId);
    await getRepository(MessageReceiverWorkers).insert(dbPayload);
}

/**
 * Get list of sent messages
 * @param  {number} sendBy
 * @param  {any} params
 * @param  {string} sortBy
 * @param  {any} sortType
 * @param  {number} page
 * @param  {number} limit
 */
export const getSentMessageList: any = async (sendBy: number, params: any, sortBy: string, sortType: any, page: number, limit: number) => {
    let whereCondition = `message.send_by = :send_by`;
    let whereConditionValue = { "send_by": sendBy };

    // No column name "sent_to" in table, so assigne default sorting values.
    if (sortBy.toLowerCase() === "sent_to") {
        sortBy = "created_at";
        sortType = "DESC";
    }

    for (let key in params) {
        whereCondition += ` AND message.${key} = :${key}`
        whereConditionValue[key] = params[key];
    }

    let likeDislikeSubquery = getRepository(MessageReaction).createQueryBuilder("message_reaction")
        .select([`COALESCE(SUM(reaction = 'LIKE'), 0) AS likes`, `COALESCE(SUM(reaction = 'DISLIKE'), 0) AS dislikes`, `message_reaction.message_id AS message_id`])
        .groupBy("message_id")

    return [
        await getRepository(Message).createQueryBuilder("message")
            .leftJoinAndSelect(
                `(${likeDislikeSubquery.getQuery()})`,
                "subquery",
                "subquery.message_id = message.id"
            )
            .select([`message.id as id`, `message.name as name`, `message.title as title`, `message.titleTranslations as title_translations`, `message.type as type`, `message.from as 'from'`, `message.body as body`, `message.receiver as receiver`, `message.createdAt as created_at`,
                `IFNULL(subquery.likes, 0) as likes`,
                `IFNULL(subquery.dislikes, 0) as dislikes`
            ])
            .where(whereCondition, whereConditionValue)
            .orderBy(['likes', 'dislikes'].includes(sortBy.toLowerCase()) ? sortBy : `message.${sortBy}`, sortType)
            .offset((page - 1) * limit)
            .limit(limit)
            .getRawMany(),
        await getRepository(Message).createQueryBuilder("message")
            .where(whereCondition, whereConditionValue).getCount()
    ]
};


/**
 * Get list of SYSTEM_DEFAULT messages Specific to given Client or Agency  
 * @param  {any} params
 * @param  {string} sortBy
 * @param  {any} sortType
 * @param  {number} page
 * @param  {number} limit
 */
export const getSystemDefaultMessageList: any = async (params: any, sortBy: string, sortType: any, page: number, limit: number) => {
    let whereCondition = `message.send_by = '1'`;
    let whereConditionValue = {};

    for (let key in params) {
        whereCondition += ` AND message.${key} = :${key}`;
        whereConditionValue[key] = params[key];
    }

    return [
        await getRepository(Message).createQueryBuilder("message")
            .select([`message.id as id`, `message.name as name`, `message.title as title`, `message.titleTranslations as title_translations`, `message.type as type`, `message.from as 'from'`, `message.body as body`, `message.receiver as receiver`, `message.createdAt as created_at`])
            .where(whereCondition, whereConditionValue)
            .orderBy(`message.${sortBy}`, sortType)
            .offset((page - 1) * limit)
            .limit(limit)
            .getRawMany(),
        await getRepository(Message).createQueryBuilder("message")
            .where(whereCondition, whereConditionValue).getCount()
    ]
};



/**
 * Assign AUTOMATED(`SYSTEM_DEFAULT`) messages to Newly created AGENCY.
 * @param  {number} agency_id
 */
export const assigneAutomatedMessagesToAgency: any = async (agency_id: number) => {
    const entityManager = getManager();

    return await entityManager.query(`
        INSERT INTO message (name, title, title_translations, type, message.from, client_id, site_id, agency_id, label, body, receiver, send_by, created_by, created_at, updated_by, updated_at, is_comment_allowed)
        SELECT md.name, md.title, md.title_translations, 'SYSTEM_DEFAULT', md.from, NULL, NULL, ${agency_id}, md.label, md.body, NULL, 1, md.created_by, NOW(), md.updated_by, NOW(), 0 FROM message_system_default as md where md.assigned_to_agency = 1;
    `)
};


/**
 * Assign AUTOMATED(`SYSTEM_DEFAULT`) messages to Newly created CLIENT.
 * @param  {number} client_id
 */
export const assigneAutomatedMessagesToClient: any = async (client_id: number) => {
    const entityManager = getManager();

    return await entityManager.query(`
        INSERT INTO message (name, title, title_translations, type, message.from, client_id, site_id, agency_id, label, body, receiver, send_by, created_by, created_at, updated_by, updated_at, is_comment_allowed)
        SELECT md.name, md.title, md.title_translations, 'SYSTEM_DEFAULT', md.from, ${client_id}, NULL, NULL, md.label, md.body, NULL, 1, md.created_by, NOW(), md.updated_by, NOW(), 0 FROM message_system_default as md where md.assigned_to_client = 1;
        `)
};



/**
 * update existing SYSTEM_DEFAULT message from `message` table for the given messageId.
 * @param  {number} messageId
 * @param  {any} payload
 * @param  {number} loggedInUser
 */
export const updateAutomatedMessage: any = async (messageId: number, payload: any, loggedInUser: number) => {
    let update_payload = {
        body: payload.body,
        updatedBy: loggedInUser.toString(),
        updatedAt: new Date()
    }

    if (payload.from) {
        update_payload["from"] = payload.from;
    }

    return await getConnection().createQueryBuilder()
        .update('message')
        .set(update_payload)
        .where({ id: messageId, type: 'SYSTEM_DEFAULT' })
        .execute();
};


/**
 * Get list of sent messages
 * @param  {number} sendBy
 * @param  {any} params
 * @param  {string} sortBy
 * @param  {any} sortType
 * @param  {number} page
 * @param  {number} limit
 */
export const getWorkerSideMessagesListFromDatabase: any = async (
    whereCondition: any, sortBy: string, sortType: any, page: number, limit: number, userId: number, messageTypes: any, type: string, groupBy: string) => {

    let str = []
    messageTypes.forEach((type) => {
        str.push(JSON.stringify(type))
    });

    let countQuery = getRepository(MessageReceiverWorkers).createQueryBuilder("message_receiver_workers")
        .innerJoin("message_receiver_workers.message", "message")
        .innerJoin("message_receiver_workers.worker", "workers")
        .innerJoin("workers.user", "user")
        .where(`message.type IN (${str})`)
        .orderBy(`${sortBy === "created_at" ? 'message_receiver_workers' : 'message'}.${sortBy}`, sortType);

    let selectQuery = getRepository(MessageReceiverWorkers).createQueryBuilder("message_receiver_workers")
        .select([
            `message.id`,
            `message.name`,
            `message.title`,
            `message.titleTranslations`,
            `message.from`,
            `message.body`,
            `message_receiver_workers.createdAt`,
            `message.client_id`,
            `message.agency_id`,
            `message.type as message_type`,
            `message.site_id`,
            `message.receiver`,
            `message.is_comment_allowed`
        ])
        .innerJoin("message_receiver_workers.message", "message")
        .innerJoin("message_receiver_workers.worker", "workers")
        .innerJoin("workers.user", "user")
        .where(`message.type IN (${str})`)
        .orderBy(`${sortBy === "created_at" ? 'message_receiver_workers' : 'message'}.${sortBy}`, sortType)
        .offset((page - 1) * limit).limit(limit);

    let unreadMessageCountQuery = getRepository(MessageReceiverWorkers).createQueryBuilder("message_receiver_workers")
        .innerJoin("message_receiver_workers.message", "message")
        .innerJoin("message_receiver_workers.worker", "workers")
        .innerJoin("workers.user", "user")
        .select([
            `IFNULL(SUM(IF(is_message_read = 0, 1, 0)), 0) AS unread_message_count`
        ])
        .where(`message.type IN (${str})`)


    if (type === WorkerSideMessagesType.FEED) {
        selectQuery = selectQuery.andWhere(whereCondition).andWhere(
            `((message.type = '${MessageType.GENERAL}' AND user.id = ${userId}) OR message.type IN ('${MessageType.RECOGNITION}', '${MessageType.AWARD}'))`
        ).groupBy(groupBy);
        countQuery = countQuery.andWhere(whereCondition).andWhere(
            `((message.type = '${MessageType.GENERAL}' AND user.id = ${userId}) OR message.type IN ('${MessageType.RECOGNITION}', '${MessageType.AWARD}'))`
        ).groupBy(groupBy);
        unreadMessageCountQuery = null;

    } else if (type === WorkerSideMessagesType.BADGE) {
        selectQuery = selectQuery.leftJoin(WorkerTraining, "worker_training", "message.id = worker_training.message_id")
            .addSelect([`message_receiver_workers.is_message_read`, `message_receiver_workers.id`])
            .andWhere(whereCondition).andWhere(`user.id = ${userId}`);
        countQuery = countQuery.leftJoin(WorkerTraining, "worker_training", "message.id = worker_training.message_id").andWhere(whereCondition).andWhere(`user.id = ${userId}`);
        unreadMessageCountQuery = unreadMessageCountQuery.leftJoin(WorkerTraining, "worker_training", "message.id = worker_training.message_id").andWhere(whereCondition).andWhere(`user.id = ${userId}`);

    } else if (!whereCondition) {
        selectQuery = selectQuery.addSelect([`message_receiver_workers.is_message_read`, `message_receiver_workers.id`]).andWhere(`user.id = ${userId}`);
        countQuery = countQuery.andWhere(`user.id = ${userId}`);
        unreadMessageCountQuery = unreadMessageCountQuery.andWhere(`user.id = ${userId}`);

    } else {
        selectQuery = selectQuery.addSelect([`message_receiver_workers.is_message_read`, `message_receiver_workers.id`]).andWhere(`user.id = ${userId}`).andWhere(whereCondition);
        countQuery = countQuery.andWhere(`user.id = ${userId}`).andWhere(whereCondition);
        unreadMessageCountQuery = unreadMessageCountQuery.andWhere(`user.id = ${userId}`).andWhere(whereCondition);
    }

    return [
        (await getManager().query(`SELECT COUNT(*) as message_count FROM (${countQuery.getQuery()}) AS temp_table;`))[0].message_count || 0,
        await selectQuery.getRawMany(),
        unreadMessageCountQuery ? await unreadMessageCountQuery.getRawOne() : {
            "unread_message_count": "0"
        }
    ]
};


/**
 * Get Message Reactions Count (Specifically counts of LIKES & DISLIKES)
 * @param  {Array<any>} message_ids
 */
export const getMessageReactionCount: any = async (message_ids: Array<any>) => {
    return await getRepository(MessageReaction).createQueryBuilder("message_reaction")
        .select([`COUNT(*) as reaction_counts, message_reaction.reaction, message_reaction.message_id`])
        .groupBy(`CONCAT(message_reaction.reaction, '|||', message_reaction.message_id)`)
        .where(`message_reaction.message_id IN (${message_ids}) AND message_reaction.reaction IN ('LIKE', 'DISLIKE')`)
        .getRawMany();
}


/**
 * Get worker specific reaction
 * @param  {Array<any>} message_ids
 * @param  {number} userId
 */
export const getMessageWorkerReactions: any = async (message_ids: Array<any>, userId: number) => {
    return await getRepository(MessageReaction).createQueryBuilder("message_reaction")
        .innerJoin('message_reaction.worker', 'worker')
        .innerJoin('worker.user', 'user')
        .select([`message_reaction.message_id`, `message_reaction.reaction as reaction`])
        .where(` message_reaction.message_id IN (${message_ids}) AND user.id = '${userId}'`)
        .getRawMany();
}


/**
 * Get worker associated site, client and agencies
 */
export const getWorkerAssociatedSiteAndAgency: any = async (userId: number) => {

    return await getRepository(Workers).createQueryBuilder("workers")
        .innerJoin("workers.user", "user")
        .innerJoin("workers.job", "job")
        .innerJoin("job.jobAssociations", "job_association")
        .select('`workers`.`client_id`, `workers`.`agency_id`, `job_association`.`site_id`')
        .where(`user.id = :user_id`, { "user_id": userId })
        .getRawMany();
}

/**
 * Add record in template
 * @param  {} payload
 * @param  {Number} loggedInUser
 */
export const createMessageTemplate: any = async (payload) => {
    return (await getRepository(Template).save(payload));
};

/**
 * Update record in template
 * @param  {} id
 * @param  {} body
 * @param  {Number} loggedInUser
 */
export const updateMessageTemplate: any = async (id, body, loggedInUser: number) => {
    if (body.languages) {
        body.language = body.languages.label;
        body.code = body.languages.value;
        delete body.languages;
    }
    if (body.hasOwnProperty("title_translations")) {
        body.titleTranslations = body.title_translations;
        delete body.title_translations;
    }
    body.modifiedAt = new Date();
    body.modifiedBy = loggedInUser.toString();

    return await getRepository(Template).update({ id }, body)
};


/**
 * Get list of available templates
 * @param  {number} sendBy
 * @param  {any} params
 */
export const getTemplateList: any = async (sendBy: number, params: any) => {
    let whereCondition = `template.created_by = ${sendBy}`;
    let whereConditionValue = { "send_by": sendBy };

    for (let key in params) {
        whereCondition += ` AND template.${key} = :${key}`
        whereConditionValue[key] = params[key];
    }

    return getRepository(Template).createQueryBuilder("template")
        .select([
            'template.id AS id',
            'CASE WHEN template.title_translations IS NOT NULL THEN CONCAT("(M) ", template.name) ELSE template.name END AS name'
        ])
        .orderBy("created_at", "DESC")
        .where(whereCondition, whereConditionValue)
        .execute();
};

/**
 * Get training message details
 * @param  {number} id
 */
export const getTrainingMessageDetails: any = async (id: number, userId: number) => {

    return await getRepository(WorkerTraining).createQueryBuilder("worker_training")
        .innerJoin("worker_training.message", "message")
        .innerJoin("worker_training.worker", "workers")
        .innerJoin("workers.user", "user")
        .select([
            `message.id`,
            `message.name`,
            `message.title`,
            `message.titleTranslations`,
            `message.from`,
            `message.body`,
            `message.createdAt`,
            `message.client_id`,
            `message.agency_id`,
            `user.name`,
            `worker_training.is_training_completed`,
            `worker_training.training_completed_at`,
            `worker_training.require_more_training`,
            `worker_training.require_training_updated_at`
        ])
        .where(`message.id = :id`, { "id": id })
        .andWhere(`workers.user_id = :user_id`, { "user_id": userId })
        .getRawOne()
};


/**
 * Get message details
 * @param  {number} id
 * @param  {number} message_id  
 */
export const getMessageDetailsModel: any = async (id: number, message_receiver_worker_id: number) => {
    let selectQuery = getRepository(Message).createQueryBuilder("message")
        .select([
            `message.id`,
            `message.name`,
            `message.title`,
            `message.titleTranslations`,
            `message.from`,
            `message.body`,
            `message.createdAt`,
            `message.client_id`,
            `message.agency_id`,
            `message.site_id`,
            `message.type`
        ])
        .where(`message.id = ${id}`)

    if (message_receiver_worker_id) {
        selectQuery = selectQuery.innerJoin("message.messageReceiverWorkers", "messageReceiverWorkers")
            .addSelect(`messageReceiverWorkers.createdAt AS createdAt`)
            .andWhere(`messageReceiverWorkers.id = :message_receiver_worker_id`, { "message_receiver_worker_id": message_receiver_worker_id })
    }

    return selectQuery.getRawOne();
};

/**
 * Get client/agency specific system_default message details
 * @param  {number} id
 * @param  {any} params 
 */
export const getAutomatedMessageDetails: any = async (id: number, params: any) => {
    let whereCondition = `message.id = :id AND message.type = :type`

    whereCondition += params.client_id ? ` AND message.client_id = :client_id` : "";
    whereCondition += params.agency_id ? ` AND message.agency_id = :agency_id` : "";

    let whereClauseValue = { "id": id, "client_id": params.client_id, "agency_id": params.agency_id, "type": "SYSTEM_DEFAULT" }

    let selectQuery = getRepository(Message).createQueryBuilder("message")
        .select([
            `message.id as id`,
            `message.name as name`,
            `message.title as title`,
            `message.from as 'from'`,
            `message.body as body`,
            `message.createdAt as created_at`,
            `message.client_id as client_id`,
            `message.agency_id as agency_id`,
            `message.site_id as site_id`,
            `message.type as type`
        ])
        .where(whereCondition, whereClauseValue)

    return selectQuery.getRawOne();
};

/**
 * Get training message details
 * @param  {number} id
 */
export const updateMessageReadStatusHelper: any = async (id: number, userId: number) => {
    const entityManager = getManager()
    return await entityManager.query(`
    UPDATE message_receiver_workers as mrw inner join 
    workers as wk on mrw.worker_id = wk.id  
    set mrw.is_message_read = 1, mrw.message_read_at = '${new Date().toISOString().slice(0, 19).replace('T', ' ')}'  
    where mrw.message_id =${id} AND wk.user_id = ${userId};
    `)

};


export const getMessageDetailsById = async (messageId: number) => {
    return await getRepository(Message).findOne({
        where: {
            id: messageId
        }
    })
}

/**
 * Get message template details
 * @param  {number} id
 */
export const getMessageTemplateDetails: any = async (id: number) => {
    return await getRepository(Template).createQueryBuilder('template')
        .select(['id', 'name', 'title', 'title_translations', 'type', 'body', '`from`', 'language', 'code', 'created_by', 'created_at'])
        .where(`template.id = ${id}`)
        .getRawOne()
};

/**
 * Delete message template details
 * @param  {number} templateId
 */
export const deleteMessageTemplateById: any = async (templateId) => {
    return await getRepository(Template).createQueryBuilder('template')
        .delete()
        .where('id = :id', { id: templateId })
        .execute();
};

/**
 * Get default message template details
 */
export const getDefaultMessageTemplate: any = async () => {
    return getRepository(Template).createQueryBuilder("template")
        .select(['name', 'title', 'type', 'body', '`from`'])
        .where(`is_default = 1`)
        .getRawMany();
};


/**
 * get Message Reaction By Worker Id Message Id
 * @param  {number} messageId
 * @param  {number} workerId
 */
export const getMessageReactionByWorkerIdMessageId = async (messageId: number, workerId: number) => {
    return await getRepository(MessageReaction).findOne({
        where: {
            messageId: messageId,
            workerId: workerId
        }
    })
}


/**
 * Create message reaction
 * @param  {} payload
 */
export const createMessageReaction: any = async (payload) => {
    return await getRepository(MessageReaction).save(payload);
};


/**
 * Update message reaction
 * @param  {} id
 * @param  {} payload
 */
export const updateMessageReaction: any = async (id, payload) => {
    return await getRepository(MessageReaction).update({ id }, payload)
};


/**
 * Create message comment
 * @param  {} payload
 */
export const createMessageComment: any = async (payload) => {
    return await getRepository(MessageComment).save(payload);
};


/**
 * Get message comments
 * @param  {number} messageId
 * @param  {number} page
 * @param  {number} limit
 */
export const getMessageComments: any = async (messageId: number, page: number, limit: number) => {
    const MessageCommentRepository = getRepository(MessageComment);
    let getMessageCommentsQuery = MessageCommentRepository.createQueryBuilder("message_comment")
        .innerJoin('message_comment.worker', 'worker')
        .leftJoin('worker.user', 'user')
        .select([
            'message_comment.id as id',
            'message_comment.message_id as message_id',
            'message_comment.worker_id  as worker_id',
            'worker.first_name as first_name',
            'worker.last_name as last_name',
            'user.resource as resource',
            'message_comment.comment as comment',
            'message_comment.created_at as created_at'
        ])
        .where(`message_comment.message_id = :message_id`, { "message_id": messageId })
        .orderBy("created_at", "DESC")

    const [query, parameters] = getMessageCommentsQuery.getQueryAndParameters();
    return [
        (await getManager().query(`SELECT COUNT(*) as comment_count FROM (${query}) AS temp_table;`, parameters))[0].comment_count || 0,
        await getMessageCommentsQuery.offset((page - 1) * limit).limit(limit).getRawMany()
    ]
};

/**
 * Get message recieved worker id by message id
 * @param  {number} message_id
 */
export const getWorkerIdFromMessageId: any = async (message_id: number) => {
    const messageReceiverWorkers = await getRepository(MessageReceiverWorkers).find({
        where: { messageId: message_id },
        select: ['workerId']
    });
    const workerIds = messageReceiverWorkers.map(({ workerId }) => Number(workerId));
    return workerIds;
};
