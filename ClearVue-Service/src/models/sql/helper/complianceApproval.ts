import { getRepository } from 'typeorm';
import { ComplianceApproval } from '..';
import { ComplinaceCardsType } from '../../../common';

export const getLatestComplianceApproval = async (workerId: string, complianceCardId: number) => {
    return await getRepository(ComplianceApproval)
        .createQueryBuilder('approval')
        .where('approval.workerId = :workerId AND approval.complianceCardId = :complianceCardId', { workerId, complianceCardId })
        .orderBy('approval.id', 'DESC')
        .getOne();
};

export const insertComplianceApproval = async (payload) => {
    return await getRepository(ComplianceApproval).save(payload);
};

// Get Compliance Approval For Workers to check if any row exist for given workers
export const getComplianceApprovalForWorkersIfExists = async (allWorkerIds) => {
    return await getRepository(ComplianceApproval)
        .createQueryBuilder('approval')
        .select(['approval.workerId', 'approval.complianceCardId'])
        .where('approval.workerId IN (:...workerIds)', { workerIds: allWorkerIds })
        .andWhere('approval.complianceCardId IN (:...cardIds)', { cardIds: [ComplinaceCardsType.MULTIPLE_OCCUPANCY, ComplinaceCardsType.BANKING] })
        .groupBy('approval.workerId, approval.complianceCardId')
        .getMany()
};

export const insertComplianceApprovalTx = async (transactionalEntityManager, payload) => {
    if (transactionalEntityManager) {
        return await transactionalEntityManager.getRepository(ComplianceApproval).save(payload);
    } else {
        return await getRepository(ComplianceApproval).save(payload);
    }
};