import { getRepository } from 'typeorm';
import { UserActivity } from '../entities/UserActivity';

/**
 * Initialize user activity for fresh login - removes old records and creates new one
 * This ensures clean session tracking for fresh logins
 * @param userId - User ID
 * @param sessionId - Session identifier
 * @param authTime - Time when user authenticated
 */
export const initializeUserActivityForFreshLogin = async (
    userId: string,
    sessionId: string,
    authTime: Date
): Promise<UserActivity> => {
    const userActivityRepository = getRepository(UserActivity);
    const currentTime = new Date();

    // Remove any existing activity records for this user (clean slate for fresh login)
    await userActivityRepository
        .createQueryBuilder()
        .delete()
        .where('userId = :userId', { userId })
        .execute();

    // Create new activity record for fresh login
    const userActivity = userActivityRepository.create({
        userId,
        sessionId,
        lastActivityAt: currentTime,
        authTime,
        createdAt: currentTime,
        updatedAt: currentTime
    });

    return await userActivityRepository.save(userActivity);
};

/**
 * Update or create user activity record
 * @param userId - User ID
 * @param sessionId - Session identifier (can be Firebase UID or JWT token hash)
 * @param authTime - Time when user authenticated (from Firebase auth_time or login time)
 * @param isFreshLogin - Whether this is a fresh login (should update auth_time)
 */
export const updateUserActivity = async (
    userId: string,
    sessionId: string,
    authTime: Date,
    isFreshLogin: boolean = false
): Promise<UserActivity> => {
    const currentTime = new Date();
    const userActivityRepository = getRepository(UserActivity);

    // Try to find existing activity record for this user and session
    let userActivity = await userActivityRepository.findOne({
        where: { userId, sessionId }
    });

    if (userActivity) {
        // Update existing record
        userActivity.lastActivityAt = currentTime;
        userActivity.updatedAt = currentTime;

        // If this is a fresh login, update auth_time to reflect new authentication session
        if (isFreshLogin) userActivity.authTime = authTime;

    } else {
        // Create new record
        userActivity = userActivityRepository.create({
            userId,
            sessionId,
            lastActivityAt: currentTime,
            authTime,
            createdAt: currentTime,
            updatedAt: currentTime
        });
    }

    return await userActivityRepository.save(userActivity);
};

/**
 * Get user activity record
 * @param userId - User ID
 * @param sessionId - Session identifier
 */
export const getUserActivity = async (
    userId: string,
    sessionId: string
): Promise<UserActivity | undefined> => {
    const userActivityRepository = getRepository(UserActivity);

    return await userActivityRepository.findOne({
        where: { userId, sessionId }
    });
};

/**
 * Combined check for both inactivity and session duration timeouts (OPTIMIZED - Single DB Call)
 * @param userId - User ID
 * @param sessionId - Session identifier
 * @param inactivityTimeoutSeconds - Inactivity timeout in seconds
 * @param maxSessionDurationSeconds - Maximum session duration in seconds
 * @returns Object with both timeout statuses and activity info
 */
export const checkUserActivityTimeouts = async (
    userId: string,
    sessionId: string,
    inactivityTimeoutSeconds: number,
    maxSessionDurationSeconds: number
): Promise<{
    activityRecord?: UserActivity;
    inactivityTimeout: {
        isTimedOut: boolean;
        lastActivityAt?: Date;
        inactivityDuration?: number;
    };
    sessionDurationTimeout: {
        isTimedOut: boolean;
        authTime?: Date;
        sessionDuration?: number;
    };
}> => {
    // Single database call to get user activity
    const activityRecord = await getUserActivity(userId, sessionId);

    if (!activityRecord) {
        // No activity record found - both timeouts should be triggered
        return {
            inactivityTimeout: { isTimedOut: true },
            sessionDurationTimeout: { isTimedOut: true }
        };
    }

    const currentTime = new Date();

    // Check inactivity timeout
    const lastActivityTime = activityRecord.lastActivityAt;
    const inactivityDuration = Math.floor((currentTime.getTime() - lastActivityTime.getTime()) / 1000);
    const inactivityTimedOut = inactivityDuration > inactivityTimeoutSeconds;

    // Check session duration timeout
    const authTime = activityRecord.authTime;
    const sessionDuration = Math.floor((currentTime.getTime() - authTime.getTime()) / 1000);
    const sessionDurationTimedOut = sessionDuration > maxSessionDurationSeconds;

    return {
        activityRecord,
        inactivityTimeout: {
            isTimedOut: inactivityTimedOut,
            lastActivityAt: lastActivityTime,
            inactivityDuration
        },
        sessionDurationTimeout: {
            isTimedOut: sessionDurationTimedOut,
            authTime,
            sessionDuration
        }
    };
};


