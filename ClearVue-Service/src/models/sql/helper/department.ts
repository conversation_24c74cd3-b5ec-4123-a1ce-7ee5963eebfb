import { getRepository, In } from 'typeorm';
import { Departments } from '../';
import { DepartmentSiteAssociation } from '../entities/DepartmentSiteAssociation';

/**
 * create department
 */
export const createDepartment: any = async (body) => {
    const departmentRepository = getRepository(Departments);
    return await departmentRepository.save(body);
};

/**
 * update department
 */
export const updateDepartment: any = async (id, body) => {
    const departmentRepository = getRepository(Departments);
    body.updatedAt = new Date();
    return await departmentRepository.update({ id }, body);
};

/**
 * get department list
 */
export const getDepartmentListWithPagination: any = async (whereClause, whereClauseValue) => {
    const departmentRepository = getRepository(Departments);

    let selectQuery = departmentRepository.createQueryBuilder('departments')
        .leftJoin(
            qb => (
                qb.subQuery()
                    .from(DepartmentSiteAssociation, 'dsa')
                    .leftJoin('dsa.site', 'site')
                    .leftJoin('site.region', 'region')
                    .select([
                        'DISTINCT(dsa.`department_id`)',
                        `GROUP_CONCAT(CONCAT(site.id, ":", site.name, ":", region.id, ":", region.name, ":", site.region_id) SEPARATOR ";") AS associations`
                    ])
                    .where('site.client_id = :clientId', { clientId: whereClauseValue.client_id })
                    .groupBy('dsa.department_id'))
            ,
            'subQ',
            'subQ.department_id = departments.id')
        .leftJoin('departments.departmentSiteAssociations', 'departmentSiteAssociations')
        .leftJoin('departmentSiteAssociations.site', 'site')
        .leftJoin('site.region', 'region')
        .select([
            'DISTINCT(departments.id) AS id',
            'departments.name AS name',
            'departments.created_by AS createdBy',
            'departments.created_at AS createdAt',
            'departments.updated_by AS updatedBy',
            'departments.updated_at AS updatedAt',
            'departments.client_id AS clientId',
            'IFNULL(departments.cost_centre, "") AS costCentre',
            'subQ.associations',
        ])
        .where(whereClause, whereClauseValue)
        .groupBy('departments.id')
        .orderBy('departments.name', 'ASC');

    const results = await selectQuery.getRawMany();

    // Process associations
    const processedResults = results.map(result => {
        const associations = result.associations ? result.associations.split(';').map(association => {
            const [site_id, site_name, region_id, region_name, regionIdFromSite] = association.split(':');
            return {
                site_id,
                site_name,
                region_id,
                region_name,
            };
        }) : [];

        return {
            ...result,
            associations,
        };
    });

    return processedResults;
};

/**
 * get department By Id
 */
export const getDepartmentById: any = async (id) => {
    const departmentRepository = getRepository(Departments);
    return await departmentRepository.createQueryBuilder("departments")
        .where("departments.id = :id", { id })
        .select(['name', 'client_id'])
        .getRawOne();
};

/**
 * get department By whereclase
 */
export const getDepartmentByWhereClause: any = async (whereClause) => {
    const departmentRepository = getRepository(Departments);
    return await departmentRepository.findOne(
        {
            where: whereClause,
            select: ['id']
        }
    );
};


export const getDepartmentsByNames: any = async (names, clientId) => {
    return await getRepository(Departments).createQueryBuilder('department')
        .select(['department.id as id', 'LOWER(department.name) as name', 'department.cost_centre as cost_centre'])
        .where('department.name IN (:...names)', { names })
        .andWhere(`department.client_id =  ${clientId}`)
        .getRawMany();
};

/**
 * get department names
 */
export const getDepartmentNames: any = async (departmentIds) => {
    return await getRepository(Departments).find(
        {
            where: `id IN (${departmentIds.join(",")})`,
            select: ['id', 'name']
        }
    );
};


export const getDepartmentListLowerCase: any = async (client_id) => {
    return await getRepository(Departments).createQueryBuilder("departments")
        .select(['departments.id AS id', 'LOWER(departments.name) AS name'])
        .where(`departments.client_id = :client_id `, { "client_id": client_id })
        .orderBy("departments.name", "ASC")
        .getRawMany();
};



/**
 * Queries for Departments IDs based on provided names and client_id, 
 *      Returns mapping of found names to their IDs and a list of names not found in the database..
 */
export const getDepartmentsIdsListByNames: any = async (names, client_id) => {
    const departmentsRepository = getRepository(Departments);
    const foundDepartments = await departmentsRepository.find({
        where: {
            name: In(names),
            clientId: client_id
        },
        select: ['id', 'name']
    });

    const foundNames = foundDepartments.map((shift) => shift.name.toLowerCase());

    const notFoundNames = names.filter((name) => !foundNames.includes(name));

    const FoundShiftNameIdMapping = Object.fromEntries(foundDepartments.map(obj => [obj.name.toLowerCase(), obj.id]));

    return {
        departmentNameIdMappings: FoundShiftNameIdMapping,
        notFoundDepartmentNames: notFoundNames
    };
};

/**
 * create department_site_association
 */
export const createDepartmentSiteAssociation: any = async (bodyArray) => {
    const response = await getRepository(DepartmentSiteAssociation).insert(bodyArray);
    return response.identifiers;
};

export const getDepartmentSiteAssociationsByDepartmentId = async (departmentId) => {
    const dsaRepository = getRepository(DepartmentSiteAssociation);
    return await dsaRepository.find({ where: { departmentId } });
};

export const removeDepartmentSiteAssociations = async (departmentId, siteIdsToRemove) => {
    const dsaRepository = getRepository(DepartmentSiteAssociation);

    // Delete associations where departmentId matches and siteId is in siteIdsToRemove
    await dsaRepository
        .createQueryBuilder()
        .delete()
        .from(DepartmentSiteAssociation)
        .where('departmentId = :departmentId', { departmentId })
        .andWhere('siteId IN (:...siteIds)', { siteIds: siteIdsToRemove })
        .execute();
};

export const getAssociatedDepartmentsWithSite = async (client_id, site_id?) => {
    const departmentRepository = getRepository(DepartmentSiteAssociation);

    let query = departmentRepository
        .createQueryBuilder('dsa')
        .innerJoin('dsa.department', 'department')
        .where('department.client_id = :client_id', { client_id });

    if (site_id) query = query.andWhere('dsa.site_id = :site_id', { site_id });

    let departments = await query.getMany();
    return departments.map(obj => obj.departmentId)
};