import { getRepository } from 'typeorm';
import { SurveyQuestions } from '..';
import { getClientUserIdFromWorkerUserIdWithLimit, getAgencyUserIdFromWorkerUserIdWithLimit } from '../../../models';
import { config } from '../../../configurations';
import { UserType, WorkerTypes } from '../../../common';
import { getWorkerHelper } from '.';
import { getWorkerIdFromUserIdService } from '../../../services';
import { getUsers } from '../../../models';
const _ = require('lodash');

/**
 * Get survey questions by surveyId
 */
export const getSurveyQuestions: any = async (surveyId, dataToSelect, is_default = 0, userType: number = null, userId = null) => {
    let selectQuery = getRepository(SurveyQuestions).createQueryBuilder('survey_questions')
        .select(dataToSelect)
        .where(`survey_questions.survey_id = :survey_id`, { "survey_id": surveyId })
        .andWhere(`survey_questions.is_default = :is_default`, { "is_default": is_default })

    if (surveyId == config.GENERAL_SURVEY_ID && is_default == 1) {
        selectQuery = selectQuery.andWhere(`survey_questions.belongsTo = '${userType == UserType.AGENCY_ADMIN ? 'AGENCY' : 'SITE'}'`)
    }

    if (surveyId == config.GENERAL_SURVEY_ID && is_default == 0) {
        if (userType == UserType.CLIENT_ADMIN || userType == UserType.AGENCY_ADMIN) {
            selectQuery = selectQuery.andWhere(`survey_questions.createdBy = :user_id`, { "user_id": userId })
        } else {
            let clientUser = await getClientUserIdFromWorkerUserIdWithLimit(userId);
            let agencyUser = await getAgencyUserIdFromWorkerUserIdWithLimit(userId);
            selectQuery = selectQuery.andWhere(`survey_questions.createdBy IN ('${clientUser.user_id}' ${agencyUser ? ", " + agencyUser.user_id : ""} )`)
        }
    }

    if (userType == UserType.AGENCY_WORKER) {
        let workerId = await getWorkerIdFromUserIdService(userId);
        let workerDetails = await getWorkerHelper(`workers.id = :worker_id `, { "worker_id": workerId[0] });

        if (workerDetails['worker_type'] === WorkerTypes.PERMANENT) {
            selectQuery.andWhere(`survey_questions.belongs_to != 'AGENCY'`)
        }
    }

    return await selectQuery.execute();
}

export const countSurveyQuestions = async (surveyId: number, createdBy: number, questionIds: number[]) => {
    const queryBuilder = getRepository(SurveyQuestions).createQueryBuilder('surveyquestions');
    const count = await queryBuilder
        .select('COUNT(*)', 'count')
        .where('surveyquestions.created_by = :createdBy', { createdBy })
        .andWhere('surveyquestions.survey_id = :surveyId', { surveyId })
        .andWhereInIds(questionIds)
        .getRawOne();

    return parseInt(count.count);
};


export const getSurveyQuestionsForDownload: any = async (surveyId, dataToSelect, clientId = null, agencyId = null) => {
    let whereClause = `(user.client_id = :client_id || user.agency_id = :agency_id) && (user.user_type_id = :agency_admin_id || user.user_type_id = :client_admin_id)`;
    let whereClauseValue = { "client_id": clientId, "agency_id": agencyId, "agency_admin_id": UserType.AGENCY_ADMIN, "client_admin_id": UserType.CLIENT_ADMIN };

    let userDetails = await getUsers(whereClause, whereClauseValue);

    let selectQuery = getRepository(SurveyQuestions).createQueryBuilder('survey_questions')
        .select(dataToSelect)
        .where(`survey_questions.survey_id = :survey_id`, { "survey_id": surveyId })
        .andWhere(`survey_questions.is_default='0'`)

    if (surveyId == config.GENERAL_SURVEY_ID) {
        selectQuery = selectQuery.andWhere(`survey_questions.createdBy IN (${_.map(userDetails, 'user_id')})`)
    }
    return await selectQuery.execute();
}

/**
 * Add survey questions
 */
export const createSurveyQuestions: any = async (questions) => {
    return (await getRepository(SurveyQuestions).save(questions));
};

/**
 * Update survey questions
 */
export const updateSurveyQuestionHelper: any = async (data) => {
    return await getRepository(SurveyQuestions).save(data);
};
