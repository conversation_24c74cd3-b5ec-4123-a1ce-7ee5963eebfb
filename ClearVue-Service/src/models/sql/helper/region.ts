const _ = require('lodash')
import { getRepository } from 'typeorm';
import { Region } from '../';
import { UserType } from '../../../common/enum';

export const addRegion: any = async (data) => {
    const regionRepository = getRepository(Region);
    return await regionRepository.save(data);
};

export const getClientRegion: any = async (clientId, loggedInUser) => {
    let whereClause = `region.client_id = :client_id`
    whereClause += loggedInUser.user_type_id == UserType.CLIENT_REGIONAL ? ` AND userRegionAssociation.user_id = :user_id` : ``;

    let whereClauseValue = { "client_id": clientId, "user_id": loggedInUser.user_id }
    let subquery = await getRepository(Region).createQueryBuilder('region')
        .select(['region.name as name', 'region.client_id as clientId', 'region.id as id', 'region.created_at as createdAt', 'region.created_by as createdBy', 'region.updated_at as updatedAt', 'region.updated_by as updatedBy'])
        .where(whereClause, whereClauseValue)
    if (whereClause.includes('user_id')) {
        return await subquery.innerJoin('region.userRegionAssociations', 'userRegionAssociation').addSelect(['userRegionAssociation.user_id as adminId']).execute();
    }
    return await subquery.execute();
};

/**
 * get region By Id
 */
export const getRegionById: any = async (id) => {
    const regionRepository = getRepository(Region);
    return await regionRepository.createQueryBuilder("region")
        .leftJoin('region.userRegionAssociations', 'user_region_associations')
        .where("region.id = :id", { id })
        .select(['name', 'client_id', 'user_region_associations.user_id as admin_id'])
        .getRawOne();
};

/**
 * update region
 */
export const updateRegion: any = async (id, body) => {
    const regionRepository = getRepository(Region);
    body.updatedAt = new Date();
    return await regionRepository.update({ id }, body);
};

export const getRegionForDropdown: any = async (clientId) => {
    const regionRepository = getRepository(Region);
    let response = await regionRepository.createQueryBuilder('region')
        .select(['region.id AS id', 'region.name AS name'])
        .where('region.clientId = :clientId', { clientId })
        .orderBy('region.name', 'ASC')
        .getRawMany();
    return response;
};