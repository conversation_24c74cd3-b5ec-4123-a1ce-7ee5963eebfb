{"name": "clearvue", "version": "0.1.0", "private": true, "dependencies": {"@bugsnag/js": "^7.16.0", "@bugsnag/plugin-react": "^7.16.0", "@chakra-ui/react": "^1.7.3", "@emotion/react": "^11", "@emotion/styled": "^11", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "apexcharts": "^3.33.0", "axios": "^0.24.0", "dotenv": "^10.0.0", "framer-motion": "^4", "moment": "^2.29.1", "nuka-carousel": "^4.8.4", "prop-types": "^15.8.0", "react": "^17.0.2", "react-apexcharts": "^1.3.9", "react-circular-progressbar": "^2.0.4", "react-dom": "^17.0.2", "react-multi-date-picker": "^3.3.0", "react-rating": "^2.0.5", "react-redux": "^7.2.6", "react-router-dom": "^6.2.1", "react-scripts": "5.0.0", "react-select": "^5.2.1", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.9", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "styled-components": "^5.3.3", "web-vitals": "^2.1.2", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src", "prettier:fix": "prettier --write", "prettier:check": "prettier --check ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.5.0", "eslint-config-airbnb": "^19.0.2", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^4.3.8", "lint-staged": "^11.1.1", "prettier": "^2.3.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx}": ["yarn lint"]}}