{"env": {"browser": true, "es2021": true, "jest": true}, "extends": ["plugin:react/recommended", "airbnb", "eslint:recommended", "plugin:react/recommended", "plugin:prettier/recommended", "prettier"], "settings": {"import/resolver": {"node": {"paths": ["src"]}}}, "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 13, "sourceType": "module"}, "plugins": ["react"], "rules": {"react/jsx-props-no-spreading": "off", "func-names": "off", "react/prop-types": "off", "no-nested-ternary": "off", "camelcase": "off", "react/react-in-jsx-scope": "off", "react/function-component-definition": "off", "react/jsx-filename-extension": ["error", {"extensions": [".js", ".jsx"]}], "sort-imports": ["error", {"ignoreCase": false, "ignoreDeclarationSort": true, "ignoreMemberSort": true, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"], "allowSeparatedGroups": false}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}