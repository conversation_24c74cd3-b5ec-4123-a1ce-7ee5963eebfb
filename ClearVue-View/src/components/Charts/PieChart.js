/* eslint-disable no-shadow */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import React<PERSON>pex<PERSON><PERSON> from 'react-apexcharts';
import { CHART_COLORS_FOR_AGENCY } from '../../constants';

const PieChart = function ({
  width = '250px',
  series = [44, 55, 41, 17, 15],
  labels = ['Team A', 'Team B', 'Team C', 'Team D', 'Team E']
}) {
  const [options] = useState({
    chart: {
      width: 380,
      type: 'pie'
    },
    legend: {
      show: false
    },
    colors: CHART_COLORS_FOR_AGENCY,
    labels,
    dataLabels: {
      enabled: false
    },
    tooltip: {
      custom({ series, seriesIndex }) {
        return `<div class="tooltip"> <div class="tooltip-inside"> <span class="tooltip-circle" style="background-color: ${CHART_COLORS_FOR_AGENCY[seriesIndex]}"></span> <p style="max-width: 110px; word-break: break-word;
        white-space: normal;">${labels[seriesIndex]}</p> </div>  <p>${series[seriesIndex]}</p> </div>`;
      }
    }
  });

  return <ReactApexChart options={options} series={series} type="pie" width={width} />;
};

export default PieChart;
