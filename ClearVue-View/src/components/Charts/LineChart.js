/* eslint-disable object-shorthand */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import ReactApex<PERSON>hart from 'react-apexcharts';
// import { LINE_CHART_COLORS } from '../../constants';

const LineChart = function ({ height = '100%', series, categories, colors = [] }) {
  const [options] = useState({
    chart: {
      type: 'line',
      height,
      toolbar: {
        show: false
      }
    },
    dataLabels: {
      enabled: false
    },
    colors: colors,
    stroke: {
      curve: 'straight'
    },
    title: {
      text: '',
      align: 'left'
    },
    xaxis: {
      categories,
      labels: {
        show: false,
        rotate: -90,
        rotateAlways: true,
        hideOverlappingLabels: true
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    yaxis: {
      labels: {
        show: true,
        formatter: function (val) {
          return val.toFixed(2);
        }
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    grid: {
      xaxis: {
        lines: {
          show: false
        }
      },
      yaxis: {
        lines: {
          show: false
        }
      }
    }
  });

  return <ReactApexChart height={height} width="100%" options={options} series={series} />;
};

export default LineChart;
