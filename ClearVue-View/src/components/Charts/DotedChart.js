/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-const-assign */
/* eslint-disable no-plusplus */
/* eslint-disable react/prop-types */
import React from 'react';
import { Box } from '@chakra-ui/react';
import { CircularProgressbarWithChildren, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';

const Separator = function (props) {
  return (
    <div
      style={{
        position: 'absolute',
        height: '100%',
        transform: `rotate(${props.turns}turn)`
      }}>
      <div style={props.style} />
    </div>
  );
};

function RadialSeparators(props) {
  const turns = 1 / props.count;
  const array = [];
  for (let i = 1; i <= props.count; i++) {
    array.push(<Separator turns={i * turns} style={props.style} />);
  }
  return array;
}

const DotedChart = function ({ value, max, styles, minH, maxH, minW, maxW, ...props }) {
  return (
    <Box margin="auto" minH={minH} minW={minW} maxH={maxH} maxW={maxW}>
      <CircularProgressbarWithChildren
        value={value}
        maxValue={max}
        text={`${value}%`}
        styles={styles ? buildStyles(styles) : ''}
        {...props}>
        <RadialSeparators
          count={12}
          style={{
            background: '#fff',
            width: '10px',
            height: `${10}%`
          }}
        />
      </CircularProgressbarWithChildren>
    </Box>
  );
};

export default DotedChart;
