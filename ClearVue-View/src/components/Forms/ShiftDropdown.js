/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Text, useToast } from '@chakra-ui/react';
import { TOAST_SETTINGS, USER_TYPE } from '../../constants';
import { getShifts } from '../../redux/action/shift.action';
import Dropdown from '../Dropdown';

const ShiftDropdown = function ({
  shift,
  setShift,
  validationObj,
  refresh,
  multiple,
  siteId = null,
  clientId = null,
  disabled = false
}) {
  const toast = useToast();
  const { userData, user_details } = useSelector((state) => state.authentication);
  const [shifts, setShifts] = useState([]);
  const [errorMsg, setErrorMsg] = useState(null);
  const [isValid, setValid] = useState(validationObj?.isvalid || true);
  const { selected_client_id, selected_site_id } = useSelector((state) => state.agency);

  useEffect(async () => {
    const result = await getShifts(
      clientId || selected_client_id || userData.client_id || user_details.client_id || null,
      user_details.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN
        ? ''
        : siteId || selected_site_id || userData.site_id || null,
      user_details.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN && user_details.region_id
    );
    if (result && result.ok) {
      setShifts(result.data.shifts);
    } else {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: result.error
      });
    }
  }, [
    clientId,
    siteId,
    selected_client_id,
    selected_site_id,
    userData.client_id,
    userData.site_id
  ]);

  useEffect(() => {
    if (validationObj && !validationObj.isvalid) {
      setErrorMsg(validationObj.errorMessage);
      setValid(validationObj.isvalid);
    }
  }, [refresh]);

  const setShiftData = (e) => {
    setErrorMsg(null);
    setValid(true);
    setShift(e);
  };

  return (
    <>
      <Dropdown
        label={`Shift${validationObj?.required ? '*' : ''}`}
        onSelect={setShiftData}
        options={shifts.map((s) => {
          return { label: s.name, value: s.id };
        })}
        value={shift}
        placeholder=""
        isError={!isValid}
        multiple={multiple}
        disabled={disabled}
      />
      <Text visibility={isValid ? 'hidden' : 'visible'} color="main.error" mb="2px" fontSize="11px">
        {errorMsg || 'Error !'}
      </Text>
    </>
  );
};

export default ShiftDropdown;
