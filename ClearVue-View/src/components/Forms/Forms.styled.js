import { Box } from '@chakra-ui/react';
import styled from 'styled-components';

const StyledInputWrapper = styled(Box)`
  margin: 0 0 10px 0;
  width: 100%;
  position: relative;
`;

export const StyledRow = styled.div`
  display: flex;
`;

export const StyledInfoBlock = styled.div`
  width: 50%;
`;

export const StyledInfoLabel = styled.div`
  font-weight: 600;
`;

export const StyledInfoValue = styled.div`
  font-size: 12px;
`;

export default StyledInputWrapper;
