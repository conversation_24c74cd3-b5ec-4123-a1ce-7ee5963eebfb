/* eslint-disable no-unused-vars */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { Text, useToast } from '@chakra-ui/react';
import { TOAST_SETTINGS } from '../../constants';
import { getAgency } from '../../redux/action/agency.action';
import Dropdown from '../Dropdown';

const AgencyDropdownWithDefault = function ({
  agency,
  setAgency,
  validationObj,
  refresh,
  disabled = false,
  setDefaultValue
}) {
  const toast = useToast();
  const [agencies, setAgencies] = useState([]);
  const [errorMsg, setErrorMsg] = useState(null);
  const [isValid, setValid] = useState(validationObj?.isvalid || true);

  useEffect(async () => {
    const result = await getAgency(1000, 1, 'name', 'ASC');
    if (result && result.ok) {
      setAgencies(result.data.agency_list);
      if (setDefaultValue && result.data.agency_list.length > 0) {
        setAgency({
          label: result.data.agency_list[0].agency_name,
          value: result.data.agency_list[0].agency_id
        });
      }
    } else {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: result.error
      });
    }
  }, []);

  useEffect(() => {
    if (validationObj && !validationObj.isvalid) {
      setErrorMsg(validationObj.errorMessage);
      setValid(validationObj.isvalid);
    }
  }, [refresh]);

  const setAgencyData = (e) => {
    setErrorMsg(null);
    setValid(true);
    setAgency(e);
  };

  return (
    <>
      <Dropdown
        label={`Agency${validationObj?.required ? '*' : ''}`}
        onSelect={setAgencyData}
        options={agencies.map((item) => {
          return { label: item.agency_name, value: item.agency_id };
        })}
        value={agency}
        placeholder=""
        isError={!isValid}
        disabled={disabled}
      />
      <Text visibility={isValid ? 'hidden' : 'visible'} color="main.error" mb="2px" fontSize="11px">
        {errorMsg || 'Error !'}
      </Text>
    </>
  );
};

export default AgencyDropdownWithDefault;
