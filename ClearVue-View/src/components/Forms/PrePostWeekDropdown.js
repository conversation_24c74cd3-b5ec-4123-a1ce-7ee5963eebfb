/* eslint-disable react/prop-types */
import { Text } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import { PreWeekSelectionList } from '../../constants';
import Dropdown from '../Dropdown';

const PrePostWeekDropdown = function ({ WeekValue, setWeekData, refresh, validationObj, label }) {
  const [errorMsg, setErrorMsg] = useState(null);
  const [isValid, setIsValid] = useState(validationObj?.isvalid || true);

  useEffect(() => {
    if (validationObj && !validationObj?.isvalid) {
      setErrorMsg(validationObj?.errorMessage);
      setIsValid(validationObj?.isvalid);
    }
  }, [refresh]);

  const setDropdownData = (e) => {
    setErrorMsg(null);
    setWeekData(e);
    setIsValid(true);
  };
  return (
    <>
      <Dropdown
        label={label}
        placeholder="Select"
        options={PreWeekSelectionList}
        onSelect={(e) => {
          setDropdownData(e);
        }}
        value={WeekValue}
        isError={!isValid}
        disabled={false}
      />
      <Text visibility={isValid ? 'hidden' : 'visible'} color="main.error" mb="2px" fontSize="11px">
        {errorMsg || 'Error!'}
      </Text>
    </>
  );
};

export default PrePostWeekDropdown;
