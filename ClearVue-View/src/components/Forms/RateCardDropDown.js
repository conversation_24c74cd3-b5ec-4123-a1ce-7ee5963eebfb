/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Text, useToast } from '@chakra-ui/react';
import { TOAST_SETTINGS } from '../../constants';
import { getRateCardsDropdownList } from '../../redux/action/rateCard.action';
import Dropdown from '../Dropdown';

const RateCardDropdown = function ({ rateCard, setRateCard, validationObj, refresh, multiple }) {
  const toast = useToast();
  const { user_details } = useSelector((state) => state.authentication);
  const [rateCards, setRateCards] = useState([]);
  const [errorMsg, setErrorMsg] = useState(null);
  const [isValid, setValid] = useState(validationObj?.isvalid || true);
  const { selected_client_id } = useSelector((state) => state.agency);

  useEffect(async () => {
    const result = await getRateCardsDropdownList(selected_client_id || user_details.client_id);
    if (result && result.ok) {
      setRateCards(result.data.rate_card_list);
    } else {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: result.error
      });
    }
  }, []);

  useEffect(() => {
    if (validationObj && !validationObj.isvalid) {
      setErrorMsg(validationObj.errorMessage);
      setValid(validationObj.isvalid);
    }
  }, [refresh]);

  const setRateCardData = (e) => {
    setErrorMsg(null);
    setValid(true);
    setRateCard(e);
  };

  return (
    <>
      <Dropdown
        label={`Rate Card${validationObj?.required ? '*' : ''}`}
        onSelect={setRateCardData}
        options={rateCards.map((s) => {
          return { label: s.name, value: s.id };
        })}
        value={rateCard}
        placeholder=""
        isError={!isValid}
        multiple={multiple}
      />
      <Text visibility={isValid ? 'hidden' : 'visible'} color="main.error" mb="2px" fontSize="11px">
        {errorMsg || 'Error !'}
      </Text>
    </>
  );
};

export default RateCardDropdown;
