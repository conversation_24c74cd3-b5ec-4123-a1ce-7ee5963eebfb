/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import { Text } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import Dropdown from '../Dropdown';

const RoleTypeSelectionDropdown = function ({
  roleTypeValue,
  setRoleTypeData,
  refresh,
  validationObj,
  roleTypeList
}) {
  const [errorMsg, setErrorMsg] = useState(null);
  const [isValid, setIsValid] = useState(validationObj?.isvalid || true);

  useEffect(() => {
    if (validationObj && !validationObj?.isvalid) {
      setErrorMsg(validationObj?.errorMessage);
      setIsValid(validationObj?.isvalid);
    }
  }, [refresh]);

  const setDropdownData = (e) => {
    setErrorMsg(null);
    setRoleTypeData(e);
    setIsValid(true);
  };
  return (
    <>
      <Dropdown
        label="Role Type*"
        placeholder="Select"
        options={roleTypeList.map((item) => {
          return { label: item, value: item };
        })}
        onSelect={(e) => {
          setDropdownData(e);
        }}
        value={roleTypeValue}
        isError={!isValid}
        disabled={false}
      />
      <Text visibility={isValid ? 'hidden' : 'visible'} color="main.error" mb="2px" fontSize="11px">
        {errorMsg || 'Error!'}
      </Text>
    </>
  );
};

export default RoleTypeSelectionDropdown;
