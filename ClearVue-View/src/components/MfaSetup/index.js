/* eslint-disable react/forbid-prop-types */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Image,
  Input,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Code,
  Divider
} from '@chakra-ui/react';

const MfaSetupModal = function ({ isOpen, onClose, onComplete, qrCodeUrl, secretKey, isLoading }) {
  const [totpCode, setTotpCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isOpen) {
      setTotpCode('');
      setError('');
      setIsVerifying(false);
    }
  }, [isOpen]);

  const handleVerifyCode = async () => {
    if (!totpCode || totpCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      await onComplete(totpCode);
    } catch (err) {
      setError(err.message || 'Failed to verify code. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleCodeChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setTotpCode(value);
    setError('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && totpCode.length === 6) {
      handleVerifyCode();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" closeOnOverlayClick={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Text fontSize="xl" fontWeight="bold" color="main.primary">
            Set Up Two-Factor Authentication
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch">
            <Alert status="info">
              <AlertIcon />
              <Box>
                <AlertTitle>MFA Setup Required!</AlertTitle>
                <AlertDescription>
                  Two-factor authentication is now required for your account. Please set up an
                  authenticator app to continue.
                </AlertDescription>
              </Box>
            </Alert>

            <Tabs variant="enclosed">
              <TabList>
                <Tab>Scan QR Code</Tab>
                <Tab>Manual Entry</Tab>
              </TabList>

              <TabPanels>
                <TabPanel>
                  <VStack spacing={4} align="center">
                    <Text fontSize="md" textAlign="center">
                      Scan this QR code with your authenticator app:
                    </Text>
                    {qrCodeUrl && (
                      <Box border="2px" borderColor="gray.200" borderRadius="md" p={4} bg="white">
                        <Image src={qrCodeUrl} alt="MFA QR Code" maxW="200px" />
                      </Box>
                    )}
                    <Text fontSize="sm" color="gray.600" textAlign="center">
                      Use Google Authenticator, Authy, or any compatible TOTP app
                    </Text>
                  </VStack>
                </TabPanel>

                <TabPanel>
                  <VStack spacing={4} align="stretch">
                    <Text fontSize="md">
                      If you can&apos;t scan the QR code, manually enter this secret key in your
                      authenticator app:
                    </Text>
                    <Box bg="gray.50" p={3} borderRadius="md" border="1px" borderColor="gray.200">
                      <Code fontSize="sm" wordBreak="break-all">
                        {secretKey}
                      </Code>
                    </Box>
                    <Text fontSize="sm" color="gray.600">
                      Account: ClearVue
                      <br />
                      Type: Time-based (TOTP)
                    </Text>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>

            <Divider />

            <VStack spacing={4} align="stretch">
              <Text fontSize="md" fontWeight="semibold">
                Enter the 6-digit code from your authenticator app:
              </Text>
              <Input
                value={totpCode}
                onChange={handleCodeChange}
                onKeyPress={handleKeyPress}
                placeholder="000000"
                size="lg"
                textAlign="center"
                fontSize="xl"
                letterSpacing="0.2em"
                maxLength={6}
                bg="white"
                border="2px"
                borderColor={error ? 'red.300' : 'gray.200'}
                _focus={{
                  borderColor: error ? 'red.500' : 'main.primary',
                  boxShadow: 'none'
                }}
              />
              {error && (
                <Text color="red.500" fontSize="sm" textAlign="center">
                  {error}
                </Text>
              )}
            </VStack>

            <HStack spacing={3} justify="flex-end">
              <Button variant="outline" onClick={onClose} isDisabled={isVerifying || isLoading}>
                Cancel
              </Button>
              <Button
                bg="main.semiPrimary"
                color="white"
                _hover={{ bg: 'main.primary' }}
                onClick={handleVerifyCode}
                isLoading={isVerifying}
                loadingText="Verifying..."
                isDisabled={totpCode.length !== 6 || isLoading}>
                Complete Setup
              </Button>
            </HStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

MfaSetupModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onComplete: PropTypes.func.isRequired,
  qrCodeUrl: PropTypes.string,
  secretKey: PropTypes.string,
  isLoading: PropTypes.bool
};

MfaSetupModal.defaultProps = {
  qrCodeUrl: '',
  secretKey: '',
  isLoading: false
};

export default MfaSetupModal;
