/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
import React from 'react';
import { Box, Center, Flex, Image, Skeleton, SkeletonCircle, Tooltip } from '@chakra-ui/react';
import HeaderCard from '../HeaderCard';
import Doted<PERSON>hart from '../Charts/DotedChart';
import Circular<PERSON>hart from '../Charts/CircularChart';
import Column<PERSON>hart from '../Charts/ColumnChart';
import Stack<PERSON><PERSON> from '../Charts/StackChart';
import { colors } from '../../theme';
import PieChart from '../Charts/PieChart';
import greenArrowUp from '../../assets/images/greenArrowUp.png';
import redArrowDown from '../../assets/images/redArrowDown.png';
import constants, { CHART_COLORS_FOR_AGENCY } from '../../constants';
import NoData from '../NoData';
// import Loader from '../Loader';

const ChartCard = function ({
  activityTopCardData,
  // workforceTopCardDemographics,
  workforceTopCardLOS,
  workforceTopCardShiftUti,
  workforceTopCardPoolUti,
  leaverTopCardLos,
  leaverTopCardCounts,
  // leaverTopCardShiftUti,
  activityBottomCardShiftUtil,
  activityBottomCardLostShift,
  activityBottomCardHeadCount,
  activityBottomCardSpend,
  activityBottomCardAverageHours,
  workforceShiftUti,
  // leaverShiftUtl,
  leaverNSR,
  workforceLOS,
  leaverLOS,
  leaverCount,
  isDataAvailable = true
}) {
  return (
    <>
      {/* {loading ? <Loader /> : null} */}
      <Box mt={25}>
        <Flex overflowX="auto">
          <Box minW="350px" w="100%">
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.ACTIVITY_FOR_COMPANY}</Box>

            <HeaderCard p={3} h="350px" mr={1}>
              <Flex justifyContent="space-around" mb="5px">
                <Box textOverflow="ellipsis" whiteSpace="nowrap" overflow="hidden" pb={2} w="34%">
                  <Tooltip label="Shift Fullfilment">
                    <Center>Shift Fullfilment</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {activityTopCardData &&
                      activityTopCardData.shift_fullfilment.current_range ? (
                        <>
                          {activityTopCardData.shift_fullfilment.current_range.fulfilled >=
                            activityTopCardData.shift_fullfilment.past_range.fulfilled && (
                            <Box>
                              <Image src={greenArrowUp} height={5} />
                            </Box>
                          )}
                          <DotedChart
                            value={Math.round(
                              (100 *
                                activityTopCardData.shift_fullfilment.current_range.fulfilled) /
                                (activityTopCardData.shift_fullfilment.current_range.fulfilled +
                                  activityTopCardData.shift_fullfilment.current_range.lost)
                            )}
                            max={100}
                            styles={{
                              strokeLinecap: 'butt'
                            }}
                            minW="50px"
                            maxW="90px"
                          />
                          {activityTopCardData.shift_fullfilment.current_range.fulfilled <
                            activityTopCardData.shift_fullfilment.past_range.fulfilled && (
                            <Box>
                              <Image src={redArrowDown} height={5} ml="85%" />
                            </Box>
                          )}
                        </>
                      ) : (
                        <SkeletonCircle size="110px" margin="auto" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
                {/* <Box textOverflow="ellipsis" whiteSpace="nowrap" overflow="hidden" pb={2} w="110px">
                  <Tooltip label="Average Hours Per Worker">
                    <Box>Average Hours Per Worker</Box>
                  </Tooltip>
                  {activityTopCardData ? (
                    <CircularChart
                      value={activityTopCardData.current_range_average_hours}
                      max={activityTopCardData.current_range_average_hours}
                      strokeWidth={2}
                      styles={{
                        pathColor: colors.main.gray
                      }}
                      minW="80px"
                      maxW="110px"
                    />
                  ) : (
                    <SkeletonCircle size="80px" margin="auto" />
                  )}
                </Box> */}
                <Box w="34%">
                  <Tooltip label="Lost Shifts">
                    <Center>Lost Shifts</Center>
                  </Tooltip>
                  <Box />
                  <Box margin="auto">
                    {isDataAvailable ? (
                      <>
                        {activityTopCardData ? (
                          <>
                            {activityTopCardData.shift_lost.current_range.count >=
                              activityTopCardData.shift_lost.past_range.count && (
                              <Box>
                                <Image src={greenArrowUp} height={5} />
                              </Box>
                            )}
                            <Center fontSize="5xl">
                              {activityTopCardData.shift_lost.current_range.count}
                            </Center>
                            {activityTopCardData.shift_lost.current_range.count <
                              activityTopCardData.shift_lost.past_range.count && (
                              <Box>
                                <Image src={redArrowDown} height={5} ml="85%" />
                              </Box>
                            )}
                          </>
                        ) : (
                          <Skeleton h="100px" w="100%" />
                        )}
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                </Box>
              </Flex>
              <Flex
                textOverflow="ellipsis"
                whiteSpace="nowrap"
                overflow="hidden"
                justifyContent="space-around"
                mb="5px">
                <Box textOverflow="ellipsis" whiteSpace="nowrap" overflow="hidden">
                  <Tooltip label="Spent">
                    <Center>Spent</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {activityTopCardData ? (
                        <CircularChart
                          value={activityTopCardData.total_spent.basic}
                          valueType="£"
                          max={
                            activityTopCardData.total_spent.basic +
                            activityTopCardData.total_spent.other
                          }
                          strokeWidth={15}
                          styles={{
                            pathColor: '#7B5BC6',
                            trailColor: '#628DE6',
                            strokeLinecap: 'butt'
                          }}
                          minW="120px"
                          maxW="40px"
                        />
                      ) : (
                        <SkeletonCircle size="110px" margin="auto" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
                {/* <Box textOverflow="ellipsis" whiteSpace="nowrap" overflow="hidden" w="110px">
                  <Tooltip label="Average Hours Per Worker (4 wk Avg.)">
                    <Box>Average Hours Per Worker (4 wk Avg.)</Box>
                  </Tooltip>
                  {activityTopCardData ? (
                    <CircularChart
                      value={activityTopCardData.past_average_hours}
                      max={activityTopCardData.past_average_hours}
                      strokeWidth={2}
                      styles={{
                        pathColor: colors.main.gray
                      }}
                      minW="80px"
                      maxW="110px"
                    />
                  ) : (
                    <SkeletonCircle size="80px" margin="auto" />
                  )}
                </Box> */}
                <Box textOverflow="ellipsis" whiteSpace="nowrap" overflow="hidden">
                  <Tooltip label="Hours">
                    <Center>Hours</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {activityTopCardData ? (
                        <CircularChart
                          value={activityTopCardData.total_hours.basic}
                          max={
                            activityTopCardData.total_hours.basic +
                            activityTopCardData.total_hours.other
                          }
                          strokeWidth={15}
                          styles={{
                            pathColor: '#7B5BC6',
                            trailColor: '#628DE6',
                            strokeLinecap: 'butt'
                          }}
                          minW="120px"
                          maxW="40px"
                        />
                      ) : (
                        <SkeletonCircle size="110px" margin="auto" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
              </Flex>
            </HeaderCard>
          </Box>

          <Box minW="350px" w="100%">
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.WORKFORCE_FOR_COMPANY}</Box>
            <HeaderCard p={3} h="350px" mr={1}>
              <Flex justifyContent="space-around" mb="5px">
                {/* <Box w="33%" pb="2px">
                  <Tooltip label="Demographics">
                    <Center>Demographics</Center>
                  </Tooltip>
                  <Box margin="auto">
                    {workforceTopCardDemographics ? (
                      <Center>
                        {workforceTopCardDemographics.rows &&
                        workforceTopCardDemographics.rows.length > 0 ? (
                          <PieChart
                            width="200px"
                            // series={workforceTopCardDemographics.rows.map((d) => d.value)}
                            // labels={workforceTopCardDemographics.rows.map((d) => d.label)}
                          />
                        ) : (
                          <Box mt={5}>No data</Box>
                        )}
                      </Center>
                    ) : (
                      <SkeletonCircle size="150px" />
                    )}
                  </Box>
                </Box> */}
                <Box w="33%">
                  <Tooltip label="Pool Utilisasation">
                    <Center>Pool Utilisasation</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {workforceTopCardPoolUti ? (
                        <Box mt={3}>
                          <CircularChart
                            value={workforceTopCardPoolUti.active_count}
                            valueType="%"
                            max={100}
                            strokeWidth={15}
                            circleRatio={0.5}
                            styles={{
                              pathColor: colors.main.primary,
                              trailColor: colors.main.grey,
                              strokeLinecap: 'butt',
                              rotation: -1 / 4
                            }}
                            minW="120px"
                            maxW="40px"
                          />
                        </Box>
                      ) : (
                        <SkeletonCircle m="auto" size="150px" />
                        // <SkeletonCircle size="150px" margin="auto" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
                <Box w="34%">
                  <Tooltip label="Shift Utilisation">
                    <Center>Shift Utilisation</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {workforceTopCardShiftUti && workforceTopCardShiftUti.length > 0 ? (
                        <ColumnChart
                          colors={[colors.main.primary]}
                          series={[
                            {
                              data: workforceTopCardShiftUti
                            }
                          ]}
                          categories={[1, 2, 3, 4, 5, 6, 7]}
                          header=""
                        />
                      ) : (
                        <Skeleton h="140px" w="100%" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
                {/* </Box> */}
              </Flex>
              <Box>
                <Center>Length of Service</Center>
                {isDataAvailable ? (
                  <>
                    {workforceTopCardLOS &&
                    workforceTopCardLOS.rows &&
                    workforceTopCardLOS.rows.length > 0 ? (
                      <StackChart
                        header=""
                        height="100px"
                        width="100%"
                        series={workforceTopCardLOS.rows}
                        horizontal
                      />
                    ) : (
                      <Skeleton h="100px" w="100%" />
                    )}
                  </>
                ) : (
                  <NoData height="130px" width="130px" />
                )}
              </Box>
            </HeaderCard>
          </Box>
          <Box minW="350px" w="100%">
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.LEAVERS_FOR_COMPANY}</Box>
            <HeaderCard p={3} h="350px">
              <Flex justifyContent="space-around" mb="5px">
                {/* <Box w="34%">
                  <Tooltip label="Shift Utilization">
                    <Center>Shift Utilisation</Center>
                  </Tooltip>
                  {leaverTopCardShiftUti && leaverTopCardShiftUti.length > 0 ? (
                    <ColumnChart
                      colors={[colors.main.primary]}
                      series={[
                        {
                          data: leaverTopCardShiftUti
                        }
                      ]}
                      categories={[1, 2, 3, 4, 5, 6, 7]}
                      header=""
                    />
                  ) : (
                    <Skeleton h="80%" w="100%" />
                  )}
                  <ColumnChart
                    colors={[colors.main.primary]}
                    series={[
                      {
                        name: 'series-1',
                        data: [30, 40, 45, 50, 49, 60, 10]
                      }
                    ]}
                    categories={[1, 2, 3, 4, 5, 6, 7]}
                    header=""
                  /> 
                </Box> */}
                {/* <Box> */}
                <Box w="33%" pb="2px">
                  <Tooltip label="New Starter Retention">
                    <Box
                      pl="11px"
                      pr="11px"
                      margin="auto"
                      textAlign="center"
                      textOverflow="ellipsis"
                      whiteSpace="nowrap"
                      overflow="hidden">
                      New Starter Retention
                    </Box>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {leaverTopCardCounts ? (
                        <Box mt={3}>
                          <CircularChart
                            value={leaverTopCardCounts.new_starter_retention.active}
                            valueType="%"
                            max={
                              leaverTopCardCounts.new_starter_retention.active +
                              leaverTopCardCounts.new_starter_retention.inactive
                            }
                            strokeWidth={15}
                            circleRatio={1}
                            styles={{
                              pathColor: colors.main.primary,
                              trailColor: colors.main.grey,
                              strokeLinecap: 'butt',
                              rotation: -1 / 4
                            }}
                            minW="120px"
                            maxW="40px"
                          />
                        </Box>
                      ) : (
                        <SkeletonCircle m="auto" size="150px" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </Box>
                <Box w="34%">
                  <Tooltip label="Leavers">
                    <Center>Leavers</Center>
                  </Tooltip>
                  <Box />
                  <Box margin="auto">
                    {isDataAvailable ? (
                      <>
                        {leaverTopCardCounts ? (
                          <>
                            {leaverTopCardCounts.leavers_count &&
                              leaverTopCardCounts.leavers_count.current_value >=
                                leaverTopCardCounts.leavers_count.past_value && (
                                <Box>
                                  <Image src={greenArrowUp} height={5} ml="5%" />
                                </Box>
                              )}
                            <Center fontSize="5xl">
                              {leaverTopCardCounts.leavers_count.current_value}
                            </Center>
                            {leaverTopCardCounts.leavers_count &&
                              leaverTopCardCounts.leavers_count.current_value <
                                leaverTopCardCounts.leavers_count.past_value && (
                                <Box>
                                  <Image src={redArrowDown} height={5} ml="85%" />
                                </Box>
                              )}
                          </>
                        ) : (
                          <Skeleton h="140px" w="100%" />
                        )}
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                </Box>
              </Flex>
              <Box>
                <Center>Length of Service</Center>
                {isDataAvailable ? (
                  <>
                    {leaverTopCardLos &&
                    leaverTopCardLos.rows &&
                    leaverTopCardLos.rows.length > 0 ? (
                      <StackChart
                        header=""
                        height="100px"
                        width="100%"
                        series={leaverTopCardLos.rows}
                        horizontal
                      />
                    ) : (
                      <Skeleton h="100px" w="100%" />
                    )}
                  </>
                ) : (
                  <NoData height="130px" width="130px" />
                )}
              </Box>
            </HeaderCard>
          </Box>
        </Flex>
      </Box>
      <Box mt={25}>
        <Flex overflowX="auto" overflowY="hidden">
          <Box minW="350px" w="100%" pr={1}>
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.ACTIVITY_FOR_AGENCIES}</Box>
            <Box height="460px">
              <Flex justifyContent="space-evenly" h="210px" mb={1}>
                <HeaderCard w="100%" mr={1}>
                  <Tooltip label="Shift Fullfilment">
                    <Center>Shift Fullfilment</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {activityBottomCardShiftUtil && activityBottomCardShiftUtil.length > 0 ? (
                        <ColumnChart
                          colors={[colors.main.primary]}
                          series={[
                            {
                              data: activityBottomCardShiftUtil.map((d) => d.count)
                            }
                          ]}
                          categories={activityBottomCardShiftUtil.map((d) => d.label)}
                          value={35}
                          max={100}
                          styles={{
                            strokeLinecap: 'butt'
                          }}
                        />
                      ) : (
                        <Skeleton h="85%" w="100%" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
                <HeaderCard w="100%">
                  <Tooltip label="Lost Shifts">
                    <Center>Lost Shifts</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {activityBottomCardLostShift && activityBottomCardLostShift.length > 0 ? (
                        <ColumnChart
                          colors={[colors.main.primary]}
                          series={[
                            {
                              data: activityBottomCardLostShift.map((d) => d.count)
                            }
                          ]}
                          categories={activityBottomCardLostShift.map((d) => d.label)}
                          value={35}
                          max={100}
                          styles={{
                            strokeLinecap: 'butt'
                          }}
                        />
                      ) : (
                        <Skeleton h="85%" w="100%" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
              </Flex>
              <Flex justifyContent="space-evenly" h="250px">
                <HeaderCard w="100%" mr={1}>
                  <Box>
                    <Tooltip label="Spend">
                      <Center>Spend</Center>
                    </Tooltip>
                    {isDataAvailable ? (
                      <>
                        <Center>
                          {activityBottomCardSpend && activityBottomCardSpend.length > 0 ? (
                            <PieChart
                              series={activityBottomCardSpend.map((d) => d.count)}
                              labels={activityBottomCardSpend.map((d) => d.label)}
                            />
                          ) : (
                            <SkeletonCircle size="160px" />
                          )}
                        </Center>
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                  {/* <Center>Head Count</Center>
                  <Flex justifyContent="space-around">
                    <Box>Active</Box>
                    <Box>Inactive</Box>
                  </Flex>
                  {activityBottomCardHeadCount && activityBottomCardHeadCount.length > 0 ? (
                    activityBottomCardHeadCount.map((d, i) => {
                      return (
                        <Flex
                          justifyContent="space-around"
                          alignItems="center"
                          mb="3px"
                          key={d.label}>
                          <Box>
                            <Tooltip label={d.label}>
                              <Box
                                as="button"
                                borderRadius="md"
                                bg={CHART_COLORS_FOR_AGENCY[i]}
                                p={4}
                              />
                            </Tooltip>
                          </Box>
                          <Box>
                            {' '}
                            <Box m="1">247</Box>{' '}
                          </Box>
                          <Box>
                            <CircularChart
                              maxH="80px"
                              maxW="80px"
                              value="90"
                              valueType="%"
                              max={100}
                              strokeWidth={15}
                              styles={{
                                pathColor: '#40199D',
                                trailColor: '#0059CF',
                                strokeLinecap: 'butt'
                              }}
                            />
                          </Box>

                          <Box>95</Box>
                        </Flex>
                      );
                    })
                  ) : (
                    <>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                    </>
                  )} */}
                </HeaderCard>
                <HeaderCard w="100%">
                  {/* <Box>
                    <Center>Spend</Center>
                    <Center>
                      {activityBottomCardSpend && activityBottomCardSpend.length > 0 ? (
                        <PieChart
                          series={activityBottomCardSpend.map((d) => d.count)}
                          labels={activityBottomCardSpend.map((d) => d.label)}
                        />
                      ) : (
                        <SkeletonCircle size="160px" />
                      )}
                    </Center>
                  </Box> */}
                  <Box>
                    <Tooltip label="Hours">
                      <Center>Hours</Center>
                    </Tooltip>
                    {isDataAvailable ? (
                      <>
                        {activityBottomCardAverageHours &&
                        activityBottomCardAverageHours.length > 0 ? (
                          <ColumnChart
                            colors={[colors.main.primary]}
                            series={[
                              {
                                data: activityBottomCardAverageHours.map((d) => d.count)
                              }
                            ]}
                            categories={activityBottomCardAverageHours.map((d) => d.label)}
                            value={35}
                            max={100}
                            styles={{
                              strokeLinecap: 'butt'
                            }}
                          />
                        ) : (
                          <Skeleton h="160px" w="100%" />
                        )}
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                </HeaderCard>
              </Flex>
            </Box>
          </Box>
          <Box minW="350px" w="100%" pr={1}>
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.WORKFORCE_FOR_AGENCIES}</Box>
            <Box height="auto" minH="250px">
              <Flex justifyContent="space-evenly" minH="210px" h="auto" mb={1}>
                <HeaderCard w="100%" mr={1}>
                  <Tooltip label="Shift Utilisation 1-3 Days">
                    <Center>Shift Utilisation (1-3 Days)</Center>
                  </Tooltip>
                  {/* <Flex justifyContent="space-around"> */}
                  {/* <Tooltip label="Shift Utilisation 1-3 Days"> */}
                  {/* <Box>1-3 Days</Box> */}
                  {/* </Tooltip> */}
                  {/* <Box>4+ Days</Box> */}
                  {/* </Flex> */}
                  <Box overflowY="auto" h="auto">
                    {isDataAvailable ? (
                      <>
                        {workforceShiftUti && workforceShiftUti.length > 0 ? (
                          workforceShiftUti.map((d, i) => {
                            return (
                              <Flex
                                justifyContent="space-around"
                                alignItems="center"
                                mb="3px"
                                key={d.label}>
                                <Box>
                                  <Tooltip label={d.label}>
                                    <Box
                                      as="button"
                                      borderRadius="md"
                                      bg={CHART_COLORS_FOR_AGENCY[i]}
                                      p={4}
                                    />
                                  </Tooltip>
                                </Box>
                                <Box>
                                  {' '}
                                  <Box m="1">{d['1-3']}</Box>{' '}
                                </Box>
                                {/* <Box>
                            <CircularChart
                              maxH="60px"
                              maxW="60px"
                              value={Math.round((100 * d['4+']) / d.total) || 0}
                              valueType="%"
                              max={100}
                              strokeWidth={15}
                              styles={{
                                pathColor: '#40199D',
                                trailColor: '#0059CF',
                                strokeLinecap: 'butt'
                              }}
                            />
                          </Box> */}

                                {/* <Box>{d['4+']}</Box> */}
                              </Flex>
                            );
                          })
                        ) : (
                          <>
                            <Flex justifyContent="space-around" alignItems="center" mt="5px">
                              <Box>
                                <Skeleton h="75px" w="75px" />
                              </Box>
                              <Box>
                                <SkeletonCircle size="75px" />
                              </Box>
                            </Flex>
                            <Flex justifyContent="space-around" alignItems="center" mt="5px">
                              <Box>
                                <Skeleton h="75px" w="75px" />
                              </Box>
                              <Box>
                                <SkeletonCircle size="75px" />
                              </Box>
                            </Flex>
                          </>
                        )}
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                </HeaderCard>
                <HeaderCard w="100%" mr={1}>
                  <Tooltip label="Unutilized Workers">
                    <Center>Unutilized Workers</Center>
                  </Tooltip>
                  {/* <Flex justifyContent="space-around"> */}
                  {/* <Box>Active</Box> */}
                  {/* <Tooltip label="Unutilized Workers"> */}
                  {/* <Box>Workers</Box> */}
                  {/* </Tooltip> */}
                  {/* </Flex> */}
                  <Box overflowY="auto" h="auto">
                    {isDataAvailable ? (
                      <>
                        {activityBottomCardHeadCount && activityBottomCardHeadCount.length > 0 ? (
                          activityBottomCardHeadCount.map((d, i) => {
                            return (
                              <Flex
                                justifyContent="space-around"
                                alignItems="center"
                                mb="3px"
                                key={d.label}>
                                <Box>
                                  <Tooltip label={d.label}>
                                    <Box
                                      as="button"
                                      borderRadius="md"
                                      bg={CHART_COLORS_FOR_AGENCY[i]}
                                      p={4}
                                    />
                                  </Tooltip>
                                </Box>
                                {/* <Box>
                            {' '}
                            <Box m="1">247</Box>{' '}
                          </Box> */}
                                <Box>
                                  <CircularChart
                                    maxH="60px"
                                    maxW="60px"
                                    value="90"
                                    valueType="%"
                                    max={100}
                                    strokeWidth={15}
                                    styles={{
                                      pathColor: '#40199D',
                                      trailColor: '#0059CF',
                                      strokeLinecap: 'butt'
                                    }}
                                  />
                                </Box>

                                <Box>95</Box>
                              </Flex>
                            );
                          })
                        ) : (
                          <>
                            <Flex justifyContent="space-around" alignItems="center" mt="5px">
                              <Box>
                                <Skeleton h="75px" w="75px" />
                              </Box>
                              <Box>
                                <SkeletonCircle size="75px" />
                              </Box>
                            </Flex>
                            <Flex justifyContent="space-around" alignItems="center" mt="5px">
                              <Box>
                                <Skeleton h="75px" w="75px" />
                              </Box>
                              <Box>
                                <SkeletonCircle size="75px" />
                              </Box>
                            </Flex>
                          </>
                        )}
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </Box>
                </HeaderCard>
              </Flex>
              <Flex justifyContent="space-evenly" h="250px">
                <HeaderCard w="100%">
                  <Tooltip label="Length of Service">
                    <Center>Length of Service</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {workforceLOS && (
                        <StackChart
                          header=""
                          height="90%"
                          horizontal
                          series={workforceLOS?.rows}
                          label={workforceLOS.agencies}
                          // categories={['1-2', '3-4']}
                        />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
                {/* {workforceLOS && (
                  <HeaderCard w="100%">
                    <Tooltip label="Length of Service">
                      <Center>Length of Service</Center>
                    </Tooltip>
                    {isDataAvailable ? (
                      <>
                        <StackChart
                          header=""
                          height="90%"
                          horizontal
                          series={workforceLOS?.rows}
                          label={workforceLOS.agencies}
                          // categories={['1-2', '3-4']}
                        />
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </HeaderCard>
                )} */}

                {/* <HeaderCard w="100%">
                  <Center>LOS</Center>
                  <StackChart
                    header="Length of Service"
                    height="90%"
                    horizontal={false}
                    series={[
                      {
                        name: '1-2',
                        data: [44, 55, 41, 37, 22, 43, 21]
                      },
                      {
                        name: '3-4',
                        data: [53, 32, 33, 52, 13, 43, 32]
                      },
                      {
                        name: '5-8',
                        data: [12, 17, 11, 9, 15, 11, 20]
                      },
                      {
                        name: '9-12',
                        data: [9, 7, 5, 8, 6, 9, 4]
                      },
                      {
                        name: '13-16',
                        data: [25, 12, 19, 32, 25, 24, 10]
                      }
                    ]}
                    categories={['1-2', '3-4', '5-8', '9-12', '13-16']}
                  />
                </HeaderCard> */}
              </Flex>
            </Box>
          </Box>
          <Box minW="350px" w="100%">
            <Box fontWeight="semibold">{constants.DASHBOARD_VIEW.LEAVERS_FOR_AGENCIES}</Box>
            <Box height="460px">
              <Flex justifyContent="space-evenly" h="210px" mb={1}>
                <HeaderCard w="100%" mr={1}>
                  <Tooltip label="New Starter Retention">
                    <Center>New Starter Retention</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {leaverNSR && leaverNSR.length > 0 ? (
                        <ColumnChart
                          series={[
                            {
                              data: leaverNSR.map((d) => d.count)
                            }
                          ]}
                          categories={leaverNSR.map((d) => d.label)}
                          value={35}
                          max={100}
                          styles={{
                            strokeLinecap: 'butt'
                          }}
                        />
                      ) : (
                        <Skeleton h="85%" w="100%" />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
                <HeaderCard w="100%">
                  <Tooltip label="Leavers">
                    <Center>Leavers</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {leaverCount &&
                        leaverCount.length > 0 &&
                        leaverCount.map((leaver, index) => {
                          return (
                            <>
                              {index % 2 === 0 ? (
                                <Flex>
                                  {' '}
                                  <CircularChart
                                    maxH="80px"
                                    maxW="80px"
                                    value={leaver.active}
                                    valueType="%"
                                    max={leaver.active + leaver.inactive}
                                    strokeWidth={15}
                                    styles={{
                                      pathColor: '#40199D',
                                      trailColor: '#0059CF',
                                      strokeLinecap: 'butt'
                                    }}
                                  />{' '}
                                </Flex>
                              ) : (
                                <CircularChart
                                  maxH="80px"
                                  maxW="80px"
                                  value={leaver.active}
                                  valueType="%"
                                  max={leaver.active + leaver.inactive}
                                  strokeWidth={15}
                                  styles={{
                                    pathColor: '#40199D',
                                    trailColor: '#0059CF',
                                    strokeLinecap: 'butt'
                                  }}
                                />
                              )}
                            </>
                          );
                        })}
                      {/* <Flex mb="1px">
                        <CircularChart
                          maxH="80px"
                          maxW="80px"
                          value="90"
                          valueType="%"
                          max={100}
                          strokeWidth={15}
                          styles={{
                            pathColor: '#40199D',
                            trailColor: '#0059CF',
                            strokeLinecap: 'butt'
                          }}
                        />
                        <CircularChart
                          maxH="80px"
                          maxW="80px"
                          value="90%"
                          max={100}
                          strokeWidth={15}
                          styles={{
                            pathColor: '#40199D',
                            trailColor: '#0059CF',
                            strokeLinecap: 'butt'
                          }}
                        />
                      </Flex>
                      <Flex>
                        <CircularChart
                          maxH="80px"
                          maxW="80px"
                          value="90"
                          valueType="%"
                          max={100}
                          strokeWidth={15}
                          styles={{
                            pathColor: '#40199D',
                            trailColor: '#0059CF',
                            strokeLinecap: 'butt'
                          }}
                        />
                        <CircularChart
                          maxH="80px"
                          maxW="80px"
                          value="90%"
                          max={100}
                          strokeWidth={15}
                          styles={{
                            pathColor: '#40199D',
                            trailColor: '#0059CF',
                            strokeLinecap: 'butt'
                          }}
                        />
                      </Flex> */}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
              </Flex>
              <Flex justifyContent="space-evenly" h="250px">
                {/* <HeaderCard w="100%" mr={1}>
                  <Center>Shift Utilisation</Center>
                  <Flex justifyContent="space-around">
                    <Box>1-3 Days</Box>
                    <Box>4+ Days</Box>
                  </Flex>
                  {leaverShiftUtl && leaverShiftUtl.length > 0 ? (
                    leaverShiftUtl.map((d, i) => {
                      return (
                        <Flex
                          justifyContent="space-around"
                          alignItems="center"
                          mb="3px"
                          key={d.label}>
                          <Box>
                            <Tooltip label={d.label}>
                              <Box
                                as="button"
                                borderRadius="md"
                                bg={CHART_COLORS_FOR_AGENCY[i]}
                                p={4}
                              />
                            </Tooltip>
                          </Box>
                          <Box>
                            <Box m="1">{d['1-3']}</Box>{' '}
                          </Box>
                          <Box>
                            <CircularChart
                              maxH="80px"
                              maxW="80px"
                              value={Math.round((100 * d['4+']) / d.total)}
                              valueType="%"
                              max={100}
                              strokeWidth={15}
                              styles={{
                                pathColor: '#40199D',
                                trailColor: '#0059CF',
                                strokeLinecap: 'butt'
                              }}
                            />
                          </Box>

                          <Box>{d['4+']}</Box>
                        </Flex>
                      );
                    })
                  ) : (
                    <>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                      <Flex justifyContent="space-around" alignItems="center" mt="5px">
                        <Box>
                          <Skeleton h="75px" w="75px" />
                        </Box>
                        <Box>
                          <SkeletonCircle size="75px" />
                        </Box>
                      </Flex>
                    </>
                  )}
                </HeaderCard> */}
                <HeaderCard w="100%">
                  <Tooltip label="Length of Service">
                    <Center>Length of Service</Center>
                  </Tooltip>
                  {isDataAvailable ? (
                    <>
                      {leaverLOS && (
                        <StackChart
                          header=""
                          height="90%"
                          horizontal
                          series={leaverLOS?.rows}
                          label={leaverLOS.agencies}
                          // categories={['1-2', '3-4']}
                        />
                      )}
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )}
                </HeaderCard>
                {/* {leaverLOS && (
                  <HeaderCard w="100%">
                    <Tooltip label="Length of Service">
                      <Center>Length of Service</Center>
                    </Tooltip>
                    {isDataAvailable ? (
                      <>
                        <StackChart
                          header=""
                          height="90%"
                          horizontal
                          series={leaverLOS?.rows}
                          label={leaverLOS.agencies}
                          // categories={['1-2', '3-4']}
                        />
                      </>
                    ) : (
                      <NoData height="130px" width="130px" />
                    )}
                  </HeaderCard>
                )} */}
                {/* {isDataAvailable ? (
                    <>
                      <StackChart
                        header=""
                        height="90%"
                        horizontal
                        series={[
                          {
                            name: '1-2',
                            data: [44, 55, 41, 37, 22, 43, 21]
                          },
                          {
                            name: '3-4',
                            data: [53, 32, 33, 52, 13, 43, 32]
                          },
                          {
                            name: '5-8',
                            data: [12, 17, 11, 9, 15, 11, 20]
                          },
                          {
                            name: '9-12',
                            data: [9, 7, 5, 8, 6, 9, 4]
                          },
                          {
                            name: '13-16',
                            data: [25, 12, 19, 32, 25, 24, 10]
                          }
                        ]}
                        categories={['1-2', '3-4', '5-8', '9-12', '13-16']}
                      />
                    </>
                  ) : (
                    <NoData height="130px" width="130px" />
                  )} */}
              </Flex>
            </Box>
          </Box>
        </Flex>
      </Box>
    </>
  );
};

export default ChartCard;
