/* eslint-disable react/forbid-prop-types */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Input,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton
} from '@chakra-ui/react';

const MfaVerificationModal = function ({
  isOpen,
  onClose,
  onComplete,
  isLoading,
  error: externalError
}) {
  const [totpCode, setTotpCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isOpen) {
      setTotpCode('');
      setError('');
      setIsVerifying(false);
    }
  }, [isOpen]);

  useEffect(() => {
    setError(externalError || '');
  }, [externalError]);

  const handleVerifyCode = async () => {
    if (!totpCode || totpCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      await onComplete(totpCode);
    } catch (err) {
      setError(err.message || 'Invalid code. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleCodeChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setTotpCode(value);
    setError('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && totpCode.length === 6) {
      handleVerifyCode();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" closeOnOverlayClick={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Text fontSize="xl" fontWeight="bold" color="main.primary">
            Two-Factor Authentication
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch">
            <Alert status="info">
              <AlertIcon />
              <Box>
                <AlertTitle>Verification Required</AlertTitle>
                <AlertDescription>
                  Please enter the 6-digit code from your authenticator app to complete login.
                </AlertDescription>
              </Box>
            </Alert>

            <VStack spacing={4} align="stretch">
              <Text fontSize="md" fontWeight="semibold" textAlign="center">
                Enter your authentication code:
              </Text>
              <Input
                value={totpCode}
                onChange={handleCodeChange}
                onKeyPress={handleKeyPress}
                placeholder="000000"
                size="lg"
                textAlign="center"
                fontSize="xl"
                letterSpacing="0.2em"
                maxLength={6}
                bg="white"
                border="2px"
                borderColor={error ? 'red.300' : 'gray.200'}
                _focus={{
                  borderColor: error ? 'red.500' : 'main.primary',
                  boxShadow: 'none'
                }}
                autoFocus
              />
              {error && (
                <Text color="red.500" fontSize="sm" textAlign="center">
                  {error}
                </Text>
              )}
              <Text fontSize="sm" color="gray.600" textAlign="center">
                Open your authenticator app and enter the current 6-digit code
              </Text>
            </VStack>

            <HStack spacing={3} justify="flex-end">
              <Button variant="outline" onClick={onClose} isDisabled={isVerifying || isLoading}>
                Cancel
              </Button>
              <Button
                bg="main.semiPrimary"
                color="white"
                _hover={{ bg: 'main.primary' }}
                onClick={handleVerifyCode}
                isLoading={isVerifying || isLoading}
                loadingText="Verifying..."
                isDisabled={totpCode.length !== 6}>
                Verify
              </Button>
            </HStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

MfaVerificationModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onComplete: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  error: PropTypes.string
};

MfaVerificationModal.defaultProps = {
  isLoading: false,
  error: ''
};

export default MfaVerificationModal;
