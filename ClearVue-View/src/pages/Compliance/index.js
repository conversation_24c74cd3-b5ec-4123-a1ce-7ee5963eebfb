/* eslint-disable camelcase */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable no-param-reassign */

import {
  Box,
  Button,
  Flex,
  Icon,
  Image,
  SimpleGrid,
  Text,
  Tooltip,
  useMediaQuery,
  useToast,
  Switch
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import Card from '../../components/Card';
import HeaderCard from '../../components/HeaderCard';
import ServerSideDatatable from '../../components/ServerSideDatatable';
import { CARDS_DATA, PAGE_TITLE, TOAST_SETTINGS, USER_TYPE } from '../../constants';
import {
  getCompliances,
  getCompliancesCountData,
  updateComplianceApproval
} from '../../redux/action/compliance.action';
import { StyledContainer } from '../AdminDashboard/AdminDashboard.styled';
import StyledComplianceContainer from './Compliance.styled';
import Loader from '../../components/Loader';
import InfoIcon from '../../assets/images/info.svg';

// Define card-specific column configurations
const CARD_COLUMNS_CONFIG = {
  1: ['employee_id', 'agency_name', 'site_name', 'week', 'total_hours'],
  2: [
    'employee_id',
    'agency_name',
    'site_name',
    'client_approval_status',
    'agency_approval_status'
  ],
  3: ['employee_id', 'agency_name', 'site_name', 'week', 'total_hours', 'los'],
  4: ['employee_id', 'agency_name', 'site_name', 'consecutive_days'],
  5: ['employee_id', 'agency_name', 'site_name'],
  6: [
    'employee_id',
    'agency_name',
    'site_name',
    'masked_account_number',
    'client_approval_status',
    'agency_approval_status'
  ],
  7: ['employee_id', 'agency_name', 'site_name', 'week', 'total_hours', 'los']
};

// Card-specific extra downlod CSV columns (in addition to table columns)
const CARD_CSV_EXTRA_COLUMNS = {
  3: ['student_visa', 'limited_hours']
};

// Define column definitions (includes CSV-only columns)
const COLUMN_DEFINITIONS = {
  employee_id: { label: 'Employee ID', field: 'employee_id', sort: true },
  first_name: { label: 'First Name', field: 'first_name', sort: true },
  agency_name: { label: 'Agency', field: 'agency_name', sort: true },
  site_name: { label: 'Site', field: 'site_name', sort: true },
  week: { label: 'Week', field: 'week', sort: true },
  masked_account_number: {
    label: 'Masked Account Number',
    field: 'masked_account_number',
    sort: true
  },
  shift_name: { label: 'Shift', field: 'shift_name', sort: true },
  job_name: { label: 'Role', field: 'job_name', sort: true },
  date_of_birth: { label: 'DOB', field: 'date_of_birth', sort: true },
  post_code: { label: 'Post Code', field: 'post_code', sort: true },
  total_hours: { label: 'Total Hours', field: 'total_hours', sort: true },
  consecutive_days: { label: 'Consecutive Days', field: 'consecutive_days', sort: true },
  los: { label: 'LOS', field: 'los', sort: true },
  client_approval_status: {
    label: 'Client Approval',
    field: 'client_approval_status',
    sort: true,
    formatValue: (val) => (val ? 'Approved' : 'Pending')
  },
  agency_approval_status: {
    label: 'Agency Approval',
    field: 'agency_approval_status',
    sort: true,
    formatValue: (val) => (val ? 'Approved' : 'Pending')
  },

  // CSV-only columns
  student_visa: {
    label: 'Student Visa',
    field: 'student_visa',
    sort: true,
    formatValue: (val) => (val ? 'Yes' : 'No')
  },
  limited_hours: {
    label: 'Limited Hours',
    field: 'limited_hours',
    sort: true,
    formatValue: (val) => (val ? 'Yes' : 'No')
  }
};

/**
 * Gets all CSV columns for a specific card (table columns + extra CSV columns)
 */
const getCSVColumns = (selectedCard) => {
  const tableColumns = CARD_COLUMNS_CONFIG[selectedCard] || [];
  const extraCSVColumns = CARD_CSV_EXTRA_COLUMNS[selectedCard] || [];
  return [...tableColumns, ...extraCSVColumns];
};

/**
 * Generates and downloads CSV file based on selected card configuration
 */
const generateCSVFile = (result, selectedCard, toast) => {
  if (!result || !result.ok) {
    toast({
      ...TOAST_SETTINGS.SETTINGS,
      status: TOAST_SETTINGS.ERROR,
      title: result.error
    });
    return;
  }

  const csvColumns = getCSVColumns(selectedCard);

  // Create header row
  const headerRow = csvColumns.map((field) => COLUMN_DEFINITIONS[field].label).join(',');

  // Create data rows
  const dataRows = result.data.workers.map((row) => {
    return csvColumns
      .map((field) => {
        const columnDef = COLUMN_DEFINITIONS[field];
        const value = row[field];

        // Apply formatting if defined (for CSV-only columns like Yes/No)
        if (columnDef && columnDef.formatValue && typeof columnDef.formatValue === 'function') {
          return columnDef.formatValue(value);
        }

        return value !== undefined && value !== null ? value : '';
      })
      .join(',');
  });

  // Combine header and data
  const csvContent = [headerRow, ...dataRows].join('\r\n');
  const encodedUri = encodeURI(`data:text/csv;charset=utf-8,${csvContent}`);

  // Create and trigger download
  const link = document.createElement('a');
  link.setAttribute('href', encodedUri);
  link.setAttribute('download', 'Compliance Data Of Workers.csv');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const Campliance = function () {
  const { user_details: userDetails } = useSelector((state) => state.authentication);
  const { selected_client_id, selected_site_id } = useSelector((state) => state.agency);
  const toast = useToast();

  const [reload, setReload] = useState(false);
  const [isSmallerThan1440] = useMediaQuery('(max-width: 1440px)');
  const [selectedCard, setSelectedCard] = useState(0);
  const { selected_start_date } = useSelector((state) => state.agency);
  const { selected_end_date } = useSelector((state) => state.agency);
  const [columns, setcolumns] = useState([]);
  const [count, setcount] = useState(0);
  const [isPageLoader, setIsPageLoader] = useState(false);
  const [paramsData, setparamsData] = useState([]);
  const [countData, setcountData] = useState();
  const [cardData, setcardData] = useState(CARDS_DATA);
  const [showOnlyApproved, setShowOnlyApproved] = useState(false);
  const [approvalLoadingId, setApprovalLoadingId] = useState(null);

  useEffect(() => {
    document.title = PAGE_TITLE.COMPLIANCE;
  }, []);

  const handleCardClick = (e, index) => {
    if (isPageLoader) {
      e.preventDefault();
    } else {
      setcount(count + 1);
      setSelectedCard(index);
    }
  };

  const downloadComplianceDataClickHandler = async () => {
    const params = paramsData;
    params[0] = countData;
    params[1] = 1;
    const result = await getCompliances(
      selected_client_id || userDetails.client_id,
      (userDetails.user_type_id === USER_TYPE.AGENCY_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN) &&
        userDetails.agency_id,
      userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.REGION_ADMIN
        ? userDetails.region_id
        : '',
      userDetails.user_type_id === USER_TYPE.SITE_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN
        ? userDetails.site_id
        : '',
      selectedCard,
      selected_start_date,
      selected_end_date,
      true,
      showOnlyApproved ? 1 : 0,
      ...params
    );
    generateCSVFile(result, selectedCard, toast);
  };

  const retrieveComplianceData = async (...params) => {
    setparamsData(params);
    const result = await getCompliances(
      selected_client_id || userDetails.client_id,
      (userDetails.user_type_id === USER_TYPE.AGENCY_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN) &&
        userDetails.agency_id,
      userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.REGION_ADMIN
        ? userDetails.region_id
        : '',
      userDetails.user_type_id === USER_TYPE.SITE_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN
        ? userDetails.site_id
        : '',
      selectedCard,
      selected_start_date,
      selected_end_date,
      false,
      showOnlyApproved ? 1 : 0,
      ...params
    );
    if (result.ok) {
      setcountData(result.data.count);
      return result;
    }
    return {
      data: []
    };
  };
  const retriveCountData = async () => {
    const result = await getCompliancesCountData(
      selected_client_id || userDetails.client_id,
      (userDetails.user_type_id === USER_TYPE.AGENCY_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN) &&
        userDetails.agency_id,
      userDetails.user_type_id === USER_TYPE.AGENCY_REGION_ADMIN ||
        userDetails.user_type_id === USER_TYPE.REGION_ADMIN
        ? userDetails.region_id
        : '',
      userDetails.user_type_id === USER_TYPE.SITE_ADMIN ||
        userDetails.user_type_id === USER_TYPE.AGENCY_SITE_ADMIN
        ? userDetails.site_id
        : '',

      selected_start_date,
      selected_end_date
    );
    setIsPageLoader(false);
    if (result.ok) {
      const updatedCARDS_DATA = cardData.map((card) => {
        const matchingData = result.data.count.find((item) => item.id === card.index);
        if (matchingData) {
          card.value = matchingData.count;
        }
        return card;
      });
      setcardData(updatedCARDS_DATA);
    }
  };

  useEffect(() => {
    setIsPageLoader(true);
    retriveCountData();
    setReload(!reload);
  }, [selected_start_date, selected_end_date, selectedCard, selected_client_id, showOnlyApproved]);

  /**
   * Gets all unique field names that should be managed by this system
   */
  const getAllManagedFields = () => {
    return [...new Set(Object.values(CARD_COLUMNS_CONFIG).flat())];
  };

  /**
   * Updates columns array based on selected card configuration
   */
  const updateColumnsForCard = (columns_, selectedCard2) => {
    const managedFields = getAllManagedFields();
    const requiredFields = CARD_COLUMNS_CONFIG[selectedCard2] || [];

    // Remove columns_ that shouldn't be present for this card
    for (let i = columns_.length - 1; i >= 0; i -= 1) {
      const column = columns_[i];
      if (managedFields.includes(column.field) && !requiredFields.includes(column.field)) {
        columns_.splice(i, 1);
      }
    }

    // Add columns_ that should be present for this card
    requiredFields.forEach((fieldName) => {
      if (!columns_.some((col) => col.field === fieldName)) {
        columns_.push(COLUMN_DEFINITIONS[fieldName]);
      }
    });
  };

  useEffect(() => {
    updateColumnsForCard(columns, selectedCard);
  }, [selectedCard, columns]);

  // Handler to toggle approval status
  const handleApprovalToggle = async (canToggle, row, val) => {
    if (!canToggle || approvalLoadingId === row.worker_id) return;
    setApprovalLoadingId(row.worker_id);
    try {
      const result = await updateComplianceApproval(
        selected_client_id || userDetails.client_id,
        selectedCard,
        row.worker_id,
        !val // toggle value
      );
      if (result.ok) {
        setReload((r) => !r);
      } else {
        toast({
          ...TOAST_SETTINGS.SETTINGS,
          status: TOAST_SETTINGS.ERROR,
          title: result.error
        });
      }
    } catch (err) {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: 'Failed to update approval status.'
      });
    } finally {
      setApprovalLoadingId(null);
    }
  };

  // Update column definitions to use the handler
  const columnsWithToggle = (CARD_COLUMNS_CONFIG[selectedCard] || []).map((field) => {
    const col = { ...COLUMN_DEFINITIONS[field] };
    if (field === 'client_approval_status' || field === 'agency_approval_status') {
      col.render = (val, row, rowIndex) => {
        const userType = userDetails.user_type_id;
        const isClient = field === 'client_approval_status';
        const isAgency = field === 'agency_approval_status';
        const canToggle =
          (isClient &&
            (userType === USER_TYPE.CLIENT_ADMIN ||
              userType === USER_TYPE.SITE_ADMIN ||
              userType === USER_TYPE.REGION_ADMIN)) ||
          (isAgency &&
            (userType === USER_TYPE.AGENCY_ADMIN ||
              userType === USER_TYPE.AGENCY_REGION_ADMIN ||
              userType === USER_TYPE.AGENCY_SITE_ADMIN));
        return (
          <Button
            size="sm"
            colorScheme={val ? 'green' : 'gray'}
            variant="solid"
            style={{ minWidth: 80 }}
            disabled={!canToggle || approvalLoadingId === row.worker_id}
            isLoading={approvalLoadingId === row.worker_id}
            onClick={() => handleApprovalToggle(canToggle, row, val)}>
            {val ? 'Approved' : 'Pending'}
          </Button>
        );
      };
    }
    return col;
  });

  return (
    <StyledComplianceContainer bg="main.secondary">
      {isPageLoader ? <Loader /> : null}
      <StyledContainer>
        <SimpleGrid
          width={isSmallerThan1440 && '1190px'}
          //   height="250px"
          minChildWidth="220px">
          {cardData.map((card, index) => (
            <HeaderCard
              textAlign="center"
              fontWeight="semibold"
              disabled="true"
              width="220px"
              height="220px"
              //   p={4}
              cursor={isPageLoader ? 'not-allowed' : 'pointer'}
              key={card.index}
              bg={selectedCard === card.index ? 'main.semiPrimary' : 'main.white'}
              color={selectedCard === card.index ? 'main.white' : ''}
              onClick={(e) => handleCardClick(e, card.index)}>
              <Text fontSize="lg" fontWeight="normal">
                {card.title}
              </Text>
              <Text
                fontSize="6xl"
                color={selectedCard === card.index ? 'main.white' : 'main.semiPrimary'}
                fontWeight="normal">
                {card.value}
              </Text>
            </HeaderCard>
          ))}
        </SimpleGrid>
      </StyledContainer>
      {selectedCard > 0 && (
        <Card style={{ marginTop: '20px' }}>
          <Flex align="center" bg="gray.50" borderRadius="md" px={4} py={2} mb={4} gap={4}>
            <Button
              mr={2}
              bg="main.semiPrimary"
              color="main.white"
              _hover={{ bg: 'main.primary' }}
              _active={{
                bg: 'main.semiPrimary',
                borderColor: 'main.primary'
              }}
              disabled={countData === 0}
              onClick={downloadComplianceDataClickHandler}>
              Export
            </Button>
            {(selectedCard === 2 || selectedCard === 6) && (
              <>
                <Tooltip
                  label="Date filter irrelevant for this card."
                  placement="top"
                  bg="gray.700"
                  color="white"
                  fontSize="sm"
                  hasArrow>
                  <Box display="inline-block" cursor="pointer" mr={4}>
                    <Image src={InfoIcon} alt="Info" boxSize="20px" />
                  </Box>
                </Tooltip>
                <Flex align="center" gap={2}>
                  <Text fontWeight="semibold" fontSize="md" color="gray.700" mr={2}>
                    Approved Workers
                  </Text>
                  <Tooltip
                    label="When ON, only workers approved by both client and agency are shown/exported."
                    placement="top"
                    bg="gray.700"
                    color="white"
                    fontSize="sm"
                    hasArrow>
                    <Box as="span">
                      <Switch
                        isChecked={showOnlyApproved}
                        onChange={() => setShowOnlyApproved((v) => !v)}
                        colorScheme="teal"
                        size="md"
                        display="inline-block"
                      />
                    </Box>
                  </Tooltip>
                </Flex>
              </>
            )}
          </Flex>
          <ServerSideDatatable
            pageSize={10}
            initialSortKey="employee_id"
            column={columnsWithToggle}
            onPaginate={retrieveComplianceData}
            afterPaginateData={() => setIsPageLoader(false)}
            pagination
            pages={[10, 15]}
            dataKey="workers"
            refresh={reload}
            moveToFirstPage="true"
            userType={userDetails.user_type_id}
          />
        </Card>
      )}
    </StyledComplianceContainer>
  );
};

export default Campliance;
