/* eslint-disable no-param-reassign */
import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  Text,
  FormErrorMessage
} from '@chakra-ui/react';
import DatePicker from 'react-multi-date-picker';
import moment from 'moment';
import ModalComponent from '../../../../components/Modal';
import InputComponent from '../../../../components/Input';
import { allowTwoDecimalNumbersInRange, deepClone, validateForm } from '../../../../utils/helper';
import constants, { ADD_FINANCIAL_RULE, ADD_FINANCIAL_RULE_DATES } from '../../../../constants';

const getFirstWeekdayOfMarch = (year, weekday) => {
  const date = moment(`${year}/03/01`);
  while (date.day() !== weekday) {
    date.add(1, 'day');
  }
  return date;
};

const FinancialRuleAddEdit = function ({
  isOpen,
  onClose,
  onSave,
  clientWeekDay,
  defaultDate,
  isEdit,
  selectedData,
  selectedIndex,
  rulesExists
}) {
  const todayDate = moment();
  const [userCurrentDate, setUserCurrentDate] = useState();
  const [userEndDate, setUserEndDate] = useState();
  const [validationObject, setValidationObject] = useState(deepClone(ADD_FINANCIAL_RULE));
  const [validationDatesObject, setValidationDatesObject] = useState(
    deepClone(ADD_FINANCIAL_RULE_DATES)
  );
  const [refreshFlag, setRefreshFlag] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const calendarRef = useRef();
  const calendarEndRef = useRef();

  const weekTotalOptions = [
    { value: '52', label: '52 Weeks' },
    { value: '53', label: '53 Weeks' }
  ];
  const weekDayMap = new Map([
    ['SUN', { idx: 0, display: 'Sunday' }],
    ['MON', { idx: 1, display: 'Monday' }],
    ['TUE', { idx: 2, display: 'Tuesday' }],
    ['WED', { idx: 3, display: 'Wednesday' }],
    ['THU', { idx: 4, display: 'Thuesday' }],
    ['FRI', { idx: 5, display: 'Friday' }],
    ['SAT', { idx: 6, display: 'Saturday' }]
  ]);

  const [formData, setFormData] = useState({
    weekStartDay: '',
    weekStartDate: '',
    selectedWeeks: null,
    weekEndDay: '',
    weekEndDate: '',
    niPercentage: '',
    niThreshold: '',
    pensionPercentage: '',
    pensionThreshold: '',
    appLevyPercentage: ''
  });
  const resetForm = () => {
    clientWeekDay = null;
    setFormData({
      weekStartDay: '',
      weekStartDate: '',
      selectedWeeks: null,
      weekEndDay: '',
      weekEndDate: '',
      niPercentage: '',
      niThreshold: '',
      pensionPercentage: '',
      pensionThreshold: ''
    });
    setValidationObject(deepClone(ADD_FINANCIAL_RULE));
    setValidationDatesObject(deepClone(ADD_FINANCIAL_RULE_DATES));
  };
  useEffect(() => {
    if (clientWeekDay) {
      setFormData((oldData) => ({
        ...oldData,
        weekStartDay: weekDayMap.get(clientWeekDay).display
      }));

      if (!defaultDate) {
        const year = todayDate.year();
        const date = getFirstWeekdayOfMarch(year, weekDayMap.get(clientWeekDay).idx);
        setUserCurrentDate(date);
      } else {
        let date = moment(defaultDate);
        if (!isEdit) {
          setUserCurrentDate(date.add(1, 'day'));
          setUserEndDate(date.clone().add(6, 'day'));
        } else {
          date = moment(selectedData?.start_date);
          setUserCurrentDate(date);
          setUserEndDate(moment(selectedData?.end_date));
        }
      }
    }
    if (isEdit && selectedData) {
      setFormData((oldData) => ({
        ...oldData,
        selectedWeeks: weekTotalOptions.find(
          (option) => +option.value === selectedData.total_weeks
        ),
        niPercentage: selectedData.ni_percent,
        niThreshold: selectedData.ni_threshold,
        pensionPercentage: selectedData.pension_percent,
        pensionThreshold: selectedData.pension_threshold,
        appLevyPercentage: selectedData.app_levy_percent
      }));
    }
  }, [isOpen, defaultDate]);

  useEffect(() => {
    if (userCurrentDate) {
      setFormData((oldData) => ({
        ...oldData,
        weekStartDate: userCurrentDate
      }));
    }
  }, [userCurrentDate]);

  useEffect(() => {
    if (userEndDate) {
      const diff = moment(userEndDate).diff(moment(userCurrentDate), 'weeks');
      setFormData((oldData) => ({
        ...oldData,
        weekEndDate: userEndDate,
        selectedWeeks: diff + 1,
        weekEndDay: userEndDate.format('dddd')
      }));
    }
  }, [userEndDate]);

  const handleDateEndChange = (value) => {
    const newData = { weekEndDate: moment(value.toString(), 'DD/MM/YYYY') };

    // find difference between start date and end date in teams of weeks
    const diff = moment(newData.weekEndDate).diff(moment(formData.weekStartDate), 'weeks');

    if (moment(newData.weekEndDate).isBefore(moment(formData.weekStartDate)) && diff <= 0) {
      newData.weekEndDate = moment(formData.weekStartDate).add(7, 'day');
      newData.selectedWeeks = 0;
    } else {
      newData.selectedWeeks = diff + 1;
    }

    setFormData((oldData) => {
      const newDataForValidation = {
        ...oldData,
        ...newData,
        weekEndDay: newData.weekEndDate.format('dddd')
      };

      const [validatedDatesObject] = validateForm(newDataForValidation, validationDatesObject);
      setValidationDatesObject(validatedDatesObject);

      return newDataForValidation;
    });
  };

  const handleDateChange = (value) => {
    const newStartDate = moment(value.toString(), 'DD/MM/YYYY');

    // Create the new data object
    const newData = {
      weekStartDate: newStartDate
    };

    // Check if the new start date is same as or after the current end date
    if (formData.weekEndDate && newStartDate.isSameOrAfter(formData.weekEndDate)) {
      // If so, set end date to start date + 6 days
      newData.weekEndDate = '';
      newData.selectedWeeks = 0;
    } else {
      newData.selectedWeeks = moment(formData.weekEndDate).diff(newStartDate, 'weeks') + 1;
    }

    // Update form data with the new values
    setFormData((oldData) => ({ ...oldData, ...newData }));
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleFormSubmit = () => {
    setHasSubmitted(true);
    const [validatedObject, isFormValid] = validateForm(formData, validationObject);
    setValidationObject(validatedObject);

    const [validatedDatesObject, isDatesValid] = validateForm(formData, validationDatesObject);
    setValidationDatesObject(validatedDatesObject);

    setRefreshFlag(!refreshFlag);
    if (isFormValid && isDatesValid) {
      onSave(formData);
      resetForm();
      setHasSubmitted(false);
    }
  };
  const handleInputChange = (field, value) => {
    setFormData((oldData) => {
      const newData = { ...oldData, [field]: value };
      // Only validate if the user has already tried to submit
      if (hasSubmitted) {
        const [validatedObject] = validateForm(newData, validationObject);
        setValidationObject(validatedObject);
      }
      return newData;
    });
  };
  const getForm = () => {
    const isDayDisabled = (date) => date.weekDay.index !== weekDayMap.get(clientWeekDay)?.idx;
    const isEndDayDisabled = (date) => {
      // Get the start day index from the selected start date (not from clientWeekDay)
      const startDate = formData.weekStartDate;
      if (!startDate) return true; // Disable all if no start date

      const startDayIdx = startDate.day(); // moment().day() gives 0 (Sun) to 6 (Sat)
      const endDayIdx = (startDayIdx + 6) % 7; // Day before start day

      const isBeforeStart = startDate && moment(date.toDate()).isBefore(startDate, 'day');
      return date.weekDay.index !== endDayIdx || isBeforeStart;
    };

    return (
      <Grid templateColumns="repeat(4, 1fr)" gap={4} p={4}>
        {isEdit && (
          <GridItem colSpan={4}>
            <Text as="span" fontSize="sm" color="red">
              Note: Editing dates may result in the deletion of the following rule, if applicable.
            </Text>
          </GridItem>
        )}
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              lable="Week Start Day"
              placeholder="Week Start Day"
              value={formData.weekStartDay}
              disabled
              templatevalidation
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl isInvalid={!validationDatesObject.weekStartDate.isvalid} translate="no">
            <FormLabel color="main.primary" fontWeight="normal">
              Start Date
            </FormLabel>
            <DatePicker
              locale="en"
              placeholder="Start Date"
              format="DD/MM/YYYY"
              inputClass={
                (rulesExists && !isEdit) || (!!defaultDate && selectedIndex !== 0)
                  ? 'date-picker-disabled'
                  : 'date-picker'
              }
              ref={calendarRef}
              value={formData.weekStartDate ? formData.weekStartDate?.format('DD/MM/YYYY') : ''}
              weekStartDayIndex={weekDayMap.get(clientWeekDay)?.idx}
              onChange={handleDateChange}
              mapDays={({ date }) => ({ disabled: isDayDisabled(date) })}
              disabled={(rulesExists && !isEdit) || (!!defaultDate && selectedIndex !== 0)}
              editable={false}>
              <Button
                bg="main.semiPrimary"
                color="main.white"
                _hover={{ bg: 'main.primary' }}
                _active={{
                  bg: 'main.semiPrimary',
                  borderColor: 'main.primary'
                }}
                onClick={() => calendarRef.current.closeCalendar()}
                size="xs"
                mb={4}>
                {constants.DASHBOARD_VIEW.CLOSE_BUTTON_TEXT}
              </Button>
            </DatePicker>
            {!validationDatesObject.weekStartDate.isvalid && (
              <FormErrorMessage>
                {validationDatesObject.weekStartDate.errorMessage}
              </FormErrorMessage>
            )}
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              lable="Week End Day"
              placeholder="Week End Day"
              value={formData.weekEndDay}
              disabled
              templatevalidation
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl isInvalid={!validationDatesObject.weekEndDate.isvalid}>
            <FormLabel color="main.primary" fontWeight="normal">
              End Date
            </FormLabel>
            <DatePicker
              locale="en"
              placeholder="End Date"
              format="DD/MM/YYYY"
              inputClass="date-picker"
              ref={calendarEndRef}
              value={formData.weekEndDate ? formData.weekEndDate?.format('DD/MM/YYYY') : ''}
              weekStartDayIndex={weekDayMap.get(clientWeekDay)?.idx}
              minDate={
                formData.weekStartDate ? formData.weekStartDate.format('DD/MM/YYYY') : undefined
              }
              onChange={handleDateEndChange}
              mapDays={({ date }) => ({ disabled: isEndDayDisabled(date) })}
              editable={false}>
              <Button
                bg="main.semiPrimary"
                color="main.white"
                _hover={{ bg: 'main.primary' }}
                _active={{
                  bg: 'main.semiPrimary',
                  borderColor: 'main.primary'
                }}
                onClick={() => calendarEndRef.current.closeCalendar()}
                size="xs"
                mb={4}>
                {constants.DASHBOARD_VIEW.CLOSE_BUTTON_TEXT}
              </Button>
            </DatePicker>
            {!validationDatesObject.weekEndDate.isvalid && (
              <FormErrorMessage>{validationDatesObject.weekEndDate.errorMessage}</FormErrorMessage>
            )}
          </FormControl>
        </GridItem>
        <GridItem colSpan={4}>
          <FormControl>
            <InputComponent
              lable="Total Weeks"
              placeholder="Total Weeks"
              labelFontSize="md"
              value={formData.selectedWeeks}
              templatevalidation
              style={{ marginTop: '0.5rem' }}
              disabled
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              type="number"
              lable="NI Percent"
              placeholder="NI Percent"
              value={formData.niPercentage}
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
              onChange={(value) => {
                handleInputChange('niPercentage', allowTwoDecimalNumbersInRange(value, 0, 100));
              }}
              validationObj={validationObject.niPercentage}
              refresh={refreshFlag}
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              type="number"
              lable="NI Threshold"
              placeholder="NI Threshold"
              value={formData.niThreshold}
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
              onChange={(value) => {
                handleInputChange('niThreshold', allowTwoDecimalNumbersInRange(value, 0, 999));
              }}
              validationObj={validationObject.niThreshold}
              refresh={refreshFlag}
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={4}>
          <FormControl>
            <InputComponent
              type="number"
              lable="App Levy Percent"
              placeholder="App Levy Percent"
              labelFontSize="md"
              value={formData.appLevyPercentage}
              style={{ marginTop: '0.5rem' }}
              onChange={(value) => {
                handleInputChange(
                  'appLevyPercentage',
                  allowTwoDecimalNumbersInRange(value, 0, 100)
                );
              }}
              validationObj={validationObject.appLevyPercentage}
              refresh={refreshFlag}
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              type="number"
              lable="Pension Percent"
              placeholder="Pension Percent"
              value={formData.pensionPercentage}
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
              onChange={(value) => {
                handleInputChange(
                  'pensionPercentage',
                  allowTwoDecimalNumbersInRange(value, 0, 100)
                );
              }}
              validationObj={validationObject.pensionPercentage}
              refresh={refreshFlag}
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <InputComponent
              type="number"
              lable="Pension Threshold"
              placeholder="Pension Threshold"
              value={formData.pensionThreshold}
              style={{ marginTop: '0.5rem' }}
              labelFontSize="md"
              onChange={(value) => {
                handleInputChange('pensionThreshold', allowTwoDecimalNumbersInRange(value, 0, 999));
              }}
              validationObj={validationObject.pensionThreshold}
              refresh={refreshFlag}
            />
          </FormControl>
        </GridItem>
      </Grid>
    );
  };

  return (
    <ModalComponent
      specifySize="xl"
      isOpen={isOpen}
      onClose={handleClose}
      modalTitle={isEdit ? 'Edit Financial Rule' : 'Add Financial Rule'}
      modalContent={getForm()}
      negative={
        <Button onClick={handleClose} bg="main.secondary" mr={3} color="main.black">
          Cancel
        </Button>
      }
      positive={
        <Button
          onClick={handleFormSubmit}
          bg="main.semiPrimary"
          color="main.white"
          _hover={{ bg: 'main.primary' }}
          mr={3}
          _active={{
            bg: 'main.semiPrimary',
            borderColor: 'main.primary'
          }}>
          {isEdit ? 'Update' : 'Save'}
        </Button>
      }
    />
  );
};

export default FinancialRuleAddEdit;
