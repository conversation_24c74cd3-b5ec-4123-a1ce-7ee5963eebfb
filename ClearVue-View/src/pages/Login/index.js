/* eslint-disable no-unused-expressions */
/* eslint-disable no-console */
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Box, useToast } from '@chakra-ui/react';
import LoginBox from '../../components/LoginBox';
import MfaSetupModal from '../../components/MfaSetup';
import MfaVerificationModal from '../../components/MfaVerification';
import { userSignIn, getUserData } from '../../redux/action/user.action';
import constants, {
  LOGIN_VALIDATIONS,
  PATH,
  TOAST_SETTINGS,
  PAGE_TITLE,
  TOKENS,
  USER_TYPE
} from '../../constants';
import { deepClone, validateForm } from '../../utils/helper';
import { getClientData } from '../../redux/action/client.action';
import { getAgencyData } from '../../redux/action/agency.action';
import {
  startMfaEnrollment,
  completeMfaEnrollment,
  completeMfaSignIn
} from '../../services/mfaService';
import { setCookie } from '../../utils/cookie';
// import Loader from '../../components/Loader';

const LoginPage = function () {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const toast = useToast();

  useEffect(() => {
    document.title = PAGE_TITLE.LOGIN;

    // Check for force re-login message
    const forceReloginMessage = sessionStorage.getItem('force_relogin_message');
    if (forceReloginMessage) {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: forceReloginMessage
      });
      // Clear the message after showing it
      sessionStorage.removeItem('force_relogin_message');
    }
  }, [toast]);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [validations, setValidations] = useState(deepClone(LOGIN_VALIDATIONS));
  const [refresh, setRefresh] = useState(false);
  // const [isPageLoader, setIsPageLoader] = useState(false);

  // MFA states
  const [showMfaSetup, setShowMfaSetup] = useState(false);
  const [showMfaVerification, setShowMfaVerification] = useState(false);
  const [mfaData, setMfaData] = useState({
    idToken: '',
    sessionInfo: '',
    qrCodeUrl: '',
    secretKey: '',
    mfaPendingCredential: '',
    mfaEnrollmentId: '',
    loginResult: null // Store the full login result with permissions
  });
  const [isLoading, setIsLoading] = useState(false);

  // Handle MFA setup flow
  const handleMfaSetup = async (idToken, loginResult = null) => {
    try {
      const enrollmentResult = await startMfaEnrollment(idToken);
      if (enrollmentResult.ok) {
        setMfaData({
          ...mfaData,
          idToken,
          sessionInfo: enrollmentResult.data.sessionInfo,
          qrCodeUrl: enrollmentResult.data.qrCodeUrl,
          secretKey: enrollmentResult.data.secretKey,
          loginResult: loginResult || mfaData.loginResult
        });
        setShowMfaSetup(true);
      } else {
        toast({
          ...TOAST_SETTINGS.SETTINGS,
          status: TOAST_SETTINGS.ERROR,
          title: enrollmentResult.error || 'Failed to start MFA setup'
        });
      }
    } catch (error) {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: 'Failed to start MFA setup'
      });
    }
  };

  // Handle MFA verification flow for TOTP
  const handleMfaVerification = async (mfaPendingCredential, mfaInfo, loginResult = null) => {
    try {
      // Extract the first TOTP MFA enrollment ID
      let mfaEnrollmentId = null;
      if (mfaInfo && Array.isArray(mfaInfo) && mfaInfo.length > 0) {
        const totpMfa = mfaInfo.find((mfa) => mfa.totpInfo);
        if (totpMfa && totpMfa.mfaEnrollmentId) {
          mfaEnrollmentId = totpMfa.mfaEnrollmentId;
        }
      }

      // Store the MFA data and show verification screen directly
      const finalLoginResult = loginResult || mfaData.loginResult;

      setMfaData({
        ...mfaData,
        mfaPendingCredential,
        mfaEnrollmentId,
        loginResult: finalLoginResult // Use passed loginResult or keep the stored one
      });
      setShowMfaVerification(true);
    } catch (error) {
      toast({
        ...TOAST_SETTINGS.SETTINGS,
        status: TOAST_SETTINGS.ERROR,
        title: 'Failed to start MFA verification'
      });
    }
  };

  const handleClick = async () => {
    const [validationObject, isFormValid] = validateForm(
      {
        email,
        password
      },
      validations
    );

    setValidations(validationObject);
    if (isFormValid) {
      setIsLoading(true);

      try {
        const result = await dispatch(
          userSignIn({
            email,
            password
          })
        );

        // Handle MFA setup requirement
        if (result && result.data && result.data.requiresMfaSetup) {
          setMfaData({
            ...mfaData,
            idToken: result.data.idToken,
            loginResult: result.data // Store the full login result with permissions
          });
          await handleMfaSetup(result.data.idToken, result.data);
          setIsLoading(false);
          return;
        }

        // Handle MFA verification requirement
        if (result && result.data && result.data.requiresMfaVerification) {
          setMfaData({
            ...mfaData,
            mfaPendingCredential: result.data.mfaPendingCredential,
            loginResult: result.data // Store the full login result with permissions
          });
          await handleMfaVerification(
            result.data.mfaPendingCredential,
            result.data.mfaInfo,
            result.data
          );
          setIsLoading(false);
          return;
        }

        if (result && result.ok) {
          try {
            const userInfo = await dispatch(getUserData());
            const userTypeId = userInfo.data.user_details.user_type_id;

            // STEP: Retrieve login user profile detail
            switch (userTypeId) {
              case 1:
                // Super Admin
                break;
              case 2:
                try {
                  await dispatch(getClientData(userInfo.data.user_details.client_id));
                } catch (error) {
                  // console.error(error);
                }
                // Client Admin
                break;
              case 3:
                try {
                  await dispatch(getAgencyData(userInfo.data.user_details.agency_id));
                } catch (error) {
                  // console.log(error);
                }
                // Agency Admin
                break;
              case 4:
                try {
                  await dispatch(getClientData(userInfo.data.user_details.client_id));
                } catch (error) {
                  // console.log(error);
                }
                // Region Admin
                break;
              case 5:
                try {
                  await dispatch(getClientData(userInfo.data.user_details.client_id));
                } catch (error) {
                  // console.log(error);
                }
                // Site Admin
                break;
              default:
                break;
            }
            userTypeId === USER_TYPE.MESSAGE_ADMIN
              ? navigate(PATH.WORKER.TO)
              : navigate(PATH.DASHBOARD.TO);
          } catch (userDataError) {
            console.error('Error fetching user data:', userDataError);
            toast({
              ...TOAST_SETTINGS.SETTINGS,
              status: TOAST_SETTINGS.ERROR,
              title: 'Failed to load user data. Please try logging in again.'
            });
            // Clear stored tokens on error
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('auth_type');
          }
        } else {
          toast({
            ...TOAST_SETTINGS.SETTINGS,
            status: TOAST_SETTINGS.ERROR,
            title: result.error
          });
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Login error:', error);
        toast({
          ...TOAST_SETTINGS.SETTINGS,
          status: TOAST_SETTINGS.ERROR,
          title: 'Login failed. Please try again.'
        });
        setIsLoading(false);
      }
    } else {
      setRefresh(!refresh);
    }
  };

  // Complete MFA setup
  const handleCompleteMfaSetup = async (totpCode) => {
    try {
      const result = await completeMfaEnrollment(
        mfaData.idToken,
        mfaData.sessionInfo,
        totpCode,
        email
      );

      if (result.ok) {
        // Store new tokens
        setCookie(TOKENS.ACCESS, result.data.idToken);
        setCookie(TOKENS.REFRESH, result.data.refreshToken);

        setShowMfaSetup(false);
        setIsLoading(false);

        toast({
          ...TOAST_SETTINGS.SETTINGS,
          status: TOAST_SETTINGS.SUCCESS,
          title: 'MFA setup completed successfully!'
        });

        // Use the stored login result data with permissions instead of fetching user data
        try {
          if (mfaData.loginResult && mfaData.loginResult.permissions) {
            // Dispatch USER_LOGIN action with the permissions from login result
            dispatch({
              type: 'USER_LOGIN',
              payload: {
                id: mfaData.loginResult.user_id,
                user_id: mfaData.loginResult.user_id,
                permissions: mfaData.loginResult.permissions,
                user_type: mfaData.loginResult.user_type,
                client_id: mfaData.loginResult.client_id,
                agency_id: mfaData.loginResult.agency_id
              }
            });

            // Fetch profile data based on user type from stored login result
            if (mfaData.loginResult.user_type === 'client' && mfaData.loginResult.client_id) {
              // CLIENT_ADMIN
              dispatch(getClientData(mfaData.loginResult.client_id));
            } else if (
              mfaData.loginResult.user_type === 'agency' &&
              mfaData.loginResult.agency_id
            ) {
              // AGENCY_ADMIN
              dispatch(getAgencyData(mfaData.loginResult.agency_id));
            }

            // Wait a bit for Redux state to update before navigating
            setTimeout(() => {
              navigate(PATH.DASHBOARD.TO);
            }, 100);
          } else {
            console.error('No stored login result data available after MFA setup');
            // Fallback to page reload if login result data is not available
            window.location.href = PATH.DASHBOARD.TO;
          }
        } catch (userDataError) {
          console.error('Error fetching user data after MFA setup:', userDataError);
          // Fallback to page reload if user data fetch fails
          window.location.href = PATH.DASHBOARD.TO;
        }
      } else {
        setIsLoading(false);
        throw new Error(result.error || 'Failed to complete MFA setup');
      }
    } catch (error) {
      setIsLoading(false);
      throw new Error(error.message || 'Failed to complete MFA setup');
    }
  };

  // Complete MFA verification for TOTP
  const handleCompleteMfaVerification = async (totpCode) => {
    try {
      const result = await completeMfaSignIn(
        mfaData.mfaPendingCredential,
        mfaData.mfaEnrollmentId,
        totpCode
      );

      if (result.ok) {
        // Store new tokens
        setCookie(TOKENS.ACCESS, result.data.idToken);
        setCookie(TOKENS.REFRESH, result.data.refreshToken);

        setShowMfaVerification(false);
        setIsLoading(false);

        // Use the stored login result data with permissions instead of fetching user data
        try {
          // Use the stored login result data, but also check if it's available
          const loginResult = mfaData?.loginResult;

          if (loginResult && loginResult.permissions) {
            // Dispatch USER_LOGIN action with the permissions from login result
            dispatch({
              type: 'USER_LOGIN',
              payload: {
                id: loginResult.user_id,
                user_id: loginResult.user_id,
                permissions: loginResult.permissions,
                user_type: loginResult.user_type,
                client_id: loginResult.client_id,
                agency_id: loginResult.agency_id
              }
            });

            // Fetch profile data based on user type from stored login result
            if (loginResult.user_type === 'client' && loginResult.client_id) {
              // CLIENT_ADMIN
              dispatch(getClientData(loginResult.client_id));
            } else if (loginResult.user_type === 'agency' && loginResult.agency_id) {
              // AGENCY_ADMIN
              dispatch(getAgencyData(loginResult.agency_id));
            }

            // Wait a bit for Redux state to update before navigating
            setTimeout(() => {
              navigate(PATH.DASHBOARD.TO);
            }, 100);
          } else {
            console.error('No stored login result data available after MFA verification');
            // Fallback to page reload if login result data is not available
            window.location.href = PATH.DASHBOARD.TO;
          }
        } catch (userDataError) {
          console.error('Error fetching user data after MFA:', userDataError);
          // Fallback to page reload if user data fetch fails
          window.location.href = PATH.DASHBOARD.TO;
        }
      } else {
        setIsLoading(false);
        throw new Error(result.error || 'Invalid verification code');
      }
    } catch (error) {
      setIsLoading(false);
      throw new Error(error.message || 'Failed to verify MFA code');
    }
  };

  return (
    <Box bg="main.blueBackground">
      {/* {isPageLoader ? <Loader /> : null} */}
      <LoginBox
        leftButtonText={constants.LOGIN}
        leftButtonClick={handleClick}
        rightButtonText={constants.FORGOT_PASSWORD}
        rightLinkTo="/forgot-password"
        type="LOGIN"
        email={email}
        setEmail={setEmail}
        password={password}
        setPassword={setPassword}
        validations={validations}
        refresh={refresh}
        isLoading={isLoading}
      />

      {/* MFA Setup Modal */}
      <MfaSetupModal
        isOpen={showMfaSetup}
        onClose={() => setShowMfaSetup(false)}
        onComplete={handleCompleteMfaSetup}
        qrCodeUrl={mfaData.qrCodeUrl}
        secretKey={mfaData.secretKey}
        sessionInfo={mfaData.sessionInfo}
        isLoading={isLoading}
      />

      {/* MFA Verification Modal */}
      <MfaVerificationModal
        isOpen={showMfaVerification}
        onClose={() => setShowMfaVerification(false)}
        onComplete={handleCompleteMfaVerification}
        isLoading={isLoading}
      />
    </Box>
  );
};

LoginPage.propTypes = {};

export default LoginPage;
