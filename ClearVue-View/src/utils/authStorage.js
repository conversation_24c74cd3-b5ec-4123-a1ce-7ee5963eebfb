/**
 * Authentication Storage Utilities
 * Handles token storage with support for both Firebase and Legacy authentication
 */

import { TOKENS } from '../constants';
import { deleteCookie } from './cookie';

/**
 * Set authentication token in localStorage
 * @param {string} key - Token key (ACCESS, REFRESH, AUTH_TYPE)
 * @param {string} value - Token value
 */
export const setAuthToken = (key, value) => {
  try {
    if (value) {
      localStorage.setItem(key, value);
    }
  } catch (error) {
    console.error('Error setting auth token:', error);
  }
};

/**
 * Get authentication token from localStorage
 * @param {string} key - Token key (ACCESS, REFRESH, AUTH_TYPE)
 * @returns {string|null} Token value or null if not found
 */
export const getAuthToken = (key) => {
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Remove authentication token from localStorage
 * @param {string} key - Token key (ACCESS, REFRESH, AUTH_TYPE)
 */
export const removeAuthToken = (key) => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing auth token:', error);
  }
};

/**
 * Clear all authentication tokens
 */
export const clearAllAuthTokens = () => {
  try {
    localStorage.removeItem(TOKENS.ACCESS);
    localStorage.removeItem(TOKENS.REFRESH);
    localStorage.removeItem(TOKENS.AUTH_TYPE);
  } catch (error) {
    console.error('Error clearing auth tokens:', error);
  }
};

/**
 * Store complete authentication data from login response
 * @param {Object} authData - Authentication data from login response
 * @param {string} authData.access_token - Access token
 * @param {string} authData.refresh_token - Refresh token
 * @param {string} authData.auth_type - Authentication type ('firebase' or 'legacy')
 */
export const storeAuthData = (authData) => {
  try {
    if (authData.access_token) {
      setAuthToken(TOKENS.ACCESS, authData.access_token);
    }
    if (authData.refresh_token) {
      setAuthToken(TOKENS.REFRESH, authData.refresh_token);
    }
    if (authData.auth_type) {
      setAuthToken(TOKENS.AUTH_TYPE, authData.auth_type);
    }
  } catch (error) {
    console.error('Error storing auth data:', error);
  }
};

/**
 * Get current authentication type
 * @returns {string|null} 'firebase', 'legacy', or null
 */
export const getAuthType = () => {
  return getAuthToken(TOKENS.AUTH_TYPE);
};

/**
 * Check if user is authenticated (has valid access token)
 * @returns {boolean} True if authenticated
 */
export const isAuthenticated = () => {
  const accessToken = getAuthToken(TOKENS.ACCESS);
  return !!accessToken;
};

/**
 * Get current access token
 * @returns {string|null} Access token or null
 */
export const getAccessToken = () => {
  return getAuthToken(TOKENS.ACCESS);
};

/**
 * Get current refresh token
 * @returns {string|null} Refresh token or null
 */
export const getRefreshToken = () => {
  return getAuthToken(TOKENS.REFRESH);
};

/**
 * Update tokens after refresh (maintains auth_type)
 * @param {Object} refreshData - Token refresh response
 * @param {string} refreshData.access_token - New access token
 * @param {string} refreshData.refresh_token - New refresh token (optional for legacy)
 * @param {string} refreshData.auth_type - Authentication type
 */
export const updateTokensAfterRefresh = (refreshData) => {
  try {
    if (refreshData.access_token) {
      setAuthToken(TOKENS.ACCESS, refreshData.access_token);
    }
    if (refreshData.refresh_token) {
      setAuthToken(TOKENS.REFRESH, refreshData.refresh_token);
    }
    if (refreshData.auth_type) {
      setAuthToken(TOKENS.AUTH_TYPE, refreshData.auth_type);
    }
  } catch (error) {
    console.error('Error updating tokens after refresh:', error);
  }
};

/**
 * Comprehensive logout function that clears all user data
 * @param {boolean} showMessage - Whether to show a logout message
 * @param {string} message - Custom message to display
 */
export const performCompleteLogout = (
  showMessage = false,
  message = 'Session expired. Please log in again.'
) => {
  try {
    // 1. Clear all localStorage tokens and data
    clearAllAuthTokens();

    // 2. Clear additional localStorage items including Redux Persist
    try {
      localStorage.removeItem('user_data');
      localStorage.removeItem('permissions');
      localStorage.removeItem('menu');
      localStorage.removeItem('profile_data');
      localStorage.removeItem('client_data');
      localStorage.removeItem('agency_data');

      // Clear Redux Persist storage (critical for preventing old user data)
      localStorage.removeItem('persist:root');

      // Clear any other potential Redux Persist keys
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith('persist:')) {
          localStorage.removeItem(key);
        }
      });
    } catch (localStorageError) {
      console.error('Error clearing additional localStorage items:', localStorageError);
    }

    // 3. Clear all cookies
    try {
      deleteCookie(TOKENS.ACCESS);
      deleteCookie(TOKENS.REFRESH);
      deleteCookie(TOKENS.AUTH_TYPE);
    } catch (cookieError) {
      console.error('Error clearing cookies:', cookieError);
    }

    // 4. Clear sessionStorage (except for logout message if needed)
    try {
      if (showMessage) {
        sessionStorage.setItem('force_relogin_message', message);
      }
      // Clear other sessionStorage items but preserve the message
      const messageToPreserve = sessionStorage.getItem('force_relogin_message');
      sessionStorage.clear();
      if (messageToPreserve && showMessage) {
        sessionStorage.setItem('force_relogin_message', messageToPreserve);
      }
    } catch (sessionStorageError) {
      console.error('Error clearing sessionStorage:', sessionStorageError);
    }
  } catch (error) {
    console.error('Error during complete logout:', error);
  }
};

/**
 * Handle force re-login scenario
 * Clears all tokens and redirects to login
 * @param {string} message - Optional custom message to display
 */
export const handleForceRelogin = (message = 'Session expired. Please log in again.') => {
  try {
    // Perform complete logout with message
    performCompleteLogout(true, message);

    // Redirect to login page
    window.location.href = '/';
  } catch (error) {
    console.error('Error handling force relogin:', error);
  }
};
