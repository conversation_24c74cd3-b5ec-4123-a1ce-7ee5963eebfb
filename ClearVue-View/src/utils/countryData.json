[{"name": "Afghanistan", "label": "+93", "value": "+93", "min": "9"}, {"name": "Albania", "label": "+355", "value": "+355", "min": "3", "max": "9"}, {"name": "Algeria", "label": "+213", "value": "+213", "min": "8"}, {"name": "American Samoa", "label": "+1 (684)", "value": "+1 (684)", "min": "7"}, {"name": "Andorra", "label": "+376", "value": "+376", "min": "6,", "max": "9"}, {"name": "Angola", "label": "+244", "value": "+244", "min": "9"}, {"name": "<PERSON><PERSON><PERSON>", "label": "+1 (264)", "value": "+1 (264)", "min": "7"}, {"name": "Antigua and Barbuda", "label": "+1 (268)", "value": "+1 (268)", "min": "7"}, {"name": "Argentina", "label": "+54", "value": "+54", "min": "10"}, {"name": "Armenia", "label": "+374", "value": "+374", "min": "8"}, {"name": "Aruba", "label": "+297", "value": "+297", "min": "7"}, {"name": "Ascension", "label": "+247", "value": "+247", "min": "4"}, {"name": "Australia", "label": "+61", "value": "+61", "min": "4", "max": "15"}, {"name": "Australian Capital Territory", "label": "+672", "value": "+672", "min": "6"}, {"name": "Austria", "label": "+43", "value": "+43", "min": "4", "max": "13"}, {"name": "Azerbaijan", "label": "+994", "value": "+994", "min": "8", "max": "9"}, {"name": "Bahamas", "label": "+1 (242)", "value": "+1 (242)", "min": "7"}, {"name": "Bahrain", "label": "+973", "value": "+973", "min": "8"}, {"name": "Bangladesh", "label": "+880", "value": "+880", "min": "6", "max": "10"}, {"name": "Barbados", "label": "+1 (246)", "value": "+1 (246)", "min": "7"}, {"name": "Belarus", "label": "+375", "value": "+375", "min": "9", "max": "10"}, {"name": "Belgium", "label": "+32", "value": "+32", "min": "8", "max": "9"}, {"name": "Belize", "label": "+501", "value": "+501", "min": "7"}, {"name": "Benin", "label": "+229", "value": "+229", "min": "8"}, {"name": "Bermuda", "label": "+1 (441)", "value": "+1 (441)", "min": "7"}, {"name": "Bhutan", "label": "+975", "value": "+975", "min": "7", "max": "8"}, {"name": "Bolivia", "label": "+591", "value": "+591", "min": "8"}, {"name": "Bosnia and Herzegovina", "label": "+387", "value": "+387", "min": "8"}, {"name": "Botswana", "label": "+267", "value": "+267", "min": "7", "max": "8"}, {"name": "Brazil", "label": "+55", "value": "+55", "min": "10"}, {"name": "British Virgin Islands", "label": "+1 (284)", "value": "+1 (284)", "min": "7"}, {"name": "Brunei", "label": "+673", "value": "+673", "min": "7"}, {"name": "Bulgaria", "label": "+359", "value": "+359", "min": "7", "max": "9"}, {"name": "Burkina Faso", "label": "+226", "value": "+226", "min": "8"}, {"name": "Burma", "label": "+95", "value": "+95", "min": "7", "max": "9"}, {"name": "Burundi", "label": "+257", "value": "+257", "min": "8"}, {"name": "Cambodia", "label": "+855", "value": "+855", "min": "8"}, {"name": "Cameroon", "label": "+237", "value": "+237", "min": "8"}, {"name": "Canada", "label": "+1", "value": "+1", "min": "10"}, {"name": "Cape Verde", "label": "+238", "value": "+238", "min": "7"}, {"name": "Cayman Islands", "label": "+1 (345)", "value": "+1 (345)", "min": "7"}, {"name": "Central African Republic", "label": "+236", "value": "+236", "min": "8"}, {"name": "Chad", "label": "+235", "value": "+235", "min": "6"}, {"name": "Chile", "label": "+56", "value": "+56", "min": "8", "max": "9"}, {"name": "China", "label": "+86", "value": "+86", "min": "5", "max": "12"}, {"name": "Colombia", "label": "+57", "value": "+57", "min": "8"}, {"name": "Comoros", "label": "+269", "value": "+269", "min": "7"}, {"name": "Cook Islands", "label": "+682", "value": "+682", "min": "5"}, {"name": "Costa Rica", "label": "+506", "value": "+506", "min": "8"}, {"name": "Côte d’Ivoire", "label": "+225", "value": "+225", "min": "8"}, {"name": "Croatia", "label": "+385", "value": "+385", "min": "8", "max": "12"}, {"name": "Cuba", "label": "+53", "value": "+53", "min": "6", "max": "8"}, {"name": "Cyprus", "label": "+357", "value": "+357", "min": "8,"}, {"name": "Czech Republic", "label": "+420", "value": "+420", "min": "4", "max": "12"}, {"name": "Democratic Republic of the Congo", "label": "+243", "value": "+243", "min": "5", "max": "9"}, {"name": "Denmark", "label": "+45", "value": "+45", "min": "8"}, {"name": "<PERSON>", "label": "+246", "value": "+246", "min": "7"}, {"name": "Djibouti", "label": "+253", "value": "+253", "min": "6"}, {"name": "Dominica", "label": "+1 (767)", "value": "+1 (767)", "min": "7"}, {"name": "Dominican Republic", "label": "+1 (809/829)", "value": "+1 (809/829)", "min": "7"}, {"name": "Ecuador", "label": "+593", "value": "+593", "min": "8"}, {"name": "Egypt", "label": "+20", "value": "+20", "min": "7", "max": "9"}, {"name": "El Salvador", "label": "+503", "value": "+503", "min": "7,", "max": "11"}, {"name": "Equatorial Guinea", "label": "+240", "value": "+240", "min": "6"}, {"name": "Eritrea", "label": "+291", "value": "+291", "min": "7"}, {"name": "Estonia", "label": "+372", "value": "+372", "min": "7", "max": "10"}, {"name": "Ethiopia", "label": "+251", "value": "+251", "min": "9"}, {"name": "Falkland Islands (Islas Malvinas)", "label": "+500", "value": "+500", "min": "5"}, {"name": "Faroe Islands", "label": "+298", "value": "+298", "min": "6"}, {"name": "Fiji", "label": "+679", "value": "+679", "min": "7"}, {"name": "Finland", "label": "+358", "value": "+358", "min": "5", "max": "12"}, {"name": "France", "label": "+33", "value": "+33", "min": "9"}, {"name": "French Departments and Territories in the Indian Ocean", "label": "+262", "value": "+262", "min": "9"}, {"name": "French Guiana", "label": "+594", "value": "+594", "min": "9"}, {"name": "French Polynesia", "label": "+689", "value": "+689", "min": "6"}, {"name": "Gabon", "label": "+241", "value": "+241", "min": "6,"}, {"name": "Gambia", "label": "+220", "value": "+220", "min": "7"}, {"name": "Georgia", "label": "+995", "value": "+995", "min": "8"}, {"name": "Germany", "label": "+49", "value": "+49", "min": "6", "max": "13"}, {"name": "Ghana", "label": "+233", "value": "+233", "min": "5", "max": "9"}, {"name": "Gibraltar", "label": "+350", "value": "+350", "min": "8"}, {"name": "Greece", "label": "+30", "value": "+30", "min": "10"}, {"name": "Greenland", "label": "+299", "value": "+299", "min": "6"}, {"name": "Grenada", "label": "+1 (473)", "value": "+1 (473)", "min": "7"}, {"name": "Guadeloupe", "label": "+590", "value": "+590", "min": "9"}, {"name": "Guam", "label": "+1 (671)", "value": "+1 (671)", "min": "7"}, {"name": "Guatemala", "label": "+502", "value": "+502", "min": "8"}, {"name": "Guinea", "label": "+224", "value": "+224", "min": "8"}, {"name": "Guinea-Bissau", "label": "+245", "value": "+245", "min": "7"}, {"name": "Guyana", "label": "+592", "value": "+592", "min": "7"}, {"name": "Haiti", "label": "+509", "value": "+509", "min": "8"}, {"name": "Holy See (Vatican City)", "label": "+379", "value": "+379", "min": ""}, {"name": "Honduras", "label": "+504", "value": "+504", "min": "7"}, {"name": "Hong Kong", "label": "+852", "value": "+852", "min": "4,", "max": "to"}, {"name": "Hungary", "label": "+36", "value": "+36", "min": "8", "max": "9"}, {"name": "Iceland", "label": "+354", "value": "+354", "min": "7"}, {"name": "India", "label": "+91", "value": "+91", "min": "7", "max": "10"}, {"name": "Indonesia", "label": "+62", "value": "+62", "min": "5", "max": "10"}, {"name": "Iran", "label": "+98", "value": "+98", "min": "6", "max": "10"}, {"name": "Iraq", "label": "+964", "value": "+964", "min": "8", "max": "10"}, {"name": "Ireland", "label": "+353", "value": "+353", "min": "7", "max": "11"}, {"name": "Israel", "label": "+972", "value": "+972", "min": "8", "max": "9"}, {"name": "Italy", "label": "+39", "value": "+39", "min": "up", "max": "11"}, {"name": "Jamaica", "label": "+1 (876)", "value": "+1 (876)", "min": "7"}, {"name": "Japan", "label": "+81", "value": "+81", "min": "9", "max": "10"}, {"name": "Jordan", "label": "+962", "value": "+962", "min": "5", "max": "9"}, {"name": "Kazakhstan", "label": "+7", "value": "+7", "min": "10"}, {"name": "Kenya", "label": "+254", "value": "+254", "min": "6", "max": "10"}, {"name": "Kiribati", "label": "+686", "value": "+686", "min": "5"}, {"name": "Korea, North", "label": "+850", "value": "+850", "min": "3", "max": "10"}, {"name": "Korea, South", "label": "+82", "value": "+82", "min": "8", "max": "11"}, {"name": "Kuwait", "label": "+965", "value": "+965", "min": "7"}, {"name": "Kyrgyzstan", "label": "+996", "value": "+996", "min": "9"}, {"name": "Laos", "label": "+856", "value": "+856", "min": "8", "max": "9"}, {"name": "Latvia", "label": "+371", "value": "+371", "min": "7"}, {"name": "Lebanon", "label": "+961", "value": "+961", "min": "7", "max": "8"}, {"name": "Lesotho", "label": "+266", "value": "+266", "min": "8"}, {"name": "Liberia", "label": "+231", "value": "+231", "min": "7", "max": "8"}, {"name": "Libya", "label": "+218", "value": "+218", "min": "8", "max": "9"}, {"name": "Liechtenstein", "label": "+423", "value": "+423", "min": "7", "max": "9"}, {"name": "Lithuania", "label": "+370", "value": "+370", "min": "8"}, {"name": "Luxembourg", "label": "+352", "value": "+352", "min": "4", "max": "11"}, {"name": "Macau", "label": "+853", "value": "+853", "min": "7", "max": "8"}, {"name": "Macedonia [FYROM]", "label": "+389", "value": "+389", "min": "8"}, {"name": "Madagascar", "label": "+261", "value": "+261", "min": "9", "max": "10"}, {"name": "Malawi", "label": "+265", "value": "+265", "min": "7"}, {"name": "Malaysia", "label": "+60", "value": "+60", "min": "7", "max": "9"}, {"name": "Maldives", "label": "+960", "value": "+960", "min": "7"}, {"name": "Mali", "label": "+223", "value": "+223", "min": "8"}, {"name": "Malta", "label": "+356", "value": "+356", "min": "8"}, {"name": "Marshall Islands", "label": "+692", "value": "+692", "min": "7"}, {"name": "Martinique", "label": "+596", "value": "+596", "min": "9"}, {"name": "Mauritania", "label": "+222", "value": "+222", "min": "7"}, {"name": "Mauritius", "label": "+230", "value": "+230", "min": "7"}, {"name": "Mayotte", "label": "+269", "value": "+269", "min": "…"}, {"name": "Mexico", "label": "+52", "value": "+52", "min": "10"}, {"name": "Moldova", "label": "+373", "value": "+373", "min": "8"}, {"name": "Monaco", "label": "+377", "value": "+377", "min": "5", "max": "9"}, {"name": "Mongolia", "label": "+976", "value": "+976", "min": "7", "max": "8"}, {"name": "Montenegro", "label": "+382", "value": "+382", "min": "4", "max": "12"}, {"name": "Montserrat", "label": "+1 (664)", "value": "+1 (664)", "min": "7"}, {"name": "Morocco", "label": "+212", "value": "+212", "min": "9"}, {"name": "Mozambique", "label": "+258", "value": "+258", "min": "8", "max": "9"}, {"name": "Namibia", "label": "+264", "value": "+264", "min": "6", "max": "10"}, {"name": "Nauru", "label": "+674", "value": "+674", "min": "4"}, {"name": "Nepal", "label": "+977", "value": "+977", "min": "8", "max": "9"}, {"name": "Netherlands", "label": "+31", "value": "+31", "min": "9"}, {"name": "Netherlands Antilles", "label": "+599", "value": "+599", "min": "6", "max": "8"}, {"name": "New Caledonia", "label": "+687", "value": "+687", "min": "6"}, {"name": "New Zealand", "label": "+64", "value": "+64", "min": "3", "max": "10"}, {"name": "Nicaragua", "label": "+505", "value": "+505", "min": "8"}, {"name": "Niger", "label": "+227", "value": "+227", "min": "8"}, {"name": "Nigeria", "label": "+234", "value": "+234", "min": "7", "max": "10"}, {"name": "Niue", "label": "+683", "value": "+683", "min": "4"}, {"name": "Northern Territory", "label": "+1 (670)", "value": "+1 (670)", "min": "7"}, {"name": "Norway", "label": "+47", "value": "+47", "min": "5"}, {"name": "Oman", "label": "+968", "value": "+968", "min": "7", "max": "8"}, {"name": "Pakistan", "label": "+92", "value": "+92", "min": "8", "max": "11"}, {"name": "<PERSON><PERSON>", "label": "+680", "value": "+680", "min": "7"}, {"name": "Panama", "label": "+507", "value": "+507", "min": "7"}, {"name": "Papua New Guinea", "label": "+675", "value": "+675", "min": "4", "max": "11"}, {"name": "Paraguay", "label": "+595", "value": "+595", "min": "5", "max": "9"}, {"name": "Peru", "label": "+51", "value": "+51", "min": "8", "max": "11"}, {"name": "Philippines", "label": "+63", "value": "+63", "min": "8", "max": "10"}, {"name": "Poland", "label": "+48", "value": "+48", "min": "6", "max": "9"}, {"name": "Portugal", "label": "+351", "value": "+351", "min": "9"}, {"name": "Puerto Rico", "label": "+1 (787/939)", "value": "+1 (787/939)", "min": "7"}, {"name": "Qatar", "label": "+974", "value": "+974", "min": "6,", "max": "10"}, {"name": "Republic of the Congo", "label": "+242", "value": "+242", "min": "6", "max": "7"}, {"name": "Reserved", "label": "+970", "value": "+970", "min": ""}, {"name": "Romania", "label": "+40", "value": "+40", "min": "9"}, {"name": "Russia", "label": "+7", "value": "+7", "min": "10"}, {"name": "Rwanda", "label": "+250", "value": "+250", "min": "9"}, {"name": "Saint Helena", "label": "+290", "value": "+290", "min": "4"}, {"name": "Saint Kitts and Nevis", "label": "+1 (869)", "value": "+1 (869)", "min": "7"}, {"name": "Saint Lucia", "label": "+1 (758)", "value": "+1 (758)", "min": "7"}, {"name": "Saint Pierre and Miquelon", "label": "+508", "value": "+508", "min": "6"}, {"name": "Saint Vincent and the Grenadines", "label": "+1 (784)", "value": "+1 (784)", "min": "7"}, {"name": "Samoa", "label": "+685", "value": "+685", "min": "3", "max": "7"}, {"name": "San Marino", "label": "+378", "value": "+378", "min": "6", "max": "10"}, {"name": "Sao Tome and Principe", "label": "+239", "value": "+239", "min": "7"}, {"name": "Saudi Arabia", "label": "+966", "value": "+966", "min": "8", "max": "9"}, {"name": "Senegal", "label": "+221", "value": "+221", "min": "9"}, {"name": "Serbia", "label": "+381", "value": "+381", "min": "4", "max": "12"}, {"name": "Seychelles", "label": "+248", "value": "+248", "min": "6"}, {"name": "Sierra Leone", "label": "+232", "value": "+232", "min": "8"}, {"name": "Singapore", "label": "+65", "value": "+65", "min": "8", "max": "12"}, {"name": "Slovakia", "label": "+421", "value": "+421", "min": "4", "max": "9"}, {"name": "Slovenia", "label": "+386", "value": "+386", "min": "8"}, {"name": "Solomon Islands", "label": "+677", "value": "+677", "min": "5"}, {"name": "Somalia", "label": "+252", "value": "+252", "min": "5", "max": "8"}, {"name": "South Africa", "label": "+27", "value": "+27", "min": "9"}, {"name": "Spain", "label": "+34", "value": "+34", "min": "9"}, {"name": "Sri Lanka", "label": "+94", "value": "+94", "min": "9"}, {"name": "Sudan", "label": "+249", "value": "+249", "min": "9"}, {"name": "Suriname", "label": "+597", "value": "+597", "min": "6", "max": "7"}, {"name": "Swaziland", "label": "+268", "value": "+268", "min": "7", "max": "8"}, {"name": "Sweden", "label": "+46", "value": "+46", "min": "7", "max": "13"}, {"name": "Switzerland", "label": "+41", "value": "+41", "min": "4", "max": "12"}, {"name": "Syria", "label": "+963", "value": "+963", "min": "8", "max": "10"}, {"name": "Taiwan", "label": "+886", "value": "+886", "min": "8", "max": "9"}, {"name": "Tajikistan", "label": "+992", "value": "+992", "min": "9"}, {"name": "Tanzania", "label": "+255", "value": "+255", "min": "9"}, {"name": "Thailand", "label": "+66", "value": "+66", "min": "8"}, {"name": "Timor-Leste", "label": "+670", "value": "+670", "min": "7"}, {"name": "Togo", "label": "+228", "value": "+228", "min": "7"}, {"name": "Tokelau", "label": "+690", "value": "+690", "min": "4"}, {"name": "Tonga", "label": "+676", "value": "+676", "min": "5"}, {"name": "Trinidad and Tobago", "label": "+1 (868)", "value": "+1 (868)", "min": "7"}, {"name": "<PERSON>", "label": "+290", "value": "+290", "min": "4"}, {"name": "Tunisia", "label": "+216", "value": "+216", "min": "8"}, {"name": "Turkey", "label": "+90", "value": "+90", "min": "10"}, {"name": "Turkmenistan", "label": "+993", "value": "+993", "min": "8"}, {"name": "Turks and Caicos Islands", "label": "+1 (649)", "value": "+1 (649)", "min": "7"}, {"name": "Tuvalu", "label": "+688", "value": "+688", "min": "5"}, {"name": "Uganda", "label": "+256", "value": "+256", "min": "9"}, {"name": "Ukraine", "label": "+380", "value": "+380", "min": "9"}, {"name": "United Arab Emirates", "label": "+971", "value": "+971", "min": "8", "max": "9"}, {"name": "United Kingdom", "label": "+44", "value": "+44", "min": "7", "max": "10"}, {"name": "United States", "label": "+1", "value": "+1", "min": "10"}, {"name": "Uruguay", "label": "+598", "value": "+598", "min": "4", "max": "11"}, {"name": "Uzbekistan", "label": "+998", "value": "+998", "min": "9"}, {"name": "Vanuatu", "label": "+678", "value": "+678", "min": "5"}, {"name": "Venezuela", "label": "+58", "value": "+58", "min": "10"}, {"name": "Vietnam", "label": "+84", "value": "+84", "min": "7", "max": "10"}, {"name": "Virgin Islands", "label": "+1 (340)", "value": "+1 (340)", "min": "7"}, {"name": "Wallis and Futuna", "label": "+681", "value": "+681", "min": "6"}, {"name": "Yemen", "label": "+967", "value": "+967", "min": "6", "max": "9"}, {"name": "Zambia", "label": "+260", "value": "+260", "min": "9"}, {"name": "Zimbabwe", "label": "+263", "value": "+263", "min": "5", "max": "9"}]