import React from 'react';

const AssociationIcon = function () {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      fill="none"
      version="1"
      viewBox="0 0 225 225">
      <path
        fill="currentColor"
        d="M309 2166c-91-35-175-112-212-195-31-71-35-190-7-263 28-76 104-159 178-195 50-25 68-28 153-28 88 0 101 3 158 32l62 32 60-59c32-32 59-62 59-65 0-4-13-25-28-48-30-44-65-123-76-171-4-16-7-65-7-110 0-95 23-171 78-260l36-58-50-50-50-50-44 31c-62 45-137 65-224 58-129-10-230-78-287-195-30-60-33-75-32-152 0-69 5-95 26-140 38-81 89-134 166-172 60-30 76-33 152-33 72 0 94 4 147 29 73 33 144 103 176 174 18 38 22 66 22 142 0 83-3 102-27 150l-28 55 53 52 52 52 55-37c165-111 398-105 559 16l33 25 43-47 43-48-32-60c-28-56-31-69-31-157 1-84 4-103 28-152 33-69 99-132 175-167 48-23 71-27 142-27s94 4 140 26c82 39 135 90 172 167 30 59 33 76 33 152 0 73-4 94-29 147-54 117-160 191-287 200-82 7-153-9-215-49l-47-30-45 45-45 45 26 34c43 56 77 141 89 226 10 69 9 92-5 158-17 81-54 163-90 202l-21 23 59 59 60 60 55-28c48-24 67-27 150-27 113 0 163 19 238 89 178 167 137 455-82 569-56 29-69 32-156 32-85 0-101-3-153-29-69-35-140-109-169-174-26-59-34-164-18-232 7-28 28-74 47-102l34-51-53-54c-29-29-55-53-59-53s-30 16-59 35c-82 55-172 78-288 73-87-3-105-7-172-40-41-20-87-45-103-55l-27-18-61 61-61 61 30 46c42 66 55 124 50 211-8 129-80 236-197 294-46 22-71 27-141 29-53 2-98-3-121-11zm204-72c110-37 187-147 187-265 0-75-13-114-62-179l-33-45-5 82c-6 89-26 125-75 137-23 6-23 7-8 36 48 98-90 189-168 111-31-31-39-83-19-121 9-16 6-21-15-26-48-12-65-48-65-138l-1-81-40 44c-49 55-74 132-66 208 19 180 199 296 370 237zm1406-1c185-59 252-308 121-446l-39-42-1 78c0 87-13 118-60 137-24 10-28 15-20 30 36 66-14 150-89 150-78 0-125-71-98-147 7-19 4-25-15-32-47-18-62-47-68-134l-5-82-33 45c-49 65-62 104-62 179 0 194 183 324 369 264zM490 1725c0-24-5-35-15-35-12 0-15-13-15-55v-55h-70v51c0 42-3 52-20 56-15 4-20 14-20 39v34h140v-35zm1410 1c0-25-5-35-20-39-17-4-20-14-20-56v-51h-70v55c0 42-3 55-15 55-10 0-15 11-15 35v35h140v-34zm-652-220c153-41 289-200 308-359l7-57h-193V927c0-154-5-187-30-187-4 0-7 98-7 218-1 119-2 245-2 280l-1 62H950v-280c0-154-4-280-8-280-27 0-32 30-32 187v163H720v40c0 22 9 68 21 102 71 212 291 331 507 274zm17-346v-35l-122-3-123-3v82l123-3 122-3v-35zM880 985v-35H770v70h110v-35zm390 0v-35h-250v70h250v-35zm240 0v-35h-49c-44 0-49 3-55 25-10 38 0 45 55 45h49v-35zm-320-192c15-20 20-38 18-67l-3-41-67-3-68-3v51c0 37 5 55 20 70 29 29 74 26 100-7zM545 674c59-27 114-86 140-147 8-21 15-66 15-104 0-77-29-151-74-192l-26-24v67c0 80-17 118-60 136-20 8-29 18-25 27 25 66 5 126-51 149-47 20-87 11-119-28-27-32-33-84-15-119 9-15 7-19-5-19-9 0-30-13-46-29-27-27-29-35-29-105 0-42-4-76-8-76-31 0-92 112-99 185-22 214 205 373 402 279zm1400 2c142-66 204-240 135-377-23-46-58-89-72-89-4 0-8 34-8 76 0 70-2 78-29 105-16 16-36 29-45 29-13 0-14 5-6 26 23 60 4 115-48 140-29 14-41 14-73 5-61-18-89-82-66-147 6-18 2-24-24-34-41-17-59-61-59-141v-62l-26 24c-70 64-96 207-55 303 24 56 83 115 141 143 64 31 168 31 235-1zM490 315c0-24-5-35-14-35-11 0-16-15-18-52-3-51-4-52-35-53l-33-1v53c0 46-3 53-20 53-16 0-20 7-20 35v35h140v-35zm1410 0c0-28-4-35-20-35-18 0-20-7-20-53v-54l-32 1c-33 1-33 2-36 54-2 37-7 52-18 52-9 0-14 11-14 35v35h140v-35z"
        transform="matrix(.1 0 0 -.1 0 225)"
      />
    </svg>
  );
};

export default AssociationIcon;
