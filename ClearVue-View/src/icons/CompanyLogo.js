/* eslint-disable react/forbid-prop-types */
import PropTypes from 'prop-types';
import * as React from 'react';

const CompanyLogo = function ({ style }) {
  return (
    <svg
      width={173}
      height={76}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      style={style}>
      <path fill="url(#a)" d="M0 0h173v76H0z" />
      <defs>
        <pattern id="a" patternContentUnits="objectBoundingBox" width={1} height={1}>
          <use xlinkHref="#b" transform="matrix(.00483 0 0 .01099 -.019 0)" />
        </pattern>
        <image
          id="b"
          width={215}
          height={91}
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

CompanyLogo.propTypes = {
  style: PropTypes.object
};

CompanyLogo.defaultProps = {
  style: {}
};

export default CompanyLogo;
