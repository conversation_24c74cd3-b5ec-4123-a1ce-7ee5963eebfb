import React from 'react';

const ArrowDown = function (props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={11}
      height={11}
      viewBox="0 0 17.828 22"
      {...props}>
      <defs>
        <style>
          {
            '.cls-1{fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px}'
          }
        </style>
      </defs>
      <g id="Arrow_Down" transform="translate(1.414 1)">
        <path id="Line_1" d="M0 0v20" className="cls-1" transform="translate(7.5)" />
        <path
          id="Path_1"
          d="M19.5 14.5L12 22l-7.5-7.5"
          className="cls-1"
          transform="translate(-4.5 -2)"
        />
      </g>
    </svg>
  );
};

export default ArrowDown;
