/**
 * MFA Service for Firebase TOTP MFA operations
 */
import axiosInstance from '../config/axios.config';
import { prepareSuccessReponse, prepareErrorResponse } from '../utils/responseHandler';

/**
 * Check if MFA is required for the system
 */
export const checkMfaRequired = async () => {
  try {
    const response = await axiosInstance.get('/mfa/status');
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};

/**
 * Get user's MFA status
 */
export const getUserMfaStatus = async (email) => {
  try {
    const response = await axiosInstance.get(`/mfa/user-status?email=${email}`);
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};

/**
 * Start MFA enrollment process
 */
export const startMfaEnrollment = async (idToken) => {
  try {
    const response = await axiosInstance.post(
      '/mfa/enrollment/start',
      {
        idToken
      },
      {
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      }
    );
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};

/**
 * Complete MFA enrollment process
 */
export const completeMfaEnrollment = async (idToken, sessionInfo, totpCode, email) => {
  try {
    const response = await axiosInstance.post(
      '/mfa/enrollment/complete',
      {
        idToken,
        sessionInfo,
        totpCode,
        email
      },
      {
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      }
    );
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};

/**
 * Start MFA sign-in process
 */
export const startMfaSignIn = async (mfaPendingCredential, mfaEnrollmentId) => {
  try {
    const requestBody = {
      mfaPendingCredential
    };

    // Include mfaEnrollmentId if provided
    if (mfaEnrollmentId) {
      requestBody.mfaEnrollmentId = mfaEnrollmentId;
    }

    const response = await axiosInstance.post('/mfa/signin/start', requestBody, {
      headers: {
        Authorization: `Bearer ${mfaPendingCredential}`
      }
    });
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};

/**
 * Complete MFA sign-in process for TOTP
 */
export const completeMfaSignIn = async (mfaPendingCredential, mfaEnrollmentId, totpCode) => {
  try {
    const response = await axiosInstance.post(
      '/mfa/signin/complete',
      {
        mfaPendingCredential,
        mfaEnrollmentId,
        totpCode
      },
      {
        headers: {
          Authorization: `Bearer ${mfaPendingCredential}`
        }
      }
    );
    if (response && response.ok) {
      return prepareSuccessReponse(response);
    }
    return prepareErrorResponse(response);
  } catch (error) {
    return prepareErrorResponse(error);
  }
};
