/* eslint-disable no-case-declarations */
/* eslint-disable default-param-last */
const initialState = {
  userData: {},
  permissions: {},
  user_details: {},
  profile_details: {},
  menu: []
};

const authentication = (state = initialState, action) => {
  switch (action.type) {
    case 'USER_LOGIN':
      const { payload } = action;
      const { permissions } = payload;

      // Safely map permissions to menu array
      let menu = [];
      if (permissions && Array.isArray(permissions)) {
        menu = permissions
          .map((d) => {
            return d.feature_code;
          })
          .filter((code) => {
            return code && typeof code === 'string';
          }); // Filter out undefined/null values
      }

      // Create a clean payload without permissions for userData
      const cleanPayload = { ...payload };
      delete cleanPayload.permissions;

      return {
        ...state,
        userData: cleanPayload,
        permissions,
        menu
      };
    case 'USER_DETAILS':
      return { ...state, user_details: action.payload.user_details };
    case 'USER_LOGOUT':
      return initialState;
    case 'PROFILE_DETAIL':
      let data = {};
      if (action.payload.client_details) {
        data = action.payload.client_details;
      } else if (action.payload.agency) {
        data = action.payload.agency;
      }
      return { ...state, profile_details: { ...data } };
    default:
      return state;
  }
};

export default authentication;
