/* eslint-disable no-promise-executor-return */
/* eslint-disable no-unreachable */
/* eslint-disable no-underscore-dangle */
import axios from 'axios';
import { TOKENS } from '../constants';
import { getCookie, setCookie } from '../utils/cookie';
import {
  getAccessToken,
  getRefreshToken,
  updateTokensAfterRefresh,
  performCompleteLogout
} from '../utils/authStorage';

// Flag to prevent multiple simultaneous force relogin attempts
let isForceReloginInProgress = false;

// Enhanced force relogin handler that immediately stops all requests
const handleForceReloginImmediate = (message) => {
  if (isForceReloginInProgress) {
    return;
  }

  isForceReloginInProgress = true;

  // Perform complete logout
  performCompleteLogout(false);

  // Store message in sessionStorage to show after redirect
  sessionStorage.setItem(
    'force_relogin_message',
    message || 'Session expired. Please log in again.'
  );

  // Force immediate redirect - use replace to prevent back button issues
  window.location.replace('/');
};

const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  headers: {
    Authorization: getAccessToken() ? `Bearer ${getAccessToken()}` : null
  }
});

axiosInstance.interceptors.request.use((config) => {
  // Force fresh token retrieval to avoid any caching issues
  const localStorageToken = localStorage.getItem(TOKENS.ACCESS);
  const cookieToken = getCookie(TOKENS.ACCESS);
  const accessToken = localStorageToken || cookieToken;

  const con = {
    ...config,
    headers: { ...config.headers, Authorization: `Bearer ${accessToken}` }
  };
  return con;
});

// Track ongoing refresh requests to prevent multiple simultaneous refreshes
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

axiosInstance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    // If force relogin is already in progress, reject all requests immediately
    if (isForceReloginInProgress) {
      return Promise.reject(new Error('Force relogin in progress'));
    }

    const originalRequest = error.config;

    // Handle force re-login scenario
    if (error?.response?.status === 401 && error.response.data?.force_relogin) {
      // Prevent multiple simultaneous force relogin attempts
      if (isForceReloginInProgress) {
        return Promise.reject(error.response);
      }

      handleForceReloginImmediate(error.response.data?.message);
      return Promise.reject(error.response);
    }

    // Handle standard unauthorized errors with token refresh
    if (error?.response?.status === 401 && error?.response?.data?.error === 'UNAUTHORIZED') {
      const refreshToken = getRefreshToken() || getCookie(TOKENS.REFRESH);

      if (originalRequest.__isRetryRequest || !refreshToken) {
        // Already retried or no refresh token available
        handleForceReloginImmediate();
        return Promise.reject(error.response);
      }

      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest.__isRetryRequest = true;
      isRefreshing = true;

      try {
        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            refresh_token: refreshToken
          })
        });

        const data = await response.json();

        if (data.ok && data.access_token) {
          // Update tokens with new auth_type support
          updateTokensAfterRefresh({
            access_token: data.access_token,
            refresh_token: data.refresh_token,
            auth_type: data.auth_type
          });

          // Also update cookies for backward compatibility
          setCookie(TOKENS.ACCESS, data.access_token);
          if (data.refresh_token) {
            setCookie(TOKENS.REFRESH, data.refresh_token);
          }

          // Update the authorization header for the original request
          originalRequest.headers.Authorization = `Bearer ${data.access_token}`;

          // Process the queue with the new token
          processQueue(null, data.access_token);

          isRefreshing = false;

          // Retry the original request
          return axiosInstance(originalRequest);
        }

        // Token refresh failed
        handleForceReloginImmediate();

        processQueue(new Error('Token refresh failed'), null);
        isRefreshing = false;

        return Promise.reject(error.response);
      } catch (refreshError) {
        // eslint-disable-next-line no-console
        console.error('Token refresh error:', refreshError);

        // Clear tokens and redirect to login
        handleForceReloginImmediate();

        processQueue(refreshError, null);
        isRefreshing = false;

        return Promise.reject(error.response);
      }
    }

    // Return the error response data for other types of errors
    return error?.response?.data || error;
  }
);

export default axiosInstance;
