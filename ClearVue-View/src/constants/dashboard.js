export const DASHBOARD_CONSTANTS = {
  FILTERS: {
    DATE_RANGE: 'DATE_RANGE',
    DASHBOARD: 'DASHBOARD',
    REGION: 'REGION',
    SITE: 'SITE',
    SHIFT: 'SHIFT',
    DEPARTMENT: 'DEPARTMENT'
  },
  CHARTS: {
    NEW_STARTER_PERFORMANCE: 'NEW_STARTER_PERFORMANCE',
    PERFORMANCE_BY_TENURE: 'PERFORMANCE_BY_TENURE'
  },
  TENURE_PERIODS: {
    SIX_MONTHS: '6m',
    ONE_YEAR: '1 Yr',
    TWO_YEARS: '2 Yr',
    THREE_YEARS: '3 Yr',
    FOUR_YEARS: '4 Yr',
    FIVE_YEARS: '5 Yr'
  },
  SHIFTS: {
    ONE: '1 Shift',
    TWO: '2 Shifts',
    THREE: '3 Shifts',
    FOUR: '4 Shifts',
    FIVE: '5 Shifts',
    SIX: '6 Shifts',
    SEVEN: '7 Shifts'
  }
};

export const DEFAULT_DATE_RANGE = {
  startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
  endDate: new Date()
};

export const CHART_COLORS = {
  primary: '#1976d2',
  secondary: '#f06292',
  accent: '#00e5ff'
};
